"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStateCityService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const city_entity_1 = require("../../../shared/database/typeorm/entities/city.entity");
const state_entity_1 = require("../../../shared/database/typeorm/entities/state.entity");
const typeorm_2 = require("typeorm");
let CreateStateCityService = class CreateStateCityService {
    constructor(stateRepository, cityRepository) {
        this.stateRepository = stateRepository;
        this.cityRepository = cityRepository;
    }
    async perform(stateDto) {
        let state = await this.stateRepository.findOne({
            where: { abbreviation: stateDto.abbreviation },
        });
        if (!state) {
            state = new state_entity_1.StateEntity();
            state.name = stateDto.name;
            state.abbreviation = stateDto.abbreviation;
            state = await this.stateRepository.save(state);
        }
        if (!stateDto.cities) {
            throw new common_1.BadRequestException('Estado já registrado.');
        }
        await Promise.all(stateDto.cities.map(async (cityDto) => {
            const city = new city_entity_1.CityEntity();
            city.name = cityDto.name;
            city.state = state;
            await this.cityRepository.save(city);
        }));
    }
};
exports.CreateStateCityService = CreateStateCityService;
exports.CreateStateCityService = CreateStateCityService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(state_entity_1.StateEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(city_entity_1.CityEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CreateStateCityService);
//# sourceMappingURL=create-state-city.service.js.map