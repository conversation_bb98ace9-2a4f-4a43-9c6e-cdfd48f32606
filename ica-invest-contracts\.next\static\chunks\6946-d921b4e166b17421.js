(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6946],{5859:function(e,t,n){"use strict";n.d(t,{Ry:function(){return hideOthers}});var i=new WeakMap,a=new WeakMap,o={},s=0,unwrapHost=function(e){return e&&(e.host||unwrapHost(e.parentNode))},applyAttributeToOthers=function(e,t,n,c){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=unwrapHost(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});o[n]||(o[n]=new WeakMap);var p=o[n],f=[],v=new Set,g=new Set(u),keep=function(e){!e||v.has(e)||(v.add(e),keep(e.parentNode))};u.forEach(keep);var deep=function(e){!e||g.has(e)||Array.prototype.forEach.call(e.children,function(e){if(v.has(e))deep(e);else try{var t=e.getAttribute(c),o=null!==t&&"false"!==t,s=(i.get(e)||0)+1,u=(p.get(e)||0)+1;i.set(e,s),p.set(e,u),f.push(e),1===s&&o&&a.set(e,!0),1===u&&e.setAttribute(n,"true"),o||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return deep(t),v.clear(),s++,function(){f.forEach(function(e){var t=i.get(e)-1,o=p.get(e)-1;i.set(e,t),p.set(e,o),t||(a.has(e)||e.removeAttribute(c),a.delete(e)),o||e.removeAttribute(n)}),--s||(i=new WeakMap,i=new WeakMap,a=new WeakMap,o={})}},hideOthers=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),a=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return a?(i.push.apply(i,Array.from(a.querySelectorAll("[aria-live], script"))),applyAttributeToOthers(i,a,n,"aria-hidden")):function(){return null}}},8544:function(e,t){"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(0===n.length)return!0;var i=e.name||"",a=(e.type||"").toLowerCase(),o=a.replace(/\/.*$/,"");return n.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):a===t})}return!0}},8203:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var i=n(5531);let a=(0,i.Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},1291:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var i=n(5531);let a=(0,i.Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},7158:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var i=n(5531);let a=(0,i.Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},2549:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var i=n(5531);let a=(0,i.Z)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6964:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let i=n(1024),a=n(8533),o=a._(n(2265)),s=i._(n(4887)),c=i._(n(9232)),u=n(3655),p=n(6921),f=n(8143);n(7707);let v=n(7650),g=i._(n(5324)),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function handleLoading(e,t,n,i,a,o){let s=null==e?void 0:e.src;if(!e||e["data-loaded-src"]===s)return;e["data-loaded-src"]=s;let c="decode"in e?e.decode():Promise.resolve();c.catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&a(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,a=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{a=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}})}function getDynamicProps(e){let[t,n]=o.version.split("."),i=parseInt(t,10),a=parseInt(n,10);return i>18||18===i&&a>=3?{fetchPriority:e}:{fetchpriority:e}}let y=(0,o.forwardRef)((e,t)=>{let{src:n,srcSet:i,sizes:a,height:s,width:c,decoding:u,className:p,style:f,fetchPriority:v,placeholder:g,loading:h,unoptimized:y,fill:b,onLoadRef:w,onLoadingCompleteRef:D,setBlurComplete:C,setShowAltText:S,onLoad:E,onError:j,...M}=e;return o.default.createElement("img",{...M,...getDynamicProps(v),loading:h,width:c,height:s,decoding:u,"data-nimg":b?"fill":"1",className:p,style:f,sizes:a,srcSet:i,src:n,ref:(0,o.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(j&&(e.src=e.src),e.complete&&handleLoading(e,g,w,D,C,y))},[n,g,w,D,C,j,y,t]),onLoad:e=>{let t=e.currentTarget;handleLoading(t,g,w,D,C,y)},onError:e=>{S(!0),"empty"!==g&&C(!0),j&&j(e)}})});function ImagePreload(e){let{isAppRouter:t,imgAttributes:n}=e,i={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...getDynamicProps(n.fetchPriority)};return t&&s.default.preload?(s.default.preload(n.src,i),null):o.default.createElement(c.default,null,o.default.createElement("link",{key:"__nimg-"+n.src+n.srcSet+n.sizes,rel:"preload",href:n.srcSet?void 0:n.src,...i}))}let b=(0,o.forwardRef)((e,t)=>{let n=(0,o.useContext)(v.RouterContext),i=(0,o.useContext)(f.ImageConfigContext),a=(0,o.useMemo)(()=>{let e=h||i||p.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),n=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:n}},[i]),{onLoad:s,onLoadingComplete:c}=e,b=(0,o.useRef)(s);(0,o.useEffect)(()=>{b.current=s},[s]);let w=(0,o.useRef)(c);(0,o.useEffect)(()=>{w.current=c},[c]);let[D,C]=(0,o.useState)(!1),[S,E]=(0,o.useState)(!1),{props:j,meta:M}=(0,u.getImgProps)(e,{defaultLoader:g.default,imgConf:a,blurComplete:D,showAltText:S});return o.default.createElement(o.default.Fragment,null,o.default.createElement(y,{...j,unoptimized:M.unoptimized,placeholder:M.placeholder,fill:M.fill,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:C,setShowAltText:E,ref:t}),M.priority?o.default.createElement(ImagePreload,{isAppRouter:!n,imgAttributes:j}):null)});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6948:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return o}});let i=n(1024),a=i._(n(2265)),o=a.default.createContext({})},8827:function(e,t){"use strict";function isInAmpMode(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||n&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return isInAmpMode}})},3655:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return getImgProps}}),n(7707);let i=n(8932),a=n(6921);function isStaticRequire(e){return void 0!==e.default}function getInt(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function getImgProps(e,t){var n;let o,s,c,{src:u,sizes:p,unoptimized:f=!1,priority:v=!1,loading:g,className:h,quality:y,width:b,height:w,fill:D=!1,style:C,onLoad:S,onLoadingComplete:E,placeholder:j="empty",blurDataURL:M,fetchPriority:O,layout:P,objectFit:T,objectPosition:A,lazyBoundary:L,lazyRoot:N,...I}=e,{imgConf:W,showAltText:z,blurComplete:q,defaultLoader:H}=t,B=W||a.imageConfigDefault;if("allSizes"in B)o=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t);o={...B,allSizes:e,deviceSizes:t}}let Y=I.loader||H;delete I.loader,delete I.srcSet;let V="__next_img_default"in Y;if(V){if("custom"===o.loader)throw Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=Y;Y=t=>{let{config:n,...i}=t;return e(i)}}if(P){"fill"===P&&(D=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[P];e&&(C={...C,...e});let t={responsive:"100vw",fill:"100vw"}[P];t&&!p&&(p=t)}let Z="",$=getInt(b),G=getInt(w);if("object"==typeof(n=u)&&(isStaticRequire(n)||void 0!==n.src)){let e=isStaticRequire(u)?u.default:u;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(s=e.blurWidth,c=e.blurHeight,M=M||e.blurDataURL,Z=e.src,!D){if($||G){if($&&!G){let t=$/e.width;G=Math.round(e.height*t)}else if(!$&&G){let t=G/e.height;$=Math.round(e.width*t)}}else $=e.width,G=e.height}}let X=!v&&("lazy"===g||void 0===g);(!(u="string"==typeof u?u:Z)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,X=!1),o.unoptimized&&(f=!0),V&&u.endsWith(".svg")&&!o.dangerouslyAllowSVG&&(f=!0),v&&(O="high");let Q=getInt(y),J=Object.assign(D?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:T,objectPosition:A}:{},z?{}:{color:"transparent"},C),et=q||"empty"===j?null:"blur"===j?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:$,heightInt:G,blurWidth:s,blurHeight:c,blurDataURL:M||"",objectFit:J.objectFit})+'")':'url("'+j+'")',en=et?{backgroundSize:J.objectFit||"cover",backgroundPosition:J.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:et}:{},ei=function(e){let{config:t,src:n,unoptimized:i,width:a,quality:o,sizes:s,loader:c}=e;if(i)return{src:n,srcSet:void 0,sizes:void 0};let{widths:u,kind:p}=function(e,t,n){let{deviceSizes:i,allSizes:a}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(n);i)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:a,kind:"w"}}if("number"!=typeof t)return{widths:i,kind:"w"};let o=[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))];return{widths:o,kind:"x"}}(t,a,s),f=u.length-1;return{sizes:s||"w"!==p?s:"100vw",srcSet:u.map((e,i)=>c({config:t,src:n,quality:o,width:e})+" "+("w"===p?e:i+1)+p).join(", "),src:c({config:t,src:n,quality:o,width:u[f]})}}({config:o,src:u,unoptimized:f,width:$,quality:Q,sizes:p,loader:Y}),ea={...I,loading:X?"lazy":g,fetchPriority:O,width:$,height:G,decoding:"async",className:h,style:{...J,...en},sizes:ei.sizes,srcSet:ei.srcSet,src:ei.src},er={unoptimized:f,priority:v,placeholder:j,fill:D};return{props:ea,meta:er}}},9232:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{defaultHead:function(){return defaultHead},default:function(){return _default}});let i=n(1024),a=n(8533),o=a._(n(2265)),s=i._(n(5184)),c=n(6948),u=n(3305),p=n(8827);function defaultHead(e){void 0===e&&(e=!1);let t=[o.default.createElement("meta",{charSet:"utf-8"})];return e||t.push(o.default.createElement("meta",{name:"viewport",content:"width=device-width"})),t}function onlyReactElement(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(7707);let f=["name","httpEquiv","charSet","itemProp"];function reduceComponents(e,t){let{inAmpMode:n}=t;return e.reduce(onlyReactElement,[]).reverse().concat(defaultHead(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,i={};return a=>{let o=!0,s=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){s=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?o=!1:t.add(a.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(a.props.hasOwnProperty(t)){if("charSet"===t)n.has(t)?o=!1:n.add(t);else{let e=a.props[t],n=i[t]||new Set;("name"!==t||!s)&&n.has(e)?o=!1:(n.add(e),i[t]=n)}}}}return o}}()).reverse().map((e,t)=>{let i=e.key||t;if(!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:i})})}let _default=function(e){let{children:t}=e,n=(0,o.useContext)(c.AmpStateContext),i=(0,o.useContext)(u.HeadManagerContext);return o.default.createElement(s.default,{reduceComponentsToState:reduceComponents,headManager:i,inAmpMode:(0,p.isInAmpMode)(n)},t)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8932:function(e,t){"use strict";function getImageBlurSvg(e){let{widthInt:t,heightInt:n,blurWidth:i,blurHeight:a,blurDataURL:o,objectFit:s}=e,c=i?40*i:t,u=a?40*a:n,p=c&&u?"viewBox='0 0 "+c+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+p+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(p?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return getImageBlurSvg}})},8143:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return s}});let i=n(1024),a=i._(n(2265)),o=n(6921),s=a.default.createContext(o.imageConfigDefault)},6921:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return i}});let n=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},2597:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return u},unstable_getImgProps:function(){return unstable_getImgProps}});let i=n(1024),a=n(3655),o=n(7707),s=n(6964),c=i._(n(5324)),unstable_getImgProps=e=>{(0,o.warnOnce)("Warning: unstable_getImgProps() is experimental and may change or be removed at any time. Use at your own risk.");let{props:t}=(0,a.getImgProps)(e,{defaultLoader:c.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}},u=s.Image},5324:function(e,t){"use strict";function defaultLoader(e){let{config:t,src:n,width:i,quality:a}=e;return t.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+(a||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),defaultLoader.__next_img_default=!0;let n=defaultLoader},7650:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return o}});let i=n(1024),a=i._(n(2265)),o=a.default.createContext(null)},5184:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return SideEffect}});let i=n(8533),a=i._(n(2265)),o=a.useLayoutEffect,s=a.useEffect;function SideEffect(e){let{headManager:t,reduceComponentsToState:n}=e;function emitChange(){if(t&&t.mountedInstances){let i=a.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(i,e))}}return o(()=>{var n;return null==t||null==(n=t.mountedInstances)||n.add(e.children),()=>{var n;null==t||null==(n=t.mountedInstances)||n.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=emitChange),()=>{t&&(t._pendingUpdate=emitChange)})),s(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},7707:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return warnOnce}});let warnOnce=e=>{}},6691:function(e,t,n){e.exports=n(2597)},4033:function(e,t,n){e.exports=n(290)},3018:function(e,t,n){"use strict";var i=n(1289);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,n,a,o,s){if(s!==i){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},4275:function(e,t,n){e.exports=n(3018)()},1289:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3170:function(e,t,n){"use strict";n.d(t,{_W:function(){return DayPicker}});var i,a,o,s=n(7437),c=n(2265);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function requiredArgs(e,t){if(t.length<e)throw TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function isDate(e){return requiredArgs(1,arguments),e instanceof Date||"object"===_typeof(e)&&"[object Date]"===Object.prototype.toString.call(e)}function toDate(e){requiredArgs(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"===_typeof(e)&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):(("string"==typeof e||"[object String]"===t)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))}function toInteger(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function startOfUTCISOWeek(e){requiredArgs(1,arguments);var t=toDate(e),n=t.getUTCDay();return t.setUTCDate(t.getUTCDate()-((n<1?7:0)+n-1)),t.setUTCHours(0,0,0,0),t}function getUTCISOWeekYear(e){requiredArgs(1,arguments);var t=toDate(e),n=t.getUTCFullYear(),i=new Date(0);i.setUTCFullYear(n+1,0,4),i.setUTCHours(0,0,0,0);var a=startOfUTCISOWeek(i),o=new Date(0);o.setUTCFullYear(n,0,4),o.setUTCHours(0,0,0,0);var s=startOfUTCISOWeek(o);return t.getTime()>=a.getTime()?n+1:t.getTime()>=s.getTime()?n:n-1}var u={};function startOfUTCWeek(e,t){requiredArgs(1,arguments);var n,i,a,o,s,c,p,f,v=toInteger(null!==(n=null!==(i=null!==(a=null!==(o=null==t?void 0:t.weekStartsOn)&&void 0!==o?o:null==t?void 0:null===(s=t.locale)||void 0===s?void 0:null===(c=s.options)||void 0===c?void 0:c.weekStartsOn)&&void 0!==a?a:u.weekStartsOn)&&void 0!==i?i:null===(p=u.locale)||void 0===p?void 0:null===(f=p.options)||void 0===f?void 0:f.weekStartsOn)&&void 0!==n?n:0);if(!(v>=0&&v<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var g=toDate(e),h=g.getUTCDay();return g.setUTCDate(g.getUTCDate()-((h<v?7:0)+h-v)),g.setUTCHours(0,0,0,0),g}function getUTCWeekYear(e,t){requiredArgs(1,arguments);var n,i,a,o,s,c,p,f,v=toDate(e),g=v.getUTCFullYear(),h=toInteger(null!==(n=null!==(i=null!==(a=null!==(o=null==t?void 0:t.firstWeekContainsDate)&&void 0!==o?o:null==t?void 0:null===(s=t.locale)||void 0===s?void 0:null===(c=s.options)||void 0===c?void 0:c.firstWeekContainsDate)&&void 0!==a?a:u.firstWeekContainsDate)&&void 0!==i?i:null===(p=u.locale)||void 0===p?void 0:null===(f=p.options)||void 0===f?void 0:f.firstWeekContainsDate)&&void 0!==n?n:1);if(!(h>=1&&h<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var y=new Date(0);y.setUTCFullYear(g+1,0,h),y.setUTCHours(0,0,0,0);var b=startOfUTCWeek(y,t),w=new Date(0);w.setUTCFullYear(g,0,h),w.setUTCHours(0,0,0,0);var D=startOfUTCWeek(w,t);return v.getTime()>=b.getTime()?g+1:v.getTime()>=D.getTime()?g:g-1}function addLeadingZeros(e,t){for(var n=Math.abs(e).toString();n.length<t;)n="0"+n;return(e<0?"-":"")+n}var p={y:function(e,t){var n=e.getUTCFullYear(),i=n>0?n:1-n;return addLeadingZeros("yy"===t?i%100:i,t.length)},M:function(e,t){var n=e.getUTCMonth();return"M"===t?String(n+1):addLeadingZeros(n+1,2)},d:function(e,t){return addLeadingZeros(e.getUTCDate(),t.length)},a:function(e,t){var n=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:function(e,t){return addLeadingZeros(e.getUTCHours()%12||12,t.length)},H:function(e,t){return addLeadingZeros(e.getUTCHours(),t.length)},m:function(e,t){return addLeadingZeros(e.getUTCMinutes(),t.length)},s:function(e,t){return addLeadingZeros(e.getUTCSeconds(),t.length)},S:function(e,t){var n=t.length;return addLeadingZeros(Math.floor(e.getUTCMilliseconds()*Math.pow(10,n-3)),t.length)}},f={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"};function formatTimezoneShort(e,t){var n=e>0?"-":"+",i=Math.abs(e),a=Math.floor(i/60),o=i%60;return 0===o?n+String(a):n+String(a)+(t||"")+addLeadingZeros(o,2)}function formatTimezoneWithOptionalMinutes(e,t){return e%60==0?(e>0?"-":"+")+addLeadingZeros(Math.abs(e)/60,2):formatTimezone(e,t)}function formatTimezone(e,t){var n=e>0?"-":"+",i=Math.abs(e);return n+addLeadingZeros(Math.floor(i/60),2)+(t||"")+addLeadingZeros(i%60,2)}var v={G:function(e,t,n){var i=e.getUTCFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(i,{width:"abbreviated"});case"GGGGG":return n.era(i,{width:"narrow"});default:return n.era(i,{width:"wide"})}},y:function(e,t,n){if("yo"===t){var i=e.getUTCFullYear(),a=i>0?i:1-i;return n.ordinalNumber(a,{unit:"year"})}return p.y(e,t)},Y:function(e,t,n,i){var a=getUTCWeekYear(e,i),o=a>0?a:1-a;return"YY"===t?addLeadingZeros(o%100,2):"Yo"===t?n.ordinalNumber(o,{unit:"year"}):addLeadingZeros(o,t.length)},R:function(e,t){return addLeadingZeros(getUTCISOWeekYear(e),t.length)},u:function(e,t){return addLeadingZeros(e.getUTCFullYear(),t.length)},Q:function(e,t,n){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"Q":return String(i);case"QQ":return addLeadingZeros(i,2);case"Qo":return n.ordinalNumber(i,{unit:"quarter"});case"QQQ":return n.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(i,{width:"narrow",context:"formatting"});default:return n.quarter(i,{width:"wide",context:"formatting"})}},q:function(e,t,n){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"q":return String(i);case"qq":return addLeadingZeros(i,2);case"qo":return n.ordinalNumber(i,{unit:"quarter"});case"qqq":return n.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(i,{width:"narrow",context:"standalone"});default:return n.quarter(i,{width:"wide",context:"standalone"})}},M:function(e,t,n){var i=e.getUTCMonth();switch(t){case"M":case"MM":return p.M(e,t);case"Mo":return n.ordinalNumber(i+1,{unit:"month"});case"MMM":return n.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(i,{width:"narrow",context:"formatting"});default:return n.month(i,{width:"wide",context:"formatting"})}},L:function(e,t,n){var i=e.getUTCMonth();switch(t){case"L":return String(i+1);case"LL":return addLeadingZeros(i+1,2);case"Lo":return n.ordinalNumber(i+1,{unit:"month"});case"LLL":return n.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(i,{width:"narrow",context:"standalone"});default:return n.month(i,{width:"wide",context:"standalone"})}},w:function(e,t,n,i){var a=function(e,t){requiredArgs(1,arguments);var n=toDate(e);return Math.round((startOfUTCWeek(n,t).getTime()-(function(e,t){requiredArgs(1,arguments);var n,i,a,o,s,c,p,f,v=toInteger(null!==(n=null!==(i=null!==(a=null!==(o=null==t?void 0:t.firstWeekContainsDate)&&void 0!==o?o:null==t?void 0:null===(s=t.locale)||void 0===s?void 0:null===(c=s.options)||void 0===c?void 0:c.firstWeekContainsDate)&&void 0!==a?a:u.firstWeekContainsDate)&&void 0!==i?i:null===(p=u.locale)||void 0===p?void 0:null===(f=p.options)||void 0===f?void 0:f.firstWeekContainsDate)&&void 0!==n?n:1),g=getUTCWeekYear(e,t),h=new Date(0);return h.setUTCFullYear(g,0,v),h.setUTCHours(0,0,0,0),startOfUTCWeek(h,t)})(n,t).getTime())/6048e5)+1}(e,i);return"wo"===t?n.ordinalNumber(a,{unit:"week"}):addLeadingZeros(a,t.length)},I:function(e,t,n){var i=function(e){requiredArgs(1,arguments);var t=toDate(e);return Math.round((startOfUTCISOWeek(t).getTime()-(function(e){requiredArgs(1,arguments);var t=getUTCISOWeekYear(e),n=new Date(0);return n.setUTCFullYear(t,0,4),n.setUTCHours(0,0,0,0),startOfUTCISOWeek(n)})(t).getTime())/6048e5)+1}(e);return"Io"===t?n.ordinalNumber(i,{unit:"week"}):addLeadingZeros(i,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getUTCDate(),{unit:"date"}):p.d(e,t)},D:function(e,t,n){var i=function(e){requiredArgs(1,arguments);var t=toDate(e),n=t.getTime();return t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0),Math.floor((n-t.getTime())/864e5)+1}(e);return"Do"===t?n.ordinalNumber(i,{unit:"dayOfYear"}):addLeadingZeros(i,t.length)},E:function(e,t,n){var i=e.getUTCDay();switch(t){case"E":case"EE":case"EEE":return n.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},e:function(e,t,n,i){var a=e.getUTCDay(),o=(a-i.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return addLeadingZeros(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,i){var a=e.getUTCDay(),o=(a-i.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return addLeadingZeros(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){var i=e.getUTCDay(),a=0===i?7:i;switch(t){case"i":return String(a);case"ii":return addLeadingZeros(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},a:function(e,t,n){var i=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(e,t,n){var i,a=e.getUTCHours();switch(i=12===a?f.noon:0===a?f.midnight:a/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(e,t,n){var i,a=e.getUTCHours();switch(i=a>=17?f.evening:a>=12?f.afternoon:a>=4?f.morning:f.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){var i=e.getUTCHours()%12;return 0===i&&(i=12),n.ordinalNumber(i,{unit:"hour"})}return p.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getUTCHours(),{unit:"hour"}):p.H(e,t)},K:function(e,t,n){var i=e.getUTCHours()%12;return"Ko"===t?n.ordinalNumber(i,{unit:"hour"}):addLeadingZeros(i,t.length)},k:function(e,t,n){var i=e.getUTCHours();return(0===i&&(i=24),"ko"===t)?n.ordinalNumber(i,{unit:"hour"}):addLeadingZeros(i,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):p.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):p.s(e,t)},S:function(e,t){return p.S(e,t)},X:function(e,t,n,i){var a=(i._originalDate||e).getTimezoneOffset();if(0===a)return"Z";switch(t){case"X":return formatTimezoneWithOptionalMinutes(a);case"XXXX":case"XX":return formatTimezone(a);default:return formatTimezone(a,":")}},x:function(e,t,n,i){var a=(i._originalDate||e).getTimezoneOffset();switch(t){case"x":return formatTimezoneWithOptionalMinutes(a);case"xxxx":case"xx":return formatTimezone(a);default:return formatTimezone(a,":")}},O:function(e,t,n,i){var a=(i._originalDate||e).getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+formatTimezoneShort(a,":");default:return"GMT"+formatTimezone(a,":")}},z:function(e,t,n,i){var a=(i._originalDate||e).getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+formatTimezoneShort(a,":");default:return"GMT"+formatTimezone(a,":")}},t:function(e,t,n,i){return addLeadingZeros(Math.floor((i._originalDate||e).getTime()/1e3),t.length)},T:function(e,t,n,i){return addLeadingZeros((i._originalDate||e).getTime(),t.length)}},dateLongFormatter=function(e,t){switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},timeLongFormatter=function(e,t){switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},g={p:timeLongFormatter,P:function(e,t){var n,i=e.match(/(P+)(p+)?/)||[],a=i[1],o=i[2];if(!o)return dateLongFormatter(e,t);switch(a){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",dateLongFormatter(a,t)).replace("{{time}}",timeLongFormatter(o,t))}};function getTimezoneOffsetInMilliseconds(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}var h=["D","DD"],y=["YY","YYYY"];function throwProtectedError(e,t,n){if("YYYY"===e)throw RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("YY"===e)throw RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("D"===e)throw RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("DD"===e)throw RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var b={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function buildFormatLongFn(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}var w={date:buildFormatLongFn({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:buildFormatLongFn({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:buildFormatLongFn({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},D={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function buildLocalizeFn(e){return function(t,n){var i;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&e.formattingValues){var a=e.defaultFormattingWidth||e.defaultWidth,o=null!=n&&n.width?String(n.width):a;i=e.formattingValues[o]||e.formattingValues[a]}else{var s=e.defaultWidth,c=null!=n&&n.width?String(n.width):e.defaultWidth;i=e.values[c]||e.values[s]}return i[e.argumentCallback?e.argumentCallback(t):t]}}function buildMatchFn(e){return function(t){var n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=i.width,o=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],s=t.match(o);if(!s)return null;var c=s[0],u=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],p=Array.isArray(u)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(u,function(e){return e.test(c)}):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(u,function(e){return e.test(c)});return n=e.valueCallback?e.valueCallback(p):p,{value:n=i.valueCallback?i.valueCallback(n):n,rest:t.slice(c.length)}}}var C={code:"en-US",formatDistance:function(e,t,n){var i,a=b[e];return(i="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",t.toString()),null!=n&&n.addSuffix)?n.comparison&&n.comparison>0?"in "+i:i+" ago":i},formatLong:w,formatRelative:function(e,t,n,i){return D[e]},localize:{ordinalNumber:function(e,t){var n=Number(e),i=n%100;if(i>20||i<10)switch(i%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:buildLocalizeFn({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:buildLocalizeFn({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:buildLocalizeFn({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:buildLocalizeFn({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:buildLocalizeFn({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(i={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}},function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.match(i.matchPattern);if(!n)return null;var a=n[0],o=e.match(i.parsePattern);if(!o)return null;var s=i.valueCallback?i.valueCallback(o[0]):o[0];return{value:s=t.valueCallback?t.valueCallback(s):s,rest:e.slice(a.length)}}),era:buildMatchFn({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:buildMatchFn({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:buildMatchFn({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:buildMatchFn({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:buildMatchFn({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},S=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,E=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,j=/^'([^]*?)'?$/,M=/''/g,O=/[a-zA-Z]/;function format_format(e,t,n){requiredArgs(2,arguments);var i,a,o,s,c,p,f,b,w,D,P,T,A,L,N,I,W,z,q=String(t),H=null!==(i=null!==(a=null==n?void 0:n.locale)&&void 0!==a?a:u.locale)&&void 0!==i?i:C,B=toInteger(null!==(o=null!==(s=null!==(c=null!==(p=null==n?void 0:n.firstWeekContainsDate)&&void 0!==p?p:null==n?void 0:null===(f=n.locale)||void 0===f?void 0:null===(b=f.options)||void 0===b?void 0:b.firstWeekContainsDate)&&void 0!==c?c:u.firstWeekContainsDate)&&void 0!==s?s:null===(w=u.locale)||void 0===w?void 0:null===(D=w.options)||void 0===D?void 0:D.firstWeekContainsDate)&&void 0!==o?o:1);if(!(B>=1&&B<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var Y=toInteger(null!==(P=null!==(T=null!==(A=null!==(L=null==n?void 0:n.weekStartsOn)&&void 0!==L?L:null==n?void 0:null===(N=n.locale)||void 0===N?void 0:null===(I=N.options)||void 0===I?void 0:I.weekStartsOn)&&void 0!==A?A:u.weekStartsOn)&&void 0!==T?T:null===(W=u.locale)||void 0===W?void 0:null===(z=W.options)||void 0===z?void 0:z.weekStartsOn)&&void 0!==P?P:0);if(!(Y>=0&&Y<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!H.localize)throw RangeError("locale must contain localize property");if(!H.formatLong)throw RangeError("locale must contain formatLong property");var V=toDate(e);if(!function(e){return requiredArgs(1,arguments),(!!isDate(e)||"number"==typeof e)&&!isNaN(Number(toDate(e)))}(V))throw RangeError("Invalid time value");var Z=getTimezoneOffsetInMilliseconds(V),$=function(e,t){return requiredArgs(2,arguments),function(e,t){requiredArgs(2,arguments);var n=toDate(e).getTime(),i=toInteger(t);return new Date(n+i)}(e,-toInteger(t))}(V,Z),G={firstWeekContainsDate:B,weekStartsOn:Y,locale:H,_originalDate:V};return q.match(E).map(function(e){var t=e[0];return"p"===t||"P"===t?(0,g[t])(e,H.formatLong):e}).join("").match(S).map(function(i){if("''"===i)return"'";var a,o=i[0];if("'"===o)return(a=i.match(j))?a[1].replace(M,"'"):i;var s=v[o];if(s)return null!=n&&n.useAdditionalWeekYearTokens||-1===y.indexOf(i)||throwProtectedError(i,t,String(e)),null!=n&&n.useAdditionalDayOfYearTokens||-1===h.indexOf(i)||throwProtectedError(i,t,String(e)),s($,i,H.localize,G);if(o.match(O))throw RangeError("Format string contains an unescaped latin alphabet character `"+o+"`");return i}).join("")}function startOfMonth(e){requiredArgs(1,arguments);var t=toDate(e);return t.setDate(1),t.setHours(0,0,0,0),t}function endOfMonth(e){requiredArgs(1,arguments);var t=toDate(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}function startOfDay(e){requiredArgs(1,arguments);var t=toDate(e);return t.setHours(0,0,0,0),t}function setMonth(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toInteger(t),a=n.getFullYear(),o=n.getDate(),s=new Date(0);s.setFullYear(a,i,15),s.setHours(0,0,0,0);var c=function(e){requiredArgs(1,arguments);var t=toDate(e),n=t.getFullYear(),i=t.getMonth(),a=new Date(0);return a.setFullYear(n,i+1,0),a.setHours(0,0,0,0),a.getDate()}(s);return n.setMonth(i,Math.min(o,c)),n}function setYear(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toInteger(t);return isNaN(n.getTime())?new Date(NaN):(n.setFullYear(i),n)}function differenceInCalendarMonths(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toDate(t);return 12*(n.getFullYear()-i.getFullYear())+(n.getMonth()-i.getMonth())}function addMonths(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toInteger(t);if(isNaN(i))return new Date(NaN);if(!i)return n;var a=n.getDate(),o=new Date(n.getTime());return(o.setMonth(n.getMonth()+i+1,0),a>=o.getDate())?o:(n.setFullYear(o.getFullYear(),o.getMonth(),a),n)}function isSameMonth(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toDate(t);return n.getFullYear()===i.getFullYear()&&n.getMonth()===i.getMonth()}function isBefore(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toDate(t);return n.getTime()<i.getTime()}function startOfWeek(e,t){requiredArgs(1,arguments);var n,i,a,o,s,c,p,f,v=toInteger(null!==(n=null!==(i=null!==(a=null!==(o=null==t?void 0:t.weekStartsOn)&&void 0!==o?o:null==t?void 0:null===(s=t.locale)||void 0===s?void 0:null===(c=s.options)||void 0===c?void 0:c.weekStartsOn)&&void 0!==a?a:u.weekStartsOn)&&void 0!==i?i:null===(p=u.locale)||void 0===p?void 0:null===(f=p.options)||void 0===f?void 0:f.weekStartsOn)&&void 0!==n?n:0);if(!(v>=0&&v<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var g=toDate(e),h=g.getDay();return g.setDate(g.getDate()-((h<v?7:0)+h-v)),g.setHours(0,0,0,0),g}function startOfISOWeek(e){return requiredArgs(1,arguments),startOfWeek(e,{weekStartsOn:1})}function addDays(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toInteger(t);return isNaN(i)?new Date(NaN):(i&&n.setDate(n.getDate()+i),n)}function isSameDay(e,t){requiredArgs(2,arguments);var n=startOfDay(e),i=startOfDay(t);return n.getTime()===i.getTime()}function isAfter(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toDate(t);return n.getTime()>i.getTime()}function subDays(e,t){return requiredArgs(2,arguments),addDays(e,-toInteger(t))}function differenceInCalendarDays_differenceInCalendarDays(e,t){requiredArgs(2,arguments);var n=startOfDay(e),i=startOfDay(t);return Math.round((n.getTime()-getTimezoneOffsetInMilliseconds(n)-(i.getTime()-getTimezoneOffsetInMilliseconds(i)))/864e5)}function addWeeks(e,t){return requiredArgs(2,arguments),addDays(e,7*toInteger(t))}function addYears(e,t){return requiredArgs(2,arguments),addMonths(e,12*toInteger(t))}function endOfWeek(e,t){requiredArgs(1,arguments);var n,i,a,o,s,c,p,f,v=toInteger(null!==(n=null!==(i=null!==(a=null!==(o=null==t?void 0:t.weekStartsOn)&&void 0!==o?o:null==t?void 0:null===(s=t.locale)||void 0===s?void 0:null===(c=s.options)||void 0===c?void 0:c.weekStartsOn)&&void 0!==a?a:u.weekStartsOn)&&void 0!==i?i:null===(p=u.locale)||void 0===p?void 0:null===(f=p.options)||void 0===f?void 0:f.weekStartsOn)&&void 0!==n?n:0);if(!(v>=0&&v<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var g=toDate(e),h=g.getDay();return g.setDate(g.getDate()+((h<v?-7:0)+6-(h-v))),g.setHours(23,59,59,999),g}function endOfISOWeek(e){return requiredArgs(1,arguments),endOfWeek(e,{weekStartsOn:1})}var __assign=function(){return(__assign=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function __spreadArray(e,t,n){if(n||2==arguments.length)for(var i,a=0,o=t.length;a<o;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))}function isDayPickerMultiple(e){return"multiple"===e.mode}function isDayPickerRange(e){return"range"===e.mode}function isDayPickerSingle(e){return"single"===e.mode}"function"==typeof SuppressedError&&SuppressedError;var P={root:"rdp",multiple_months:"rdp-multiple_months",with_weeknumber:"rdp-with_weeknumber",vhidden:"rdp-vhidden",button_reset:"rdp-button_reset",button:"rdp-button",caption:"rdp-caption",caption_start:"rdp-caption_start",caption_end:"rdp-caption_end",caption_between:"rdp-caption_between",caption_label:"rdp-caption_label",caption_dropdowns:"rdp-caption_dropdowns",dropdown:"rdp-dropdown",dropdown_month:"rdp-dropdown_month",dropdown_year:"rdp-dropdown_year",dropdown_icon:"rdp-dropdown_icon",months:"rdp-months",month:"rdp-month",table:"rdp-table",tbody:"rdp-tbody",tfoot:"rdp-tfoot",head:"rdp-head",head_row:"rdp-head_row",head_cell:"rdp-head_cell",nav:"rdp-nav",nav_button:"rdp-nav_button",nav_button_previous:"rdp-nav_button_previous",nav_button_next:"rdp-nav_button_next",nav_icon:"rdp-nav_icon",row:"rdp-row",weeknumber:"rdp-weeknumber",cell:"rdp-cell",day:"rdp-day",day_today:"rdp-day_today",day_outside:"rdp-day_outside",day_selected:"rdp-day_selected",day_disabled:"rdp-day_disabled",day_hidden:"rdp-day_hidden",day_range_start:"rdp-day_range_start",day_range_end:"rdp-day_range_end",day_range_middle:"rdp-day_range_middle"},T=Object.freeze({__proto__:null,formatCaption:function(e,t){return format_format(e,"LLLL y",t)},formatDay:function(e,t){return format_format(e,"d",t)},formatMonthCaption:function(e,t){return format_format(e,"LLLL",t)},formatWeekNumber:function(e){return"".concat(e)},formatWeekdayName:function(e,t){return format_format(e,"cccccc",t)},formatYearCaption:function(e,t){return format_format(e,"yyyy",t)}}),A=Object.freeze({__proto__:null,labelDay:function(e,t,n){return format_format(e,"do MMMM (EEEE)",n)},labelMonthDropdown:function(){return"Month: "},labelNext:function(){return"Go to next month"},labelPrevious:function(){return"Go to previous month"},labelWeekNumber:function(e){return"Week n. ".concat(e)},labelWeekday:function(e,t){return format_format(e,"cccc",t)},labelYearDropdown:function(){return"Year: "}}),L=(0,c.createContext)(void 0);function DayPickerProvider(e){var t,n,i,a,o,c,u,p,f=e.initialProps,v={captionLayout:"buttons",classNames:P,formatters:T,labels:A,locale:C,modifiersClassNames:{},modifiers:{},numberOfMonths:1,styles:{},today:new Date,mode:"default"},g=(t=f.fromYear,n=f.toYear,i=f.fromMonth,a=f.toMonth,o=f.fromDate,c=f.toDate,i?o=startOfMonth(i):t&&(o=new Date(t,0,1)),a?c=endOfMonth(a):n&&(c=new Date(n,11,31)),{fromDate:o?startOfDay(o):void 0,toDate:c?startOfDay(c):void 0}),h=g.fromDate,y=g.toDate,b=null!==(u=f.captionLayout)&&void 0!==u?u:v.captionLayout;"buttons"===b||h&&y||(b="buttons"),(isDayPickerSingle(f)||isDayPickerMultiple(f)||isDayPickerRange(f))&&(p=f.onSelect);var w=__assign(__assign(__assign({},v),f),{captionLayout:b,classNames:__assign(__assign({},v.classNames),f.classNames),components:__assign({},f.components),formatters:__assign(__assign({},v.formatters),f.formatters),fromDate:h,labels:__assign(__assign({},v.labels),f.labels),mode:f.mode||v.mode,modifiers:__assign(__assign({},v.modifiers),f.modifiers),modifiersClassNames:__assign(__assign({},v.modifiersClassNames),f.modifiersClassNames),onSelect:p,styles:__assign(__assign({},v.styles),f.styles),toDate:y});return(0,s.jsx)(L.Provider,{value:w,children:e.children})}function useDayPicker(){var e=(0,c.useContext)(L);if(!e)throw Error("useDayPicker must be used within a DayPickerProvider.");return e}function CaptionLabel(e){var t=useDayPicker(),n=t.locale,i=t.classNames,a=t.styles,o=t.formatters.formatCaption;return(0,s.jsx)("div",{className:i.caption_label,style:a.caption_label,"aria-live":"polite",role:"presentation",id:e.id,children:o(e.displayMonth,{locale:n})})}function IconDropdown(e){return(0,s.jsx)("svg",__assign({width:"8px",height:"8px",viewBox:"0 0 120 120","data-testid":"iconDropdown"},e,{children:(0,s.jsx)("path",{d:"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.**********,59.4824074 -0.**********,52.5175926 4.22182541,48.2218254 Z",fill:"currentColor",fillRule:"nonzero"})}))}function Dropdown(e){var t,n,i=e.onChange,a=e.value,o=e.children,c=e.caption,u=e.className,p=e.style,f=useDayPicker(),v=null!==(n=null===(t=f.components)||void 0===t?void 0:t.IconDropdown)&&void 0!==n?n:IconDropdown;return(0,s.jsxs)("div",{className:u,style:p,children:[(0,s.jsx)("span",{className:f.classNames.vhidden,children:e["aria-label"]}),(0,s.jsx)("select",{name:e.name,"aria-label":e["aria-label"],className:f.classNames.dropdown,style:f.styles.dropdown,value:a,onChange:i,children:o}),(0,s.jsxs)("div",{className:f.classNames.caption_label,style:f.styles.caption_label,"aria-hidden":"true",children:[c,(0,s.jsx)(v,{className:f.classNames.dropdown_icon,style:f.styles.dropdown_icon})]})]})}function MonthsDropdown(e){var t,n=useDayPicker(),i=n.fromDate,a=n.toDate,o=n.styles,c=n.locale,u=n.formatters.formatMonthCaption,p=n.classNames,f=n.components,v=n.labels.labelMonthDropdown;if(!i||!a)return(0,s.jsx)(s.Fragment,{});var g=[];if(function(e,t){requiredArgs(2,arguments);var n=toDate(e),i=toDate(t);return n.getFullYear()===i.getFullYear()}(i,a))for(var h=startOfMonth(i),y=i.getMonth();y<=a.getMonth();y++)g.push(setMonth(h,y));else for(var h=startOfMonth(new Date),y=0;y<=11;y++)g.push(setMonth(h,y));var b=null!==(t=null==f?void 0:f.Dropdown)&&void 0!==t?t:Dropdown;return(0,s.jsx)(b,{name:"months","aria-label":v(),className:p.dropdown_month,style:o.dropdown_month,onChange:function(t){var n=Number(t.target.value),i=setMonth(startOfMonth(e.displayMonth),n);e.onChange(i)},value:e.displayMonth.getMonth(),caption:u(e.displayMonth,{locale:c}),children:g.map(function(e){return(0,s.jsx)("option",{value:e.getMonth(),children:u(e,{locale:c})},e.getMonth())})})}function YearsDropdown(e){var t,n=e.displayMonth,i=useDayPicker(),a=i.fromDate,o=i.toDate,c=i.locale,u=i.styles,p=i.classNames,f=i.components,v=i.formatters.formatYearCaption,g=i.labels.labelYearDropdown,h=[];if(!a||!o)return(0,s.jsx)(s.Fragment,{});for(var y=a.getFullYear(),b=o.getFullYear(),w=y;w<=b;w++)h.push(setYear(function(e){requiredArgs(1,arguments);var t=toDate(e),n=new Date(0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}(new Date),w));var D=null!==(t=null==f?void 0:f.Dropdown)&&void 0!==t?t:Dropdown;return(0,s.jsx)(D,{name:"years","aria-label":g(),className:p.dropdown_year,style:u.dropdown_year,onChange:function(t){var i=setYear(startOfMonth(n),Number(t.target.value));e.onChange(i)},value:n.getFullYear(),caption:v(n,{locale:c}),children:h.map(function(e){return(0,s.jsx)("option",{value:e.getFullYear(),children:v(e,{locale:c})},e.getFullYear())})})}var N=(0,c.createContext)(void 0);function NavigationProvider(e){var t,n,i,a,o,u,p,f,v,g,h,y,b,w,D,C,S=useDayPicker(),E=(D=(i=(n=t=useDayPicker()).month,a=n.defaultMonth,o=n.today,u=i||a||o||new Date,p=n.toDate,f=n.fromDate,v=n.numberOfMonths,p&&0>differenceInCalendarMonths(p,u)&&(u=addMonths(p,-1*((void 0===v?1:v)-1))),f&&0>differenceInCalendarMonths(u,f)&&(u=f),g=startOfMonth(u),h=t.month,b=(y=(0,c.useState)(g))[0],w=[void 0===h?b:h,y[1]])[0],C=w[1],[D,function(e){if(!t.disableNavigation){var n,i=startOfMonth(e);C(i),null===(n=t.onMonthChange)||void 0===n||n.call(t,i)}}]),j=E[0],M=E[1],O=function(e,t){for(var n=t.reverseMonths,i=t.numberOfMonths,a=startOfMonth(e),o=differenceInCalendarMonths(startOfMonth(addMonths(a,i)),a),s=[],c=0;c<o;c++){var u=addMonths(a,c);s.push(u)}return n&&(s=s.reverse()),s}(j,S),P=function(e,t){if(!t.disableNavigation){var n=t.toDate,i=t.pagedNavigation,a=t.numberOfMonths,o=void 0===a?1:a,s=i?o:1,c=startOfMonth(e);if(!n||!(differenceInCalendarMonths(n,e)<o))return addMonths(c,s)}}(j,S),T=function(e,t){if(!t.disableNavigation){var n=t.fromDate,i=t.pagedNavigation,a=t.numberOfMonths,o=i?void 0===a?1:a:1,s=startOfMonth(e);if(!n||!(0>=differenceInCalendarMonths(s,n)))return addMonths(s,-o)}}(j,S),isDateDisplayed=function(e){return O.some(function(t){return isSameMonth(e,t)})};return(0,s.jsx)(N.Provider,{value:{currentMonth:j,displayMonths:O,goToMonth:M,goToDate:function(e,t){isDateDisplayed(e)||(t&&isBefore(e,t)?M(addMonths(e,1+-1*S.numberOfMonths)):M(e))},previousMonth:T,nextMonth:P,isDateDisplayed:isDateDisplayed},children:e.children})}function useNavigation(){var e=(0,c.useContext)(N);if(!e)throw Error("useNavigation must be used within a NavigationProvider");return e}function CaptionDropdowns(e){var t,n=useDayPicker(),i=n.classNames,a=n.styles,o=n.components,c=useNavigation().goToMonth,handleMonthChange=function(t){c(addMonths(t,e.displayIndex?-e.displayIndex:0))},u=null!==(t=null==o?void 0:o.CaptionLabel)&&void 0!==t?t:CaptionLabel,p=(0,s.jsx)(u,{id:e.id,displayMonth:e.displayMonth});return(0,s.jsxs)("div",{className:i.caption_dropdowns,style:a.caption_dropdowns,children:[(0,s.jsx)("div",{className:i.vhidden,children:p}),(0,s.jsx)(MonthsDropdown,{onChange:handleMonthChange,displayMonth:e.displayMonth}),(0,s.jsx)(YearsDropdown,{onChange:handleMonthChange,displayMonth:e.displayMonth})]})}function IconLeft(e){return(0,s.jsx)("svg",__assign({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:(0,s.jsx)("path",{d:"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z",fill:"currentColor",fillRule:"nonzero"})}))}function IconRight(e){return(0,s.jsx)("svg",__assign({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:(0,s.jsx)("path",{d:"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z",fill:"currentColor"})}))}var I=(0,c.forwardRef)(function(e,t){var n=useDayPicker(),i=n.classNames,a=n.styles,o=[i.button_reset,i.button];e.className&&o.push(e.className);var c=o.join(" "),u=__assign(__assign({},a.button_reset),a.button);return e.style&&Object.assign(u,e.style),(0,s.jsx)("button",__assign({},e,{ref:t,type:"button",className:c,style:u}))});function Navigation(e){var t,n,i=useDayPicker(),a=i.dir,o=i.locale,c=i.classNames,u=i.styles,p=i.labels,f=p.labelPrevious,v=p.labelNext,g=i.components;if(!e.nextMonth&&!e.previousMonth)return(0,s.jsx)(s.Fragment,{});var h=f(e.previousMonth,{locale:o}),y=[c.nav_button,c.nav_button_previous].join(" "),b=v(e.nextMonth,{locale:o}),w=[c.nav_button,c.nav_button_next].join(" "),D=null!==(t=null==g?void 0:g.IconRight)&&void 0!==t?t:IconRight,C=null!==(n=null==g?void 0:g.IconLeft)&&void 0!==n?n:IconLeft;return(0,s.jsxs)("div",{className:c.nav,style:u.nav,children:[!e.hidePrevious&&(0,s.jsx)(I,{name:"previous-month","aria-label":h,className:y,style:u.nav_button_previous,disabled:!e.previousMonth,onClick:e.onPreviousClick,children:"rtl"===a?(0,s.jsx)(D,{className:c.nav_icon,style:u.nav_icon}):(0,s.jsx)(C,{className:c.nav_icon,style:u.nav_icon})}),!e.hideNext&&(0,s.jsx)(I,{name:"next-month","aria-label":b,className:w,style:u.nav_button_next,disabled:!e.nextMonth,onClick:e.onNextClick,children:"rtl"===a?(0,s.jsx)(C,{className:c.nav_icon,style:u.nav_icon}):(0,s.jsx)(D,{className:c.nav_icon,style:u.nav_icon})})]})}function CaptionNavigation(e){var t=useDayPicker().numberOfMonths,n=useNavigation(),i=n.previousMonth,a=n.nextMonth,o=n.goToMonth,c=n.displayMonths,u=c.findIndex(function(t){return isSameMonth(e.displayMonth,t)}),p=0===u,f=u===c.length-1;return(0,s.jsx)(Navigation,{displayMonth:e.displayMonth,hideNext:t>1&&(p||!f),hidePrevious:t>1&&(f||!p),nextMonth:a,previousMonth:i,onPreviousClick:function(){i&&o(i)},onNextClick:function(){a&&o(a)}})}function Caption(e){var t,n,i=useDayPicker(),a=i.classNames,o=i.disableNavigation,c=i.styles,u=i.captionLayout,p=i.components,f=null!==(t=null==p?void 0:p.CaptionLabel)&&void 0!==t?t:CaptionLabel;return n=o?(0,s.jsx)(f,{id:e.id,displayMonth:e.displayMonth}):"dropdown"===u?(0,s.jsx)(CaptionDropdowns,{displayMonth:e.displayMonth,id:e.id}):"dropdown-buttons"===u?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(CaptionDropdowns,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id}),(0,s.jsx)(CaptionNavigation,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f,{id:e.id,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),(0,s.jsx)(CaptionNavigation,{displayMonth:e.displayMonth,id:e.id})]}),(0,s.jsx)("div",{className:a.caption,style:c.caption,children:n})}function Footer(e){var t=useDayPicker(),n=t.footer,i=t.styles,a=t.classNames.tfoot;return n?(0,s.jsx)("tfoot",{className:a,style:i.tfoot,children:(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:8,children:n})})}):(0,s.jsx)(s.Fragment,{})}function HeadRow(){var e=useDayPicker(),t=e.classNames,n=e.styles,i=e.showWeekNumber,a=e.locale,o=e.weekStartsOn,c=e.ISOWeek,u=e.formatters.formatWeekdayName,p=e.labels.labelWeekday,f=function(e,t,n){for(var i=n?startOfISOWeek(new Date):startOfWeek(new Date,{locale:e,weekStartsOn:t}),a=[],o=0;o<7;o++){var s=addDays(i,o);a.push(s)}return a}(a,o,c);return(0,s.jsxs)("tr",{style:n.head_row,className:t.head_row,children:[i&&(0,s.jsx)("td",{style:n.head_cell,className:t.head_cell}),f.map(function(e,i){return(0,s.jsx)("th",{scope:"col",className:t.head_cell,style:n.head_cell,"aria-label":p(e,{locale:a}),children:u(e,{locale:a})},i)})]})}function Head(){var e,t=useDayPicker(),n=t.classNames,i=t.styles,a=t.components,o=null!==(e=null==a?void 0:a.HeadRow)&&void 0!==e?e:HeadRow;return(0,s.jsx)("thead",{style:i.head,className:n.head,children:(0,s.jsx)(o,{})})}function DayContent(e){var t=useDayPicker(),n=t.locale,i=t.formatters.formatDay;return(0,s.jsx)(s.Fragment,{children:i(e.date,{locale:n})})}var W=(0,c.createContext)(void 0);function SelectMultipleProvider(e){return isDayPickerMultiple(e.initialProps)?(0,s.jsx)(SelectMultipleProviderInternal,{initialProps:e.initialProps,children:e.children}):(0,s.jsx)(W.Provider,{value:{selected:void 0,modifiers:{disabled:[]}},children:e.children})}function SelectMultipleProviderInternal(e){var t=e.initialProps,n=e.children,i=t.selected,a=t.min,o=t.max,c={disabled:[]};return i&&c.disabled.push(function(e){var t=o&&i.length>o-1,n=i.some(function(t){return isSameDay(t,e)});return!!(t&&!n)}),(0,s.jsx)(W.Provider,{value:{selected:i,onDayClick:function(e,n,s){if(null===(c=t.onDayClick)||void 0===c||c.call(t,e,n,s),(!n.selected||!a||(null==i?void 0:i.length)!==a)&&(n.selected||!o||(null==i?void 0:i.length)!==o)){var c,u,p=i?__spreadArray([],i,!0):[];if(n.selected){var f=p.findIndex(function(t){return isSameDay(e,t)});p.splice(f,1)}else p.push(e);null===(u=t.onSelect)||void 0===u||u.call(t,p,e,n,s)}},modifiers:c},children:n})}function useSelectMultiple(){var e=(0,c.useContext)(W);if(!e)throw Error("useSelectMultiple must be used within a SelectMultipleProvider");return e}var z=(0,c.createContext)(void 0);function SelectRangeProvider(e){return isDayPickerRange(e.initialProps)?(0,s.jsx)(SelectRangeProviderInternal,{initialProps:e.initialProps,children:e.children}):(0,s.jsx)(z.Provider,{value:{selected:void 0,modifiers:{range_start:[],range_end:[],range_middle:[],disabled:[]}},children:e.children})}function SelectRangeProviderInternal(e){var t=e.initialProps,n=e.children,i=t.selected,a=i||{},o=a.from,c=a.to,u=t.min,p=t.max,f={range_start:[],range_end:[],range_middle:[],disabled:[]};if(o?(f.range_start=[o],c?(f.range_end=[c],isSameDay(o,c)||(f.range_middle=[{after:o,before:c}])):f.range_end=[o]):c&&(f.range_start=[c],f.range_end=[c]),u&&(o&&!c&&f.disabled.push({after:subDays(o,u-1),before:addDays(o,u-1)}),o&&c&&f.disabled.push({after:o,before:addDays(o,u-1)}),!o&&c&&f.disabled.push({after:subDays(c,u-1),before:addDays(c,u-1)})),p){if(o&&!c&&(f.disabled.push({before:addDays(o,-p+1)}),f.disabled.push({after:addDays(o,p-1)})),o&&c){var v=p-(differenceInCalendarDays_differenceInCalendarDays(c,o)+1);f.disabled.push({before:subDays(o,v)}),f.disabled.push({after:addDays(c,v)})}!o&&c&&(f.disabled.push({before:addDays(c,-p+1)}),f.disabled.push({after:addDays(c,p-1)}))}return(0,s.jsx)(z.Provider,{value:{selected:i,onDayClick:function(e,n,a){null===(u=t.onDayClick)||void 0===u||u.call(t,e,n,a);var o,s,c,u,p,f=(s=(o=i||{}).from,c=o.to,s&&c?isSameDay(c,e)&&isSameDay(s,e)?void 0:isSameDay(c,e)?{from:c,to:void 0}:isSameDay(s,e)?void 0:isAfter(s,e)?{from:e,to:c}:{from:s,to:e}:c?isAfter(e,c)?{from:c,to:e}:{from:e,to:c}:s?isBefore(e,s)?{from:e,to:s}:{from:s,to:e}:{from:e,to:void 0});null===(p=t.onSelect)||void 0===p||p.call(t,f,e,n,a)},modifiers:f},children:n})}function useSelectRange(){var e=(0,c.useContext)(z);if(!e)throw Error("useSelectRange must be used within a SelectRangeProvider");return e}function matcherToArray(e){return Array.isArray(e)?__spreadArray([],e,!0):void 0!==e?[e]:[]}(a=o||(o={})).Outside="outside",a.Disabled="disabled",a.Selected="selected",a.Hidden="hidden",a.Today="today",a.RangeStart="range_start",a.RangeEnd="range_end",a.RangeMiddle="range_middle";var q=o.Selected,H=o.Disabled,B=o.Hidden,Y=o.Today,V=o.RangeEnd,Z=o.RangeMiddle,$=o.RangeStart,G=o.Outside,X=(0,c.createContext)(void 0);function ModifiersProvider(e){var t,n,i,a=useDayPicker(),o=useSelectMultiple(),c=useSelectRange(),u=((t={})[q]=matcherToArray(a.selected),t[H]=matcherToArray(a.disabled),t[B]=matcherToArray(a.hidden),t[Y]=[a.today],t[V]=[],t[Z]=[],t[$]=[],t[G]=[],a.fromDate&&t[H].push({before:a.fromDate}),a.toDate&&t[H].push({after:a.toDate}),isDayPickerMultiple(a)?t[H]=t[H].concat(o.modifiers[H]):isDayPickerRange(a)&&(t[H]=t[H].concat(c.modifiers[H]),t[$]=c.modifiers[$],t[Z]=c.modifiers[Z],t[V]=c.modifiers[V]),t),p=(n=a.modifiers,i={},Object.entries(n).forEach(function(e){var t=e[0],n=e[1];i[t]=matcherToArray(n)}),i),f=__assign(__assign({},u),p);return(0,s.jsx)(X.Provider,{value:f,children:e.children})}function useModifiers(){var e=(0,c.useContext)(X);if(!e)throw Error("useModifiers must be used within a ModifiersProvider");return e}function getActiveModifiers(e,t,n){var i=Object.keys(t).reduce(function(n,i){return t[i].some(function(t){if("boolean"==typeof t)return t;if(isDate(t))return isSameDay(e,t);if(Array.isArray(t)&&t.every(isDate))return t.includes(e);if(t&&"object"==typeof t&&"from"in t)return i=t.from,a=t.to,i&&a?(0>differenceInCalendarDays_differenceInCalendarDays(a,i)&&(i=(n=[a,i])[0],a=n[1]),differenceInCalendarDays_differenceInCalendarDays(e,i)>=0&&differenceInCalendarDays_differenceInCalendarDays(a,e)>=0):a?isSameDay(a,e):!!i&&isSameDay(i,e);if(t&&"object"==typeof t&&"dayOfWeek"in t)return t.dayOfWeek.includes(e.getDay());if(t&&"object"==typeof t&&"before"in t&&"after"in t){var n,i,a,o=differenceInCalendarDays_differenceInCalendarDays(t.before,e),s=differenceInCalendarDays_differenceInCalendarDays(t.after,e),c=o>0,u=s<0;return isAfter(t.before,t.after)?u&&c:c||u}return t&&"object"==typeof t&&"after"in t?differenceInCalendarDays_differenceInCalendarDays(e,t.after)>0:t&&"object"==typeof t&&"before"in t?differenceInCalendarDays_differenceInCalendarDays(t.before,e)>0:"function"==typeof t&&t(e)})&&n.push(i),n},[]),a={};return i.forEach(function(e){return a[e]=!0}),n&&!isSameMonth(e,n)&&(a.outside=!0),a}var Q=(0,c.createContext)(void 0);function FocusProvider(e){var t=useNavigation(),n=useModifiers(),i=(0,c.useState)(),a=i[0],o=i[1],u=(0,c.useState)(),p=u[0],f=u[1],v=function(e,t){for(var n,i,a=startOfMonth(e[0]),o=endOfMonth(e[e.length-1]),s=a;s<=o;){var c=getActiveModifiers(s,t);if(!(!c.disabled&&!c.hidden)){s=addDays(s,1);continue}if(c.selected)return s;c.today&&!i&&(i=s),n||(n=s),s=addDays(s,1)}return i||n}(t.displayMonths,n),g=(null!=a?a:p&&t.isDateDisplayed(p))?p:v,focus=function(e){o(e)},h=useDayPicker(),moveFocus=function(e,i){if(a){var o=function getNextFocus(e,t){var n=t.moveBy,i=t.direction,a=t.context,o=t.modifiers,s=t.retry,c=void 0===s?{count:0,lastFocused:e}:s,u=a.weekStartsOn,p=a.fromDate,f=a.toDate,v=a.locale,g=({day:addDays,week:addWeeks,month:addMonths,year:addYears,startOfWeek:function(e){return a.ISOWeek?startOfISOWeek(e):startOfWeek(e,{locale:v,weekStartsOn:u})},endOfWeek:function(e){return a.ISOWeek?endOfISOWeek(e):endOfWeek(e,{locale:v,weekStartsOn:u})}})[n](e,"after"===i?1:-1);"before"===i&&p?g=function(e){var t,n;if(requiredArgs(1,arguments),e&&"function"==typeof e.forEach)t=e;else{if("object"!==_typeof(e)||null===e)return new Date(NaN);t=Array.prototype.slice.call(e)}return t.forEach(function(e){var t=toDate(e);(void 0===n||n<t||isNaN(Number(t)))&&(n=t)}),n||new Date(NaN)}([p,g]):"after"===i&&f&&(g=function(e){var t,n;if(requiredArgs(1,arguments),e&&"function"==typeof e.forEach)t=e;else{if("object"!==_typeof(e)||null===e)return new Date(NaN);t=Array.prototype.slice.call(e)}return t.forEach(function(e){var t=toDate(e);(void 0===n||n>t||isNaN(t.getDate()))&&(n=t)}),n||new Date(NaN)}([f,g]));var h=!0;if(o){var y=getActiveModifiers(g,o);h=!y.disabled&&!y.hidden}return h?g:c.count>365?c.lastFocused:getNextFocus(g,{moveBy:n,direction:i,context:a,modifiers:o,retry:__assign(__assign({},c),{count:c.count+1})})}(a,{moveBy:e,direction:i,context:h,modifiers:n});isSameDay(a,o)||(t.goToDate(o,a),focus(o))}};return(0,s.jsx)(Q.Provider,{value:{focusedDay:a,focusTarget:g,blur:function(){f(a),o(void 0)},focus:focus,focusDayAfter:function(){return moveFocus("day","after")},focusDayBefore:function(){return moveFocus("day","before")},focusWeekAfter:function(){return moveFocus("week","after")},focusWeekBefore:function(){return moveFocus("week","before")},focusMonthBefore:function(){return moveFocus("month","before")},focusMonthAfter:function(){return moveFocus("month","after")},focusYearBefore:function(){return moveFocus("year","before")},focusYearAfter:function(){return moveFocus("year","after")},focusStartOfWeek:function(){return moveFocus("startOfWeek","before")},focusEndOfWeek:function(){return moveFocus("endOfWeek","after")}},children:e.children})}function useFocusContext(){var e=(0,c.useContext)(Q);if(!e)throw Error("useFocusContext must be used within a FocusProvider");return e}var J=(0,c.createContext)(void 0);function SelectSingleProvider(e){return isDayPickerSingle(e.initialProps)?(0,s.jsx)(SelectSingleProviderInternal,{initialProps:e.initialProps,children:e.children}):(0,s.jsx)(J.Provider,{value:{selected:void 0},children:e.children})}function SelectSingleProviderInternal(e){var t=e.initialProps,n=e.children,i={selected:t.selected,onDayClick:function(e,n,i){var a,o,s;if(null===(a=t.onDayClick)||void 0===a||a.call(t,e,n,i),n.selected&&!t.required){null===(o=t.onSelect)||void 0===o||o.call(t,void 0,e,n,i);return}null===(s=t.onSelect)||void 0===s||s.call(t,e,e,n,i)}};return(0,s.jsx)(J.Provider,{value:i,children:n})}function useSelectSingle(){var e=(0,c.useContext)(J);if(!e)throw Error("useSelectSingle must be used within a SelectSingleProvider");return e}function Day(e){var t,n,i,a,u,p,f,v,g,h,y,b,w,D,C,S,E,j,M,O,P,T,A,L,N,W,z,q,H,B,Y,V,Z,$,G,X,Q,J,et,en,ei,ea=(0,c.useRef)(null),er=(t=e.date,n=e.displayMonth,p=useDayPicker(),f=useFocusContext(),v=getActiveModifiers(t,useModifiers(),n),g=useDayPicker(),h=useSelectSingle(),y=useSelectMultiple(),b=useSelectRange(),D=(w=useFocusContext()).focusDayAfter,C=w.focusDayBefore,S=w.focusWeekAfter,E=w.focusWeekBefore,j=w.blur,M=w.focus,O=w.focusMonthBefore,P=w.focusMonthAfter,T=w.focusYearBefore,A=w.focusYearAfter,L=w.focusStartOfWeek,N=w.focusEndOfWeek,W=useDayPicker(),z=useSelectSingle(),q=useSelectMultiple(),H=useSelectRange(),B=isDayPickerSingle(W)?z.selected:isDayPickerMultiple(W)?q.selected:isDayPickerRange(W)?H.selected:void 0,Y=!!(p.onDayClick||"default"!==p.mode),(0,c.useEffect)(function(){var e;!v.outside&&f.focusedDay&&Y&&isSameDay(f.focusedDay,t)&&(null===(e=ea.current)||void 0===e||e.focus())},[f.focusedDay,t,ea,Y,v.outside]),Z=(V=[p.classNames.day],Object.keys(v).forEach(function(e){var t=p.modifiersClassNames[e];if(t)V.push(t);else if(Object.values(o).includes(e)){var n=p.classNames["day_".concat(e)];n&&V.push(n)}}),V).join(" "),$=__assign({},p.styles.day),Object.keys(v).forEach(function(e){var t;$=__assign(__assign({},$),null===(t=p.modifiersStyles)||void 0===t?void 0:t[e])}),G=$,X=!!(v.outside&&!p.showOutsideDays||v.hidden),Q=null!==(u=null===(a=p.components)||void 0===a?void 0:a.DayContent)&&void 0!==u?u:DayContent,J={style:G,className:Z,children:(0,s.jsx)(Q,{date:t,displayMonth:n,activeModifiers:v}),role:"gridcell"},et=f.focusTarget&&isSameDay(f.focusTarget,t)&&!v.outside,en=f.focusedDay&&isSameDay(f.focusedDay,t),ei=__assign(__assign(__assign({},J),((i={disabled:v.disabled,role:"gridcell"})["aria-selected"]=v.selected,i.tabIndex=en||et?0:-1,i)),{onClick:function(e){var n,i,a,o;isDayPickerSingle(g)?null===(n=h.onDayClick)||void 0===n||n.call(h,t,v,e):isDayPickerMultiple(g)?null===(i=y.onDayClick)||void 0===i||i.call(y,t,v,e):isDayPickerRange(g)?null===(a=b.onDayClick)||void 0===a||a.call(b,t,v,e):null===(o=g.onDayClick)||void 0===o||o.call(g,t,v,e)},onFocus:function(e){var n;M(t),null===(n=g.onDayFocus)||void 0===n||n.call(g,t,v,e)},onBlur:function(e){var n;j(),null===(n=g.onDayBlur)||void 0===n||n.call(g,t,v,e)},onKeyDown:function(e){var n;switch(e.key){case"ArrowLeft":e.preventDefault(),e.stopPropagation(),"rtl"===g.dir?D():C();break;case"ArrowRight":e.preventDefault(),e.stopPropagation(),"rtl"===g.dir?C():D();break;case"ArrowDown":e.preventDefault(),e.stopPropagation(),S();break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),E();break;case"PageUp":e.preventDefault(),e.stopPropagation(),e.shiftKey?T():O();break;case"PageDown":e.preventDefault(),e.stopPropagation(),e.shiftKey?A():P();break;case"Home":e.preventDefault(),e.stopPropagation(),L();break;case"End":e.preventDefault(),e.stopPropagation(),N()}null===(n=g.onDayKeyDown)||void 0===n||n.call(g,t,v,e)},onKeyUp:function(e){var n;null===(n=g.onDayKeyUp)||void 0===n||n.call(g,t,v,e)},onMouseEnter:function(e){var n;null===(n=g.onDayMouseEnter)||void 0===n||n.call(g,t,v,e)},onMouseLeave:function(e){var n;null===(n=g.onDayMouseLeave)||void 0===n||n.call(g,t,v,e)},onPointerEnter:function(e){var n;null===(n=g.onDayPointerEnter)||void 0===n||n.call(g,t,v,e)},onPointerLeave:function(e){var n;null===(n=g.onDayPointerLeave)||void 0===n||n.call(g,t,v,e)},onTouchCancel:function(e){var n;null===(n=g.onDayTouchCancel)||void 0===n||n.call(g,t,v,e)},onTouchEnd:function(e){var n;null===(n=g.onDayTouchEnd)||void 0===n||n.call(g,t,v,e)},onTouchMove:function(e){var n;null===(n=g.onDayTouchMove)||void 0===n||n.call(g,t,v,e)},onTouchStart:function(e){var n;null===(n=g.onDayTouchStart)||void 0===n||n.call(g,t,v,e)}}),{isButton:Y,isHidden:X,activeModifiers:v,selectedDays:B,buttonProps:ei,divProps:J});return er.isHidden?(0,s.jsx)("div",{role:"gridcell"}):er.isButton?(0,s.jsx)(I,__assign({name:"day",ref:ea},er.buttonProps)):(0,s.jsx)("div",__assign({},er.divProps))}function WeekNumber(e){var t=e.number,n=e.dates,i=useDayPicker(),a=i.onWeekNumberClick,o=i.styles,c=i.classNames,u=i.locale,p=i.labels.labelWeekNumber,f=(0,i.formatters.formatWeekNumber)(Number(t),{locale:u});if(!a)return(0,s.jsx)("span",{className:c.weeknumber,style:o.weeknumber,children:f});var v=p(Number(t),{locale:u});return(0,s.jsx)(I,{name:"week-number","aria-label":v,className:c.weeknumber,style:o.weeknumber,onClick:function(e){a(t,n,e)},children:f})}function Row(e){var t,n,i,a=useDayPicker(),o=a.styles,c=a.classNames,u=a.showWeekNumber,p=a.components,f=null!==(t=null==p?void 0:p.Day)&&void 0!==t?t:Day,v=null!==(n=null==p?void 0:p.WeekNumber)&&void 0!==n?n:WeekNumber;return u&&(i=(0,s.jsx)("td",{className:c.cell,style:o.cell,children:(0,s.jsx)(v,{number:e.weekNumber,dates:e.dates})})),(0,s.jsxs)("tr",{className:c.row,style:o.row,children:[i,e.dates.map(function(t){return(0,s.jsx)("td",{className:c.cell,style:o.cell,role:"presentation",children:(0,s.jsx)(f,{displayMonth:e.displayMonth,date:t})},function(e){return requiredArgs(1,arguments),Math.floor(function(e){return requiredArgs(1,arguments),toDate(e).getTime()}(e)/1e3)}(t))})]})}function daysToMonthWeeks(e,t,n){for(var i=(null==n?void 0:n.ISOWeek)?endOfISOWeek(t):endOfWeek(t,n),a=(null==n?void 0:n.ISOWeek)?startOfISOWeek(e):startOfWeek(e,n),o=differenceInCalendarDays_differenceInCalendarDays(i,a),s=[],c=0;c<=o;c++)s.push(addDays(a,c));return s.reduce(function(e,t){var i=(null==n?void 0:n.ISOWeek)?function(e){requiredArgs(1,arguments);var t=toDate(e);return Math.round((startOfISOWeek(t).getTime()-(function(e){requiredArgs(1,arguments);var t=function(e){requiredArgs(1,arguments);var t=toDate(e),n=t.getFullYear(),i=new Date(0);i.setFullYear(n+1,0,4),i.setHours(0,0,0,0);var a=startOfISOWeek(i),o=new Date(0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);var s=startOfISOWeek(o);return t.getTime()>=a.getTime()?n+1:t.getTime()>=s.getTime()?n:n-1}(e),n=new Date(0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),startOfISOWeek(n)})(t).getTime())/6048e5)+1}(t):function(e,t){requiredArgs(1,arguments);var n=toDate(e);return Math.round((startOfWeek(n,t).getTime()-(function(e,t){requiredArgs(1,arguments);var n,i,a,o,s,c,p,f,v=toInteger(null!==(n=null!==(i=null!==(a=null!==(o=null==t?void 0:t.firstWeekContainsDate)&&void 0!==o?o:null==t?void 0:null===(s=t.locale)||void 0===s?void 0:null===(c=s.options)||void 0===c?void 0:c.firstWeekContainsDate)&&void 0!==a?a:u.firstWeekContainsDate)&&void 0!==i?i:null===(p=u.locale)||void 0===p?void 0:null===(f=p.options)||void 0===f?void 0:f.firstWeekContainsDate)&&void 0!==n?n:1),g=function(e,t){requiredArgs(1,arguments);var n,i,a,o,s,c,p,f,v=toDate(e),g=v.getFullYear(),h=toInteger(null!==(n=null!==(i=null!==(a=null!==(o=null==t?void 0:t.firstWeekContainsDate)&&void 0!==o?o:null==t?void 0:null===(s=t.locale)||void 0===s?void 0:null===(c=s.options)||void 0===c?void 0:c.firstWeekContainsDate)&&void 0!==a?a:u.firstWeekContainsDate)&&void 0!==i?i:null===(p=u.locale)||void 0===p?void 0:null===(f=p.options)||void 0===f?void 0:f.firstWeekContainsDate)&&void 0!==n?n:1);if(!(h>=1&&h<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var y=new Date(0);y.setFullYear(g+1,0,h),y.setHours(0,0,0,0);var b=startOfWeek(y,t),w=new Date(0);w.setFullYear(g,0,h),w.setHours(0,0,0,0);var D=startOfWeek(w,t);return v.getTime()>=b.getTime()?g+1:v.getTime()>=D.getTime()?g:g-1}(e,t),h=new Date(0);return h.setFullYear(g,0,v),h.setHours(0,0,0,0),startOfWeek(h,t)})(n,t).getTime())/6048e5)+1}(t,n),a=e.find(function(e){return e.weekNumber===i});return a?a.dates.push(t):e.push({weekNumber:i,dates:[t]}),e},[])}function Table(e){var t,n,i,a=useDayPicker(),o=a.locale,c=a.classNames,u=a.styles,p=a.hideHead,f=a.fixedWeeks,v=a.components,g=a.weekStartsOn,h=a.firstWeekContainsDate,y=a.ISOWeek,b=function(e,t){var n=daysToMonthWeeks(startOfMonth(e),endOfMonth(e),t);if(null==t?void 0:t.useFixedWeeks){var i=function(e,t){return requiredArgs(1,arguments),function(e,t,n){requiredArgs(2,arguments);var i=startOfWeek(e,n),a=startOfWeek(t,n);return Math.round((i.getTime()-getTimezoneOffsetInMilliseconds(i)-(a.getTime()-getTimezoneOffsetInMilliseconds(a)))/6048e5)}(function(e){requiredArgs(1,arguments);var t=toDate(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(0,0,0,0),t}(e),startOfMonth(e),t)+1}(e,t);if(i<6){var a=n[n.length-1],o=a.dates[a.dates.length-1],s=addWeeks(o,6-i),c=daysToMonthWeeks(addWeeks(o,1),s,t);n.push.apply(n,c)}}return n}(e.displayMonth,{useFixedWeeks:!!f,ISOWeek:y,locale:o,weekStartsOn:g,firstWeekContainsDate:h}),w=null!==(t=null==v?void 0:v.Head)&&void 0!==t?t:Head,D=null!==(n=null==v?void 0:v.Row)&&void 0!==n?n:Row,C=null!==(i=null==v?void 0:v.Footer)&&void 0!==i?i:Footer;return(0,s.jsxs)("table",{id:e.id,className:c.table,style:u.table,role:"grid","aria-labelledby":e["aria-labelledby"],children:[!p&&(0,s.jsx)(w,{}),(0,s.jsx)("tbody",{className:c.tbody,style:u.tbody,children:b.map(function(t){return(0,s.jsx)(D,{displayMonth:e.displayMonth,dates:t.dates,weekNumber:t.weekNumber},t.weekNumber)})}),(0,s.jsx)(C,{displayMonth:e.displayMonth})]})}var et="undefined"!=typeof window&&window.document&&window.document.createElement?c.useLayoutEffect:c.useEffect,en=!1,ei=0;function genId(){return"react-day-picker-".concat(++ei)}function Month(e){var t,n,i,a,o,u,p,f,v=useDayPicker(),g=v.dir,h=v.classNames,y=v.styles,b=v.components,w=useNavigation().displayMonths,D=(i=null!=(t=v.id?"".concat(v.id,"-").concat(e.displayIndex):void 0)?t:en?genId():null,o=(a=(0,c.useState)(i))[0],u=a[1],et(function(){null===o&&u(genId())},[]),(0,c.useEffect)(function(){!1===en&&(en=!0)},[]),null!==(n=null!=t?t:o)&&void 0!==n?n:void 0),C=v.id?"".concat(v.id,"-grid-").concat(e.displayIndex):void 0,S=[h.month],E=y.month,j=0===e.displayIndex,M=e.displayIndex===w.length-1,O=!j&&!M;"rtl"===g&&(M=(p=[j,M])[0],j=p[1]),j&&(S.push(h.caption_start),E=__assign(__assign({},E),y.caption_start)),M&&(S.push(h.caption_end),E=__assign(__assign({},E),y.caption_end)),O&&(S.push(h.caption_between),E=__assign(__assign({},E),y.caption_between));var P=null!==(f=null==b?void 0:b.Caption)&&void 0!==f?f:Caption;return(0,s.jsxs)("div",{className:S.join(" "),style:E,children:[(0,s.jsx)(P,{id:D,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),(0,s.jsx)(Table,{id:C,"aria-labelledby":D,displayMonth:e.displayMonth})]},e.displayIndex)}function Months(e){var t=useDayPicker(),n=t.classNames,i=t.styles;return(0,s.jsx)("div",{className:n.months,style:i.months,children:e.children})}function Root(e){var t,n,i=e.initialProps,a=useDayPicker(),o=useFocusContext(),u=useNavigation(),p=(0,c.useState)(!1),f=p[0],v=p[1];(0,c.useEffect)(function(){a.initialFocus&&o.focusTarget&&(f||(o.focus(o.focusTarget),v(!0)))},[a.initialFocus,f,o.focus,o.focusTarget,o]);var g=[a.classNames.root,a.className];a.numberOfMonths>1&&g.push(a.classNames.multiple_months),a.showWeekNumber&&g.push(a.classNames.with_weeknumber);var h=__assign(__assign({},a.styles.root),a.style),y=Object.keys(i).filter(function(e){return e.startsWith("data-")}).reduce(function(e,t){var n;return __assign(__assign({},e),((n={})[t]=i[t],n))},{}),b=null!==(n=null===(t=i.components)||void 0===t?void 0:t.Months)&&void 0!==n?n:Months;return(0,s.jsx)("div",__assign({className:g.join(" "),style:h,dir:a.dir,id:a.id,nonce:i.nonce,title:i.title,lang:i.lang},y,{children:(0,s.jsx)(b,{children:u.displayMonths.map(function(e,t){return(0,s.jsx)(Month,{displayIndex:t,displayMonth:e},t)})})}))}function RootProvider(e){var t=e.children,n=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>t.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(n[i[a]]=e[i[a]]);return n}(e,["children"]);return(0,s.jsx)(DayPickerProvider,{initialProps:n,children:(0,s.jsx)(NavigationProvider,{children:(0,s.jsx)(SelectSingleProvider,{initialProps:n,children:(0,s.jsx)(SelectMultipleProvider,{initialProps:n,children:(0,s.jsx)(SelectRangeProvider,{initialProps:n,children:(0,s.jsx)(ModifiersProvider,{children:(0,s.jsx)(FocusProvider,{children:t})})})})})})})}function DayPicker(e){return(0,s.jsx)(RootProvider,__assign({},e,{children:(0,s.jsx)(Root,{initialProps:e})}))}},7552:function(e,t,n){"use strict";n.d(t,{Z:function(){return L}});var i,a,o,s,c,u,p=n(44),f=n(2265),v="right-scroll-bar-position",g="width-before-scroll-bar";function assignRef(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var h="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,y=new WeakMap,b=(void 0===i&&(i={}),(void 0===a&&(a=function(e){return e}),o=[],s=!1,c={read:function(){if(s)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return o.length?o[o.length-1]:null},useMedium:function(e){var t=a(e,s);return o.push(t),function(){o=o.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(s=!0;o.length;){var t=o;o=[],t.forEach(e)}o={push:function(t){return e(t)},filter:function(){return o}}},assignMedium:function(e){s=!0;var t=[];if(o.length){var n=o;o=[],n.forEach(e),t=o}var executeQueue=function(){var n=t;t=[],n.forEach(e)},cycle=function(){return Promise.resolve().then(executeQueue)};cycle(),o={push:function(e){t.push(e),cycle()},filter:function(e){return t=t.filter(e),o}}}}).options=(0,p.pi)({async:!0,ssr:!1},i),c),nothing=function(){},w=f.forwardRef(function(e,t){var n,i,a,o,s=f.useRef(null),c=f.useState({onScrollCapture:nothing,onWheelCapture:nothing,onTouchMoveCapture:nothing}),u=c[0],v=c[1],g=e.forwardProps,w=e.children,D=e.className,C=e.removeScrollBar,S=e.enabled,E=e.shards,j=e.sideCar,M=e.noRelative,O=e.noIsolation,P=e.inert,T=e.allowPinchZoom,A=e.as,L=void 0===A?"div":A,N=e.gapMode,I=(0,p._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),W=(n=[s,t],i=function(e){return n.forEach(function(t){return assignRef(t,e)})},(a=(0,f.useState)(function(){return{value:null,callback:i,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=i,o=a.facade,h(function(){var e=y.get(o);if(e){var t=new Set(e),i=new Set(n),a=o.current;t.forEach(function(e){i.has(e)||assignRef(e,null)}),i.forEach(function(e){t.has(e)||assignRef(e,a)})}y.set(o,n)},[n]),o),z=(0,p.pi)((0,p.pi)({},I),u);return f.createElement(f.Fragment,null,S&&f.createElement(j,{sideCar:b,removeScrollBar:C,shards:E,noRelative:M,noIsolation:O,inert:P,setCallbacks:v,allowPinchZoom:!!T,lockRef:s,gapMode:N}),g?f.cloneElement(f.Children.only(w),(0,p.pi)((0,p.pi)({},z),{ref:W})):f.createElement(L,(0,p.pi)({},z,{className:D,ref:W}),w))});w.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},w.classNames={fullWidth:g,zeroRight:v};var SideCar=function(e){var t=e.sideCar,n=(0,p._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var i=t.read();if(!i)throw Error("Sidecar medium not found");return f.createElement(i,(0,p.pi)({},n))};SideCar.isSideCarExport=!0;var stylesheetSingleton=function(){var e=0,t=null;return{add:function(i){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=u||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,o;(a=t).styleSheet?a.styleSheet.cssText=i:a.appendChild(document.createTextNode(i)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},styleHookSingleton=function(){var e=stylesheetSingleton();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},styleSingleton=function(){var e=styleHookSingleton();return function(t){return e(t.styles,t.dynamic),null}},D={left:0,top:0,right:0,gap:0},parse=function(e){return parseInt(e||"",10)||0},getOffset=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],i=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[parse(n),parse(i),parse(a)]},getGapWidth=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return D;var t=getOffset(e),n=document.documentElement.clientWidth,i=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,i-n+t[2]-t[0])}},C=styleSingleton(),S="data-scroll-locked",getStyles=function(e,t,n,i){var a=e.left,o=e.top,s=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(i,";\n   padding-right: ").concat(c,"px ").concat(i,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(i,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(i,";"),"margin"===n&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(i,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(i,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(v," {\n    right: ").concat(c,"px ").concat(i,";\n  }\n  \n  .").concat(g," {\n    margin-right: ").concat(c,"px ").concat(i,";\n  }\n  \n  .").concat(v," .").concat(v," {\n    right: 0 ").concat(i,";\n  }\n  \n  .").concat(g," .").concat(g," {\n    margin-right: 0 ").concat(i,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},getCurrentUseCounter=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},useLockAttribute=function(){f.useEffect(function(){return document.body.setAttribute(S,(getCurrentUseCounter()+1).toString()),function(){var e=getCurrentUseCounter()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},RemoveScrollBar=function(e){var t=e.noRelative,n=e.noImportant,i=e.gapMode,a=void 0===i?"margin":i;useLockAttribute();var o=f.useMemo(function(){return getGapWidth(a)},[a]);return f.createElement(C,{styles:getStyles(o,!t,a,n?"":"!important")})},E=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return E=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){E=!1}var M=!!E&&{passive:!1},elementCanBeScrolled=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},locationCouldBeScrolled=function(e,t){var n=t.ownerDocument,i=t;do{if("undefined"!=typeof ShadowRoot&&i instanceof ShadowRoot&&(i=i.host),elementCouldBeScrolled(e,i)){var a=getScrollVariables(e,i);if(a[1]>a[2])return!0}i=i.parentNode}while(i&&i!==n.body);return!1},elementCouldBeScrolled=function(e,t){return"v"===e?elementCanBeScrolled(t,"overflowY"):elementCanBeScrolled(t,"overflowX")},getScrollVariables=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},handleScroll=function(e,t,n,i,a){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),c=s*i,u=n.target,p=t.contains(u),f=!1,v=c>0,g=0,h=0;do{if(!u)break;var y=getScrollVariables(e,u),b=y[0],w=y[1]-y[2]-s*b;(b||w)&&elementCouldBeScrolled(e,u)&&(g+=w,h+=b);var D=u.parentNode;u=D&&D.nodeType===Node.DOCUMENT_FRAGMENT_NODE?D.host:D}while(!p&&u!==document.body||p&&(t.contains(u)||t===u));return v&&(a&&1>Math.abs(g)||!a&&c>g)?f=!0:!v&&(a&&1>Math.abs(h)||!a&&-c>h)&&(f=!0),f},getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},getDeltaXY=function(e){return[e.deltaX,e.deltaY]},extractRef=function(e){return e&&"current"in e?e.current:e},O=0,P=[],T=(b.useMedium(function(e){var t=f.useRef([]),n=f.useRef([0,0]),i=f.useRef(),a=f.useState(O++)[0],o=f.useState(styleSingleton)[0],s=f.useRef(e);f.useEffect(function(){s.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(0,p.ev)([e.lockRef.current],(e.shards||[]).map(extractRef),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var a,o=getTouchXY(e),c=n.current,u="deltaX"in e?e.deltaX:c[0]-o[0],p="deltaY"in e?e.deltaY:c[1]-o[1],f=e.target,v=Math.abs(u)>Math.abs(p)?"h":"v";if("touches"in e&&"h"===v&&"range"===f.type)return!1;var g=locationCouldBeScrolled(v,f);if(!g)return!0;if(g?a=v:(a="v"===v?"h":"v",g=locationCouldBeScrolled(v,f)),!g)return!1;if(!i.current&&"changedTouches"in e&&(u||p)&&(i.current=a),!a)return!0;var h=i.current||a;return handleScroll(h,t,e,"h"===h?u:p,!0)},[]),u=f.useCallback(function(e){if(P.length&&P[P.length-1]===o){var n="deltaY"in e?getDeltaXY(e):getTouchXY(e),i=t.current.filter(function(t){var i;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(i=t.delta)[0]===n[0]&&i[1]===n[1]})[0];if(i&&i.should){e.cancelable&&e.preventDefault();return}if(!i){var a=(s.current.shards||[]).map(extractRef).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?c(e,a[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),v=f.useCallback(function(e,n,i,a){var o={name:e,delta:n,target:i,should:a,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(i)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),g=f.useCallback(function(e){n.current=getTouchXY(e),i.current=void 0},[]),h=f.useCallback(function(t){v(t.type,getDeltaXY(t),t.target,c(t,e.lockRef.current))},[]),y=f.useCallback(function(t){v(t.type,getTouchXY(t),t.target,c(t,e.lockRef.current))},[]);f.useEffect(function(){return P.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:y}),document.addEventListener("wheel",u,M),document.addEventListener("touchmove",u,M),document.addEventListener("touchstart",g,M),function(){P=P.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,M),document.removeEventListener("touchmove",u,M),document.removeEventListener("touchstart",g,M)}},[]);var b=e.removeScrollBar,w=e.inert;return f.createElement(f.Fragment,null,w?f.createElement(o,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,b?f.createElement(RemoveScrollBar,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),SideCar),A=f.forwardRef(function(e,t){return f.createElement(w,(0,p.pi)({},e,{ref:t,sideCar:T}))});A.classNames=w.classNames;var L=A},9808:function(e,t,n){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i=n(2265),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useState,s=i.useEffect,c=i.useLayoutEffect,u=i.useDebugValue;function checkIfSnapshotChanged(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(e){return!0}}var p="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),i=o({inst:{value:n,getSnapshot:t}}),a=i[0].inst,p=i[1];return c(function(){a.value=n,a.getSnapshot=t,checkIfSnapshotChanged(a)&&p({inst:a})},[e,n,t]),s(function(){return checkIfSnapshotChanged(a)&&p({inst:a}),e(function(){checkIfSnapshotChanged(a)&&p({inst:a})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:p},6272:function(e,t,n){"use strict";e.exports=n(9808)},3482:function(e,t,n){"use strict";n.d(t,{p:function(){return z}});var i,a,o,s,c,u,p,f,v=n(2265),g=n.t(v,2),h=Object.defineProperty,d=(e,t,n)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,r=(e,t,n)=>(d(e,"symbol"!=typeof t?t+"":t,n),n);let y=new class{constructor(){r(this,"current",this.detect()),r(this,"handoffState","pending"),r(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},l=(e,t)=>{y.isServer?(0,v.useEffect)(e,t):(0,v.useLayoutEffect)(e,t)},use_event_o=function(e){let t;let n=(t=(0,v.useRef)(e),l(()=>{t.current=e},[e]),t);return v.useCallback((...e)=>n.current(...e),[n])},b=null!=(p=v.useId)?p:function(){let e=function(){let e;let t=(e="undefined"==typeof document,(0,g.useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[n,i]=v.useState(y.isHandoffComplete);return n&&!1===y.isHandoffComplete&&i(!1),v.useEffect(()=>{!0!==n&&i(!0)},[n]),v.useEffect(()=>y.handoff(),[]),!t&&n}(),[t,n]=v.useState(e?()=>y.nextId():null);return l(()=>{null===t&&n(y.nextId())},[t]),null!=t?""+t:void 0};function use_resolve_button_type_i(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";if("string"==typeof n&&"button"===n.toLowerCase())return"button"}let w=Symbol();function use_sync_refs_y(...e){let t=(0,v.useRef)(e);(0,v.useEffect)(()=>{t.current=e},[e]);let n=use_event_o(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[w]))?void 0:n}let D=(0,v.createContext)(null);D.displayName="OpenClosedContext";var C=((i=C||{})[i.Open=1]="Open",i[i.Closed=2]="Closed",i[i.Closing=4]="Closing",i[i.Opening=8]="Opening",i);function open_closed_s({value:e,children:t}){return v.createElement(D.Provider,{value:e},t)}function match_u(e,t,...n){if(e in t){let i=t[e];return"function"==typeof i?i(...n):i}let i=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(i,match_u),i}function class_names_t(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}var S=((a=S||{})[a.None=0]="None",a[a.RenderStrategy=1]="RenderStrategy",a[a.Static=2]="Static",a),E=((o=E||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function render_C({ourProps:e,theirProps:t,slot:n,defaultTag:i,features:a,visible:o=!0,name:s,mergeRefs:c}){c=null!=c?c:k;let u=R(t,e);if(o)return m(u,n,i,s,c);let p=null!=a?a:0;if(2&p){let{static:e=!1,...t}=u;if(e)return m(t,n,i,s,c)}if(1&p){let{unmount:e=!0,...t}=u;return match_u(e?0:1,{0:()=>null,1:()=>m({...t,hidden:!0,style:{display:"none"}},n,i,s,c)})}return m(u,n,i,s,c)}function m(e,t={},n,i,a){let{as:o=n,children:s,refName:c="ref",...u}=F(e,["unmount","static"]),p=void 0!==e.ref?{[c]:e.ref}:{},f="function"==typeof s?s(t):s;"className"in u&&u.className&&"function"==typeof u.className&&(u.className=u.className(t));let g={};if(t){let e=!1,n=[];for(let[i,a]of Object.entries(t))"boolean"==typeof a&&(e=!0),!0===a&&n.push(i);e&&(g["data-headlessui-state"]=n.join(" "))}if(o===v.Fragment&&Object.keys(x(u)).length>0){if(!(0,v.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw Error(['Passing props on "Fragment"!',"",`The current component <${i} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(u).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>class_names_t(null==e?void 0:e.className(...t),u.className):class_names_t(null==e?void 0:e.className,u.className),n=t?{className:t}:{};return(0,v.cloneElement)(f,Object.assign({},R(f.props,x(F(u,["ref"]))),g,p,{ref:a(f.ref,p.ref)},n))}return(0,v.createElement)(o,Object.assign({},F(u,["ref"]),o!==v.Fragment&&p,o!==v.Fragment&&g),f)}function render_I(){let e=(0,v.useRef)([]),t=(0,v.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]);return(...n)=>{if(!n.every(e=>null==e))return e.current=n,t}}function k(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function R(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let i of e)for(let e in i)e.startsWith("on")&&"function"==typeof i[e]?(null!=n[e]||(n[e]=[]),n[e].push(i[e])):t[e]=i[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(e=>[e,void 0])));for(let e in n)Object.assign(t,{[e](t,...i){for(let a of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;a(t,...i)}}});return t}function U(e){var t;return Object.assign((0,v.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function x(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function F(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}let j=null!=(f=v.startTransition)?f:function(e){e()};var M=((s=M||{}).Space=" ",s.Enter="Enter",s.Escape="Escape",s.Backspace="Backspace",s.Delete="Delete",s.ArrowLeft="ArrowLeft",s.ArrowUp="ArrowUp",s.ArrowRight="ArrowRight",s.ArrowDown="ArrowDown",s.Home="Home",s.End="End",s.PageUp="PageUp",s.PageDown="PageDown",s.Tab="Tab",s),O=((c=O||{})[c.Open=0]="Open",c[c.Closed=1]="Closed",c),P=((u=P||{})[u.ToggleDisclosure=0]="ToggleDisclosure",u[u.CloseDisclosure=1]="CloseDisclosure",u[u.SetButtonId=2]="SetButtonId",u[u.SetPanelId=3]="SetPanelId",u[u.LinkPanel=4]="LinkPanel",u[u.UnlinkPanel=5]="UnlinkPanel",u);let T={0:e=>({...e,disclosureState:match_u(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},A=(0,v.createContext)(null);function _(e){let t=(0,v.useContext)(A);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,_),t}return t}A.displayName="DisclosureContext";let L=(0,v.createContext)(null);L.displayName="DisclosureAPIContext";let N=(0,v.createContext)(null);function ee(e,t){return match_u(t.type,T,e,t)}N.displayName="DisclosurePanelContext";let I=v.Fragment,W=S.RenderStrategy|S.Static,z=Object.assign(U(function(e,t){let{defaultOpen:n=!1,...i}=e,a=(0,v.useRef)(null),o=use_sync_refs_y(t,function(e,t=!0){return Object.assign(e,{[w]:t})}(e=>{a.current=e},void 0===e.as||e.as===v.Fragment)),s=(0,v.useRef)(null),c=(0,v.useRef)(null),u=(0,v.useReducer)(ee,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:c,panelRef:s,buttonId:null,panelId:null}),[{disclosureState:p,buttonId:f},g]=u,h=use_event_o(e=>{g({type:1});let t=y.isServer?null:a instanceof Node?a.ownerDocument:null!=a&&a.hasOwnProperty("current")&&a.current instanceof Node?a.current.ownerDocument:document;if(!t||!f)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(f):t.getElementById(f);null==n||n.focus()}),b=(0,v.useMemo)(()=>({close:h}),[h]),D=(0,v.useMemo)(()=>({open:0===p,close:h}),[p,h]);return v.createElement(A.Provider,{value:u},v.createElement(L.Provider,{value:b},v.createElement(open_closed_s,{value:match_u(p,{0:C.Open,1:C.Closed})},render_C({ourProps:{ref:o},theirProps:i,slot:D,defaultTag:I,name:"Disclosure"}))))}),{Button:U(function(e,t){let n=b(),{id:i=`headlessui-disclosure-button-${n}`,...a}=e,[o,s]=_("Disclosure.Button"),c=(0,v.useContext)(N),u=null!==c&&c===o.panelId,p=(0,v.useRef)(null),f=use_sync_refs_y(p,t,u?null:o.buttonRef),g=render_I();(0,v.useEffect)(()=>{if(!u)return s({type:2,buttonId:i}),()=>{s({type:2,buttonId:null})}},[i,s,u]);let h=use_event_o(e=>{var t;if(u){if(1===o.disclosureState)return;switch(e.key){case M.Space:case M.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(t=o.buttonRef.current)||t.focus()}}else switch(e.key){case M.Space:case M.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),y=use_event_o(e=>{e.key===M.Space&&e.preventDefault()}),w=use_event_o(t=>{var n;(function(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let i=(null==t?void 0:t.getAttribute("disabled"))==="";return!(i&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&i})(t.currentTarget)||e.disabled||(u?(s({type:0}),null==(n=o.buttonRef.current)||n.focus()):s({type:0}))}),D=(0,v.useMemo)(()=>({open:0===o.disclosureState}),[o]),C=function(e,t){let[n,i]=(0,v.useState)(()=>use_resolve_button_type_i(e));return l(()=>{i(use_resolve_button_type_i(e))},[e.type,e.as]),l(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&i("button")},[n,t]),n}(e,p);return render_C({mergeRefs:g,ourProps:u?{ref:f,type:C,onKeyDown:h,onClick:w}:{ref:f,id:i,type:C,"aria-expanded":0===o.disclosureState,"aria-controls":o.linkedPanel?o.panelId:void 0,onKeyDown:h,onKeyUp:y,onClick:w},theirProps:a,slot:D,defaultTag:"button",name:"Disclosure.Button"})}),Panel:U(function(e,t){let n=b(),{id:i=`headlessui-disclosure-panel-${n}`,...a}=e,[o,s]=_("Disclosure.Panel"),{close:c}=function K(e){let t=(0,v.useContext)(L);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,K),t}return t}("Disclosure.Panel"),u=render_I(),p=use_sync_refs_y(t,o.panelRef,e=>{j(()=>s({type:e?4:5}))});(0,v.useEffect)(()=>(s({type:3,panelId:i}),()=>{s({type:3,panelId:null})}),[i,s]);let f=(0,v.useContext)(D),g=null!==f?(f&C.Open)===C.Open:0===o.disclosureState,h=(0,v.useMemo)(()=>({open:0===o.disclosureState,close:c}),[o,c]);return v.createElement(N.Provider,{value:o.panelId},render_C({mergeRefs:u,ourProps:{ref:p,id:i},theirProps:a,slot:h,defaultTag:"div",features:W,visible:g,name:"Disclosure.Panel"}))})})},5969:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"}))});t.Z=a},1566:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"}))});t.Z=a},9874:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});t.Z=a},7965:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))});t.Z=a},4164:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))});t.Z=a},2307:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))});t.Z=a},1095:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=a},5044:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});t.Z=a},365:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.125 2.25h-4.5c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125v-9M10.125 2.25h.375a9 9 0 0 1 9 9v.375M10.125 2.25A3.375 3.375 0 0 1 13.5 5.625v1.5c0 .621.504 1.125 1.125 1.125h1.5a3.375 3.375 0 0 1 3.375 3.375M9 15l2.25 2.25L15 12"}))});t.Z=a},2361:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});t.Z=a},6610:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});t.Z=a},4020:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});t.Z=a},7227:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});t.Z=a},1809:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))});t.Z=a},2893:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=a},6689:function(e,t,n){"use strict";var i=n(2265);let a=i.forwardRef(function({title:e,titleId:t,...n},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=a},5744:function(e,t,n){"use strict";function composeEventHandlers(e,t,{checkForDefaultPrevented:n=!0}={}){return function(i){if(e?.(i),!1===n||!i.defaultPrevented)return t?.(i)}}n.d(t,{M:function(){return composeEventHandlers}})},6989:function(e,t,n){"use strict";n.d(t,{b:function(){return createContextScope},k:function(){return createContext2}});var i=n(2265),a=n(7437);function createContext2(e,t){let n=i.createContext(t),Provider=e=>{let{children:t,...o}=e,s=i.useMemo(()=>o,Object.values(o));return(0,a.jsx)(n.Provider,{value:s,children:t})};return Provider.displayName=e+"Provider",[Provider,function(a){let o=i.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function createContextScope(e,t=[]){let n=[],createScope=()=>{let t=n.map(e=>i.createContext(e));return function(n){let a=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:a}}),[n,a])}};return createScope.scopeName=e,[function(t,o){let s=i.createContext(o),c=n.length;n=[...n,o];let Provider=t=>{let{scope:n,children:o,...u}=t,p=n?.[e]?.[c]||s,f=i.useMemo(()=>u,Object.values(u));return(0,a.jsx)(p.Provider,{value:f,children:o})};return Provider.displayName=t+"Provider",[Provider,function(n,a){let u=a?.[e]?.[c]||s,p=i.useContext(u);if(p)return p;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let createScope=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=n.reduce((t,{useScope:n,scopeName:i})=>{let a=n(e),o=a[`__scope${i}`];return{...t,...o}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return createScope.scopeName=t.scopeName,createScope}(createScope,...t)]}},9249:function(e,t,n){"use strict";n.d(t,{XB:function(){return g}});var i,a=n(2265),o=n(5744),s=n(9381),c=n(2210),u=n(6459),p=n(7437),f="dismissableLayer.update",v=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=a.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:y,onInteractOutside:b,onDismiss:w,...D}=e,C=a.useContext(v),[S,E]=a.useState(null),j=S?.ownerDocument??globalThis?.document,[,M]=a.useState({}),O=(0,c.e)(t,e=>E(e)),P=Array.from(C.layers),[T]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),A=P.indexOf(T),L=S?P.indexOf(S):-1,N=C.layersWithOutsidePointerEventsDisabled.size>0,I=L>=A,W=function(e,t=globalThis?.document){let n=(0,u.W)(e),i=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!i.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=handleAndDispatchPointerDownOutsideEvent2,t.addEventListener("click",o.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else t.removeEventListener("click",o.current);i.current=!1},e=window.setTimeout(()=>{t.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),t.removeEventListener("pointerdown",handlePointerDown),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));!I||n||(h?.(e),b?.(e),e.defaultPrevented||w?.())},j),z=function(e,t=globalThis?.document){let n=(0,u.W)(e),i=a.useRef(!1);return a.useEffect(()=>{let handleFocus=e=>{e.target&&!i.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",handleFocus),()=>t.removeEventListener("focusin",handleFocus)},[t,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));n||(y?.(e),b?.(e),e.defaultPrevented||w?.())},j);return!function(e,t=globalThis?.document){let n=(0,u.W)(e);a.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>t.removeEventListener("keydown",handleKeyDown,{capture:!0})},[n,t])}(e=>{let t=L===C.layers.size-1;t&&(g?.(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},j),a.useEffect(()=>{if(S)return n&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(i=j.body.style.pointerEvents,j.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(S)),C.layers.add(S),dispatchUpdate(),()=>{n&&1===C.layersWithOutsidePointerEventsDisabled.size&&(j.body.style.pointerEvents=i)}},[S,j,n,C]),a.useEffect(()=>()=>{S&&(C.layers.delete(S),C.layersWithOutsidePointerEventsDisabled.delete(S),dispatchUpdate())},[S,C]),a.useEffect(()=>{let handleUpdate=()=>M({});return document.addEventListener(f,handleUpdate),()=>document.removeEventListener(f,handleUpdate)},[]),(0,p.jsx)(s.WV.div,{...D,ref:O,style:{pointerEvents:N?I?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,z.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,z.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,W.onPointerDownCapture)})});function dispatchUpdate(){let e=new CustomEvent(f);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,t,n,{discrete:i}){let a=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),i?(0,s.jH)(a,o):a.dispatchEvent(o)}g.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(v),i=a.useRef(null),o=(0,c.e)(t,i);return a.useEffect(()=>{let e=i.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(s.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},9290:function(e,t,n){"use strict";n.d(t,{VY:function(){return te},__:function(){return tt},Uv:function(){return e9},fC:function(){return e7},Z0:function(){return tn},xz:function(){return e8}});var i=n(2265),a=n(5744),o=n(2210),s=n(6989),c=n(3763),u=n(9381),p=n(7256),f=n(7437);function createCollection(e){let t=e+"CollectionProvider",[n,a]=(0,s.b)(t),[c,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),CollectionProvider=e=>{let{scope:t,children:n}=e,a=i.useRef(null),o=i.useRef(new Map).current;return(0,f.jsx)(c,{scope:t,itemMap:o,collectionRef:a,children:n})};CollectionProvider.displayName=t;let v=e+"CollectionSlot",g=(0,p.Z8)(v),h=i.forwardRef((e,t)=>{let{scope:n,children:i}=e,a=u(v,n),s=(0,o.e)(t,a.collectionRef);return(0,f.jsx)(g,{ref:s,children:i})});h.displayName=v;let y=e+"CollectionItemSlot",b="data-radix-collection-item",w=(0,p.Z8)(y),D=i.forwardRef((e,t)=>{let{scope:n,children:a,...s}=e,c=i.useRef(null),p=(0,o.e)(t,c),v=u(y,n);return i.useEffect(()=>(v.itemMap.set(c,{ref:c,...s}),()=>void v.itemMap.delete(c))),(0,f.jsx)(w,{[b]:"",ref:p,children:a})});return D.displayName=y,[{Provider:CollectionProvider,Slot:h,ItemSlot:D},function(t){let n=u(e+"CollectionConsumer",t),a=i.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${b}]`)),i=Array.from(n.itemMap.values()),a=i.sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current));return a},[n.collectionRef,n.itemMap]);return a},a]}var v=i.createContext(void 0);function useDirection(e){let t=i.useContext(v);return e||t||"ltr"}var g=n(9249),h=n(1244),y=n(2759),b=n(966),w=n(4402),D=n(2730),C=n(5606),S=n(6459),E="rovingFocusGroup.onEntryFocus",j={bubbles:!1,cancelable:!0},M="RovingFocusGroup",[O,P,T]=createCollection(M),[A,L]=(0,s.b)(M,[T]),[N,I]=A(M),W=i.forwardRef((e,t)=>(0,f.jsx)(O.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(O.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(z,{...e,ref:t})})}));W.displayName=M;var z=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:s,loop:p=!1,dir:v,currentTabStopId:g,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:w=!1,...D}=e,C=i.useRef(null),O=(0,o.e)(t,C),T=useDirection(v),[A,L]=(0,c.T)({prop:g,defaultProp:h??null,onChange:y,caller:M}),[I,W]=i.useState(!1),z=(0,S.W)(b),q=P(n),H=i.useRef(!1),[B,Y]=i.useState(0);return i.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(E,z),()=>e.removeEventListener(E,z)},[z]),(0,f.jsx)(N,{scope:n,orientation:s,dir:T,loop:p,currentTabStopId:A,onItemFocus:i.useCallback(e=>L(e),[L]),onItemShiftTab:i.useCallback(()=>W(!0),[]),onFocusableItemAdd:i.useCallback(()=>Y(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>Y(e=>e-1),[]),children:(0,f.jsx)(u.WV.div,{tabIndex:I||0===B?-1:0,"data-orientation":s,...D,ref:O,style:{outline:"none",...e.style},onMouseDown:(0,a.M)(e.onMouseDown,()=>{H.current=!0}),onFocus:(0,a.M)(e.onFocus,e=>{let t=!H.current;if(e.target===e.currentTarget&&t&&!I){let t=new CustomEvent(E,j);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=q().filter(e=>e.focusable),t=e.find(e=>e.active),n=e.find(e=>e.id===A),i=[t,n,...e].filter(Boolean),a=i.map(e=>e.ref.current);focusFirst(a,w)}}H.current=!1}),onBlur:(0,a.M)(e.onBlur,()=>W(!1))})})}),q="RovingFocusGroupItem",H=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:o=!0,active:s=!1,tabStopId:c,children:p,...v}=e,g=(0,b.M)(),h=c||g,y=I(q,n),w=y.currentTabStopId===h,D=P(n),{onFocusableItemAdd:C,onFocusableItemRemove:S,currentTabStopId:E}=y;return i.useEffect(()=>{if(o)return C(),()=>S()},[o,C,S]),(0,f.jsx)(O.ItemSlot,{scope:n,id:h,focusable:o,active:s,children:(0,f.jsx)(u.WV.span,{tabIndex:w?0:-1,"data-orientation":y.orientation,...v,ref:t,onMouseDown:(0,a.M)(e.onMouseDown,e=>{o?y.onItemFocus(h):e.preventDefault()}),onFocus:(0,a.M)(e.onFocus,()=>y.onItemFocus(h)),onKeyDown:(0,a.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){y.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var i;let a=(i=e.key,"rtl"!==n?i:"ArrowLeft"===i?"ArrowRight":"ArrowRight"===i?"ArrowLeft":i);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return B[a]}(e,y.orientation,y.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let a=D().filter(e=>e.focusable),o=a.map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,i;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=y.loop?(n=o,i=a+1,n.map((e,t)=>n[(i+t)%n.length])):o.slice(a+1)}setTimeout(()=>focusFirst(o))}}),children:"function"==typeof p?p({isCurrentTabStop:w,hasTabStop:null!=E}):p})})});H.displayName=q;var B={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function focusFirst(e,t=!1){let n=document.activeElement;for(let i of e)if(i===n||(i.focus({preventScroll:t}),document.activeElement!==n))return}var Y=n(5859),V=n(7552),Z=["Enter"," "],$=["ArrowUp","PageDown","End"],G=["ArrowDown","PageUp","Home",...$],X={ltr:[...Z,"ArrowRight"],rtl:[...Z,"ArrowLeft"]},Q={ltr:["ArrowLeft"],rtl:["ArrowRight"]},J="Menu",[et,en,ei]=createCollection(J),[ea,er]=(0,s.b)(J,[ei,w.D7,L]),eo=(0,w.D7)(),el=L(),[es,ec]=ea(J),[ed,eu]=ea(J),Menu=e=>{let{__scopeMenu:t,open:n=!1,children:a,dir:o,onOpenChange:s,modal:c=!0}=e,u=eo(t),[p,v]=i.useState(null),g=i.useRef(!1),h=(0,S.W)(s),y=useDirection(o);return i.useEffect(()=>{let handleKeyDown=()=>{g.current=!0,document.addEventListener("pointerdown",handlePointer,{capture:!0,once:!0}),document.addEventListener("pointermove",handlePointer,{capture:!0,once:!0})},handlePointer=()=>g.current=!1;return document.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>{document.removeEventListener("keydown",handleKeyDown,{capture:!0}),document.removeEventListener("pointerdown",handlePointer,{capture:!0}),document.removeEventListener("pointermove",handlePointer,{capture:!0})}},[]),(0,f.jsx)(w.fC,{...u,children:(0,f.jsx)(es,{scope:t,open:n,onOpenChange:h,content:p,onContentChange:v,children:(0,f.jsx)(ed,{scope:t,onClose:i.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:g,dir:y,modal:c,children:a})})})};Menu.displayName=J;var ep=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e,a=eo(n);return(0,f.jsx)(w.ee,{...a,...i,ref:t})});ep.displayName="MenuAnchor";var ef="MenuPortal",[em,ev]=ea(ef,{forceMount:void 0}),MenuPortal=e=>{let{__scopeMenu:t,forceMount:n,children:i,container:a}=e,o=ec(ef,t);return(0,f.jsx)(em,{scope:t,forceMount:n,children:(0,f.jsx)(C.z,{present:n||o.open,children:(0,f.jsx)(D.h,{asChild:!0,container:a,children:i})})})};MenuPortal.displayName=ef;var eg="MenuContent",[eh,ey]=ea(eg),ex=i.forwardRef((e,t)=>{let n=ev(eg,e.__scopeMenu),{forceMount:i=n.forceMount,...a}=e,o=ec(eg,e.__scopeMenu),s=eu(eg,e.__scopeMenu);return(0,f.jsx)(et.Provider,{scope:e.__scopeMenu,children:(0,f.jsx)(C.z,{present:i||o.open,children:(0,f.jsx)(et.Slot,{scope:e.__scopeMenu,children:s.modal?(0,f.jsx)(eb,{...a,ref:t}):(0,f.jsx)(ew,{...a,ref:t})})})})}),eb=i.forwardRef((e,t)=>{let n=ec(eg,e.__scopeMenu),s=i.useRef(null),c=(0,o.e)(t,s);return i.useEffect(()=>{let e=s.current;if(e)return(0,Y.Ry)(e)},[]),(0,f.jsx)(e_,{...e,ref:c,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ew=i.forwardRef((e,t)=>{let n=ec(eg,e.__scopeMenu);return(0,f.jsx)(e_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),ek=(0,p.Z8)("MenuContent.ScrollLock"),e_=i.forwardRef((e,t)=>{let{__scopeMenu:n,loop:s=!1,trapFocus:c,onOpenAutoFocus:u,onCloseAutoFocus:p,disableOutsidePointerEvents:v,onEntryFocus:b,onEscapeKeyDown:D,onPointerDownOutside:C,onFocusOutside:S,onInteractOutside:E,onDismiss:j,disableOutsideScroll:M,...O}=e,P=ec(eg,n),T=eu(eg,n),A=eo(n),L=el(n),N=en(n),[I,z]=i.useState(null),q=i.useRef(null),H=(0,o.e)(t,q,P.onContentChange),B=i.useRef(0),Y=i.useRef(""),Z=i.useRef(0),X=i.useRef(null),Q=i.useRef("right"),J=i.useRef(0),et=M?V.Z:i.Fragment,ei=M?{as:ek,allowPinchZoom:!0}:void 0,handleTypeaheadSearch=e=>{let t=Y.current+e,n=N().filter(e=>!e.disabled),i=document.activeElement,a=n.find(e=>e.ref.current===i)?.textValue,o=n.map(e=>e.textValue),s=function(e,t,n){var i;let a=t.length>1&&Array.from(t).every(e=>e===t[0]),o=a?t[0]:t,s=n?e.indexOf(n):-1,c=(i=Math.max(s,0),e.map((t,n)=>e[(i+n)%e.length])),u=1===o.length;u&&(c=c.filter(e=>e!==n));let p=c.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return p!==n?p:void 0}(o,t,a),c=n.find(e=>e.textValue===s)?.ref.current;!function updateSearch(e){Y.current=e,window.clearTimeout(B.current),""!==e&&(B.current=window.setTimeout(()=>updateSearch(""),1e3))}(t),c&&setTimeout(()=>c.focus())};i.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,h.EW)();let ea=i.useCallback(e=>{let t=Q.current===X.current?.side;return t&&function(e,t){if(!t)return!1;let n={x:e.clientX,y:e.clientY};return function(e,t){let{x:n,y:i}=e,a=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],c=t[o],u=s.x,p=s.y,f=c.x,v=c.y,g=p>i!=v>i&&n<(f-u)*(i-p)/(v-p)+u;g&&(a=!a)}return a}(n,t)}(e,X.current?.area)},[]);return(0,f.jsx)(eh,{scope:n,searchRef:Y,onItemEnter:i.useCallback(e=>{ea(e)&&e.preventDefault()},[ea]),onItemLeave:i.useCallback(e=>{ea(e)||(q.current?.focus(),z(null))},[ea]),onTriggerLeave:i.useCallback(e=>{ea(e)&&e.preventDefault()},[ea]),pointerGraceTimerRef:Z,onPointerGraceIntentChange:i.useCallback(e=>{X.current=e},[]),children:(0,f.jsx)(et,{...ei,children:(0,f.jsx)(y.M,{asChild:!0,trapped:c,onMountAutoFocus:(0,a.M)(u,e=>{e.preventDefault(),q.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:p,children:(0,f.jsx)(g.XB,{asChild:!0,disableOutsidePointerEvents:v,onEscapeKeyDown:D,onPointerDownOutside:C,onFocusOutside:S,onInteractOutside:E,onDismiss:j,children:(0,f.jsx)(W,{asChild:!0,...L,dir:T.dir,orientation:"vertical",loop:s,currentTabStopId:I,onCurrentTabStopIdChange:z,onEntryFocus:(0,a.M)(b,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,f.jsx)(w.VY,{role:"menu","aria-orientation":"vertical","data-state":getOpenState(P.open),"data-radix-menu-content":"",dir:T.dir,...A,...O,ref:H,style:{outline:"none",...O.style},onKeyDown:(0,a.M)(O.onKeyDown,e=>{let t=e.target,n=t.closest("[data-radix-menu-content]")===e.currentTarget,i=e.ctrlKey||e.altKey||e.metaKey,a=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!i&&a&&handleTypeaheadSearch(e.key));let o=q.current;if(e.target!==o||!G.includes(e.key))return;e.preventDefault();let s=N().filter(e=>!e.disabled),c=s.map(e=>e.ref.current);$.includes(e.key)&&c.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(c)}),onBlur:(0,a.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),Y.current="")}),onPointerMove:(0,a.M)(e.onPointerMove,whenMouse(e=>{let t=e.target,n=J.current!==e.clientX;if(e.currentTarget.contains(t)&&n){let t=e.clientX>J.current?"right":"left";Q.current=t,J.current=e.clientX}}))})})})})})})});ex.displayName=eg;var eD=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e;return(0,f.jsx)(u.WV.div,{role:"group",...i,ref:t})});eD.displayName="MenuGroup";var eC=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e;return(0,f.jsx)(u.WV.div,{...i,ref:t})});eC.displayName="MenuLabel";var eS="MenuItem",eE="menu.itemSelect",ej=i.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:s,...c}=e,p=i.useRef(null),v=eu(eS,e.__scopeMenu),g=ey(eS,e.__scopeMenu),h=(0,o.e)(t,p),y=i.useRef(!1);return(0,f.jsx)(eM,{...c,ref:h,disabled:n,onClick:(0,a.M)(e.onClick,()=>{let e=p.current;if(!n&&e){let t=new CustomEvent(eE,{bubbles:!0,cancelable:!0});e.addEventListener(eE,e=>s?.(e),{once:!0}),(0,u.jH)(e,t),t.defaultPrevented?y.current=!1:v.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),y.current=!0},onPointerUp:(0,a.M)(e.onPointerUp,e=>{y.current||e.currentTarget?.click()}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{let t=""!==g.searchRef.current;!n&&(!t||" "!==e.key)&&Z.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ej.displayName=eS;var eM=i.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:s=!1,textValue:c,...p}=e,v=ey(eS,n),g=el(n),h=i.useRef(null),y=(0,o.e)(t,h),[b,w]=i.useState(!1),[D,C]=i.useState("");return i.useEffect(()=>{let e=h.current;e&&C((e.textContent??"").trim())},[p.children]),(0,f.jsx)(et.ItemSlot,{scope:n,disabled:s,textValue:c??D,children:(0,f.jsx)(H,{asChild:!0,...g,focusable:!s,children:(0,f.jsx)(u.WV.div,{role:"menuitem","data-highlighted":b?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...p,ref:y,onPointerMove:(0,a.M)(e.onPointerMove,whenMouse(e=>{if(s)v.onItemLeave(e);else if(v.onItemEnter(e),!e.defaultPrevented){let t=e.currentTarget;t.focus({preventScroll:!0})}})),onPointerLeave:(0,a.M)(e.onPointerLeave,whenMouse(e=>v.onItemLeave(e))),onFocus:(0,a.M)(e.onFocus,()=>w(!0)),onBlur:(0,a.M)(e.onBlur,()=>w(!1))})})})}),eO=i.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:i,...o}=e;return(0,f.jsx)(eI,{scope:e.__scopeMenu,checked:n,children:(0,f.jsx)(ej,{role:"menuitemcheckbox","aria-checked":isIndeterminate(n)?"mixed":n,...o,ref:t,"data-state":getCheckedState(n),onSelect:(0,a.M)(o.onSelect,()=>i?.(!!isIndeterminate(n)||!n),{checkForDefaultPrevented:!1})})})});eO.displayName="MenuCheckboxItem";var eP="MenuRadioGroup",[eT,eA]=ea(eP,{value:void 0,onValueChange:()=>{}}),eR=i.forwardRef((e,t)=>{let{value:n,onValueChange:i,...a}=e,o=(0,S.W)(i);return(0,f.jsx)(eT,{scope:e.__scopeMenu,value:n,onValueChange:o,children:(0,f.jsx)(eD,{...a,ref:t})})});eR.displayName=eP;var eL="MenuRadioItem",eN=i.forwardRef((e,t)=>{let{value:n,...i}=e,o=eA(eL,e.__scopeMenu),s=n===o.value;return(0,f.jsx)(eI,{scope:e.__scopeMenu,checked:s,children:(0,f.jsx)(ej,{role:"menuitemradio","aria-checked":s,...i,ref:t,"data-state":getCheckedState(s),onSelect:(0,a.M)(i.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});eN.displayName=eL;var eF="MenuItemIndicator",[eI,eW]=ea(eF,{checked:!1}),ez=i.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:i,...a}=e,o=eW(eF,n);return(0,f.jsx)(C.z,{present:i||isIndeterminate(o.checked)||!0===o.checked,children:(0,f.jsx)(u.WV.span,{...a,ref:t,"data-state":getCheckedState(o.checked)})})});ez.displayName=eF;var eU=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e;return(0,f.jsx)(u.WV.div,{role:"separator","aria-orientation":"horizontal",...i,ref:t})});eU.displayName="MenuSeparator";var eq=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e,a=eo(n);return(0,f.jsx)(w.Eh,{...a,...i,ref:t})});eq.displayName="MenuArrow";var[eH,eB]=ea("MenuSub"),eY="MenuSubTrigger",eV=i.forwardRef((e,t)=>{let n=ec(eY,e.__scopeMenu),s=eu(eY,e.__scopeMenu),c=eB(eY,e.__scopeMenu),u=ey(eY,e.__scopeMenu),p=i.useRef(null),{pointerGraceTimerRef:v,onPointerGraceIntentChange:g}=u,h={__scopeMenu:e.__scopeMenu},y=i.useCallback(()=>{p.current&&window.clearTimeout(p.current),p.current=null},[]);return i.useEffect(()=>y,[y]),i.useEffect(()=>{let e=v.current;return()=>{window.clearTimeout(e),g(null)}},[v,g]),(0,f.jsx)(ep,{asChild:!0,...h,children:(0,f.jsx)(eM,{id:c.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":c.contentId,"data-state":getOpenState(n.open),...e,ref:(0,o.F)(t,c.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,a.M)(e.onPointerMove,whenMouse(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||p.current||(u.onPointerGraceIntentChange(null),p.current=window.setTimeout(()=>{n.onOpenChange(!0),y()},100))})),onPointerLeave:(0,a.M)(e.onPointerLeave,whenMouse(e=>{y();let t=n.content?.getBoundingClientRect();if(t){let i=n.content?.dataset.side,a="right"===i,o=t[a?"left":"right"],s=t[a?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:o,y:t.top},{x:s,y:t.top},{x:s,y:t.bottom},{x:o,y:t.bottom}],side:i}),window.clearTimeout(v.current),v.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,a.M)(e.onKeyDown,t=>{let i=""!==u.searchRef.current;!e.disabled&&(!i||" "!==t.key)&&X[s.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eV.displayName=eY;var eK="MenuSubContent",eZ=i.forwardRef((e,t)=>{let n=ev(eg,e.__scopeMenu),{forceMount:s=n.forceMount,...c}=e,u=ec(eg,e.__scopeMenu),p=eu(eg,e.__scopeMenu),v=eB(eK,e.__scopeMenu),g=i.useRef(null),h=(0,o.e)(t,g);return(0,f.jsx)(et.Provider,{scope:e.__scopeMenu,children:(0,f.jsx)(C.z,{present:s||u.open,children:(0,f.jsx)(et.Slot,{scope:e.__scopeMenu,children:(0,f.jsx)(e_,{id:v.contentId,"aria-labelledby":v.triggerId,...c,ref:h,align:"start",side:"rtl"===p.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{p.isUsingKeyboardRef.current&&g.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>{e.target!==v.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,a.M)(e.onEscapeKeyDown,e=>{p.onClose(),e.preventDefault()}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=Q[p.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),v.trigger?.focus(),e.preventDefault())})})})})})});function getOpenState(e){return e?"open":"closed"}function isIndeterminate(e){return"indeterminate"===e}function getCheckedState(e){return isIndeterminate(e)?"indeterminate":e?"checked":"unchecked"}function whenMouse(e){return t=>"mouse"===t.pointerType?e(t):void 0}eZ.displayName=eK;var e$="DropdownMenu",[eG,eX]=(0,s.b)(e$,[er]),eQ=er(),[eJ,e0]=eG(e$),DropdownMenu=e=>{let{__scopeDropdownMenu:t,children:n,dir:a,open:o,defaultOpen:s,onOpenChange:u,modal:p=!0}=e,v=eQ(t),g=i.useRef(null),[h,y]=(0,c.T)({prop:o,defaultProp:s??!1,onChange:u,caller:e$});return(0,f.jsx)(eJ,{scope:t,triggerId:(0,b.M)(),triggerRef:g,contentId:(0,b.M)(),open:h,onOpenChange:y,onOpenToggle:i.useCallback(()=>y(e=>!e),[y]),modal:p,children:(0,f.jsx)(Menu,{...v,open:h,onOpenChange:y,dir:a,modal:p,children:n})})};DropdownMenu.displayName=e$;var e1="DropdownMenuTrigger",e2=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:i=!1,...s}=e,c=e0(e1,n),p=eQ(n);return(0,f.jsx)(ep,{asChild:!0,...p,children:(0,f.jsx)(u.WV.button,{type:"button",id:c.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":c.open?c.contentId:void 0,"data-state":c.open?"open":"closed","data-disabled":i?"":void 0,disabled:i,...s,ref:(0,o.F)(t,c.triggerRef),onPointerDown:(0,a.M)(e.onPointerDown,e=>{i||0!==e.button||!1!==e.ctrlKey||(c.onOpenToggle(),c.open||e.preventDefault())}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{!i&&(["Enter"," "].includes(e.key)&&c.onOpenToggle(),"ArrowDown"===e.key&&c.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e2.displayName=e1;var DropdownMenuPortal=e=>{let{__scopeDropdownMenu:t,...n}=e,i=eQ(t);return(0,f.jsx)(MenuPortal,{...i,...n})};DropdownMenuPortal.displayName="DropdownMenuPortal";var e5="DropdownMenuContent",e3=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,s=e0(e5,n),c=eQ(n),u=i.useRef(!1);return(0,f.jsx)(ex,{id:s.contentId,"aria-labelledby":s.triggerId,...c,...o,ref:t,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{u.current||s.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,a.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,i=2===t.button||n;(!s.modal||i)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e3.displayName=e5,i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eD,{...a,...i,ref:t})}).displayName="DropdownMenuGroup";var e4=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eC,{...a,...i,ref:t})});e4.displayName="DropdownMenuLabel",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(ej,{...a,...i,ref:t})}).displayName="DropdownMenuItem",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eO,{...a,...i,ref:t})}).displayName="DropdownMenuCheckboxItem",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eR,{...a,...i,ref:t})}).displayName="DropdownMenuRadioGroup",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eN,{...a,...i,ref:t})}).displayName="DropdownMenuRadioItem",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(ez,{...a,...i,ref:t})}).displayName="DropdownMenuItemIndicator";var e6=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eU,{...a,...i,ref:t})});e6.displayName="DropdownMenuSeparator",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eq,{...a,...i,ref:t})}).displayName="DropdownMenuArrow",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eV,{...a,...i,ref:t})}).displayName="DropdownMenuSubTrigger",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eQ(n);return(0,f.jsx)(eZ,{...a,...i,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e7=DropdownMenu,e8=e2,e9=DropdownMenuPortal,te=e3,tt=e4,tn=e6},1244:function(e,t,n){"use strict";n.d(t,{EW:function(){return useFocusGuards}});var i=n(2265),a=0;function useFocusGuards(){i.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??createFocusGuard()),document.body.insertAdjacentElement("beforeend",e[1]??createFocusGuard()),a++,()=>{1===a&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),a--}},[])}function createFocusGuard(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2759:function(e,t,n){"use strict";let i;n.d(t,{M:function(){return g}});var a=n(2265),o=n(2210),s=n(9381),c=n(6459),u=n(7437),p="focusScope.autoFocusOnMount",f="focusScope.autoFocusOnUnmount",v={bubbles:!1,cancelable:!0},g=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:i=!1,onMountAutoFocus:g,onUnmountAutoFocus:y,...b}=e,[w,D]=a.useState(null),C=(0,c.W)(g),S=(0,c.W)(y),E=a.useRef(null),j=(0,o.e)(t,e=>D(e)),M=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(i){let handleFocusIn2=function(e){if(M.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:focus(E.current,{select:!0})},handleFocusOut2=function(e){if(M.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||focus(E.current,{select:!0})};document.addEventListener("focusin",handleFocusIn2),document.addEventListener("focusout",handleFocusOut2);let e=new MutationObserver(function(e){let t=document.activeElement;if(t===document.body)for(let t of e)t.removedNodes.length>0&&focus(w)});return w&&e.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",handleFocusIn2),document.removeEventListener("focusout",handleFocusOut2),e.disconnect()}}},[i,w,M.paused]),a.useEffect(()=>{if(w){h.add(M);let e=document.activeElement,t=w.contains(e);if(!t){let t=new CustomEvent(p,v);w.addEventListener(p,C),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let i of e)if(focus(i,{select:t}),document.activeElement!==n)return}(getTabbableCandidates(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&focus(w))}return()=>{w.removeEventListener(p,C),setTimeout(()=>{let t=new CustomEvent(f,v);w.addEventListener(f,S),w.dispatchEvent(t),t.defaultPrevented||focus(e??document.body,{select:!0}),w.removeEventListener(f,S),h.remove(M)},0)}}},[w,C,S,M]);let O=a.useCallback(e=>{if(!n&&!i||M.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[i,o]=function(e){let t=getTabbableCandidates(e),n=findVisible(t,e),i=findVisible(t.reverse(),e);return[n,i]}(t),s=i&&o;s?e.shiftKey||a!==o?e.shiftKey&&a===i&&(e.preventDefault(),n&&focus(o,{select:!0})):(e.preventDefault(),n&&focus(i,{select:!0})):a===t&&e.preventDefault()}},[n,i,M.paused]);return(0,u.jsx)(s.WV.div,{tabIndex:-1,...b,ref:j,onKeyDown:O})});function getTabbableCandidates(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function findVisible(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function focus(e,{select:t=!1}={}){if(e&&e.focus){var n;let i=document.activeElement;e.focus({preventScroll:!0}),e!==i&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}g.displayName="FocusScope";var h=(i=[],{add(e){let t=i[0];e!==t&&t?.pause(),(i=arrayRemove(i,e)).unshift(e)},remove(e){i=arrayRemove(i,e),i[0]?.resume()}});function arrayRemove(e,t){let n=[...e],i=n.indexOf(t);return -1!==i&&n.splice(i,1),n}},966:function(e,t,n){"use strict";n.d(t,{M:function(){return useId}});var i,a=n(2265),o=n(1030),s=(i||(i=n.t(a,2)))[" useId ".trim().toString()]||(()=>void 0),c=0;function useId(e){let[t,n]=a.useState(s());return(0,o.b)(()=>{e||n(e=>e??String(c++))},[e]),e||(t?`radix-${t}`:"")}},5050:function(e,t,n){"use strict";n.d(t,{VY:function(){return en},ee:function(){return Q},fC:function(){return X},h_:function(){return et},xz:function(){return J}});var i=n(2265),a=n(5744),o=n(2210),s=n(6989),c=n(9249),u=n(1244),p=n(2759),f=n(966),v=n(4402),g=n(2730),h=n(5606),y=n(9381),b=n(7256),w=n(3763),D=n(5859),C=n(7552),S=n(7437),E="Popover",[j,M]=(0,s.b)(E,[v.D7]),O=(0,v.D7)(),[P,T]=j(E),Popover=e=>{let{__scopePopover:t,children:n,open:a,defaultOpen:o,onOpenChange:s,modal:c=!1}=e,u=O(t),p=i.useRef(null),[g,h]=i.useState(!1),[y,b]=(0,w.T)({prop:a,defaultProp:o??!1,onChange:s,caller:E});return(0,S.jsx)(v.fC,{...u,children:(0,S.jsx)(P,{scope:t,contentId:(0,f.M)(),triggerRef:p,open:y,onOpenChange:b,onOpenToggle:i.useCallback(()=>b(e=>!e),[b]),hasCustomAnchor:g,onCustomAnchorAdd:i.useCallback(()=>h(!0),[]),onCustomAnchorRemove:i.useCallback(()=>h(!1),[]),modal:c,children:n})})};Popover.displayName=E;var A="PopoverAnchor",L=i.forwardRef((e,t)=>{let{__scopePopover:n,...a}=e,o=T(A,n),s=O(n),{onCustomAnchorAdd:c,onCustomAnchorRemove:u}=o;return i.useEffect(()=>(c(),()=>u()),[c,u]),(0,S.jsx)(v.ee,{...s,...a,ref:t})});L.displayName=A;var N="PopoverTrigger",I=i.forwardRef((e,t)=>{let{__scopePopover:n,...i}=e,s=T(N,n),c=O(n),u=(0,o.e)(t,s.triggerRef),p=(0,S.jsx)(y.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":getState(s.open),...i,ref:u,onClick:(0,a.M)(e.onClick,s.onOpenToggle)});return s.hasCustomAnchor?p:(0,S.jsx)(v.ee,{asChild:!0,...c,children:p})});I.displayName=N;var W="PopoverPortal",[z,q]=j(W,{forceMount:void 0}),PopoverPortal=e=>{let{__scopePopover:t,forceMount:n,children:i,container:a}=e,o=T(W,t);return(0,S.jsx)(z,{scope:t,forceMount:n,children:(0,S.jsx)(h.z,{present:n||o.open,children:(0,S.jsx)(g.h,{asChild:!0,container:a,children:i})})})};PopoverPortal.displayName=W;var H="PopoverContent",B=i.forwardRef((e,t)=>{let n=q(H,e.__scopePopover),{forceMount:i=n.forceMount,...a}=e,o=T(H,e.__scopePopover);return(0,S.jsx)(h.z,{present:i||o.open,children:o.modal?(0,S.jsx)(V,{...a,ref:t}):(0,S.jsx)(Z,{...a,ref:t})})});B.displayName=H;var Y=(0,b.Z8)("PopoverContent.RemoveScroll"),V=i.forwardRef((e,t)=>{let n=T(H,e.__scopePopover),s=i.useRef(null),c=(0,o.e)(t,s),u=i.useRef(!1);return i.useEffect(()=>{let e=s.current;if(e)return(0,D.Ry)(e)},[]),(0,S.jsx)(C.Z,{as:Y,allowPinchZoom:!0,children:(0,S.jsx)($,{...e,ref:c,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),u.current||n.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,i=2===t.button||n;u.current=i},{checkForDefaultPrevented:!1}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),Z=i.forwardRef((e,t)=>{let n=T(H,e.__scopePopover),a=i.useRef(!1),o=i.useRef(!1);return(0,S.jsx)($,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||n.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let i=t.target,s=n.triggerRef.current?.contains(i);s&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),$=i.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:o,disableOutsidePointerEvents:s,onEscapeKeyDown:f,onPointerDownOutside:g,onFocusOutside:h,onInteractOutside:y,...b}=e,w=T(H,n),D=O(n);return(0,u.EW)(),(0,S.jsx)(p.M,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:o,children:(0,S.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:y,onEscapeKeyDown:f,onPointerDownOutside:g,onFocusOutside:h,onDismiss:()=>w.onOpenChange(!1),children:(0,S.jsx)(v.VY,{"data-state":getState(w.open),role:"dialog",id:w.contentId,...D,...b,ref:t,style:{...b.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),G="PopoverClose";function getState(e){return e?"open":"closed"}i.forwardRef((e,t)=>{let{__scopePopover:n,...i}=e,o=T(G,n);return(0,S.jsx)(y.WV.button,{type:"button",...i,ref:t,onClick:(0,a.M)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=G,i.forwardRef((e,t)=>{let{__scopePopover:n,...i}=e,a=O(n);return(0,S.jsx)(v.Eh,{...a,...i,ref:t})}).displayName="PopoverArrow";var X=Popover,Q=L,J=I,et=PopoverPortal,en=B},4402:function(e,t,n){"use strict";n.d(t,{ee:function(){return ec},Eh:function(){return eu},VY:function(){return ed},fC:function(){return es},D7:function(){return $}});var i=n(2265);let a=["top","right","bottom","left"],o=Math.min,s=Math.max,c=Math.round,u=Math.floor,createCoords=e=>({x:e,y:e}),p={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function floating_ui_utils_evaluate(e,t){return"function"==typeof e?e(t):e}function floating_ui_utils_getSide(e){return e.split("-")[0]}function floating_ui_utils_getAlignment(e){return e.split("-")[1]}function getOppositeAxis(e){return"x"===e?"y":"x"}function getAxisLength(e){return"y"===e?"height":"width"}let v=new Set(["top","bottom"]);function floating_ui_utils_getSideAxis(e){return v.has(floating_ui_utils_getSide(e))?"y":"x"}function floating_ui_utils_getOppositeAlignmentPlacement(e){return e.replace(/start|end/g,e=>f[e])}let g=["left","right"],h=["right","left"],y=["top","bottom"],b=["bottom","top"];function getOppositePlacement(e){return e.replace(/left|right|bottom|top/g,e=>p[e])}function floating_ui_utils_getPaddingObject(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function floating_ui_utils_rectToClientRect(e){let{x:t,y:n,width:i,height:a}=e;return{width:i,height:a,top:n,left:t,right:t+i,bottom:n+a,x:t,y:n}}function computeCoordsFromPlacement(e,t,n){let i,{reference:a,floating:o}=e,s=floating_ui_utils_getSideAxis(t),c=getOppositeAxis(floating_ui_utils_getSideAxis(t)),u=getAxisLength(c),p=floating_ui_utils_getSide(t),f="y"===s,v=a.x+a.width/2-o.width/2,g=a.y+a.height/2-o.height/2,h=a[u]/2-o[u]/2;switch(p){case"top":i={x:v,y:a.y-o.height};break;case"bottom":i={x:v,y:a.y+a.height};break;case"right":i={x:a.x+a.width,y:g};break;case"left":i={x:a.x-o.width,y:g};break;default:i={x:a.x,y:a.y}}switch(floating_ui_utils_getAlignment(t)){case"start":i[c]-=h*(n&&f?-1:1);break;case"end":i[c]+=h*(n&&f?-1:1)}return i}let computePosition=async(e,t,n)=>{let{placement:i="bottom",strategy:a="absolute",middleware:o=[],platform:s}=n,c=o.filter(Boolean),u=await (null==s.isRTL?void 0:s.isRTL(t)),p=await s.getElementRects({reference:e,floating:t,strategy:a}),{x:f,y:v}=computeCoordsFromPlacement(p,i,u),g=i,h={},y=0;for(let n=0;n<c.length;n++){let{name:o,fn:b}=c[n],{x:w,y:D,data:C,reset:S}=await b({x:f,y:v,initialPlacement:i,placement:g,strategy:a,middlewareData:h,rects:p,platform:s,elements:{reference:e,floating:t}});f=null!=w?w:f,v=null!=D?D:v,h={...h,[o]:{...h[o],...C}},S&&y<=50&&(y++,"object"==typeof S&&(S.placement&&(g=S.placement),S.rects&&(p=!0===S.rects?await s.getElementRects({reference:e,floating:t,strategy:a}):S.rects),{x:f,y:v}=computeCoordsFromPlacement(p,g,u)),n=-1)}return{x:f,y:v,placement:g,strategy:a,middlewareData:h}};async function detectOverflow(e,t){var n;void 0===t&&(t={});let{x:i,y:a,platform:o,rects:s,elements:c,strategy:u}=e,{boundary:p="clippingAncestors",rootBoundary:f="viewport",elementContext:v="floating",altBoundary:g=!1,padding:h=0}=floating_ui_utils_evaluate(t,e),y=floating_ui_utils_getPaddingObject(h),b=c[g?"floating"===v?"reference":"floating":v],w=floating_ui_utils_rectToClientRect(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(b)))||n?b:b.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(c.floating)),boundary:p,rootBoundary:f,strategy:u})),D="floating"===v?{x:i,y:a,width:s.floating.width,height:s.floating.height}:s.reference,C=await (null==o.getOffsetParent?void 0:o.getOffsetParent(c.floating)),S=await (null==o.isElement?void 0:o.isElement(C))&&await (null==o.getScale?void 0:o.getScale(C))||{x:1,y:1},E=floating_ui_utils_rectToClientRect(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:D,offsetParent:C,strategy:u}):D);return{top:(w.top-E.top+y.top)/S.y,bottom:(E.bottom-w.bottom+y.bottom)/S.y,left:(w.left-E.left+y.left)/S.x,right:(E.right-w.right+y.right)/S.x}}function getSideOffsets(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function isAnySideFullyClipped(e){return a.some(t=>e[t]>=0)}let w=new Set(["left","top"]);async function convertValueToCoords(e,t){let{placement:n,platform:i,elements:a}=e,o=await (null==i.isRTL?void 0:i.isRTL(a.floating)),s=floating_ui_utils_getSide(n),c=floating_ui_utils_getAlignment(n),u="y"===floating_ui_utils_getSideAxis(n),p=w.has(s)?-1:1,f=o&&u?-1:1,v=floating_ui_utils_evaluate(t,e),{mainAxis:g,crossAxis:h,alignmentAxis:y}="number"==typeof v?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return c&&"number"==typeof y&&(h="end"===c?-1*y:y),u?{x:h*f,y:g*p}:{x:g*p,y:h*f}}function hasWindow(){return"undefined"!=typeof window}function getNodeName(e){return isNode(e)?(e.nodeName||"").toLowerCase():"#document"}function getWindow(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function getDocumentElement(e){var t;return null==(t=(isNode(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function isNode(e){return!!hasWindow()&&(e instanceof Node||e instanceof getWindow(e).Node)}function isElement(e){return!!hasWindow()&&(e instanceof Element||e instanceof getWindow(e).Element)}function isHTMLElement(e){return!!hasWindow()&&(e instanceof HTMLElement||e instanceof getWindow(e).HTMLElement)}function isShadowRoot(e){return!!hasWindow()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof getWindow(e).ShadowRoot)}let D=new Set(["inline","contents"]);function isOverflowElement(e){let{overflow:t,overflowX:n,overflowY:i,display:a}=getComputedStyle(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!D.has(a)}let C=new Set(["table","td","th"]),S=[":popover-open",":modal"];function isTopLayer(e){return S.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let E=["transform","translate","scale","rotate","perspective"],j=["transform","translate","scale","rotate","perspective","filter"],M=["paint","layout","strict","content"];function isContainingBlock(e){let t=isWebKit(),n=isElement(e)?getComputedStyle(e):e;return E.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||j.some(e=>(n.willChange||"").includes(e))||M.some(e=>(n.contain||"").includes(e))}function isWebKit(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let O=new Set(["html","body","#document"]);function isLastTraversableNode(e){return O.has(getNodeName(e))}function getComputedStyle(e){return getWindow(e).getComputedStyle(e)}function getNodeScroll(e){return isElement(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function getParentNode(e){if("html"===getNodeName(e))return e;let t=e.assignedSlot||e.parentNode||isShadowRoot(e)&&e.host||getDocumentElement(e);return isShadowRoot(t)?t.host:t}function getOverflowAncestors(e,t,n){var i;void 0===t&&(t=[]),void 0===n&&(n=!0);let a=function getNearestOverflowAncestor(e){let t=getParentNode(e);return isLastTraversableNode(t)?e.ownerDocument?e.ownerDocument.body:e.body:isHTMLElement(t)&&isOverflowElement(t)?t:getNearestOverflowAncestor(t)}(e),o=a===(null==(i=e.ownerDocument)?void 0:i.body),s=getWindow(a);if(o){let e=getFrameElement(s);return t.concat(s,s.visualViewport||[],isOverflowElement(a)?a:[],e&&n?getOverflowAncestors(e):[])}return t.concat(a,getOverflowAncestors(a,[],n))}function getFrameElement(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function getCssDimensions(e){let t=getComputedStyle(e),n=parseFloat(t.width)||0,i=parseFloat(t.height)||0,a=isHTMLElement(e),o=a?e.offsetWidth:n,s=a?e.offsetHeight:i,u=c(n)!==o||c(i)!==s;return u&&(n=o,i=s),{width:n,height:i,$:u}}function unwrapElement(e){return isElement(e)?e:e.contextElement}function getScale(e){let t=unwrapElement(e);if(!isHTMLElement(t))return createCoords(1);let n=t.getBoundingClientRect(),{width:i,height:a,$:o}=getCssDimensions(t),s=(o?c(n.width):n.width)/i,u=(o?c(n.height):n.height)/a;return s&&Number.isFinite(s)||(s=1),u&&Number.isFinite(u)||(u=1),{x:s,y:u}}let P=createCoords(0);function getVisualOffsets(e){let t=getWindow(e);return isWebKit()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:P}function getBoundingClientRect(e,t,n,i){var a;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=unwrapElement(e),c=createCoords(1);t&&(i?isElement(i)&&(c=getScale(i)):c=getScale(e));let u=(void 0===(a=n)&&(a=!1),i&&(!a||i===getWindow(s))&&a)?getVisualOffsets(s):createCoords(0),p=(o.left+u.x)/c.x,f=(o.top+u.y)/c.y,v=o.width/c.x,g=o.height/c.y;if(s){let e=getWindow(s),t=i&&isElement(i)?getWindow(i):i,n=e,a=getFrameElement(n);for(;a&&i&&t!==n;){let e=getScale(a),t=a.getBoundingClientRect(),i=getComputedStyle(a),o=t.left+(a.clientLeft+parseFloat(i.paddingLeft))*e.x,s=t.top+(a.clientTop+parseFloat(i.paddingTop))*e.y;p*=e.x,f*=e.y,v*=e.x,g*=e.y,p+=o,f+=s,a=getFrameElement(n=getWindow(a))}}return floating_ui_utils_rectToClientRect({width:v,height:g,x:p,y:f})}function getWindowScrollBarX(e,t){let n=getNodeScroll(e).scrollLeft;return t?t.left+n:getBoundingClientRect(getDocumentElement(e)).left+n}function getHTMLOffset(e,t,n){void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=i.left+t.scrollLeft-(n?0:getWindowScrollBarX(e,i)),o=i.top+t.scrollTop;return{x:a,y:o}}let T=new Set(["absolute","fixed"]);function getClientRectFromClippingAncestor(e,t,n){let i;if("viewport"===t)i=function(e,t){let n=getWindow(e),i=getDocumentElement(e),a=n.visualViewport,o=i.clientWidth,s=i.clientHeight,c=0,u=0;if(a){o=a.width,s=a.height;let e=isWebKit();(!e||e&&"fixed"===t)&&(c=a.offsetLeft,u=a.offsetTop)}return{width:o,height:s,x:c,y:u}}(e,n);else if("document"===t)i=function(e){let t=getDocumentElement(e),n=getNodeScroll(e),i=e.ownerDocument.body,a=s(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),o=s(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight),c=-n.scrollLeft+getWindowScrollBarX(e),u=-n.scrollTop;return"rtl"===getComputedStyle(i).direction&&(c+=s(t.clientWidth,i.clientWidth)-a),{width:a,height:o,x:c,y:u}}(getDocumentElement(e));else if(isElement(t))i=function(e,t){let n=getBoundingClientRect(e,!0,"fixed"===t),i=n.top+e.clientTop,a=n.left+e.clientLeft,o=isHTMLElement(e)?getScale(e):createCoords(1),s=e.clientWidth*o.x,c=e.clientHeight*o.y,u=a*o.x,p=i*o.y;return{width:s,height:c,x:u,y:p}}(t,n);else{let n=getVisualOffsets(e);i={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return floating_ui_utils_rectToClientRect(i)}function isStaticPositioned(e){return"static"===getComputedStyle(e).position}function getTrueOffsetParent(e,t){if(!isHTMLElement(e)||"fixed"===getComputedStyle(e).position)return null;if(t)return t(e);let n=e.offsetParent;return getDocumentElement(e)===n&&(n=n.ownerDocument.body),n}function getOffsetParent(e,t){var n;let i=getWindow(e);if(isTopLayer(e))return i;if(!isHTMLElement(e)){let t=getParentNode(e);for(;t&&!isLastTraversableNode(t);){if(isElement(t)&&!isStaticPositioned(t))return t;t=getParentNode(t)}return i}let a=getTrueOffsetParent(e,t);for(;a&&(n=a,C.has(getNodeName(n)))&&isStaticPositioned(a);)a=getTrueOffsetParent(a,t);return a&&isLastTraversableNode(a)&&isStaticPositioned(a)&&!isContainingBlock(a)?i:a||function(e){let t=getParentNode(e);for(;isHTMLElement(t)&&!isLastTraversableNode(t);){if(isContainingBlock(t))return t;if(isTopLayer(t))break;t=getParentNode(t)}return null}(e)||i}let getElementRects=async function(e){let t=this.getOffsetParent||getOffsetParent,n=this.getDimensions,i=await n(e.floating);return{reference:function(e,t,n){let i=isHTMLElement(t),a=getDocumentElement(t),o="fixed"===n,s=getBoundingClientRect(e,!0,o,t),c={scrollLeft:0,scrollTop:0},u=createCoords(0);if(i||!i&&!o){if(("body"!==getNodeName(t)||isOverflowElement(a))&&(c=getNodeScroll(t)),i){let e=getBoundingClientRect(t,!0,o,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else a&&(u.x=getWindowScrollBarX(a))}o&&!i&&a&&(u.x=getWindowScrollBarX(a));let p=!a||i||o?createCoords(0):getHTMLOffset(a,c),f=s.left+c.scrollLeft-u.x-p.x,v=s.top+c.scrollTop-u.y-p.y;return{x:f,y:v,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},A={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:i,strategy:a}=e,o="fixed"===a,s=getDocumentElement(i),c=!!t&&isTopLayer(t.floating);if(i===s||c&&o)return n;let u={scrollLeft:0,scrollTop:0},p=createCoords(1),f=createCoords(0),v=isHTMLElement(i);if((v||!v&&!o)&&(("body"!==getNodeName(i)||isOverflowElement(s))&&(u=getNodeScroll(i)),isHTMLElement(i))){let e=getBoundingClientRect(i);p=getScale(i),f.x=e.x+i.clientLeft,f.y=e.y+i.clientTop}let g=!s||v||o?createCoords(0):getHTMLOffset(s,u,!0);return{width:n.width*p.x,height:n.height*p.y,x:n.x*p.x-u.scrollLeft*p.x+f.x+g.x,y:n.y*p.y-u.scrollTop*p.y+f.y+g.y}},getDocumentElement:getDocumentElement,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:a}=e,c="clippingAncestors"===n?isTopLayer(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let i=getOverflowAncestors(e,[],!1).filter(e=>isElement(e)&&"body"!==getNodeName(e)),a=null,o="fixed"===getComputedStyle(e).position,s=o?getParentNode(e):e;for(;isElement(s)&&!isLastTraversableNode(s);){let t=getComputedStyle(s),n=isContainingBlock(s);n||"fixed"!==t.position||(a=null);let c=o?!n&&!a:!n&&"static"===t.position&&!!a&&T.has(a.position)||isOverflowElement(s)&&!n&&function hasFixedPositionAncestor(e,t){let n=getParentNode(e);return!(n===t||!isElement(n)||isLastTraversableNode(n))&&("fixed"===getComputedStyle(n).position||hasFixedPositionAncestor(n,t))}(e,s);c?i=i.filter(e=>e!==s):a=t,s=getParentNode(s)}return t.set(e,i),i}(t,this._c):[].concat(n),u=[...c,i],p=u[0],f=u.reduce((e,n)=>{let i=getClientRectFromClippingAncestor(t,n,a);return e.top=s(i.top,e.top),e.right=o(i.right,e.right),e.bottom=o(i.bottom,e.bottom),e.left=s(i.left,e.left),e},getClientRectFromClippingAncestor(t,p,a));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}},getOffsetParent,getElementRects,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=getCssDimensions(e);return{width:t,height:n}},getScale,isElement:isElement,isRTL:function(e){return"rtl"===getComputedStyle(e).direction}};function rectsAreEqual(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let floating_ui_dom_arrow=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:a,rects:c,platform:u,elements:p,middlewareData:f}=t,{element:v,padding:g=0}=floating_ui_utils_evaluate(e,t)||{};if(null==v)return{};let h=floating_ui_utils_getPaddingObject(g),y={x:n,y:i},b=getOppositeAxis(floating_ui_utils_getSideAxis(a)),w=getAxisLength(b),D=await u.getDimensions(v),C="y"===b,S=C?"clientHeight":"clientWidth",E=c.reference[w]+c.reference[b]-y[b]-c.floating[w],j=y[b]-c.reference[b],M=await (null==u.getOffsetParent?void 0:u.getOffsetParent(v)),O=M?M[S]:0;O&&await (null==u.isElement?void 0:u.isElement(M))||(O=p.floating[S]||c.floating[w]);let P=O/2-D[w]/2-1,T=o(h[C?"top":"left"],P),A=o(h[C?"bottom":"right"],P),L=O-D[w]-A,N=O/2-D[w]/2+(E/2-j/2),I=s(T,o(N,L)),W=!f.arrow&&null!=floating_ui_utils_getAlignment(a)&&N!==I&&c.reference[w]/2-(N<T?T:A)-D[w]/2<0,z=W?N<T?N-T:N-L:0;return{[b]:y[b]+z,data:{[b]:I,centerOffset:N-I-z,...W&&{alignmentOffset:z}},reset:W}}}),floating_ui_dom_computePosition=(e,t,n)=>{let i=new Map,a={platform:A,...n},o={...a.platform,_c:i};return computePosition(e,t,{...a,platform:o})};var L=n(4887),N="undefined"!=typeof document?i.useLayoutEffect:function(){};function deepEqual(e,t){let n,i,a;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(i=n;0!=i--;)if(!deepEqual(e[i],t[i]))return!1;return!0}if((n=(a=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(i=n;0!=i--;)if(!({}).hasOwnProperty.call(t,a[i]))return!1;for(i=n;0!=i--;){let n=a[i];if(("_owner"!==n||!e.$$typeof)&&!deepEqual(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function getDPR(e){if("undefined"==typeof window)return 1;let t=e.ownerDocument.defaultView||window;return t.devicePixelRatio||1}function roundByDPR(e,t){let n=getDPR(e);return Math.round(t*n)/n}function useLatestRef(e){let t=i.useRef(e);return N(()=>{t.current=e}),t}let arrow$1=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:i}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?floating_ui_dom_arrow({element:n.current,padding:i}).fn(t):{}:n?floating_ui_dom_arrow({element:n,padding:i}).fn(t):{}}}),floating_ui_react_dom_offset=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,i;let{x:a,y:o,placement:s,middlewareData:c}=e,u=await convertValueToCoords(e,n);return s===(null==(t=c.offset)?void 0:t.placement)&&null!=(i=c.arrow)&&i.alignmentOffset?{}:{x:a+u.x,y:o+u.y,data:{...u,placement:s}}}}),options:[e,t]}},floating_ui_react_dom_shift=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:i,placement:a}=e,{mainAxis:c=!0,crossAxis:u=!1,limiter:p={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=floating_ui_utils_evaluate(n,e),v={x:t,y:i},g=await detectOverflow(e,f),h=floating_ui_utils_getSideAxis(floating_ui_utils_getSide(a)),y=getOppositeAxis(h),b=v[y],w=v[h];if(c){let e=b+g["y"===y?"top":"left"],t=b-g["y"===y?"bottom":"right"];b=s(e,o(b,t))}if(u){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=w+g[e],i=w-g[t];w=s(n,o(w,i))}let D=p.fn({...e,[y]:b,[h]:w});return{...D,data:{x:D.x-t,y:D.y-i,enabled:{[y]:c,[h]:u}}}}}),options:[e,t]}},floating_ui_react_dom_limitShift=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:i,placement:a,rects:o,middlewareData:s}=e,{offset:c=0,mainAxis:u=!0,crossAxis:p=!0}=floating_ui_utils_evaluate(n,e),f={x:t,y:i},v=floating_ui_utils_getSideAxis(a),g=getOppositeAxis(v),h=f[g],y=f[v],b=floating_ui_utils_evaluate(c,e),D="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===g?"height":"width",t=o.reference[g]-o.floating[e]+D.mainAxis,n=o.reference[g]+o.reference[e]-D.mainAxis;h<t?h=t:h>n&&(h=n)}if(p){var C,S;let e="y"===g?"width":"height",t=w.has(floating_ui_utils_getSide(a)),n=o.reference[v]-o.floating[e]+(t&&(null==(C=s.offset)?void 0:C[v])||0)+(t?0:D.crossAxis),i=o.reference[v]+o.reference[e]+(t?0:(null==(S=s.offset)?void 0:S[v])||0)-(t?D.crossAxis:0);y<n?y=n:y>i&&(y=i)}return{[g]:h,[v]:y}}}),options:[e,t]}},floating_ui_react_dom_flip=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,i,a,o,s;let{placement:c,middlewareData:u,rects:p,initialPlacement:f,platform:v,elements:w}=e,{mainAxis:D=!0,crossAxis:C=!0,fallbackPlacements:S,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:j="none",flipAlignment:M=!0,...O}=floating_ui_utils_evaluate(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let P=floating_ui_utils_getSide(c),T=floating_ui_utils_getSideAxis(f),A=floating_ui_utils_getSide(f)===f,L=await (null==v.isRTL?void 0:v.isRTL(w.floating)),N=S||(A||!M?[getOppositePlacement(f)]:function(e){let t=getOppositePlacement(e);return[floating_ui_utils_getOppositeAlignmentPlacement(e),t,floating_ui_utils_getOppositeAlignmentPlacement(t)]}(f)),I="none"!==j;!S&&I&&N.push(...function(e,t,n,i){let a=floating_ui_utils_getAlignment(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?h:g;return t?g:h;case"left":case"right":return t?y:b;default:return[]}}(floating_ui_utils_getSide(e),"start"===n,i);return a&&(o=o.map(e=>e+"-"+a),t&&(o=o.concat(o.map(floating_ui_utils_getOppositeAlignmentPlacement)))),o}(f,M,j,L));let W=[f,...N],z=await detectOverflow(e,O),q=[],H=(null==(i=u.flip)?void 0:i.overflows)||[];if(D&&q.push(z[P]),C){let e=function(e,t,n){void 0===n&&(n=!1);let i=floating_ui_utils_getAlignment(e),a=getOppositeAxis(floating_ui_utils_getSideAxis(e)),o=getAxisLength(a),s="x"===a?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=getOppositePlacement(s)),[s,getOppositePlacement(s)]}(c,p,L);q.push(z[e[0]],z[e[1]])}if(H=[...H,{placement:c,overflows:q}],!q.every(e=>e<=0)){let e=((null==(a=u.flip)?void 0:a.index)||0)+1,t=W[e];if(t){let n="alignment"===C&&T!==floating_ui_utils_getSideAxis(t);if(!n||H.every(e=>e.overflows[0]>0&&floating_ui_utils_getSideAxis(e.placement)===T))return{data:{index:e,overflows:H},reset:{placement:t}}}let n=null==(o=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(E){case"bestFit":{let e=null==(s=H.filter(e=>{if(I){let t=floating_ui_utils_getSideAxis(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(n=e);break}case"initialPlacement":n=f}if(c!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},floating_ui_react_dom_size=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,i;let a,c;let{placement:u,rects:p,platform:f,elements:v}=e,{apply:g=()=>{},...h}=floating_ui_utils_evaluate(n,e),y=await detectOverflow(e,h),b=floating_ui_utils_getSide(u),w=floating_ui_utils_getAlignment(u),D="y"===floating_ui_utils_getSideAxis(u),{width:C,height:S}=p.floating;"top"===b||"bottom"===b?(a=b,c=w===(await (null==f.isRTL?void 0:f.isRTL(v.floating))?"start":"end")?"left":"right"):(c=b,a="end"===w?"top":"bottom");let E=S-y.top-y.bottom,j=C-y.left-y.right,M=o(S-y[a],E),O=o(C-y[c],j),P=!e.middlewareData.shift,T=M,A=O;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(A=j),null!=(i=e.middlewareData.shift)&&i.enabled.y&&(T=E),P&&!w){let e=s(y.left,0),t=s(y.right,0),n=s(y.top,0),i=s(y.bottom,0);D?A=C-2*(0!==e||0!==t?e+t:s(y.left,y.right)):T=S-2*(0!==n||0!==i?n+i:s(y.top,y.bottom))}await g({...e,availableWidth:A,availableHeight:T});let L=await f.getDimensions(v.floating);return C!==L.width||S!==L.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},floating_ui_react_dom_hide=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:i="referenceHidden",...a}=floating_ui_utils_evaluate(n,e);switch(i){case"referenceHidden":{let n=await detectOverflow(e,{...a,elementContext:"reference"}),i=getSideOffsets(n,t.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:isAnySideFullyClipped(i)}}}case"escaped":{let n=await detectOverflow(e,{...a,altBoundary:!0}),i=getSideOffsets(n,t.floating);return{data:{escapedOffsets:i,escaped:isAnySideFullyClipped(i)}}}default:return{}}}}),options:[e,t]}},floating_ui_react_dom_arrow=(e,t)=>({...arrow$1(e),options:[e,t]});var I=n(9381),W=n(7437),z=i.forwardRef((e,t)=>{let{children:n,width:i=10,height:a=5,...o}=e;return(0,W.jsx)(I.WV.svg,{...o,ref:t,width:i,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,W.jsx)("polygon",{points:"0,0 30,0 15,10"})})});z.displayName="Arrow";var q=n(2210),H=n(6989),B=n(6459),Y=n(1030),V="Popper",[Z,$]=(0,H.b)(V),[G,X]=Z(V),Popper=e=>{let{__scopePopper:t,children:n}=e,[a,o]=i.useState(null);return(0,W.jsx)(G,{scope:t,anchor:a,onAnchorChange:o,children:n})};Popper.displayName=V;var Q="PopperAnchor",J=i.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:a,...o}=e,s=X(Q,n),c=i.useRef(null),u=(0,q.e)(t,c);return i.useEffect(()=>{s.onAnchorChange(a?.current||c.current)}),a?null:(0,W.jsx)(I.WV.div,{...o,ref:u})});J.displayName=Q;var et="PopperContent",[en,ei]=Z(et),ea=i.forwardRef((e,t)=>{let{__scopePopper:n,side:a="bottom",sideOffset:c=0,align:p="center",alignOffset:f=0,arrowPadding:v=0,avoidCollisions:g=!0,collisionBoundary:h=[],collisionPadding:y=0,sticky:b="partial",hideWhenDetached:w=!1,updatePositionStrategy:D="optimized",onPlaced:C,...S}=e,E=X(et,n),[j,M]=i.useState(null),O=(0,q.e)(t,e=>M(e)),[P,T]=i.useState(null),A=function(e){let[t,n]=i.useState(void 0);return(0,Y.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let i,a;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,a=t.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(P),z=A?.width??0,H=A?.height??0,V="number"==typeof y?y:{top:0,right:0,bottom:0,left:0,...y},Z=Array.isArray(h)?h:[h],$=Z.length>0,G={padding:V,boundary:Z.filter(isNotNull),altBoundary:$},{refs:Q,floatingStyles:J,placement:ei,isPositioned:ea,middlewareData:er}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:a=[],platform:o,elements:{reference:s,floating:c}={},transform:u=!0,whileElementsMounted:p,open:f}=e,[v,g]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,y]=i.useState(a);deepEqual(h,a)||y(a);let[b,w]=i.useState(null),[D,C]=i.useState(null),S=i.useCallback(e=>{e!==O.current&&(O.current=e,w(e))},[]),E=i.useCallback(e=>{e!==P.current&&(P.current=e,C(e))},[]),j=s||b,M=c||D,O=i.useRef(null),P=i.useRef(null),T=i.useRef(v),A=null!=p,I=useLatestRef(p),W=useLatestRef(o),z=useLatestRef(f),q=i.useCallback(()=>{if(!O.current||!P.current)return;let e={placement:t,strategy:n,middleware:h};W.current&&(e.platform=W.current),floating_ui_dom_computePosition(O.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==z.current};H.current&&!deepEqual(T.current,t)&&(T.current=t,L.flushSync(()=>{g(t)}))})},[h,t,n,W,z]);N(()=>{!1===f&&T.current.isPositioned&&(T.current.isPositioned=!1,g(e=>({...e,isPositioned:!1})))},[f]);let H=i.useRef(!1);N(()=>(H.current=!0,()=>{H.current=!1}),[]),N(()=>{if(j&&(O.current=j),M&&(P.current=M),j&&M){if(I.current)return I.current(j,M,q);q()}},[j,M,q,I,A]);let B=i.useMemo(()=>({reference:O,floating:P,setReference:S,setFloating:E}),[S,E]),Y=i.useMemo(()=>({reference:j,floating:M}),[j,M]),V=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!Y.floating)return e;let t=roundByDPR(Y.floating,v.x),i=roundByDPR(Y.floating,v.y);return u?{...e,transform:"translate("+t+"px, "+i+"px)",...getDPR(Y.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:i}},[n,u,Y.floating,v.x,v.y]);return i.useMemo(()=>({...v,update:q,refs:B,elements:Y,floatingStyles:V}),[v,q,B,Y,V])}({strategy:"fixed",placement:a+("center"!==p?"-"+p:""),whileElementsMounted:(...e)=>{let t=function(e,t,n,i){let a;void 0===i&&(i={});let{ancestorScroll:c=!0,ancestorResize:p=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:v="function"==typeof IntersectionObserver,animationFrame:g=!1}=i,h=unwrapElement(e),y=c||p?[...h?getOverflowAncestors(h):[],...getOverflowAncestors(t)]:[];y.forEach(e=>{c&&e.addEventListener("scroll",n,{passive:!0}),p&&e.addEventListener("resize",n)});let b=h&&v?function(e,t){let n,i=null,a=getDocumentElement(e);function cleanup(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function refresh(c,p){void 0===c&&(c=!1),void 0===p&&(p=1),cleanup();let f=e.getBoundingClientRect(),{left:v,top:g,width:h,height:y}=f;if(c||t(),!h||!y)return;let b=u(g),w=u(a.clientWidth-(v+h)),D=u(a.clientHeight-(g+y)),C=u(v),S={rootMargin:-b+"px "+-w+"px "+-D+"px "+-C+"px",threshold:s(0,o(1,p))||1},E=!0;function handleObserve(t){let i=t[0].intersectionRatio;if(i!==p){if(!E)return refresh();i?refresh(!1,i):n=setTimeout(()=>{refresh(!1,1e-7)},1e3)}1!==i||rectsAreEqual(f,e.getBoundingClientRect())||refresh(),E=!1}try{i=new IntersectionObserver(handleObserve,{...S,root:a.ownerDocument})}catch(e){i=new IntersectionObserver(handleObserve,S)}i.observe(e)}(!0),cleanup}(h,n):null,w=-1,D=null;f&&(D=new ResizeObserver(e=>{let[i]=e;i&&i.target===h&&D&&(D.unobserve(t),cancelAnimationFrame(w),w=requestAnimationFrame(()=>{var e;null==(e=D)||e.observe(t)})),n()}),h&&!g&&D.observe(h),D.observe(t));let C=g?getBoundingClientRect(e):null;return g&&function frameLoop(){let t=getBoundingClientRect(e);C&&!rectsAreEqual(C,t)&&n(),C=t,a=requestAnimationFrame(frameLoop)}(),n(),()=>{var e;y.forEach(e=>{c&&e.removeEventListener("scroll",n),p&&e.removeEventListener("resize",n)}),null==b||b(),null==(e=D)||e.disconnect(),D=null,g&&cancelAnimationFrame(a)}}(...e,{animationFrame:"always"===D});return t},elements:{reference:E.anchor},middleware:[floating_ui_react_dom_offset({mainAxis:c+H,alignmentAxis:f}),g&&floating_ui_react_dom_shift({mainAxis:!0,crossAxis:!1,limiter:"partial"===b?floating_ui_react_dom_limitShift():void 0,...G}),g&&floating_ui_react_dom_flip({...G}),floating_ui_react_dom_size({...G,apply:({elements:e,rects:t,availableWidth:n,availableHeight:i})=>{let{width:a,height:o}=t.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width",`${n}px`),s.setProperty("--radix-popper-available-height",`${i}px`),s.setProperty("--radix-popper-anchor-width",`${a}px`),s.setProperty("--radix-popper-anchor-height",`${o}px`)}}),P&&floating_ui_react_dom_arrow({element:P,padding:v}),transformOrigin({arrowWidth:z,arrowHeight:H}),w&&floating_ui_react_dom_hide({strategy:"referenceHidden",...G})]}),[eo,el]=getSideAndAlignFromPlacement(ei),es=(0,B.W)(C);(0,Y.b)(()=>{ea&&es?.()},[ea,es]);let ec=er.arrow?.x,ed=er.arrow?.y,eu=er.arrow?.centerOffset!==0,[ep,ef]=i.useState();return(0,Y.b)(()=>{j&&ef(window.getComputedStyle(j).zIndex)},[j]),(0,W.jsx)("div",{ref:Q.setFloating,"data-radix-popper-content-wrapper":"",style:{...J,transform:ea?J.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ep,"--radix-popper-transform-origin":[er.transformOrigin?.x,er.transformOrigin?.y].join(" "),...er.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,W.jsx)(en,{scope:n,placedSide:eo,onArrowChange:T,arrowX:ec,arrowY:ed,shouldHideArrow:eu,children:(0,W.jsx)(I.WV.div,{"data-side":eo,"data-align":el,...S,ref:O,style:{...S.style,animation:ea?void 0:"none"}})})})});ea.displayName=et;var er="PopperArrow",eo={top:"bottom",right:"left",bottom:"top",left:"right"},el=i.forwardRef(function(e,t){let{__scopePopper:n,...i}=e,a=ei(er,n),o=eo[a.placedSide];return(0,W.jsx)("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:(0,W.jsx)(z,{...i,ref:t,style:{...i.style,display:"block"}})})});function isNotNull(e){return null!==e}el.displayName=er;var transformOrigin=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:i,middlewareData:a}=t,o=a.arrow?.centerOffset!==0,s=o?0:e.arrowWidth,c=o?0:e.arrowHeight,[u,p]=getSideAndAlignFromPlacement(n),f={start:"0%",center:"50%",end:"100%"}[p],v=(a.arrow?.x??0)+s/2,g=(a.arrow?.y??0)+c/2,h="",y="";return"bottom"===u?(h=o?f:`${v}px`,y=`${-c}px`):"top"===u?(h=o?f:`${v}px`,y=`${i.floating.height+c}px`):"right"===u?(h=`${-c}px`,y=o?f:`${g}px`):"left"===u&&(h=`${i.floating.width+c}px`,y=o?f:`${g}px`),{data:{x:h,y}}}});function getSideAndAlignFromPlacement(e){let[t,n="center"]=e.split("-");return[t,n]}var es=Popper,ec=J,ed=ea,eu=el},2730:function(e,t,n){"use strict";n.d(t,{h:function(){return u}});var i=n(2265),a=n(4887),o=n(9381),s=n(1030),c=n(7437),u=i.forwardRef((e,t)=>{let{container:n,...u}=e,[p,f]=i.useState(!1);(0,s.b)(()=>f(!0),[]);let v=n||p&&globalThis?.document?.body;return v?a.createPortal((0,c.jsx)(o.WV.div,{...u,ref:t}),v):null});u.displayName="Portal"},5606:function(e,t,n){"use strict";n.d(t,{z:function(){return Presence}});var i=n(2265),a=n(2210),o=n(1030),Presence=e=>{let t,n;let{present:s,children:c}=e,u=function(e){var t;let[n,a]=i.useState(),s=i.useRef(null),c=i.useRef(e),u=i.useRef("none"),p=e?"mounted":"unmounted",[f,v]=(t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,n)=>{let i=t[e][n];return i??e},p));return i.useEffect(()=>{let e=getAnimationName(s.current);u.current="mounted"===f?e:"none"},[f]),(0,o.b)(()=>{let t=s.current,n=c.current,i=n!==e;if(i){let i=u.current,a=getAnimationName(t);e?v("MOUNT"):"none"===a||t?.display==="none"?v("UNMOUNT"):n&&i!==a?v("ANIMATION_OUT"):v("UNMOUNT"),c.current=e}},[e,v]),(0,o.b)(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,handleAnimationEnd=i=>{let a=getAnimationName(s.current),o=a.includes(i.animationName);if(i.target===n&&o&&(v("ANIMATION_END"),!c.current)){let i=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=i)})}},handleAnimationStart=e=>{e.target===n&&(u.current=getAnimationName(s.current))};return n.addEventListener("animationstart",handleAnimationStart),n.addEventListener("animationcancel",handleAnimationEnd),n.addEventListener("animationend",handleAnimationEnd),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",handleAnimationStart),n.removeEventListener("animationcancel",handleAnimationEnd),n.removeEventListener("animationend",handleAnimationEnd)}}v("ANIMATION_END")},[n,v]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:i.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(s),p="function"==typeof c?c({present:u.isPresent}):i.Children.only(c),f=(0,a.e)(u.ref,(t=Object.getOwnPropertyDescriptor(p.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?p.ref:(t=Object.getOwnPropertyDescriptor(p,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?p.props.ref:p.props.ref||p.ref),v="function"==typeof c;return v||u.isPresent?i.cloneElement(p,{ref:f}):null};function getAnimationName(e){return e?.animationName||"none"}Presence.displayName="Presence"},9381:function(e,t,n){"use strict";n.d(t,{WV:function(){return c},jH:function(){return dispatchDiscreteCustomEvent}});var i=n(2265),a=n(4887),o=n(7256),s=n(7437),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.Z8)(`Primitive.${t}`),a=i.forwardRef((e,i)=>{let{asChild:a,...o}=e,c=a?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(c,{...o,ref:i})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function dispatchDiscreteCustomEvent(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},6459:function(e,t,n){"use strict";n.d(t,{W:function(){return useCallbackRef}});var i=n(2265);function useCallbackRef(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}},3763:function(e,t,n){"use strict";n.d(t,{T:function(){return useControllableState}});var i,a=n(2265),o=n(1030),s=(i||(i=n.t(a,2)))[" useInsertionEffect ".trim().toString()]||o.b;function useControllableState({prop:e,defaultProp:t,onChange:n=()=>{},caller:i}){let[o,c,u]=function({defaultProp:e,onChange:t}){let[n,i]=a.useState(e),o=a.useRef(n),c=a.useRef(t);return s(()=>{c.current=t},[t]),a.useEffect(()=>{o.current!==n&&(c.current?.(n),o.current=n)},[n,o]),[n,i,c]}({defaultProp:t,onChange:n}),p=void 0!==e,f=p?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==p){let t=p?"controlled":"uncontrolled";console.warn(`${i} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=p},[p,i])}let v=a.useCallback(t=>{if(p){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else c(t)},[p,e,c,u]);return[f,v]}Symbol("RADIX:SYNC_STATE")},1030:function(e,t,n){"use strict";n.d(t,{b:function(){return a}});var i=n(2265),a=globalThis?.document?i.useLayoutEffect:()=>{}},8647:function(e,t,n){"use strict";n.d(t,{uI:function(){return useDropzone}});var i=n(2265),a=n(4275),o=n(44);let s=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function toFileWithPath(e,t,n){let i=function(e){let{name:t}=e,n=t&&-1!==t.lastIndexOf(".");if(n&&!e.type){let n=t.split(".").pop().toLowerCase(),i=s.get(n);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:a}=e,o="string"==typeof t?t:"string"==typeof a&&a.length>0?a:`./${e.name}`;return"string"!=typeof i.path&&setObjProp(i,"path",o),void 0!==n&&Object.defineProperty(i,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),setObjProp(i,"relativePath",o),i}function setObjProp(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}let c=[".DS_Store","Thumbs.db"];function isObject(e){return"object"==typeof e&&null!==e}function noIgnoredFiles(e){return e.filter(e=>-1===c.indexOf(e.name))}function fromList(e){if(null===e)return[];let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i)}return t}function toFilePromises(e){if("function"!=typeof e.webkitGetAsEntry)return fromDataTransferItem(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?fromDirEntry(t):fromDataTransferItem(e,t)}function fromDataTransferItem(e,t){return(0,o.mG)(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,toFileWithPath(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);let a=toFileWithPath(i,null!==(n=null==t?void 0:t.fullPath)&&void 0!==n?n:void 0);return a})}function fromEntry(e){return(0,o.mG)(this,void 0,void 0,function*(){return e.isDirectory?fromDirEntry(e):function(e){return(0,o.mG)(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(n=>{let i=toFileWithPath(n,e.fullPath);t(i)},e=>{n(e)})})})}(e)})}function fromDirEntry(e){let t=e.createReader();return new Promise((e,n)=>{let i=[];!function readEntries(){t.readEntries(t=>(0,o.mG)(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(fromEntry));i.push(e),readEntries()}else try{let t=yield Promise.all(i);e(t)}catch(e){n(e)}}),e=>{n(e)})}()})}var u=n(8544);function _toConsumableArray(e){return function(e){if(Array.isArray(e))return _arrayLikeToArray(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||_unsupportedIterableToArray(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _slicedToArray(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var o=[],s=!0,c=!1;try{for(a=a.call(e);!(s=(n=a.next()).done)&&(o.push(n.value),!t||o.length!==t);s=!0);}catch(e){c=!0,i=e}finally{try{s||null==a.return||a.return()}finally{if(c)throw i}}return o}}(e,t)||_unsupportedIterableToArray(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}var p="function"==typeof u?u:u.default,getInvalidTypeRejectionErr=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),n=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(n)}},getTooLargeRejectionErr=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},getTooSmallRejectionErr=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},f={code:"too-many-files",message:"Too many files"};function fileAccepted(e,t){var n="application/x-moz-file"===e.type||p(e,t);return[n,n?null:getInvalidTypeRejectionErr(t)]}function fileMatchSize(e,t,n){if(isDefined(e.size)){if(isDefined(t)&&isDefined(n)){if(e.size>n)return[!1,getTooLargeRejectionErr(n)];if(e.size<t)return[!1,getTooSmallRejectionErr(t)]}else if(isDefined(t)&&e.size<t)return[!1,getTooSmallRejectionErr(t)];else if(isDefined(n)&&e.size>n)return[!1,getTooLargeRejectionErr(n)]}return[!0,null]}function isDefined(e){return null!=e}function isPropagationStopped(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function isEvtWithFiles(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function onDocumentDragOver(e){e.preventDefault()}function composeEventHandlers(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,i=Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return t.some(function(t){return!isPropagationStopped(e)&&t&&t.apply(void 0,[e].concat(i)),isPropagationStopped(e)})}}function isMIMEType(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function isExt(e){return/^.*\.[\w]+$/.test(e)}var v=["children"],g=["open"],h=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],y=["refKey","onChange","onClick"];function es_slicedToArray(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var o=[],s=!0,c=!1;try{for(a=a.call(e);!(s=(n=a.next()).done)&&(o.push(n.value),!t||o.length!==t);s=!0);}catch(e){c=!0,i=e}finally{try{s||null==a.return||a.return()}finally{if(c)throw i}}return o}}(e,t)||es_unsupportedIterableToArray(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function es_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return es_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return es_arrayLikeToArray(e,t)}}function es_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function es_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function es_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?es_ownKeys(Object(n),!0).forEach(function(t){es_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):es_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function es_defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _objectWithoutProperties(e,t){if(null==e)return{};var n,i,a=function(e,t){if(null==e)return{};var n,i,a={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)n=o[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var b=(0,i.forwardRef)(function(e,t){var n=e.children,a=useDropzone(_objectWithoutProperties(e,v)),o=a.open,s=_objectWithoutProperties(a,g);return(0,i.useImperativeHandle)(t,function(){return{open:o}},[o]),i.createElement(i.Fragment,null,n(es_objectSpread(es_objectSpread({},s),{},{open:o})))});b.displayName="Dropzone";var w={disabled:!1,getFilesFromEvent:function(e){return(0,o.mG)(this,void 0,void 0,function*(){return isObject(e)&&isObject(e.dataTransfer)?function(e,t){return(0,o.mG)(this,void 0,void 0,function*(){if(e.items){let n=fromList(e.items).filter(e=>"file"===e.kind);if("drop"!==t)return n;let i=yield Promise.all(n.map(toFilePromises));return noIgnoredFiles(function flatten(e){return e.reduce((e,t)=>[...e,...Array.isArray(t)?flatten(t):[t]],[])}(i))}return noIgnoredFiles(fromList(e.files).map(e=>toFileWithPath(e)))})}(e.dataTransfer,e.type):isObject(e)&&isObject(e.target)?fromList(e.target.files).map(e=>toFileWithPath(e)):Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return(0,o.mG)(this,void 0,void 0,function*(){let t=yield Promise.all(e.map(e=>e.getFile()));return t.map(e=>toFileWithPath(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};b.defaultProps=w,b.propTypes={children:a.func,accept:a.objectOf(a.arrayOf(a.string)),multiple:a.bool,preventDropOnDocument:a.bool,noClick:a.bool,noKeyboard:a.bool,noDrag:a.bool,noDragEventsBubbling:a.bool,minSize:a.number,maxSize:a.number,maxFiles:a.number,disabled:a.bool,getFilesFromEvent:a.func,onFileDialogCancel:a.func,onFileDialogOpen:a.func,useFsAccessApi:a.bool,autoFocus:a.bool,onDragEnter:a.func,onDragLeave:a.func,onDragOver:a.func,onDrop:a.func,onDropAccepted:a.func,onDropRejected:a.func,onError:a.func,validator:a.func};var D={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function useDropzone(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=es_objectSpread(es_objectSpread({},w),e),n=t.accept,a=t.disabled,o=t.getFilesFromEvent,s=t.maxSize,c=t.minSize,u=t.multiple,p=t.maxFiles,v=t.onDragEnter,g=t.onDragLeave,b=t.onDragOver,C=t.onDrop,S=t.onDropAccepted,E=t.onDropRejected,j=t.onFileDialogCancel,M=t.onFileDialogOpen,O=t.useFsAccessApi,P=t.autoFocus,T=t.preventDropOnDocument,A=t.noClick,L=t.noKeyboard,N=t.noDrag,I=t.noDragEventsBubbling,W=t.onError,z=t.validator,q=(0,i.useMemo)(function(){return function(e){if(isDefined(e))return Object.entries(e).reduce(function(e,t){var n=_slicedToArray(t,2),i=n[0],a=n[1];return[].concat(_toConsumableArray(e),[i],_toConsumableArray(a))},[]).filter(function(e){return isMIMEType(e)||isExt(e)}).join(",")}(n)},[n]),H=(0,i.useMemo)(function(){return isDefined(n)?[{description:"Files",accept:Object.entries(n).filter(function(e){var t=_slicedToArray(e,2),n=t[0],i=t[1],a=!0;return isMIMEType(n)||(console.warn('Skipped "'.concat(n,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),a=!1),Array.isArray(i)&&i.every(isExt)||(console.warn('Skipped "'.concat(n,'" because an invalid file extension was provided.')),a=!1),a}).reduce(function(e,t){var n=_slicedToArray(t,2),i=n[0],a=n[1];return _objectSpread(_objectSpread({},e),{},_defineProperty({},i,a))},{})}]:n},[n]),B=(0,i.useMemo)(function(){return"function"==typeof M?M:noop},[M]),Y=(0,i.useMemo)(function(){return"function"==typeof j?j:noop},[j]),V=(0,i.useRef)(null),Z=(0,i.useRef)(null),$=es_slicedToArray((0,i.useReducer)(reducer,D),2),G=$[0],X=$[1],Q=G.isFocused,J=G.isFileDialogActive,et=(0,i.useRef)("undefined"!=typeof window&&window.isSecureContext&&O&&"showOpenFilePicker"in window),onWindowFocus=function(){!et.current&&J&&setTimeout(function(){Z.current&&!Z.current.files.length&&(X({type:"closeDialog"}),Y())},300)};(0,i.useEffect)(function(){return window.addEventListener("focus",onWindowFocus,!1),function(){window.removeEventListener("focus",onWindowFocus,!1)}},[Z,J,Y,et]);var en=(0,i.useRef)([]),onDocumentDrop=function(e){V.current&&V.current.contains(e.target)||(e.preventDefault(),en.current=[])};(0,i.useEffect)(function(){return T&&(document.addEventListener("dragover",onDocumentDragOver,!1),document.addEventListener("drop",onDocumentDrop,!1)),function(){T&&(document.removeEventListener("dragover",onDocumentDragOver),document.removeEventListener("drop",onDocumentDrop))}},[V,T]),(0,i.useEffect)(function(){return!a&&P&&V.current&&V.current.focus(),function(){}},[V,P,a]);var ei=(0,i.useCallback)(function(e){W?W(e):console.error(e)},[W]),ea=(0,i.useCallback)(function(e){var t;e.preventDefault(),e.persist(),stopPropagation(e),en.current=[].concat(function(e){if(Array.isArray(e))return es_arrayLikeToArray(e)}(t=en.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||es_unsupportedIterableToArray(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),isEvtWithFiles(e)&&Promise.resolve(o(e)).then(function(t){if(!isPropagationStopped(e)||I){var n,i,a,o,f,g,h,y,b=t.length,w=b>0&&(i=(n={files:t,accept:q,minSize:c,maxSize:s,multiple:u,maxFiles:p,validator:z}).files,a=n.accept,o=n.minSize,f=n.maxSize,g=n.multiple,h=n.maxFiles,y=n.validator,(!!g||!(i.length>1))&&(!g||!(h>=1)||!(i.length>h))&&i.every(function(e){var t=_slicedToArray(fileAccepted(e,a),1)[0],n=_slicedToArray(fileMatchSize(e,o,f),1)[0],i=y?y(e):null;return t&&n&&!i}));X({isDragAccept:w,isDragReject:b>0&&!w,isDragActive:!0,type:"setDraggedFiles"}),v&&v(e)}}).catch(function(e){return ei(e)})},[o,v,ei,I,q,c,s,u,p,z]),er=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),stopPropagation(e);var t=isEvtWithFiles(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&b&&b(e),!1},[b,I]),eo=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),stopPropagation(e);var t=en.current.filter(function(e){return V.current&&V.current.contains(e)}),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),en.current=t,!(t.length>0)&&(X({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),isEvtWithFiles(e)&&g&&g(e))},[V,g,I]),el=(0,i.useCallback)(function(e,t){var n=[],i=[];e.forEach(function(e){var t=es_slicedToArray(fileAccepted(e,q),2),a=t[0],o=t[1],u=es_slicedToArray(fileMatchSize(e,c,s),2),p=u[0],f=u[1],v=z?z(e):null;if(a&&p&&!v)n.push(e);else{var g=[o,f];v&&(g=g.concat(v)),i.push({file:e,errors:g.filter(function(e){return e})})}}),(!u&&n.length>1||u&&p>=1&&n.length>p)&&(n.forEach(function(e){i.push({file:e,errors:[f]})}),n.splice(0)),X({acceptedFiles:n,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),C&&C(n,i,t),i.length>0&&E&&E(i,t),n.length>0&&S&&S(n,t)},[X,u,q,c,s,p,C,S,E,z]),es=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),stopPropagation(e),en.current=[],isEvtWithFiles(e)&&Promise.resolve(o(e)).then(function(t){(!isPropagationStopped(e)||I)&&el(t,e)}).catch(function(e){return ei(e)}),X({type:"reset"})},[o,el,ei,I]),ec=(0,i.useCallback)(function(){if(et.current){X({type:"openDialog"}),B(),window.showOpenFilePicker({multiple:u,types:H}).then(function(e){return o(e)}).then(function(e){el(e,null),X({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(Y(e),X({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(et.current=!1,Z.current?(Z.current.value=null,Z.current.click()):ei(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ei(e)});return}Z.current&&(X({type:"openDialog"}),B(),Z.current.value=null,Z.current.click())},[X,B,Y,O,el,ei,H,u]),ed=(0,i.useCallback)(function(e){V.current&&V.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ec())},[V,ec]),eu=(0,i.useCallback)(function(){X({type:"focus"})},[]),ep=(0,i.useCallback)(function(){X({type:"blur"})},[]),ef=(0,i.useCallback)(function(){A||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ec,0):ec())},[A,ec]),composeHandler=function(e){return a?null:e},composeKeyboardHandler=function(e){return L?null:composeHandler(e)},composeDragHandler=function(e){return N?null:composeHandler(e)},stopPropagation=function(e){I&&e.stopPropagation()},em=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=void 0===t?"ref":t,i=e.role,o=e.onKeyDown,s=e.onFocus,c=e.onBlur,u=e.onClick,p=e.onDragEnter,f=e.onDragOver,v=e.onDragLeave,g=e.onDrop,y=_objectWithoutProperties(e,h);return es_objectSpread(es_objectSpread(es_defineProperty({onKeyDown:composeKeyboardHandler(composeEventHandlers(o,ed)),onFocus:composeKeyboardHandler(composeEventHandlers(s,eu)),onBlur:composeKeyboardHandler(composeEventHandlers(c,ep)),onClick:composeHandler(composeEventHandlers(u,ef)),onDragEnter:composeDragHandler(composeEventHandlers(p,ea)),onDragOver:composeDragHandler(composeEventHandlers(f,er)),onDragLeave:composeDragHandler(composeEventHandlers(v,eo)),onDrop:composeDragHandler(composeEventHandlers(g,es)),role:"string"==typeof i&&""!==i?i:"presentation"},n,V),a||L?{}:{tabIndex:0}),y)}},[V,ed,eu,ep,ef,ea,er,eo,es,L,N,a]),ev=(0,i.useCallback)(function(e){e.stopPropagation()},[]),eg=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=void 0===t?"ref":t,i=e.onChange,a=e.onClick,o=_objectWithoutProperties(e,y);return es_objectSpread(es_objectSpread({},es_defineProperty({accept:q,multiple:u,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:composeHandler(composeEventHandlers(i,es)),onClick:composeHandler(composeEventHandlers(a,ev)),tabIndex:-1},n,Z)),o)}},[Z,n,u,es,a]);return es_objectSpread(es_objectSpread({},G),{},{isFocused:Q&&!a,getRootProps:em,getInputProps:eg,rootRef:V,inputRef:Z,open:composeHandler(ec)})}function reducer(e,t){switch(t.type){case"focus":return es_objectSpread(es_objectSpread({},e),{},{isFocused:!0});case"blur":return es_objectSpread(es_objectSpread({},e),{},{isFocused:!1});case"openDialog":return es_objectSpread(es_objectSpread({},D),{},{isFileDialogActive:!0});case"closeDialog":return es_objectSpread(es_objectSpread({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return es_objectSpread(es_objectSpread({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return es_objectSpread(es_objectSpread({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return es_objectSpread({},D);default:return e}}function noop(){}},1617:function(e,t,n){"use strict";n.d(t,{ZP:function(){return Z}});var i,a=n(2265),o=n(6272),s=Object.prototype.hasOwnProperty;let c=new WeakMap,noop=()=>{},u=noop(),p=Object,isUndefined=e=>e===u,isFunction=e=>"function"==typeof e,mergeObjects=(e,t)=>({...e,...t}),isPromiseLike=e=>isFunction(e.then),f={},v={},g="undefined",h=typeof window!=g,y=typeof document!=g,b=h&&"Deno"in window,hasRequestAnimationFrame=()=>h&&typeof window.requestAnimationFrame!=g,createCacheHelper=(e,t)=>{let n=c.get(e);return[()=>!isUndefined(t)&&e.get(t)||f,i=>{if(!isUndefined(t)){let a=e.get(t);t in v||(v[t]=a),n[5](t,mergeObjects(a,i),a||f)}},n[6],()=>!isUndefined(t)&&t in v?v[t]:!isUndefined(t)&&e.get(t)||f]},w=!0,[D,C]=h&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[noop,noop],S={initFocus:e=>(y&&document.addEventListener("visibilitychange",e),D("focus",e),()=>{y&&document.removeEventListener("visibilitychange",e),C("focus",e)}),initReconnect:e=>{let onOnline=()=>{w=!0,e()},onOffline=()=>{w=!1};return D("online",onOnline),D("offline",onOffline),()=>{C("online",onOnline),C("offline",onOffline)}}},E=!a.useId,j=!h||b,rAF=e=>hasRequestAnimationFrame()?window.requestAnimationFrame(e):setTimeout(e,1),M=j?a.useEffect:a.useLayoutEffect,O="undefined"!=typeof navigator&&navigator.connection,P=!j&&O&&(["slow-2g","2g"].includes(O.effectiveType)||O.saveData),T=new WeakMap,isObjectType=(e,t)=>p.prototype.toString.call(e)===`[object ${t}]`,A=0,stableHash=e=>{let t,n;let i=typeof e,a=isObjectType(e,"Date"),o=isObjectType(e,"RegExp"),s=isObjectType(e,"Object");if(p(e)!==e||a||o)t=a?e.toJSON():"symbol"==i?e.toString():"string"==i?JSON.stringify(e):""+e;else{if(t=T.get(e))return t;if(t=++A+"~",T.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=stableHash(e[n])+",";T.set(e,t)}if(s){t="#";let i=p.keys(e).sort();for(;!isUndefined(n=i.pop());)isUndefined(e[n])||(t+=n+":"+stableHash(e[n])+",");T.set(e,t)}}return t},config_context_client_v7VOFo66_serialize=e=>{if(isFunction(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?stableHash(e):"",t]},L=0,getTimestamp=()=>++L;async function internalMutate(...e){let[t,n,i,a]=e,o=mergeObjects({populateCache:!0,throwOnError:!0},"boolean"==typeof a?{revalidate:a}:a||{}),s=o.populateCache,p=o.rollbackOnError,f=o.optimisticData,rollbackOnError=e=>"function"==typeof p?p(e):!1!==p,v=o.throwOnError;if(isFunction(n)){let e=[],i=t.keys();for(let a of i)!/^\$(inf|sub)\$/.test(a)&&n(t.get(a)._k)&&e.push(a);return Promise.all(e.map(mutateByKey))}return mutateByKey(n);async function mutateByKey(n){let a;let[p]=config_context_client_v7VOFo66_serialize(n);if(!p)return;let[g,h]=createCacheHelper(t,p),[y,b,w,D]=c.get(t),startRevalidate=()=>{let e=y[p],t=isFunction(o.revalidate)?o.revalidate(g().data,n):!1!==o.revalidate;return t&&(delete w[p],delete D[p],e&&e[0])?e[0](2).then(()=>g().data):g().data};if(e.length<3)return startRevalidate();let C=i,S=getTimestamp();b[p]=[S,0];let E=!isUndefined(f),j=g(),M=j.data,O=j._c,P=isUndefined(O)?M:O;if(E&&h({data:f=isFunction(f)?f(P,M):f,_c:P}),isFunction(C))try{C=C(P)}catch(e){a=e}if(C&&isPromiseLike(C)){if(C=await C.catch(e=>{a=e}),S!==b[p][0]){if(a)throw a;return C}a&&E&&rollbackOnError(a)&&(s=!0,h({data:P,_c:u}))}if(s&&!a){if(isFunction(s)){let e=s(C,P);h({data:e,error:u,_c:u})}else h({data:C,error:u,_c:u})}if(b[p][1]=getTimestamp(),Promise.resolve(startRevalidate()).then(()=>{h({_c:u})}),a){if(v)throw a;return}return C}}let revalidateAllKeys=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},initCache=(e,t)=>{if(!c.has(e)){let n=mergeObjects(S,t),i=Object.create(null),a=internalMutate.bind(u,e),o=noop,s=Object.create(null),subscribe=(e,t)=>{let n=s[e]||[];return s[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},setter=(t,n,i)=>{e.set(t,n);let a=s[t];if(a)for(let e of a)e(n,i)},initProvider=()=>{if(!c.has(e)&&(c.set(e,[i,Object.create(null),Object.create(null),Object.create(null),a,setter,subscribe]),!j)){let t=n.initFocus(setTimeout.bind(u,revalidateAllKeys.bind(u,i,0))),a=n.initReconnect(setTimeout.bind(u,revalidateAllKeys.bind(u,i,1)));o=()=>{t&&t(),a&&a(),c.delete(e)}}};return initProvider(),[e,a,initProvider,o]}return[e,c.get(e)[4]]},[N,I]=initCache(new Map),W=mergeObjects({onLoadingSlow:noop,onSuccess:noop,onError:noop,onErrorRetry:(e,t,n,i,a)=>{let o=n.errorRetryCount,s=a.retryCount,c=~~((Math.random()+.5)*(1<<(s<8?s:8)))*n.errorRetryInterval;(isUndefined(o)||!(s>o))&&setTimeout(i,c,a)},onDiscarded:noop,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:P?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:P?5e3:3e3,compare:function dequal(e,t){var n,i;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((i=e.length)===t.length)for(;i--&&dequal(e[i],t[i]););return -1===i}if(!n||"object"==typeof e){for(n in i=0,e)if(s.call(e,n)&&++i&&!s.call(t,n)||!(n in t)||!dequal(e[n],t[n]))return!1;return Object.keys(t).length===i}}return e!=e&&t!=t},isPaused:()=>!1,cache:N,mutate:I,fallback:{}},{isOnline:()=>w,isVisible:()=>{let e=y&&document.visibilityState;return isUndefined(e)||"hidden"!==e}}),mergeConfigs=(e,t)=>{let n=mergeObjects(e,t);if(t){let{use:i,fallback:a}=e,{use:o,fallback:s}=t;i&&o&&(n.use=i.concat(o)),a&&s&&(n.fallback=mergeObjects(a,s))}return n},z=(0,a.createContext)({}),q=h&&window.__SWR_DEVTOOLS_USE__,H=q?window.__SWR_DEVTOOLS_USE__:[],normalize=e=>isFunction(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],useSWRConfig=()=>mergeObjects(W,(0,a.useContext)(z)),B=H.concat(e=>(t,n,i)=>{let a=n&&((...e)=>{let[i]=config_context_client_v7VOFo66_serialize(t),[,,,a]=c.get(N);if(i.startsWith("$inf$"))return n(...e);let o=a[i];return isUndefined(o)?n(...e):(delete a[i],o)});return e(t,a,i)}),subscribeCallback=(e,t,n)=>{let i=t[e]||(t[e]=[]);return i.push(n),()=>{let e=i.indexOf(n);e>=0&&(i[e]=i[i.length-1],i.pop())}};q&&(window.__SWR_DEVTOOLS_REACT__=a);let index_noop=()=>{};index_noop(),new WeakMap;let Y=a.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),V={dedupe:!0};p.defineProperty(e=>{let{value:t}=e,n=(0,a.useContext)(z),i=isFunction(t),o=(0,a.useMemo)(()=>i?t(n):t,[i,n,t]),s=(0,a.useMemo)(()=>i?o:mergeConfigs(n,o),[i,n,o]),c=o&&o.provider,p=(0,a.useRef)(u);c&&!p.current&&(p.current=initCache(c(s.cache||N),o));let f=p.current;return f&&(s.cache=f[0],s.mutate=f[1]),M(()=>{if(f)return f[2]&&f[2](),f[3]},[]),(0,a.createElement)(z.Provider,mergeObjects(e,{value:s}))},"defaultValue",{value:W});let Z=(i=(e,t,n)=>{let{cache:i,compare:s,suspense:p,fallbackData:f,revalidateOnMount:v,revalidateIfStale:g,refreshInterval:h,refreshWhenHidden:y,refreshWhenOffline:b,keepPreviousData:w}=n,[D,C,S,O]=c.get(i),[P,T]=config_context_client_v7VOFo66_serialize(e),A=(0,a.useRef)(!1),L=(0,a.useRef)(!1),N=(0,a.useRef)(P),I=(0,a.useRef)(t),W=(0,a.useRef)(n),getConfig=()=>W.current,isActive=()=>getConfig().isVisible()&&getConfig().isOnline(),[z,q,H,B]=createCacheHelper(i,P),Z=(0,a.useRef)({}).current,$=isUndefined(f)?isUndefined(n.fallback)?u:n.fallback[P]:f,isEqual=(e,t)=>{for(let n in Z)if("data"===n){if(!s(e[n],t[n])&&(!isUndefined(e[n])||!s(er,t[n])))return!1}else if(t[n]!==e[n])return!1;return!0},G=(0,a.useMemo)(()=>{let e=!!P&&!!t&&(isUndefined(v)?!getConfig().isPaused()&&!p&&!1!==g:v),getSelectedCache=t=>{let n=mergeObjects(t);return(delete n._k,e)?{isValidating:!0,isLoading:!0,...n}:n},n=z(),i=B(),a=getSelectedCache(n),o=n===i?a:getSelectedCache(i),s=a;return[()=>{let e=getSelectedCache(z()),t=isEqual(e,s);return t?(s.data=e.data,s.isLoading=e.isLoading,s.isValidating=e.isValidating,s.error=e.error,s):(s=e,e)},()=>o]},[i,P]),X=(0,o.useSyncExternalStore)((0,a.useCallback)(e=>H(P,(t,n)=>{isEqual(n,t)||e()}),[i,P]),G[0],G[1]),Q=!A.current,J=D[P]&&D[P].length>0,et=X.data,en=isUndefined(et)?$&&isPromiseLike($)?Y($):$:et,ei=X.error,ea=(0,a.useRef)(en),er=w?isUndefined(et)?isUndefined(ea.current)?en:ea.current:et:en,eo=(!J||!!isUndefined(ei))&&(Q&&!isUndefined(v)?v:!getConfig().isPaused()&&(p?!isUndefined(en)&&g:isUndefined(en)||g)),el=!!(P&&t&&Q&&eo),es=isUndefined(X.isValidating)?el:X.isValidating,ec=isUndefined(X.isLoading)?el:X.isLoading,ed=(0,a.useCallback)(async e=>{let t,i;let a=I.current;if(!P||!a||L.current||getConfig().isPaused())return!1;let o=!0,c=e||{},p=!S[P]||!c.dedupe,callbackSafeguard=()=>E?!L.current&&P===N.current&&A.current:P===N.current,f={isValidating:!1,isLoading:!1},finishRequestAndUpdateState=()=>{q(f)},cleanupState=()=>{let e=S[P];e&&e[1]===i&&delete S[P]},v={isValidating:!0};isUndefined(z().data)&&(v.isLoading=!0);try{if(p&&(q(v),n.loadingTimeout&&isUndefined(z().data)&&setTimeout(()=>{o&&callbackSafeguard()&&getConfig().onLoadingSlow(P,n)},n.loadingTimeout),S[P]=[a(T),getTimestamp()]),[t,i]=S[P],t=await t,p&&setTimeout(cleanupState,n.dedupingInterval),!S[P]||S[P][1]!==i)return p&&callbackSafeguard()&&getConfig().onDiscarded(P),!1;f.error=u;let e=C[P];if(!isUndefined(e)&&(i<=e[0]||i<=e[1]||0===e[1]))return finishRequestAndUpdateState(),p&&callbackSafeguard()&&getConfig().onDiscarded(P),!1;let c=z().data;f.data=s(c,t)?c:t,p&&callbackSafeguard()&&getConfig().onSuccess(t,P,n)}catch(n){cleanupState();let e=getConfig(),{shouldRetryOnError:t}=e;!e.isPaused()&&(f.error=n,p&&callbackSafeguard()&&(e.onError(n,P,e),(!0===t||isFunction(t)&&t(n))&&(!getConfig().revalidateOnFocus||!getConfig().revalidateOnReconnect||isActive())&&e.onErrorRetry(n,P,e,e=>{let t=D[P];t&&t[0]&&t[0](3,e)},{retryCount:(c.retryCount||0)+1,dedupe:!0})))}return o=!1,finishRequestAndUpdateState(),!0},[P,i]),eu=(0,a.useCallback)((...e)=>internalMutate(i,N.current,...e),[]);if(M(()=>{I.current=t,W.current=n,isUndefined(et)||(ea.current=et)}),M(()=>{if(!P)return;let e=ed.bind(u,V),t=0;if(getConfig().revalidateOnFocus){let e=Date.now();t=e+getConfig().focusThrottleInterval}let n=subscribeCallback(P,D,(n,i={})=>{if(0==n){let n=Date.now();getConfig().revalidateOnFocus&&n>t&&isActive()&&(t=n+getConfig().focusThrottleInterval,e())}else if(1==n)getConfig().revalidateOnReconnect&&isActive()&&e();else if(2==n)return ed();else if(3==n)return ed(i)});return L.current=!1,N.current=P,A.current=!0,q({_k:T}),eo&&(isUndefined(en)||j?e():rAF(e)),()=>{L.current=!0,n()}},[P]),M(()=>{let e;function next(){let t=isFunction(h)?h(z().data):h;t&&-1!==e&&(e=setTimeout(execute,t))}function execute(){!z().error&&(y||getConfig().isVisible())&&(b||getConfig().isOnline())?ed(V).then(next):next()}return next(),()=>{e&&(clearTimeout(e),e=-1)}},[h,y,b,P]),(0,a.useDebugValue)(er),p&&isUndefined(en)&&P){if(!E&&j)throw Error("Fallback data is required when using Suspense in SSR.");I.current=t,W.current=n,L.current=!1;let e=O[P];if(!isUndefined(e)){let t=eu(e);Y(t)}if(isUndefined(ei)){let e=ed(V);isUndefined(er)||(e.status="fulfilled",e.value=!0),Y(e)}else throw ei}return{mutate:eu,get data(){return Z.data=!0,er},get error(){return Z.error=!0,ei},get isValidating(){return Z.isValidating=!0,es},get isLoading(){return Z.isLoading=!0,ec}}},function(...e){let t=useSWRConfig(),[n,a,o]=normalize(e),s=mergeConfigs(t,o),c=i,{use:u}=s,p=(u||[]).concat(B);for(let e=p.length;e--;)c=p[e](c);return c(n,a||s.fetcher||null,s)})},44:function(e,t,n){"use strict";n.d(t,{_T:function(){return __rest},ev:function(){return __spreadArray},mG:function(){return __awaiter},pi:function(){return __assign}});var __assign=function(){return(__assign=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function __rest(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>t.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(n[i[a]]=e[i[a]]);return n}function __awaiter(e,t,n,i){return new(n||(n=Promise))(function(a,o){function fulfilled(e){try{step(i.next(e))}catch(e){o(e)}}function rejected(e){try{step(i.throw(e))}catch(e){o(e)}}function step(e){var t;e.done?a(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(fulfilled,rejected)}step((i=i.apply(e,t||[])).next())})}function __spreadArray(e,t,n){if(n||2==arguments.length)for(var i,a=0,o=t.length;a<o;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError}}]);