"use strict";exports.id=7669,exports.ids=[7669],exports.modules={15455:(e,t,a)=>{a.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var s=a(60080),r=a(30475),i=a(20161),o=a(9789),l=a(34635),n=a(9885),d=a(92170),c=a(34751),x=a(1712);let __WEBPACK_DEFAULT_EXPORT__=({onFileUploaded:e,isPdf:t=!1,disable:a=!1,errorMessage:m,onRemoveFile:u,fileName:p})=>{let[f,h]=(0,n.useState)(""),[v,j]=(0,n.useState)(null),[g,N]=(0,n.useState)(),b=(0,n.useMemo)(()=>t?["application/pdf"]:["image/png","image/jpeg","application/pdf"],[t]),E=(0,n.useCallback)((t,a)=>{if(j(null),!t||0===t.length)return;let s=t[0];if(!b.includes(s.type)){c.Am.error("Tipo de arquivo n\xe3o suportado."),h("");return}e([s]);let r=URL.createObjectURL(s);h(r),N(s.name)},[e,b]),{getRootProps:w,getInputProps:A,isDragActive:C,isDragReject:I}=(0,d.uI)({onDrop:E,maxFiles:1,multiple:!1,onDragEnter:e=>j(null),onDragLeave:e=>j(null),onError:e=>{console.log(e),"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&c.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:t?{"application/pdf":[".pdf"]}:{"image/png":[".png"],"image/jpeg":[".jpeg",".jpg"],"application/pdf":[".pdf"]},onFileDialogCancel:()=>{h("")},disabled:a}),T=(0,n.useMemo)(()=>C&&(I||!!v),[C,I,v]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{...w(),className:(0,x.cn)("text-white input relative group cursor-pointer rounded-sm bg-[#D9D9D9] px-16 py-10 w-full flex items-center justify-center border-2",{"border-green-500":C&&!T,"border-red-500":T,"border-2 border-red-500":m}),children:[s.jsx("input",{...A(),accept:".png,.jpg,.jpeg,.pdf"}),a?s.jsx(r.Z,{width:40,color:"#0da34e"}):f?s.jsx(i.Z,{width:40,color:"#0da34e"}):s.jsx(o.Z,{width:40,color:"#515151"}),m&&s.jsx("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[120%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:m})]}),f&&(0,s.jsxs)("div",{className:"z-50 flex justify-between items-center mt-1 px-2 py-1 text-white text-xs bg-[#1a1a1a] rounded",children:[s.jsx("span",{className:"truncate max-w-[150px]",children:g}),s.jsx("button",{type:"button",onClick:()=>{N(""),h(""),u?.(),e(void 0)},className:"ml-2 flex-shrink-0",children:s.jsx(l.Z,{className:"w-5 h-5 text-red-500 hover:text-red-700"})})]})]})}},74352:(e,t,a)=>{a.d(t,{Z:()=>FilterModal});var s=a(60080),r=a(47541),i=a(88401),o=a(34129),l=a(62433),n=a(30170),d=a(69957),c=a(9885),x=a(64731),m=a.n(x),u=a(517),p=a(1712),f=a(51910),h=a(11922),v=a(28070);function Calendar({className:e,classNames:t,showOutsideDays:a=!0,...r}){return s.jsx(v._W,{showOutsideDays:a,className:(0,p.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,p.cn)((0,d.d)({variant:"secondary"}),"h-7 w-7 p-0"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,p.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===r.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,p.cn)((0,d.d)({variant:"ghost"}),"h-8 w-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start",day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({className:e,...t})=>s.jsx(f.Z,{className:(0,p.cn)("h-4 w-4",e),...t}),IconRight:({className:e,...t})=>s.jsx(h.Z,{className:(0,p.cn)("h-4 w-4",e),...t})},...r})}Calendar.displayName="Calendar";var j=a(12820);let g=j.fC,N=j.xz;j.ee;let b=c.forwardRef(({className:e,align:t="center",sideOffset:a=4,...r},i)=>s.jsx(j.h_,{children:s.jsx(j.VY,{ref:i,align:t,sideOffset:a,className:(0,p.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",e),...r})}));function DatePicker({setValue:e,value:t}){let[a,r]=c.useState();return(0,s.jsxs)(g,{children:[s.jsx(N,{asChild:!0,children:(0,s.jsxs)(d.z,{variant:"outline",size:"lg",className:(0,p.cn)("font-normal w-full"),children:[s.jsx(u.Z,{}),s.jsx("span",{className:"text-xs",children:a?m()(a).format("DD/MM/YYYY"):"DD/MM/YYYY"})]})}),s.jsx(j.h_,{children:s.jsx(b,{className:"w-auto p-0 z-[1000000]",children:s.jsx(Calendar,{mode:"single",selected:a,className:"z-[10000]",onSelect:t=>{e(m()(t).format("YYYY-MM-DD")),r(t)},initialFocus:!0})})})]})}b.displayName=j.VY.displayName;var E=a(80223),w=a(11330),A=a(13649);let C=[{label:"Todos",value:"all"},{label:"Contratos SCP",value:"SCP"},{label:"Contratos M\xfatuo",value:"MUTUO"}];function FilterModal({activeModal:e,setActiveModal:t,filterData:a,setFilterData:c,handleSearch:x,inputPlaceholder:m="Pesquisar",children:u,hidenButton:p,isNew:f=!1}){return f?(0,s.jsxs)(A.h_,{open:e,onOpenChange:t,children:[(0,s.jsxs)(A.$F,{onClick:()=>t(!e),className:"flex w-24 text-sm justify-around p-2 rounded-lg bg-[#3A3A3A] cursor-pointer",children:[s.jsx(r.Z,{width:15,color:"#fff"}),s.jsx("p",{children:"Filtros"})]}),(0,s.jsxs)(A.AW,{className:"mr-1 max-w-[370px] px-3",children:[s.jsx(A.Ju,{className:"text-md",children:"Filtros"}),s.jsx(A.VD,{}),(0,s.jsxs)("div",{className:"flex flex-col justify-between mt-2",children:[u,!p&&s.jsx("div",{className:"m-auto mt-5 w-full mb-2",children:s.jsx(d.z,{onClick:()=>{x&&x(),t(!1)},className:"w-full",children:"Aplicar"})})]})]})]}):(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center items-end relative gap-5 justify-end",children:[a?.input!==void 0&&s.jsx("div",{className:"md:w-80 mb-2 md:mb-0",children:s.jsx(n.Z,{handleSearch:()=>{x&&x()},placeholder:m,setValue:e=>{c&&c({...a,input:e})},value:a.input})}),(0,s.jsxs)("div",{className:"flex w-24 md:items-center p-2 rounded-lg bg-[#3A3A3A] cursor-pointer",onClick:()=>t(!e),children:[s.jsx(r.Z,{width:15,color:"#fff"}),s.jsx("p",{className:"px-2 text-sm",children:"Filtros"}),e?s.jsx(i.Z,{width:15,color:"#fff"}):s.jsx(o.Z,{width:15,color:"#fff"})]}),e&&(0,s.jsxs)("div",{className:"absolute md:w-[300px]  bg-[#3A3A3A] p-5 top-10 rounded-tl-lg rounded-b-lg z-10",children:[(0,s.jsxs)("div",{className:"flex w-full justify-between items-center",children:[s.jsx("p",{className:"text-base",children:"Filtros"}),s.jsx("div",{className:"cursor-pointer",onClick:()=>t(!1),children:s.jsx(l.Z,{width:20})})]}),u?s.jsx("div",{className:"mt-5",children:u}):a&&c?(0,s.jsxs)("div",{className:"flex flex-col justify-between mt-2",children:[(0,s.jsxs)("div",{className:"mb-4 flex flex-row justify-between gap-2",children:[void 0!==a.startData&&(0,s.jsxs)("div",{className:"mb-2 md:mb-0 w-[48%]",children:[s.jsx("p",{className:"text-xs",children:"In\xedcio"}),s.jsx(DatePicker,{value:a.startData,setValue:e=>{c({...a,startData:e})}})]}),void 0!==a.endData&&(0,s.jsxs)("div",{className:"mb-2 md:mb-0 w-[48%]",children:[s.jsx("p",{className:"text-xs",children:"Fim"}),s.jsx(DatePicker,{value:a.endData,setValue:e=>{c({...a,endData:e})}})]})]}),void 0!==a.type&&(0,s.jsxs)("div",{className:"w-full",children:[s.jsx("p",{className:"text-xs",children:"Tipo de contrato"}),s.jsx(E.Z,{value:a.type,onChange:e=>{c({...a,type:e.target.value})},children:C.map((e,t)=>s.jsx("option",{value:e.value,children:e.label},t))})]}),void 0!==a.filterOptionSelected&&(0,s.jsxs)("div",{className:"w-full mt-4",children:[s.jsx("p",{className:"text-xs",children:"Status do contrato"}),s.jsx(E.Z,{value:a.filterOptionSelected,onChange:e=>{console.log(e.target.value),c({...a,filterOptionSelected:e.target.value})},children:w.Z.map((e,t)=>s.jsx("option",{value:e.value,children:e.label},t))})]})]}):void 0,!p&&s.jsx("div",{className:"m-auto mt-5",children:s.jsx(d.z,{onClick:()=>{x&&x(),t(!1)},className:"w-full",children:"Aplicar"})})]})]})}},97669:(e,t,a)=>{a.d(t,{Z:()=>Header});var s=a(60080),r=a(9885),i=a(33794),o=a(95050),l=a(57048),n=a(46140),d=a(78750),c=a(57114),x=a(34751),m=a(47956);function formatUserType(e){return({retention:"Reten\xe7\xe3o",superadmin:"Super Administrador",advisor:"Assessor",broker:"Broker",admin:"Gestor de carteira",investor:"Investidor",financial:"Financeiro"})[e]||""}var u=a(45682),p=a(74644),f=a(85814),h=a(74352),v=a(93640),j=a(15455),g=a(90682),N=a(56206);function Notifications({open:e,notifications:t,filter:a,setFilter:i,handleNotifications:l}){let n=(0,g.e)(),{setNotificationModal:d}=(0,r.useContext)(u.Z),[c,x]=(0,r.useState)(),[m,b]=(0,r.useState)(!1),[E,w]=(0,r.useState)(!1),[A,C]=(0,r.useState)(!1),[I,T]=(0,r.useState)(null),Notification=({notification:e})=>(0,s.jsxs)("div",{className:"w-full",children:[s.jsx("div",{className:"w-[95%] flex justify-end mt-2 mb-[-5px] ml-[-2.5px] cursor-pointer",onClick:()=>x(void 0),children:s.jsx(N.Z,{className:"w-5 h-5",onClick:()=>handleDeleteNotification(e.id)})}),(0,s.jsxs)("div",{className:"mt-2 p-5 bg-[#282828] rounded-lg cursor-pointer flex justify-between items-center",onClick:()=>openNotification(e.id,e),children:[(0,s.jsxs)("div",{className:"w-[95%]",children:[(0,s.jsxs)("div",{className:"w-[50%] flex items-start flex-col",children:[s.jsx("p",{className:"text-base font-bold flex",children:e.title}),s.jsx("div",{className:"w-full bg-[#FF9900] h-[1px] my-2"})]}),s.jsx("p",{className:"text-xs w-[80%] font-extralight text-[#afafaf]",children:e.description}),(0,s.jsxs)("div",{className:"flex justify-start flex-wrap w-[90%] gap-2 mt-1 items-center",children:[e.brokerName&&(0,s.jsxs)("p",{className:"text-xs",children:["Broker: ",e.brokerName]}),s.jsx("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),(0,s.jsxs)("p",{className:"text-xs",children:["Data: ",e.date]}),s.jsx("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),(0,s.jsxs)("p",{className:"text-xs",children:["Hor\xe1rio: ",e.hour]})]})]}),s.jsx("div",{className:"w-[5%]",children:!1===e.viewed&&s.jsx("div",{className:"w-[10px] h-[10px] bg-[#FF9900] rounded-full"})})]})]}),openNotification=(e,t)=>{f.Z.put("/notifications/mark-viewed",{},{params:{notificationId:e},headers:{roleId:n.roleId}}).finally(()=>{x(t)})},handleCloseNotification=()=>{x(void 0),l()},handleClose=()=>{c?x(void 0):(i(""),C(!1),d(!1))},handleDeleteNotification=e=>{window.confirm("Tem certeza que deseja deletar esta notifica\xe7\xe3o?")&&(b(!0),f.Z.delete(`/notifications/${e}`,{headers:{roleId:n.roleId}}).finally(()=>{l(),b(!1)}))};return(0,r.useEffect)(()=>{void 0!==t&&A&&setTimeout(()=>{C(!1)},300)},[t,A]),(0,r.useEffect)(()=>{let handleClickOutside=t=>{let a=t.target;e&&!a.closest(".notifications-modal")&&handleClose()};return e&&document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[e,c]),(0,r.useEffect)(()=>()=>{I&&clearTimeout(I)},[I]),(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:`${e?"fixed opacity-50":"fixed opacity-0 pointer-events-none"} inset-0 bg-black z-40 transition-opacity duration-300 ease-in-out`,onClick:handleClose}),(0,s.jsxs)("div",{className:`${e?"fixed opacity-100 translate-x-0":"fixed opacity-0 translate-x-full pointer-events-none"} w-full md:w-5/12 2xl:w-4/12 h-full bg-[#1C1C1C] z-50 right-0 top-0 text-white p-5 overflow-auto border-t border-l border-[#FF9900] notifications-modal transition-all duration-300 ease-in-out`,children:[s.jsx("div",{className:"flex w-full justify-between items-center gap-4 pb-5",children:(0,s.jsxs)("div",{className:"flex flex-1 justify-between items-center",children:[(0,s.jsxs)("div",{children:[s.jsx(o.Z,{color:"#FF9900 ",width:40}),s.jsx("h1",{className:"font-bold text-2xl mt-1",children:c?.id?"Notifica\xe7\xe3o":"Notifica\xe7\xf5es"})]}),!c&&s.jsx(h.Z,{activeModal:E,handleSearch:()=>{C(!0),I&&clearTimeout(I);let e=setTimeout(()=>{l(a),w(!1)},300);T(e)},setActiveModal:w,hidenButton:!0,children:s.jsx(v.Z,{options:[{label:"Todos",value:""},{label:"Tentativa de Duplicidade",value:"DUPLICATED_DOCUMENT"},{label:"Contratos Gerados",value:"NEW_CONTRACT"},{label:"Aditivo Criado",value:"INCLUDE_ADDITIVE"}],selected:a,setSelected:e=>{C(!0),i(e),I&&clearTimeout(I);let t=setTimeout(()=>{l(e),w(!1)},300);T(t)}})})]})}),c?(0,s.jsxs)("div",{className:"mt-2 w-full",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center gap-4 mb-2",children:[(0,s.jsxs)("div",{className:"flex flex-col w-[80%]",children:[s.jsx("p",{className:"text-xl font-bold",children:c.title}),s.jsx("div",{className:"w-[60%] bg-[#FF9900] h-[1px] my-2"})]}),s.jsx("div",{className:"flex justify-end w-[5%] cursor-pointer",children:s.jsx(N.Z,{className:"w-5 h-5 text-[#FF9900]",onClick:()=>handleCloseNotification()})})]}),s.jsx("p",{className:"text-sm w-[60%] mb-2 font-light text-[#afafaf]",children:c.description}),(0,s.jsxs)("p",{className:"text-xs mt-4 text-[#afafaf]",children:["Broker Respons\xe1vel: ",c.brokerName,s.jsx("div",{className:"w-[55%] bg-[#FF9900] h-[1px] my-2"})]}),(0,s.jsxs)("div",{className:"flex flex-col justify-between flex-wrap w-[50%] gap-2 mt-1",children:[(0,s.jsxs)("p",{className:"text-xs font-extralight text-[#afafaf]",children:["Data: ",c.date]}),s.jsx("div",{className:"w-[40%] bg-[#FF9900] h-[1px]"}),(0,s.jsxs)("p",{className:"text-xs font-extralight text-[#afafaf]",children:["Hor\xe1rio: ",c.hour]})]}),("NEW_CONTRACT"===c.type||"INCLUDE_ADDITIVE"===c.type)&&(0,s.jsxs)("div",{className:"flex flex-col mt-8",children:[s.jsx("p",{className:"text-base font-medium",children:"Anexos"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 mt-3",children:[c?.contractPdf&&(0,s.jsxs)("div",{className:"w-56",children:[s.jsx("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Contrato Principal"}),s.jsx("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),s.jsx("div",{onClick:()=>{window.open(c.contractPdf,"_blank")},children:s.jsx(j.Z,{disable:!0,onFileUploaded:()=>{}})})]}),c?.proofPayment&&(0,s.jsxs)("div",{className:"w-56",children:[s.jsx("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Comprovante de Pagamento"}),s.jsx("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),s.jsx("div",{onClick:()=>{window.open(c.proofPayment,"_blank")},children:s.jsx(j.Z,{disable:!0,onFileUploaded:()=>{}})})]}),"INCLUDE_ADDITIVE"===c.type&&(0,s.jsxs)("div",{className:"flex gap-4",children:[c?.addendumPdf&&(0,s.jsxs)("div",{className:"w-56",children:[s.jsx("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Contrato de Aditivo"}),s.jsx("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),s.jsx("div",{onClick:()=>{window.open(c?.addendumPdf,"_blank")},children:s.jsx(j.Z,{disable:!0,onFileUploaded:()=>{}})})]}),c?.proofPaymentAddendum&&(0,s.jsxs)("div",{className:"w-56",children:[s.jsx("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Comprovante de Pagamento Aditivo"}),s.jsx("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),s.jsx("div",{onClick:()=>{window.open(c?.proofPaymentAddendum,"_blank")},children:s.jsx(j.Z,{disable:!0,onFileUploaded:()=>{}})})]})]})]})]})]}):s.jsx("div",{children:!0===m||A?s.jsx("div",{children:s.jsx(()=>s.jsx("div",{children:[1,2,3].map(e=>(0,s.jsxs)("div",{className:"mt-2",children:[s.jsx("div",{className:"w-[95%] flex justify-end mt-2 mb-[-5px] ml-[-2.5px]",children:s.jsx(p.j,{height:"20px",width:"20px"})}),(0,s.jsxs)("div",{className:"mt-2 p-5 bg-[#282828] rounded-lg flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"w-[95%]",children:[(0,s.jsxs)("div",{className:"w-[50%] flex items-start flex-col",children:[s.jsx(p.j,{height:"20px",width:"60%"}),s.jsx("div",{className:"w-full bg-[#FF9900] h-[1px] my-2"})]}),s.jsx(p.j,{height:"16px",width:"80%",className:"my-2"}),(0,s.jsxs)("div",{className:"flex justify-start flex-wrap w-[90%] gap-2 mt-1 items-center",children:[s.jsx(p.j,{height:"16px",width:"30%"}),s.jsx("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),s.jsx(p.j,{height:"16px",width:"25%"}),s.jsx("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),s.jsx(p.j,{height:"16px",width:"25%"})]})]}),s.jsx("div",{className:"w-[5%]",children:s.jsx(p.j,{height:"10px",width:"10px",className:"rounded-full"})})]})]},e))}),{})}):s.jsx("div",{children:t?.map(e=>s.jsx(Notification,{notification:e},e.id))})})]})]})}var b=a(5299),E=a(15380);function Header(){let e=(0,g.e)(),[t,a]=(0,r.useState)(!1),[p,h]=(0,r.useState)(""),v=(0,c.usePathname)(),j=(0,c.useRouter)(),{push:N}=(0,c.useRouter)(),{notificationModal:w,setNotificationModal:A}=(0,r.useContext)(u.Z),getNotifications=async()=>{let t=await f.Z.get("/notifications",{params:{ownerRoleRelationId:"superadmin"!==e.name?e.roleId:void 0,type:""===p?void 0:p}});if(!t.data)throw Error("Erro");return t.data},{data:C,error:I,isLoading:T,mutate:_}=(0,b.ZP)("superadmin"===e.name?`/api/notifications?filter=${p}`:null,"superadmin"===e.name?getNotifications:null,{refreshInterval:1e4}),handleNotificationsUpdate=async t=>{"superadmin"===e.name&&(void 0!==t&&h(t),await _())},D=(0,g.P)(),y=[{name:"Sair",href:"/",action:logout}];function logout(){sessionStorage.clear(),localStorage.clear(),N("/")}return s.jsx(i.p,{as:"header",className:"bg-header",children:({open:r})=>(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between w-full relative",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("img",{className:"h-6 w-auto",src:"/logo.svg",alt:"Your Company"})}),(0,s.jsxs)("div",{onClick:()=>{D.roles.length>1&&a(!t)},className:"cursor-pointer text-white relative",children:[(0,s.jsxs)("p",{className:"select-none text-left",children:["Perfil selecionado:"," ",s.jsx("b",{children:formatUserType(e.name)})]}),t&&(0,s.jsxs)("div",{className:"z-50 absolute right-0 top-6 bg-zinc-900 w-52 rounded-s-lg rounded-br-lg p-2 text-white shadow-lg border border-[#FF9900]",children:[s.jsx("p",{className:"text-sm mb-2 select-none",children:"Selecione o perfil:"}),D.roles.map(t=>{if(t.name!==e.name)return s.jsx("p",{className:"cursor-pointer py-1 bg-orange-linear rounded-sm text-center select-none my-1",onClick:()=>{x.Am.info(`Alterando para o perfil: ${formatUserType(t.name)}`);let e=(0,g.P)();(0,E.Tl)(e),(0,E.jE)(t),(0,E.zO)(),sessionStorage.setItem("role_changed","true"),window.location.reload()},children:formatUserType(t.name)},t.roleId)})]})]}),s.jsx("div",{children:"superadmin"===e.name&&(0,s.jsxs)("div",{className:"relative",onClick:()=>A(!0),children:[C&&C.filter(e=>!1===e.viewed).length>0&&s.jsx("p",{className:"absolute select-none top-0 right-[-10px] translate-y-[-8px] text-[10px] bg-black text-white w-[15px] h-[15px] text-center rounded-full",children:C.filter(e=>!1===e.viewed).length}),s.jsx(o.Z,{className:"cursor-pointer",width:22,color:"#fff"})]})})]}),s.jsx(Notifications,{notifications:C,open:w,filter:p,setFilter:h,handleNotifications:handleNotificationsUpdate}),s.jsx("div",{className:"-mr-2 flex md:hidden",children:(0,s.jsxs)(i.p.Button,{className:"relative inline-flex items-center justify-center rounded-md  p-2 text-white  hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800",children:[s.jsx("span",{className:"absolute -inset-0.5"}),s.jsx("span",{className:"sr-only",children:"Open main menu"}),r?s.jsx(l.Z,{className:"block h-6 w-6","aria-hidden":"true"}):s.jsx(n.Z,{className:"block h-6 w-6","aria-hidden":"true"})]})})]})}),(0,s.jsxs)(i.p.Panel,{className:"md:hidden",children:[s.jsx("div",{className:"space-y-1 px-2 pb-3 pt-2 sm:px-3",children:m.L.map(t=>{if(t.role.includes(e.name))return s.jsx("div",{className:`w-full py-1 px-2 text-white rounded-md ${v===t.path?"bg-zinc-800":""}`,onClick:()=>j.push(t.path),children:s.jsx("p",{children:t.title})},t.title)})}),s.jsx("div",{className:"border-t border-gray-700 pb-3",children:(0,s.jsxs)("div",{className:"mt-3 space-y-1 px-2 flex items-center",onClick:logout,children:[s.jsx("div",{className:"",children:s.jsx(d.Z,{className:"w-8 h-8 text-red-400 p-1"})}),y.map(e=>s.jsx(i.p.Button,{as:"a",className:"block rounded-md px-3 py-2 text-base font-medium text-red-400 hover:bg-red-600 hover:text-white",children:e.name},e.name))]})})]})]})})}},30170:(e,t,a)=>{a.d(t,{Z:()=>InputSearch});var s=a(60080),r=a(96413),i=a(96198);function InputSearch({setValue:e,value:t,disabled:a=!1,handleSearch:o,placeholder:l="Pesquisar",isDocument:n=!1}){return(0,s.jsxs)("div",{className:"w-full bg-white flex items-center justify-start p-1 rounded-full md:w-auto min-w-64",children:[s.jsx("div",{onClick:()=>{o(t)},children:s.jsx(i.Z,{width:20,color:"#868E96",className:"cursor-pointer"})}),s.jsx("input",{type:"text",className:"border-none w-full text-[#868E96] ml-2 focus:outline-none",placeholder:l,onKeyDown:e=>{"Enter"===e.key&&o(t)},disabled:a,value:t,onChange:({target:t})=>{n?e(t.value.length<=14?(0,r.VL)(t.value):(0,r.PK)(t.value)):e(t.value)}})]})}},80223:(e,t,a)=>{a.d(t,{Z:()=>SelectCustom});var s=a(60080);function SelectCustom({value:e,onChange:t,children:a,className:r="",disabled:i=!1}){return(0,s.jsxs)("div",{className:"relative w-full",children:[s.jsx("select",{disabled:i,value:e,onChange:t,className:`h-12 w-full pl-6 pr-10 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black cursor-pointer appearance-none ${r}`,children:a}),s.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3",children:s.jsx("svg",{className:"h-4 w-4 text-[#FFFF]",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}a(9885)},93640:(e,t,a)=>{a.d(t,{Z:()=>Select});var s=a(60080),r=a(34129),i=a(9885);function Select({options:e,selected:t,setSelected:a,width:o="100%",size:l="normal",align:n,zIndex:d=10}){let[c,x]=(0,i.useState)(),[m,u]=(0,i.useState)(t);(0,i.useEffect)(()=>{u(e.filter(e=>e.value===t)[0]?.label)},[]),(0,i.useEffect)(()=>{u(e.filter(e=>e.value===t)[0]?.label)},[t]);let handleOptionClick=e=>{u(e.label),a(e.value),x(!1)},returnTypeBG=e=>{if("normal"===l){if(t===e)return"bg-[#3A3A3A80] mx-2"}else if(t===e)return"bg-[#7c787880] mx-2"};return(0,s.jsxs)("div",{className:`relative w-[${o}]`,children:[(0,s.jsxs)("button",{style:{zIndex:d+1},className:`w-full justify-between relative text-left ${"normal"===l?"bg-[#3A3A3A]":"bg-[#303030]"} ${"normal"===l?"border px-2 py-2 text-sm":" py-2 px-4 text-sm"} text-white rounded-md shadow-md focus:outline-none flex items-center`,onClick:()=>x(e=>!e),children:[m,s.jsx("div",{className:`ml-2 ${!0===c&&"animate-flipIn rotate-180"} ${!1===c&&"animate-flipOut"}`,children:s.jsx(r.Z,{width:20})})]}),s.jsx("div",{style:{zIndex:d,top:"20px"},className:`absolute w-full ${"normal"===l?"bg-[#141414]":"bg-[#242424]"} rounded-b-md shadow-lg py-2 ${c?"animate-openY":"hidden"} `,children:s.jsx("div",{className:"mt-5 max-h-40 overflow-auto scroll-mx-1",children:e.map((t,a)=>s.jsx("div",{className:`px-3 py-2  ${"center"===n&&"text-center"} text-white cursor-pointer text-sm rounded-lg ${"normal"===l?"hover:bg-[#3a3a3a50]":"hover:bg-[#52525250]"} hover:mx-2 my-1 transition-transform ${returnTypeBG(t.value)} ${a===e.length?"rounded-b-lg":""}`,onClick:()=>handleOptionClick(t),children:t.label},t.value))})})]})}},47956:(e,t,a)=>{a.d(t,{Z:()=>Sidebar,L:()=>h});var s=a(60080),r=a(52589),i=a(69195),o=a(55636),l=a(78750),n=a(57114),d=a(34751);let c={src:"/_next/static/media/users.eb931a1f.svg",height:20,width:28,blurWidth:0,blurHeight:0},x={src:"/_next/static/media/monitoring.11c7bfee.svg",height:30,width:27,blurWidth:0,blurHeight:0};var m=a(40491),u=a(52451),p=a.n(u),f=a(90682);let h=[{title:"Dashboard",path:"/home",icon:s.jsx(p(),{alt:"",src:{src:"/_next/static/media/dashboard.15d835ed.svg",height:23,width:24,blurWidth:0,blurHeight:0},width:20,color:"#fff"}),role:["admin","broker","investor","advisor","superadmin","retention","financial"]},{title:"Usu\xe1rios",path:"/usuarios",icon:s.jsx(p(),{src:c,alt:"",width:20,color:"#fff"}),role:["admin","superadmin"]},{title:"Movimenta\xe7\xf5es",path:"/movimentacoes",icon:s.jsx(r.Z,{width:20,color:"#fff"}),role:["investor"]},{title:"Meus Contratos",path:"/meus-contratos",icon:s.jsx(i.Z,{width:20,color:"#fff"}),role:["broker","investor","advisor"]},{title:"Contratos",path:"/contratos",icon:s.jsx(i.Z,{width:20,color:"#fff"}),role:["superadmin","admin"]},{title:"Usu\xe1rios",path:"/investidores",icon:s.jsx(p(),{src:c,alt:"",width:20,color:"#fff"}),role:["broker","advisor"]},{title:"Cadastros",path:"/cadastro-manual",icon:s.jsx(p(),{alt:"",src:{src:"/_next/static/media/create_users.3686b7db.svg",height:19,width:21,blurWidth:0,blurHeight:0},width:20,color:"#fff"}),role:["admin","superadmin","broker"]},{title:"Monitoramento",path:"/monitoramento",icon:s.jsx(p(),{alt:"",src:x,width:20,color:"#fff"}),role:["admin","superadmin"]},{title:"Metas",path:"/metas",icon:s.jsx(p(),{alt:"",src:m.Z,width:20,color:"#fff"}),role:["admin","broker","advisor","superadmin"]},{title:"Pagamentos",path:"/financeiro",icon:s.jsx(p(),{alt:"",src:x,width:20,color:"#fff"}),role:["superadmin","broker"]},{title:"Pagamentos Previstos",path:"/pagamentos-previstos",icon:s.jsx(o.Z,{width:20,color:"#fff"}),role:["financial","superadmin"]},{title:"Informe de Rendimentos",path:"/informe-rendimentos",icon:s.jsx(p(),{alt:"",src:{src:"/_next/static/media/ir.fa1b5c3e.svg",height:27,width:24,blurWidth:0,blurHeight:0},width:20,color:"#fff"}),role:["superadmin","broker","advisor"]}];function Sidebar({children:e,pathValid:t}){let a=(0,n.useRouter)(),r=(0,n.usePathname)(),i=(0,f.e)();return(0,s.jsxs)("div",{className:"flex w-full min-h-screen py-2",children:[(0,s.jsxs)("div",{className:"2xl:w-2/12 hidden md:block rounded-r-lg p-5 bg-[#1C1C1C] relative",children:[h.map((e,t)=>{if(e.role.includes(i.name)){let i=r.includes(e.path)||"/home"===e.path&&"/cadastro"===r;return(0,s.jsxs)("div",{onClick:()=>a.push(e.path),style:{borderColor:i?"#FF9900":"",borderWidth:i?"1px":""},className:"rounded-xl px-3 py-2 flex items-center justify-center flex-col cursor-pointer mb-4 hover:bg-zinc-900",children:[e.icon,s.jsx("p",{style:{color:"#fff"},className:"mt-1 text-sm text-center w-[94%]",children:e.title})]},t)}}),s.jsx("div",{children:(0,s.jsxs)("div",{onClick:()=>{d.Am.success("Deslogado com sucesso!"),localStorage.clear(),sessionStorage.clear(),a.push("/")},className:"rounded-xl px-5 py-4 flex items-center justify-center cursor-pointer mb-4 hover:bg-red-100",children:[s.jsx(l.Z,{width:20,color:"#dc2626 ",className:"mr-3"}),s.jsx("p",{className:"text-sm text-red-600",children:"Sair"})]})})]}),s.jsx("div",{className:"md:ml-6 md:mx-5 px-5 md:px-0 md:p-0 mt-6 w-full",children:e})]})}},74644:(e,t,a)=>{a.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__,j:()=>SkeletonItem});var s=a(60080);a(9885);let SkeletonItem=({width:e="100%",height:t="1rem",borderRadius:a="4px",className:r=""})=>s.jsx("div",{className:r,style:{width:e,height:t,borderRadius:a,background:"linear-gradient(90deg, #222222 25%, #363636 50%, #222222 75%)",backgroundSize:"200% 100%",animation:"skeleton-loading 5s infinite"}}),__WEBPACK_DEFAULT_EXPORT__=({width:e="100%",height:t="1rem",borderRadius:a="4px",className:r="",loading:i,children:o,minWidth:l="",maxWidth:n=""})=>i?s.jsx("div",{className:r,style:{width:e,height:t,minWidth:l,maxWidth:n,borderRadius:a,background:"linear-gradient(90deg, #222222 25%, #363636 50%, #222222 75%)",backgroundSize:"200% 100%",animation:"skeleton-loading 5s infinite"}}):s.jsx("div",{children:o})},13649:(e,t,a)=>{a.d(t,{$F:()=>DropdownMenuTrigger,AW:()=>DropdownMenuContent,Ju:()=>DropdownMenuLabel,VD:()=>DropdownMenuSeparator,h_:()=>DropdownMenu});var s=a(60080);a(9885);var r=a(31794),i=a(1712);function DropdownMenu({...e}){return s.jsx(r.fC,{"data-slot":"dropdown-menu",...e})}function DropdownMenuTrigger({...e}){return s.jsx(r.xz,{"data-slot":"dropdown-menu-trigger",...e})}function DropdownMenuContent({className:e,sideOffset:t=4,...a}){return s.jsx(r.Uv,{children:s.jsx(r.VY,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-[#3A3A3A] border-none text-white data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-sm p-1 shadow-md",e),...a})})}function DropdownMenuLabel({className:e,inset:t,...a}){return s.jsx(r.__,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,i.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...a})}function DropdownMenuSeparator({className:e,...t}){return s.jsx(r.Z0,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},11330:(e,t,a)=>{a.d(t,{Z:()=>i});var s=a(32775);let r=[{label:"Todos",value:"Todos"},{label:"Contrato Gerado",value:s.rd.GENERATED},{label:"Contrato Ativo",value:s.rd.ACTIVE},{label:"Envio da assinatura",value:s.rd.SIGNATURE_SENT},{label:"Aguardando assinatura do investidor",value:s.rd.AWAITING_INVESTOR_SIGNATURE},{label:"Aguardando comprovante de pagamento",value:s.rd.AWAITING_DEPOSIT},{label:"Aguardando auditoria",value:s.rd.AWAITING_AUDIT},{label:"Aguardando assinatura da auditoria",value:s.rd.AWAITING_AUDIT_SIGNATURE},{label:"Falha ao enviar contrato para assinatura",value:s.rd.SIGNATURE_FAILED},{label:"Expirado por falta de assinatura do investidor",value:s.rd.EXPIRED_BY_INVESTOR},{label:"Expirado por falta de assinatura da auditoria",value:s.rd.EXPIRED_BY_AUDIT},{label:"Rejeitado",value:s.rd.REJECTED},{label:"Rejeitado pela auditoria",value:s.rd.REJECTED_BY_AUDIT},{label:"Falha ao gerar o contrato",value:s.rd.GENERATE_CONTRACT_FAILED},{label:"Expirado",value:s.rd.EXPIRED},{label:"Exclu\xeddo",value:s.rd.DELETED}],i=r},32775:(e,t,a)=>{var s,r;function formatStatusContract(e){let t={DRAFT:{title:"Rascunho",description:"Contrato ainda em fase de edi\xe7\xe3o",textColor:"text-[#FF9900]"},GENERATED:{title:"Gerado",description:"Contrato gerado com sucesso e aguardando a\xe7\xe3o",textColor:"text-[#FF9900]"},SIGNATURE_SENT:{title:"Assinatura",description:"Contrato enviado para assinatura digital",textColor:"text-[#FF9900]"},AWAITING_INVESTOR_SIGNATURE:{title:"Aguardando",description:"Aguardando que o investidor assine o contrato",textColor:"text-[#FF9900]"},AWAITING_DEPOSIT:{title:"Aguardando",description:"Aguardando envio do comprovante de pagamento",textColor:"text-[#FF9900]"},AWAITING_AUDIT:{title:"Aguardando",description:"Contrato aguardando an\xe1lise da auditoria",textColor:"text-[#FF9900]"},AWAITING_AUDIT_SIGNATURE:{title:"Aguardando",description:"Aguardando assinatura final da auditoria",textColor:"text-[#FF9900]"},ACTIVE:{title:"Ativo",description:"Contrato ativo e em vigor",textColor:"text-[#3cff00]"},SIGNATURE_FAILED:{title:"Falha",description:"Erro ao tentar enviar o contrato para assinatura",textColor:"text-red-500"},EXPIRED_BY_INVESTOR:{title:"Expirado",description:"Contrato expirou por falta de assinatura do investidor",textColor:"text-red-500"},EXPIRED_BY_AUDIT:{title:"Expirado",description:"Contrato expirou por n\xe3o ter sido assinado pela auditoria",textColor:"text-red-500"},EXPIRED_FAILURE_PROOF_PAYMENT:{title:"Expirado",description:"Contrato expirado por n\xe3o envio do comprovante de pagamento",textColor:"text-red-500"},REJECTED_BY_AUDIT:{title:"Rejeitado",description:"Contrato foi rejeitado pela auditoria.",textColor:"text-red-500"},REJECTED:{title:"Rejeitado",description:"Contrato foi rejeitado por alguma das partes",textColor:"text-red-500"},GENERATE_CONTRACT_FAILED:{title:"Erro",description:"Ocorreu uma falha ao gerar o contrato",textColor:"text-red-500"},EXPIRED:{title:"Expirado",description:"Contrato expirado por tempo excedido ou inatividade",textColor:"text-red-500"},DELETED:{title:"Exclu\xeddo",description:"Contrato exclu\xeddo do sistema",textColor:"text-red-500"}};return e in t?t[e]:{title:"Desconhecido",description:"Status n\xe3o reconhecido pelo sistema",textColor:"text-gray-300"}}function formatStatusAditive(e){switch(e){case"DRAFT":return{title:"Erro",textColor:"text-[#FF0000]",description:"Ocorreu um erro na confec\xe7\xe3o do contrato"};case"SENT_FOR_SIGNATURE":return{title:"Aguardando",textColor:"text-[#FF9900]",description:"Contrato enviado para assinatura das partes"};case"PENDING_INVESTOR_SIGNATURE":return{title:"Aguardando",textColor:"text-[#FF9900]",description:"Aguardando assinatura do investidor"};case"FULLY_SIGNED":return{title:"Finalizado",textColor:"text-[#3cff00]",description:"Todas as partes assinaram o contrato"};case"CANCELED":return{title:"Cancelado",textColor:"text-[#FF0000]",description:"Contrato cancelado antes de ser conclu\xeddo"};case"EXPIRED":return{title:"Expirado",textColor:"text-[#FF0000]",description:"Contrato expirado sem ser assinado dentro do prazo"};default:return{title:"Processando",textColor:"text-[#FF9900]",description:""}}}function formatStatusIncomePayment(e){switch(e){case"PENDENT":return{title:"Pendente",textColor:"text-[#FFB238]",description:"Informe de rendimentos pendente para enviar",borderColor:"border-[#FFB238]",backgroundColor:"bg-[#FFB238]"};case"DELIVERED":return{title:"Enviando",textColor:"text-[#429AEC]",description:"Envio em andamento ao destinat\xe1rio",backgroundColor:"bg-[#429AEC]",borderColor:"border-[#429AEC]"};case"SEND":return{title:"Enviado",textColor:"text-[#1EF97C]",description:"Informe de rendimentos enviado com sucesso!",backgroundColor:"bg-[#1EF97C]",borderColor:"border-[#1EF97C]"};case"ERROR":return{title:"Erro",textColor:"text-[#F10303]",description:"Erro ao enviar o informe de rendimentos",borderColor:"border-[#F10303]",backgroundColor:"bg-[#F10303]"}}}a.d(t,{Jk:()=>formatStatusIncomePayment,XW:()=>formatStatusAditive,mP:()=>formatStatusContract,rd:()=>s}),function(e){e.EXPIRED_BY_AUDIT="EXPIRED_BY_AUDIT",e.EXPIRED_FAILURE_PROOF_PAYMENT="EXPIRED_FAILURE_PROOF_PAYMENT",e.DRAFT="DRAFT",e.GENERATED="GENERATED",e.SIGNATURE_SENT="SIGNATURE_SENT",e.AWAITING_INVESTOR_SIGNATURE="AWAITING_INVESTOR_SIGNATURE",e.AWAITING_DEPOSIT="AWAITING_DEPOSIT",e.AWAITING_AUDIT="AWAITING_AUDIT",e.AWAITING_AUDIT_SIGNATURE="AWAITING_AUDIT_SIGNATURE",e.ACTIVE="ACTIVE",e.SIGNATURE_FAILED="SIGNATURE_FAILED",e.EXPIRED_BY_INVESTOR="EXPIRED_BY_INVESTOR",e.REJECTED="REJECTED",e.REJECTED_BY_AUDIT="REJECTED_BY_AUDIT",e.GENERATE_CONTRACT_FAILED="GENERATE_CONTRACT_FAILED",e.EXPIRED="EXPIRED",e.DELETED="DELETED"}(s||(s={})),function(e){e.DRAFT="DRAFT",e.SENT_FOR_SIGNATURE="SENT_FOR_SIGNATURE",e.PENDING_INVESTOR_SIGNATURE="PENDING_INVESTOR_SIGNATURE",e.FULLY_SIGNED="FULLY_SIGNED",e.CANCELED="CANCELED",e.EXPIRED="EXPIRED"}(r||(r={}))},40491:(e,t,a)=>{a.d(t,{Z:()=>s});let s={src:"/_next/static/media/target.4ba3f0d8.svg",height:27,width:27,blurWidth:0,blurHeight:0}}};