(()=>{var e={};e.id=3538,e.ids=[3538],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},76311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>x,routeModule:()=>u,tree:()=>c});var a=s(73137),r=s(54647),l=s(4183),n=s.n(l),i=s(71775),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d=a.AppPageRouteModule,c=["",{children:["monitoramento",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76413)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\page.tsx"],m="/monitoramento/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new d({definition:{kind:r.x.APP_PAGE,page:"/monitoramento/page",pathname:"/monitoramento",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},11352:(e,t,s)=>{Promise.resolve().then(s.bind(s,20607))},20607:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Monitoramento});var a=s(60080),r=s(97669),l=s(47956),n=s(9885),i=s(85814),o=s(34751),d=s(28168),c=s(30170),x=s(57114),m=s(96413),p=s(33050),u=s(99986);let h=[{title:"Todos",value:""},{title:"Enviado para reten\xe7\xe3o",value:"SENT_TO_RETENTION"},{title:"Resgate requisitado",value:"REDEMPTION_REQUESTED"},{title:"Resgate confirmado",value:"REDEMPTION_CONFIRMED"},{title:"Renova\xe7\xe3o requisitada",value:"RENEWAL_REQUESTED"},{title:"Renova\xe7\xe3o confirmada",value:"RENEWAL_CONFIRMED"},{title:"Aditivo solicitado",value:"CONTRACT_ADDENDUM_REQUESTED"},{title:"Aditivo confirmado",value:"CONTRACT_ADDENDUM_CONFIRMED"}];function ModalMonitoramento({loading:e,setModal:t,status:s,contract:r,setModalContract:l,setTypeModal:i}){let[o,d]=(0,n.useState)("");return(0,a.jsxs)("div",{className:"z-10 md:w-10/12 w-full bg-[#1C1C1C] fixed top-0 right-0 bottom-0 text-white overflow-auto h-auto p-10",children:[a.jsx("div",{className:"w-full text-center",children:a.jsx("h1",{className:"text-2xl",children:a.jsx("b",{children:h.filter(e=>e.value===r?.event?.status)[0]?.title||"Sem status"})})}),(0,a.jsxs)("div",{children:[a.jsx("h2",{children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-between w-full mt-2",children:[(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"E-mail"}),a.jsx("p",{className:"text-sm",children:r?.investor.email})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"CPF"}),a.jsx("p",{className:"text-sm",children:r?.investor.cpfCnpj})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"RG"}),a.jsx("p",{className:"text-sm",children:r?.investor.rg||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"Perfil"}),a.jsx("p",{className:"text-sm",children:r?.investor.profile||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"Consultor"}),a.jsx("p",{className:"text-sm",children:r?.investor.consultant||"N\xe3o informado"})]})]})]}),(0,a.jsxs)("div",{className:"mt-10",children:[a.jsx("h2",{children:"Informa\xe7\xf5es de Endere\xe7o"}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-between w-full mt-2",children:[(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"Endere\xe7o"}),a.jsx("p",{className:"text-sm",children:r?.investor.address||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"N\xfamero"}),a.jsx("p",{className:"text-sm",children:r?.investor.number||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"Complemento"}),a.jsx("p",{className:"text-sm",children:r?.investor.complement||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"Bairro"}),a.jsx("p",{className:"text-sm",children:r?.investor.neighborhood||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"Cidade"}),a.jsx("p",{className:"text-sm",children:r?.investor.city||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"UF"}),a.jsx("p",{className:"text-sm",children:r?.investor.state||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"mt-2 md:mt-0",children:[a.jsx("p",{className:"text-sm font-bold",children:"CEP"}),a.jsx("p",{className:"text-sm",children:r?.investor.zipCode||"N\xe3o informado"})]})]})]}),(0,a.jsxs)("div",{className:"w-full flex mt-10 flex-wrap gap-3 items-end",children:[a.jsx("div",{className:"",children:a.jsx(u.Z,{label:"Fechar",loading:!1,variant:"secondary",disabled:!1,handleSubmit:()=>t(!1)})}),r?.event?.status?r?.event?.status.includes("REQUESTED")?a.jsx("div",{className:"",children:a.jsx(u.Z,{label:"Aprovar solicita\xe7\xe3o",loading:e,disabled:e,handleSubmit:()=>{l(!0),i("APPROVE")}})}):void 0:a.jsx("div",{className:"",children:a.jsx(u.Z,{label:"Enviar para reten\xe7\xe3o",loading:e,disabled:e,handleSubmit:()=>{l(!0),i("REQUEST")}})})]})]})}var j=s(15455),N=s(90682);function ModalTypeAction({contract:e,setOpenModal:t,typeModal:s}){let[r,l]=(0,n.useState)(),[d,c]=(0,n.useState)(""),[x,m]=(0,n.useState)(!1),p=(0,N.e)(),renewContract=()=>{m(!0);let s={contractId:e.contractId,ownerRoleId:p.roleId,comment:d};i.Z.post("/contract-lifecycle-monitoring/send-contract-to-retention",s).then(e=>{o.Am.success("Contrato enviado com sucesso!"),m(!1),t(!1)}).catch(e=>{o.Am.error(e?.response?.data?.message||"N\xe3o foi enviar o contrato para a reten\xe7\xe3o"),m(!1)})},approveContract=()=>{let s=new FormData;if(""===d)return o.Am.warning(`O campo coment\xe1rio \xe9 obrigat\xf3rio`);s.append("comment",d),s.append("ownerRoleId",p.roleId),r&&s.append("attachments",r[0]),m(!0),i.Z.post(`/contract-lifecycle-monitoring/${e.contractId}/finalize`,s).then(e=>{o.Am.success("Opera\xe7\xe3o realizada com sucesso!"),m(!1),t(!1),window.location.reload()}).catch(e=>{o.Am.error(e?.response?.data?.message||"N\xe3o foi possivel completar a opera\xe7\xe3o"),m(!1)})};return a.jsx("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-20 overflow-auto ",children:(0,a.jsxs)("div",{className:"md:w-5/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900] mb-10",children:[a.jsx("p",{className:"text-lg font-bold",children:"APPROVE"===s?"Aprovar solicita\xe7\xe3o":"Enviar para a reten\xe7\xe3o"}),a.jsx("p",{className:"text-xs",children:"*Coloque o coment\xe1rio que deseja no campo abaixo"}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 mb-10 text-white items-start justify-center mt-5",children:[a.jsx("div",{className:"w-full min-h-52",children:(0,a.jsxs)("div",{children:[a.jsx("p",{children:"Coment\xe1rio *"}),a.jsx("textarea",{value:d,onChange:({target:e})=>c(e.value),className:"h-12 w-full text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 min-h-52 p-2 mt-2"})]})}),"APPROVE"===s&&a.jsx("div",{className:"flex gap-2 text-white items-center justify-around",children:(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Anexar arquivo *"}),a.jsx(j.Z,{onFileUploaded:l})]})})]}),(0,a.jsxs)("div",{className:"flex w-full mt-10 justify-between",children:[a.jsx("div",{className:"w-44",children:a.jsx(u.Z,{label:"Confirmar",loading:x,className:"bg-orange-linear",handleSubmit:()=>{"APPROVE"===s?approveContract():renewContract()}})}),a.jsx("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer",onClick:()=>t(!1),children:a.jsx("p",{className:"text-sm",children:"Fechar"})})]})]})})}var f=s(74644),v=s(18109);let b=[{title:"Todos",value:""},{title:"Enviado para reten\xe7\xe3o",value:"SENT_TO_RETENTION"},{title:"Resgate requisitado",value:"REDEMPTION_REQUESTED"},{title:"Resgate confirmado",value:"REDEMPTION_CONFIRMED"},{title:"Renova\xe7\xe3o requisitada",value:"RENEWAL_REQUESTED"},{title:"Renova\xe7\xe3o confirmada",value:"RENEWAL_CONFIRMED"},{title:"Aditivo solicitado",value:"CONTRACT_ADDENDUM_REQUESTED"},{title:"Aditivo confirmado",value:"CONTRACT_ADDENDUM_CONFIRMED"}];function Monitoramento(){(0,x.useRouter)();let[e,t]=(0,n.useState)([]),[s,u]=(0,n.useState)(),[h,j]=(0,n.useState)(!0),[g,E]=(0,n.useState)(!1),[w,C]=(0,n.useState)(!1),[y,R]=(0,n.useState)(!1),[D,_]=(0,n.useState)(b[0]),[A,M]=(0,n.useState)(1),[P,T]=(0,n.useState)(),[F,S]=(0,n.useState)(""),[q,O]=(0,n.useState)(""),[I,Z]=(0,n.useState)({contractAddendumRequestedCount:0,expiringContractsCount:0,redemptionRequestedCount:0,renewalRequestedCount:0}),k=(0,N.e)();(0,n.useEffect)(()=>{getContracts()},[A,D]),(0,n.useEffect)(()=>{getData()},[]);let getContracts=e=>{E(!0),i.Z.get(`${"retention"===k.name?"/contract-lifecycle-monitoring/retention":"/contract-lifecycle-monitoring/expiring"}`,{params:{statuses:""===D.value?void 0:D.value,page:A,limit:10}}).then(e=>{t(e.data.data),T({total:e.data.total,perPage:e.data.limit,page:e.data.page,lastPage:e.data.totalPages})}).catch(e=>{}).finally(()=>E(!1))},getData=()=>{i.Z.get("/contract-lifecycle-monitoring/dashboard").then(e=>{Z(e.data)}).catch(e=>{o.Am.error(e.response.data.message||"N\xe3o conseguimos pegar os dados de monitoramento.")}).finally(()=>j(!1))},returnStatus=e=>{let t=b.filter(t=>t.value===e.event?.status)[0];return e.event?.status.includes("REDEMPTION")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#FF0404]",children:a.jsx("p",{className:"text-[10px] text-center text-[#FF0404] font-bold",children:t?.title})}):e.event?.status.includes("ADDENDUM")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#429AEC]",children:a.jsx("p",{className:"text-[10px] text-center text-[#429AEC] font-bold",children:t?.title})}):e.event?.status.includes("RETENTION")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#FFB238]",children:a.jsx("p",{className:"text-[10px] text-center text-[#FFB238] font-bold",children:t?.title})}):e.event?.status.includes("RENEWAL")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#1EF97C]",children:a.jsx("p",{className:"text-[10px] text-center text-[#1EF97C] font-bold",children:t?.title})}):a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border ",children:a.jsx("p",{className:"text-[10px] text-center ",children:t?.title||"\xc0 vencer"})})};return(0,a.jsxs)("div",{className:w?"fixed w-full":"relative",children:[a.jsx(r.Z,{}),a.jsx(l.Z,{children:(0,a.jsxs)("div",{className:"w-full relative",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white text-center mb-8 text-3xl",children:"Monitoramento"}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between m-auto w-full gap-4 md:w-[1100px]",children:[a.jsx("div",{className:"md:w-[40%] py-4 rounded-lg border-[#FF9900] border",children:(0,a.jsxs)("div",{className:"m-auto text-center flex flex-col items-center relative",children:[a.jsx("p",{className:"text-white mb-5 md:text-base text-sm",children:"Contratos pr\xf3ximos ao vencimento"}),a.jsx(f.Z,{loading:h,height:"65px",width:"50px",children:a.jsx("p",{className:"text-white mb-5 md:text-7xl font-semibold text-3xl",children:I.expiringContractsCount+I.redemptionRequestedCount})})]})}),a.jsx("div",{className:"md:w-[40%] py-4 rounded-lg border-[#FF9900] border",children:(0,a.jsxs)("div",{className:"m-auto text-center flex flex-col items-center relative",children:[a.jsx("p",{className:"text-white mb-5 md:text-base text-sm",children:"Solicita\xe7\xf5es"}),a.jsx(f.Z,{loading:h,height:"65px",width:"50px",children:a.jsx("p",{className:"text-white mb-5 md:text-7xl font-semibold text-3xl",children:I.renewalRequestedCount+I.contractAddendumRequestedCount})})]})})]})]}),a.jsx("div",{className:"flex bg-[#1C1C1C] p-2 rounded-md m-auto mt-5 text-white w-full md:w-fit flex-wrap md:flex-nowrap gap-2",children:b.map((e,t)=>a.jsx("div",{onClick:()=>{_(e),M(1)},className:`hover:bg-[#313131] px-2 py-3 rounded-md cursor-pointer text-center flex items-center ${D?.title===e.title?"bg-[#313131]":""}`,children:a.jsx("p",{className:`${D?.title===e.title?"text-[#FF9900]":""} text-xs`,children:e.title})},t))}),a.jsx("div",{className:"md:w-[1100px] min-h-[250px] m-auto",children:(0,a.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] text-white mt-10 overflow-x-auto w-full rounded-b-md border border-[#FF9900]",children:[a.jsx("div",{className:"flex w-full justify-end p-2",children:a.jsx("div",{className:"w-80",children:a.jsx(c.Z,{handleSearch:()=>{getContracts(F)},setValue:S,value:F})})}),(0,a.jsxs)("table",{className:"w-full relative min-h-20",children:[a.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,a.jsxs)("tr",{className:"w-full",children:[a.jsx("th",{className:"w-10",children:a.jsx("p",{className:"font-bold text-sm"})}),a.jsx("th",{className:"py-2",children:a.jsx("p",{className:"font-bold text-sm",children:"Nome"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"E-mail"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Status"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Consultor"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Inicio do Contr."})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Fim do Contr."})})]})}),a.jsx(a.Fragment,{children:!1===g?a.jsx(a.Fragment,{children:e?.length>0?a.jsx("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[a.jsx("td",{className:"w-10",children:a.jsx("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{C(!0),u(e)},children:a.jsx(d.Z,{color:"#fff",width:20})})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:e.investor?.name||"N\xe3o encontrado"})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:m.p4(e.investor?.cpfCnpj||"")?.length<=11?(0,m.VL)(e?.investor?.cpfCnpj||""):(0,m.PK)(e?.investor?.cpfCnpj||"")})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:e.investor.email||"N\xe3o encontrado"})}),a.jsx("td",{className:"",children:returnStatus(e)}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:e.investor.consultant||"N\xe3o encontrado"})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:(0,p.Z)(e.startContract)})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:(0,p.Z)(e.endContract)})})]},e.contractId))}):a.jsx("div",{className:"text-center mt-5 absolute w-full",children:a.jsx("p",{children:"Nenhum dado encontrado"})})}):a.jsx("tbody",{children:(0,a.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[a.jsx("td",{className:"w-10 px-1",children:a.jsx(f.j,{height:"25px"})}),a.jsx("td",{className:"px-1",children:a.jsx(f.j,{height:"25px"})}),a.jsx("td",{className:"px-1",children:a.jsx(f.j,{height:"25px"})}),a.jsx("td",{className:"px-1",children:a.jsx(f.j,{height:"25px"})}),a.jsx("td",{className:"px-1",children:a.jsx(f.j,{height:"25px"})}),a.jsx("td",{className:"px-1",children:a.jsx(f.j,{height:"25px"})}),a.jsx("td",{className:"px-1",children:a.jsx(f.j,{height:"25px"})}),a.jsx("td",{className:"px-1",children:a.jsx(f.j,{height:"25px"})})]})})})]}),a.jsx(v.Z,{page:A,setPage:M,lastPage:Number(P?.lastPage||1),perPage:Number(P?.perPage||10),totalItems:P?.total||"0"})]})}),w&&a.jsx(ModalMonitoramento,{loading:h,setModal:C,status:D,contract:s,setModalContract:R,setTypeModal:O}),y&&a.jsx(ModalTypeAction,{contract:s,setOpenModal:R,typeModal:q})]})})]})}},99986:(e,t,s)=>{"use strict";s.d(t,{Z:()=>Button});var a=s(60080),r=s(69957);function Button({handleSubmit:e,loading:t,label:s,disabled:l,className:n,...i}){return a.jsx(r.z,{...i,onClick:e,loading:t,disabled:l,className:n,children:s})}},33050:(e,t,s)=>{"use strict";s.d(t,{Z:()=>formatDate,l:()=>formatDateToEnglishType});var a=s(95081),r=s.n(a);function formatDate(e){return r().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?r().utc(e).format("YYYY-MM-DD"):r().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},76413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>o});var a=s(17536);let r=(0,a.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\monitoramento\page.tsx`),{__esModule:l,$$typeof:n}=r,i=r.default,o=i},28168:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(9885);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"}))}),l=r}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[4103,6426,4731,8813,5081,8394,7207,278,7669,8109],()=>__webpack_exec__(76311));module.exports=s})();