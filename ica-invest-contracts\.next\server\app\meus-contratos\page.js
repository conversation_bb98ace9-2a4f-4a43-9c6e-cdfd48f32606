(()=>{var e={};e.id=235,e.ids=[235],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},59393:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>x,routeModule:()=>p,tree:()=>d});var s=a(73137),r=a(54647),n=a(4183),o=a.n(n),i=a(71775),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c=s.AppPageRouteModule,d=["",{children:["meus-contratos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,51544)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\page.tsx"],m="/meus-contratos/page",u={require:a,loadChunk:()=>Promise.resolve()},p=new c({definition:{kind:r.x.APP_PAGE,page:"/meus-contratos/page",pathname:"/meus-contratos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},64256:(e,t,a)=>{Promise.resolve().then(a.bind(a,24178))},24178:(e,t,a)=>{"use strict";a.r(t),a.d(t,{Screen:()=>Screen});var s=a(60080),r=a(97669),n=a(47956),o=a(9885),i=a(85814),l=a(28168),c=a(12129),d=a(30170),x=a(57114),m=a(96413),u=a(33050),p=a(15455),h=a(64731),f=a.n(h),v=a(34751);function RenewContract({contract:e,setOpenModal:t}){let[a,r]=(0,o.useState)(),[n,l]=(0,o.useState)(),[c,d]=(0,o.useState)(f()().format("YYYY-MM-DD")),[x,m]=(0,o.useState)(f()().format("YYYY-MM-DD")),[u,h]=(0,o.useState)(),[j,g]=(0,o.useState)();return s.jsx("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"w-5/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[s.jsx("p",{className:"text-lg font-bold",children:"Renova\xe7\xe3o de contrato"}),(0,s.jsxs)("div",{className:"flex gap-4 mt-5",children:[(0,s.jsxs)("div",{className:"md:w-2/4",children:[s.jsx("p",{className:"text-white mb-1",children:"Inicio do contrato"}),s.jsx("input",{value:c,onChange:({target:e})=>d(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]}),(0,s.jsxs)("div",{className:"md:w-2/4",children:[s.jsx("p",{className:"text-white mb-1",children:"Fim do contrato"}),s.jsx("input",{value:x,onChange:({target:e})=>m(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 mb-10 text-white items-center justify-around mt-5",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Contrato antigo"}),s.jsx(p.Z,{onFileUploaded:r,fileName:u,onRemoveFile:()=>{r(void 0)}})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Novo Contrato"}),s.jsx(p.Z,{onFileUploaded:l,fileName:j,onRemoveFile:()=>{l(void 0)}})]})]}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[s.jsx("div",{className:"px-10 bg-orange-linear flex items-center cursor-pointer",onClick:()=>{if(null===c)return v.Am.warning("Precisa informar a data de inicio de contrato");let s=new FormData;s.append("newStartDate",c),s.append("newEndDate",x),a&&s.append("oldContractPdf",a[0]),n&&s.append("newContractPdf",n[0]),i.Z.patch(`/contract/renew/${e.id}`,s).then(e=>{v.Am.success("Contrato atualizado com sucesso!"),t(!1)}).catch(e=>{})},children:s.jsx("p",{className:"text-sm",children:"Renovar contrato"})}),s.jsx("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer",onClick:()=>t(!1),children:s.jsx("p",{className:"text-sm",children:"Fechar"})})]})]})})}var j=a(69195),g=a(957),N=a(32775);function ContractData({contract:e,setAditive:t,setModal:a,setModalPayment:r,setRenew:n,loading:o,resendContract:i}){return(0,s.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10 w-full",children:[e.statusContrato===N.rd.DELETED&&(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Motivo da exclus\xe3o"}),s.jsx("p",{className:"text-xs text-end",children:e?.cancelledReason})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"ID"}),s.jsx("p",{className:"text-xs text-end",children:e?.idContrato})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Investidor"}),s.jsx("p",{className:"text-xs text-end",children:e?.nomeInvestidor})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"CPF/CNPJ"}),s.jsx("p",{className:"text-xs text-end",children:e?.documentoInvestidor!==void 0&&m.p4(e?.documentoInvestidor)?.length<=11?(0,m.VL)(String(e?.documentoInvestidor)):(0,m.PK)(String(e?.documentoInvestidor))})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Consultor Respons\xe1vel"}),s.jsx("p",{className:"text-xs text-end",children:e?.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Comprado com"}),s.jsx("p",{className:"text-xs text-end",children:e?.compradoCom})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Assinaturas"}),s.jsx("p",{className:"text-xs text-end",children:e?.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Valor"}),s.jsx("p",{className:"text-xs text-end",children:Number(e?.valorInvestimento).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Contrato Solicitado em"}),s.jsx("p",{className:"text-xs text-end",children:(0,u.Z)(e?.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Final do contrato"}),s.jsx("p",{className:"text-xs text-end",children:(0,u.Z)(e?.fimContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Status de Contrato"}),s.jsx("div",{children:s.jsx(g.Z,{description:(0,N.mP)(e.statusContrato).description,text:(0,N.mP)(e.statusContrato).title,textColor:(0,N.mP)(e.statusContrato).title})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Qtd de Cotas/Participa\xe7\xf5es"}),s.jsx("p",{className:"text-xs text-end",children:e?.cotas||"N\xe3o encontrado"})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Data de Car\xeancia"}),s.jsx("p",{className:"text-xs text-end",children:e?.periodoCarencia})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Porcentagens"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:[e?.rendimentoInvestimento,"%"]})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Ativado em"}),s.jsx("p",{className:"text-xs text-end",children:(0,u.Z)(e?.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Tags"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:["#",e?.tags==="P2P"?"M\xfatuo":e?.tags||"NE"]})]}),e.contratoPdf&&s.jsx("div",{className:"flex w-full mt-3",children:s.jsx("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(e.contratoPdf,"_blank"),children:"Ver contrato"})}),e.comprovamentePagamento&&s.jsx("div",{className:"flex w-full mt-3",children:s.jsx("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(e.comprovamentePagamento,"_blank"),children:"Ver comprovante anexado"})})]})}var b=a(99986),C=a(9993),w=a(27017),y=a(32307),S=a(69145),P=a(49714),A=a(24577),D=a(17871),F=a(50298);let Z=F.Ry().shape({isPF:F.O7().default(!0),name:F.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").when("isPF",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),document:F.Z_().when("isPF",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),value:F.Z_().required("Obrigat\xf3rio"),profile:F.Z_().required("Obrigat\xf3rio"),initDate:F.Z_().required("Obrigat\xf3rio"),bank:F.Z_().min(2,"Nome do banco muito curto").required("Obrigat\xf3rio"),accountNumber:F.Z_().min(3,"Conta inv\xe1lida").required("Obrigat\xf3rio"),agency:F.Z_().min(2,"Ag\xeancia inv\xe1lida").required("Obrigat\xf3rio"),pix:F.Z_().required("Obrigat\xf3rio"),observations:F.Z_().notRequired()});var k=a(57086),I=a(95081),R=a.n(I),_=a(66558);function AditiveContract({contract:e,setOpenModal:t}){let[a,r]=(0,o.useState)(),[n,l]=(0,o.useState)(),[c,d]=(0,o.useState)("new"),[x,u]=(0,o.useState)(!1),[h,f]=(0,o.useState)(),[j,g]=(0,o.useState)(),N=(0,o.useRef)(null),{register:F,handleSubmit:I,watch:q,setValue:M,reset:E,formState:{errors:T}}=(0,_.cI)({resolver:(0,k.X)(Z),mode:"onChange"});(0,o.useEffect)(()=>{let handClickOutside=e=>{N.current&&!N.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",handClickOutside),()=>{document.removeEventListener("mousedown",handClickOutside)}});let createAditive=a=>{u(!0);let s={contractId:e?.idContrato,investment:{value:(0,D.Z)(a.value),profile:a.profile,yield:Number(e.rendimentoInvestimento),date:R()(a.initDate).format("YYYY-MM-DD")},accountBank:{bank:a.bank,accountNumber:a.accountNumber,agency:a.agency,pix:a.pix},owner:e.documentoInvestidor.length>11?{name:a.name,cpf:(0,m.p4)(a.document||"")}:void 0,observations:a.observations,signIca:P.l};i.Z.post("/contract/additive",s).then(e=>{v.Am.success("Contrato de aditivo criado com sucesso!"),t(!1)}).catch(e=>{(0,A.Z)(e,"N\xe3o conseguimos criar o contrato de aditivo!")}).finally(()=>u(!1))},registerAditiveExists=s=>{if("exist"===c&&(!n||!a))return v.Am.error("\xc9 necess\xe1rio anexar o contrato e o comprovante de pagamento");if("exist"===c&&R().utc(s.initDate).isSameOrAfter(R().utc(),"day"))return v.Am.warn("Data de in\xedcio do contrato n\xe3o pode ser igual ou maior que a data atual");u(!0);let r=new FormData;n&&r.append("contractPdf",n[0]),a&&r.append("proofPayment",a[0]),r.append("contractId",e?.idContrato),r.append("investment[value]",s.value.replace(".","").replace(",",".")),r.append("investment[date]",R()(String(s.initDate)).format("YYYY-MM-DD")),r.append("investment[yield]",e.rendimentoInvestimento),r.append("accountBank[bank]",s.bank),r.append("accountBank[accountNumber]",s.accountNumber),r.append("accountBank[agency]",s.agency),r.append("accountBank[pix]",s.pix),r.append("observations",String(s.observations)),i.Z.post("/contract/additive-manual",r).then(e=>{v.Am.success("Contrato de aditivo cadastrado com sucesso!"),t(!1)}).catch(e=>{(0,A.Z)(e,"N\xe3o foi poss\xedvel cadastrar o aditivo")}).finally(()=>u(!1))};return(0,o.useEffect)(()=>{e.documentoInvestidor.length>11?M("isPF",!1):M("isPF",!0)},[]),(0,o.useEffect)(()=>{"exist"!==c?M("initDate",R()().format("YYYY-MM-DD")):M("initDate","")},[c]),s.jsx("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-20",children:(0,s.jsxs)("div",{ref:N,className:"md:w-6/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900] overflow-auto h-[88%]",children:[s.jsx("p",{className:"text-2xl font-bold",children:"Criar contrato aditivo"}),s.jsx("div",{className:"mt-5 w-full",children:(0,s.jsxs)("div",{className:"mb-5",children:[(0,s.jsxs)("div",{className:"flex gap-4 mb-5",children:[(0,s.jsxs)("div",{children:[s.jsx("input",{type:"checkbox",name:"",checked:"new"===c,onChange:()=>d("new"),id:"novo",className:"mr-2 cursor-pointer"}),s.jsx("label",{htmlFor:"novo",className:"cursor-pointer select-none",children:"Criar novo aditivo"})]}),(0,s.jsxs)("div",{children:[s.jsx("input",{type:"checkbox",name:"",checked:"exist"===c,onChange:()=>d("exist"),id:"manual",className:"mr-2 cursor-pointer"}),s.jsx("label",{htmlFor:"manual",className:"cursor-pointer select-none",children:"Cadastrar aditivo existente"})]})]}),(0,s.jsxs)("form",{action:"",onSubmit:I(e=>{"new"===c?createAditive(e):registerAditiveExists(e)}),children:[e.documentoInvestidor.length>11&&s.jsx(C.Z,{color:"black",title:"Dados do Representante Legal",children:(0,s.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[s.jsx(y.Z,{register:F,name:"name",width:"300px",error:!!T.name,errorMessage:T?.name?.message,label:"Nome completo"}),s.jsx(y.Z,{register:F,name:"document",width:"200px",error:!!T.document,errorMessage:T?.document?.message,label:"CPF",setValue:e=>M("document",(0,m.VL)(e||""))})]})}),s.jsx(C.Z,{color:"black",title:"Dados de Investimento",children:(0,s.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[s.jsx(y.Z,{register:F,name:"value",width:"200px",error:!!T.value,errorMessage:T?.value?.message,label:"Valor",setValue:e=>M("value",(0,m.Ht)(e||""))}),s.jsx(w.Z,{width:"200px",name:"profile",register:F,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!T.profile,errorMessage:T?.profile?.message,label:"Perfil"}),s.jsx(y.Z,{type:"date",register:F,minDate:"exist"!==c?R().utc().format("YYYY-MM-DD"):void 0,maxDate:"exist"===c?R().utc().subtract(1,"day").format("YYYY-MM-DD"):void 0,disabled:"exist"!==c,name:"initDate",width:"200px",error:!!T.initDate,errorMessage:T?.initDate?.message,label:"Inicio do contrato"})]})}),s.jsx(C.Z,{color:"black",title:"Dados bancarios",children:(0,s.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[s.jsx(y.Z,{register:F,name:"bank",width:"200px",error:!!T.bank,errorMessage:T?.bank?.message,label:"Banco"}),s.jsx(y.Z,{register:F,name:"agency",width:"200px",error:!!T.agency,errorMessage:T?.agency?.message,label:"Ag\xeancia"}),s.jsx(y.Z,{register:F,name:"accountNumber",width:"200px",error:!!T.accountNumber,errorMessage:T?.accountNumber?.message,label:"Conta"}),s.jsx(y.Z,{register:F,name:"pix",width:"200px",error:!!T.pix,errorMessage:T?.pix?.message,label:"Pix"})]})}),s.jsx(C.Z,{color:"black",title:"Observa\xe7\xf5es",children:s.jsx(S.Z,{name:"observations",register:F})}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[s.jsx("div",{children:s.jsx(b.Z,{label:"Criar aditivo",loading:x,className:"bg-orange-linear",disabled:x})}),s.jsx("div",{children:s.jsx(b.Z,{label:"Fechar",loading:!1,handleSubmit:()=>t(!1)})})]})]})]})}),"exist"===c&&(0,s.jsxs)("div",{className:"md:flex-row flex flex-col gap-2 mb-10 text-white jus  mt-5",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Aditivo"}),s.jsx(p.Z,{onFileUploaded:l,fileName:h,onRemoveFile:()=>{l(void 0)}})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Comprovante de pagamento"}),s.jsx(p.Z,{onFileUploaded:r,fileName:j,onRemoveFile:()=>{r(void 0)}})]})]})]})})}var q=a(90682),M=a(69888),E=a(92170);function AditiveData({contract:e,aditives:t,getAditives:a}){let[r,n]=(0,o.useState)(),[c,d]=(0,o.useState)(),[x,m]=(0,o.useState)(!1),p=(0,q.e)();(0,o.useEffect)(()=>{c&&r&&onUploadFile()},[c,r]);let h=(0,o.useCallback)(e=>{n(e)},[]),onUploadFile=()=>{if(!r)return v.Am.warning("Selecione um comprovante para anexar!");m(!0),v.Am.info("Enviando comprovante...");let e=new FormData;e.append("addendumId",String(c)),r&&e.append("proofPayment",r[0]),i.Z.post("/contract/addendum/proof-payment",e).then(e=>{v.Am.success("Comprovante anexado com sucesso!"),a(),n(void 0)}).catch(e=>{(0,A.Z)(e,"Erro ao enviar comprovante")}).finally(()=>{m(!1)})},{getRootProps:f,getInputProps:j}=(0,E.uI)({onDrop:h,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&v.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{n(void 0)},disabled:x}),returnAddendumFiles=({files:e,type:t})=>{let a=e.filter(e=>e.type===t)[0];return a?s.jsx("p",{className:"w-full flex items-center justify-center",children:s.jsx(l.Z,{className:"cursor-pointer",onClick:()=>{window.open(a.url,"_blank")},color:"#fff",width:20})}):a||"PAYMENT"!==t||"broker"!==p.name&&"advisor"!==p.name?s.jsx("p",{className:"w-full flex items-center justify-center",children:s.jsx(M.Z,{color:"#FF9900",width:20})}):(0,s.jsxs)("div",{...f(),children:[s.jsx("input",{...j(),disabled:x,accept:".png,.jpg,.pdf"}),s.jsx("p",{className:`w-full flex items-center justify-center bg-orange-linear py-1 rounded-lg text-sm ${x?"opacity-50":"cursor-pointer"}`,children:"Anexar"})]})};return s.jsx("div",{className:"mt-5",children:s.jsx("div",{className:"w-full overflow-x-auto pb-20",children:(0,s.jsxs)("table",{className:"w-full min-w-[700px] text-center",children:[s.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900] h-10",children:(0,s.jsxs)("tr",{className:"w-full py-2",children:[s.jsx("th",{className:"min-w-[100px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Valor"})}),s.jsx("th",{className:"min-w-[150px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Rendimento"})}),s.jsx("th",{className:"min-w-[250px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Consultor"})}),s.jsx("th",{className:"min-w-[150px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Criado em"})}),s.jsx("th",{className:"min-w-[100px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Assinatura"})}),s.jsx("th",{className:"min-w-[100px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Contrato"})}),s.jsx("th",{className:"min-w-[120px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Comprovante"})})]})}),t.length>=1?s.jsx("tbody",{className:"w-full",children:t.map((t,a)=>(0,s.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[s.jsx("td",{children:s.jsx("p",{className:"text-xs text-center",children:Number(t.value||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),s.jsx("td",{children:(0,s.jsxs)("p",{className:"text-xs text-center",children:[t.yieldRate||"0","%"]})}),s.jsx("td",{children:s.jsx("p",{className:"text-xs text-center",children:e.consultorResponsavel})}),s.jsx("td",{children:s.jsx("p",{className:"text-xs text-center",children:(0,u.Z)(t.applicationDate)})}),s.jsx("td",{className:"select-none",children:s.jsx(g.Z,{description:(0,N.XW)(t.status).description,text:(0,N.XW)(t.status).title,textColor:(0,N.XW)(t.status).textColor})}),s.jsx("td",{children:returnAddendumFiles({files:t?.addendumFiles,type:"ADDENDUM"})}),s.jsx("td",{onClick:()=>d(t.id),children:returnAddendumFiles({files:t?.addendumFiles,type:"PAYMENT",addendumId:t.id})})]},a))}):s.jsx("div",{className:"mt-5 absolute translate-x-[-50%] left-[50%]",children:s.jsx("p",{children:"Nenhum dado encontrado"})})]})})})}var T=a(85334),Y=a(16039),z=a(69957),O=a(33806);function ModalContract({contract:e,setModal:t,setRenew:a,setModalPayment:r}){let[n,l]=(0,o.useState)(0),[c,d]=(0,o.useState)(!1),[x,m]=(0,o.useState)([]),[u,p]=(0,o.useState)(!1),{navigation:h}=(0,T.H)(),[g,b]=(0,o.useState)(!1),[C,w]=(0,o.useState)(""),[y,P]=(0,o.useState)(!1),D=(0,q.e)(),resendContract=()=>{d(!0),i.Z.post(`/contract/send-notification/${e.idContrato}`).then(e=>{v.Am.success("Contrato encaminhado novamente para o investidor.")}).catch(e=>{v.Am.error(e?.response?.data?.message||"N\xe3o conseguimos encaminhar o contrato para o investidor.")}).finally(()=>d(!1))},getAditives=()=>{i.Z.get(`/contract/${e.idContrato}/addendum`).then(e=>{m(e.data.addendums)}).catch(e=>{v.Am.error(e?.response?.data?.message||"N\xe3o conseguimos carregar os aditivos do contrato.")})};return(0,o.useEffect)(()=>{!1===u&&getAditives()},[u]),(0,s.jsxs)("div",{className:"",children:[(0,s.jsxs)("div",{className:"w-full text-white overflow-auto",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center",children:s.jsx(j.Z,{color:"#000",width:20})}),s.jsx("div",{className:"gap-y-1 flex flex-col",children:s.jsx("p",{className:"font-bold text-xs",children:"Detalhes do Contrato"})})]}),(0,s.jsxs)("div",{className:"w-full flex flex-wrap mt-4 gap-4 justify-start",children:[s.jsx("div",{className:`cursor-pointer text-center w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${0===n?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>l(0),children:s.jsx("p",{className:"md:text-sm text-xs",children:"Dados do Contrato"})}),s.jsx("div",{className:`cursor-pointer text-center w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${1===n?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>l(1),children:s.jsx("p",{className:"md:text-sm text-xs",children:"Aditivos"})})]}),g?(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-2 mt-5",children:"Digite o motivo da exclus\xe3o do contrato"}),s.jsx(S.Z,{name:"",value:C,setValue:w,className:"h-20"})]}):0===n?s.jsx(ContractData,{contract:e,loading:c,resendContract:resendContract,setAditive:p,setModal:t,setModalPayment:r,setRenew:a}):s.jsx("div",{className:"min-h-[300px]",children:s.jsx(AditiveData,{contract:e,aditives:x,getAditives:getAditives})}),(0,s.jsxs)("div",{className:"w-full flex mt-10 flex-wrap gap-2 justify-start",children:[e?.statusContrato===N.rd.EXPIRED&&"superadmin"!==D.name&&s.jsx("div",{children:s.jsx(z.z,{loading:!1,onClick:()=>{a(!0),t(!1)},children:"Renovar contrato"})}),!e?.comprovamentePagamento&&e.statusContrato===N.rd.AWAITING_DEPOSIT&&s.jsx("div",{children:s.jsx(z.z,{loading:!1,onClick:()=>{r(!0),t(!1)},children:"Anexar comprovante"})}),g?(0,s.jsxs)(s.Fragment,{children:[s.jsx(z.z,{loading:y,disabled:y,variant:"destructive",onClick:()=>{if(""===C)return v.Am.warn("Digite o motivo para excluir o contrato");P(!0),i.Z.post(`/contract/${e.idContrato}/delete`,{role:D.roleId,reason:C}).then(e=>{v.Am.success("Contrato excluido com sucesso!"),w(""),b(!1),setTimeout(()=>{window.location.reload()},2e3)}).catch(e=>{(0,A.Z)(e,"Erro ao excluir contrato!")}).finally(()=>{P(!1)})},children:"Confirmar"}),s.jsx(z.z,{disabled:y,variant:"secondary",onClick:()=>{b(!1),w("")},children:"Fechar"})]}):(0,Y.f)(e.statusContrato)?s.jsx(z.z,{variant:"destructive",onClick:()=>b(!0),children:"Excluir Contrato"}):void 0,s.jsx("div",{children:"superadmin"!==D.name&&(e.statusContrato===N.rd.AWAITING_INVESTOR_SIGNATURE||e.statusContrato===N.rd.SIGNATURE_SENT)&&s.jsx(z.z,{loading:c,onClick:resendContract,children:"Reenviar Contrato"})}),e.statusContrato===N.rd.ACTIVE&&!f().utc(e.fimContrato).isBefore(f()(),"day")&&s.jsx("div",{children:s.jsx(z.z,{onClick:()=>{p(!0)},children:"Criar Aditivo"})}),O.z.includes(e.statusContrato.toUpperCase())&&s.jsx("div",{children:s.jsx(z.z,{onClick:()=>{h(`/meus-contratos/contrato/${e.idContrato}`)},children:"Editar contrato"})}),O.f.includes(e.statusContrato.toUpperCase())&&s.jsx("div",{children:s.jsx(z.z,{onClick:()=>{h(`/meus-contratos/contrato/novo/${e.idContrato}`)},children:"Editar contrato"})})]})]}),u&&e&&s.jsx(AditiveContract,{contract:e,setOpenModal:p})]})}function AddPayment({contract:e,setOpenModal:t,documentSearch:a,getContracts:r}){let[n,l]=(0,o.useState)(),[c,d]=(0,o.useState)(!1),[x,m]=(0,o.useState)(),renewContract=async()=>{if(!n)return v.Am.warning("Selecione um comprovante para anexar!");d(!0);let a=new FormData;a.append("contractId",e.idContrato),n&&a.append("file",n[0]),i.Z.put("/contract/upload/proof-payment",a).then(e=>{v.Am.success("Comprovante anexado com sucesso!"),d(!1),t(!1),setTimeout(()=>{window.location.reload()},1e3)}).catch(e=>{v.Am.error(e?.response?.data?.message||"N\xe3o foi possivel anexar o comprovante a esse contrato"),d(!1)})};return s.jsx("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"md:w-3/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[s.jsx("p",{className:"text-lg font-bold",children:"Anexar comprovante"}),s.jsx("p",{className:"text-xs",children:"*Anexe neste campo o comprovante de Pagamento do seu\xa0Investidor"}),s.jsx("div",{className:"flex gap-2 mb-10 text-white items-start justify-center mt-5",children:(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Comprovante"}),s.jsx(p.Z,{onFileUploaded:l,fileName:x,onRemoveFile:()=>{l(void 0)}})]})}),(0,s.jsxs)("div",{className:"flex w-full mt-10 justify-between gap-10",children:[s.jsx(b.Z,{label:"Anexar",loading:c,className:"bg-orange-linear",handleSubmit:renewContract}),s.jsx("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer flex items-center",onClick:()=>t(!1),children:s.jsx("p",{className:"text-sm",children:"Fechar"})})]})]})})}var U=a(26147),L=a(48764),V=a(51778),$=a(69258),B=a(53242),G=a(76027),W=a(24774),X=a(50194);function Screen({initialSignatarie:e,initialPage:t="1",initialType:a="all",initialStartData:p="",initialEndData:h="",initialStatus:f="Todos"}){let v=(0,x.useRouter)(),j=(0,x.useSearchParams)(),[b,C]=(0,o.useState)(),[w,y]=(0,o.useState)(!1),[S,P]=(0,o.useState)(!1),[A,D]=(0,o.useState)(!1),[F,Z]=(0,o.useState)(!1),[k,I]=(0,o.useState)(f),[R,_]=(0,o.useState)(Number(t)||1),[M,E]=(0,o.useState)(e||""),T=(0,B.Nr)(M,300),[Y,z]=(0,o.useState)({startData:p,endData:h,type:a,status:f}),[O,J]=(0,o.useState)(!1),K=(0,q.e)(),{data:H,isLoading:Q}=(0,V.a)({queryKey:$.U.CONTRACTS(R,T,k,Y,K.roleId),queryFn:async()=>{console.log("API call dateTo:",Y.endData);let e=await i.Z.get(returnRoute(),{params:{roleId:K.roleId,limit:"10",page:R,status:"Todos"===k?void 0:k,signatarie:T?(0,m.p4)(T):void 0,dateFrom:""===Y.startData?void 0:Y.startData,dateTo:""===Y.endData?void 0:Y.endData,contractType:"all"===Y.type?void 0:Y.type}}),t=Number(e.data?.totalPaginas)||1;return R>t?(_(1),{data:{documentos:[],totalPaginas:t,total:0}}):e},staleTime:6e4});(0,o.useEffect)(()=>{if(localStorage.setItem("typeCreateContract",""),e){let t=new URLSearchParams(j.toString());t.set("signatarie",e),t.set("page","1"),v.replace(`?${t.toString()}`)}},[e,v,j]);let returnRoute=()=>{switch(K.name){case"broker":return"/contract/list-contracts";case"advisor":return"/advisor/contracts";case"superadmin":return"/contract/list-contracts/superadmin";default:return""}},translateTag=e=>e?.toUpperCase()==="P2P"?"MUTUO":e.toUpperCase(),handleSearch=(e,t={...Y,status:k})=>{let a=new URLSearchParams(j.toString());a.set("signatarie",e),a.set("page","1"),a.set("type",t.type||"all"),a.set("startData",t.startData||""),a.set("endData",t.endData||""),a.set("status",t.status||"Todos"),console.log("handleSearch params:",a.toString()),v.replace(`?${a.toString()}`)};return(0,s.jsxs)("div",{children:[s.jsx(r.Z,{}),s.jsx(n.Z,{children:s.jsx(s.Fragment,{children:(0,s.jsxs)(W.yo,{open:S,onOpenChange:P,children:[s.jsx("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:s.jsx("h1",{className:"m-auto font-bold text-2xl",children:"Contratos"})}),s.jsx(U.Z,{children:(0,s.jsxs)("div",{className:"w-full p-2 gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row w-full justify-between items-stretch md:items-center gap-4 mb-2",children:[("advisor"===K.name||"broker"===K.name)&&s.jsx("div",{className:"w-32 h-10 bg-orange-linear px-10 flex items-center justify-center md:mr-5 mb-2 md:mb-0 rounded-lg cursor-pointer",onClick:()=>{"advisor"===K.name?(localStorage.setItem("typeCreateContract","broker"),v.push("/meus-contratos/registro-manual")):J(!0)},children:s.jsx("p",{children:"Criar"})}),(0,s.jsxs)("div",{className:"flex items-center gap-4 w-full justify-end",children:[s.jsx("div",{className:"w-full md:w-3/12",children:s.jsx(d.Z,{isDocument:!0,handleSearch:()=>handleSearch(M),setValue:e=>{E(e);let t=new URLSearchParams(j.toString());""===e?t.delete("signatarie"):t.set("signatarie",e),t.set("page","1"),v.replace(`?${t.toString()}`)},placeholder:"Pesquisar por CPF/CNPJ",value:M})}),s.jsx(G.Z,{activeModal:w,setActiveModal:y,filterData:{...Y,status:k},setFilterData:e=>{z({startData:e.startData,endData:e.endData,type:e.type,status:e.status}),I(e.status)},handleSearch:handleSearch,setPage:_,signatarie:M})]})]}),s.jsx(L.Z,{data:H?.data?.documentos||[],headers:[{title:"",component:"id",width:"30px",render:(e,t)=>s.jsx("div",{className:"cursor-pointer",onClick:()=>{P(!0),C(t)},children:s.jsx(W.aM,{children:s.jsx(l.Z,{color:"#fff",width:20})})})},{title:"Investidor",component:"nomeInvestidor",width:"150px"},{title:"CPF/CNPJ",component:"document",position:"center",width:"150px",render:(e,t)=>s.jsx("p",{className:"text-center",children:(0,m.p4)(t.documentoInvestidor||"").length<=11?(0,m.VL)(t.documentoInvestidor||""):(0,m.PK)(t.documentoInvestidor||"")})},{title:"Valor",component:"valorInvestimento",position:"center",render:(e,t)=>s.jsx("p",{className:"text-center",children:(0,X.F)(t)})},{title:"Rendimento",component:"rendimentoInvestimento",position:"center",render:e=>(0,s.jsxs)("p",{className:"text-center",children:[String(e)||"0","%"]})},{title:"Consultor",component:"consultorResponsavel",position:"center",width:"100px"},{title:"Criado em",component:"inicioContrato",position:"center",render:e=>s.jsx("p",{className:"text-center",children:(0,u.Z)(String(e))})},{title:"Status",component:"statusContrato",position:"center",width:"100px",render:(e,t)=>s.jsx(g.Z,{description:(0,N.mP)(t.statusContrato).description,text:(0,N.mP)(t.statusContrato).title,textColor:(0,N.mP)(t.statusContrato).textColor})},{title:"Modelo",component:"inicioContrato",position:"center",render:(e,t)=>s.jsx("div",{className:"px-2",children:s.jsx("div",{className:"bg-white py-[5px] px-[10px] rounded-md text-center",children:s.jsx("p",{className:"text-xs text-[#FF9900] font-bold",children:translateTag(t.tags)||"NE"})})})}],loading:Q,pagination:{page:R,lastPage:Number(H?.data?.totalPaginas)||1,perPage:10,setPage:e=>{_(e);let t=new URLSearchParams(j.toString());t.set("page",String(e)),v.replace(`?${t.toString()}`)},totalItems:String(H?.data?.total||0)}})]})}),s.jsx(W.ue,{className:"w-full md:w-[500px]",children:s.jsx(ModalContract,{contract:b,setRenew:Z,setModal:P,setModalPayment:D})}),A&&b&&s.jsx(AddPayment,{contract:b,setOpenModal:D,documentSearch:M,getContracts:handleSearch}),F&&b&&s.jsx(RenewContract,{contract:b,setOpenModal:Z}),O&&s.jsx("div",{className:"fixed z-40 top-0 left-0 w-full h-full bg-[#3A3A3AAB]",onClick:()=>J(!1),children:(0,s.jsxs)("div",{className:"absolute w-[90%] md:w-3/6 bg-[#1C1C1C] z-50 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] border border-[#FF9900] rounded-lg",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center border-b border-[#FF9900]",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Individual"}),s.jsx("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com negocia\xe7\xe3o exclusiva para voc\xea."})]}),s.jsx("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer animate-moveXRight",onClick:()=>{localStorage.setItem("typeCreateContract","broker"),v.push("/meus-contratos/registro-manual")},children:s.jsx(c.Z,{width:20})})]}),(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Compartilhado"}),s.jsx("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com a negocia\xe7\xe3o personalizada de m\xfaltiplos assessores."})]}),s.jsx("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer",onClick:()=>{localStorage.setItem("typeCreateContract","advisors"),v.push("/meus-contratos/registro-manual")},children:s.jsx(c.Z,{width:20})})]})]})})]})})})]})}},33806:(e,t,a)=>{"use strict";a.d(t,{f:()=>n,z:()=>r});var s=a(32775);let r=[s.rd.REJECTED_BY_AUDIT,s.rd.AWAITING_AUDIT],n=[s.rd.AWAITING_INVESTOR_SIGNATURE]},51544:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Page});var s=a(48144),r=a(17536);let n=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\meus-contratos\_screen\index.tsx`),{__esModule:o,$$typeof:i}=n;n.default;let l=n.Screen;async function Page({searchParams:e}){let{signatarie:t="",page:a="1",type:r="all",startData:n="",endData:o="",status:i="Todos"}=await e;return s.jsx(l,{initialSignatarie:t,initialPage:a,initialType:r,initialStartData:n,initialEndData:o,initialStatus:i})}}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,5081,8394,6558,3356,5459,9301,7207,278,7669,8109,9012,2307,87,9327,6774,8983],()=>__webpack_exec__(59393));module.exports=a})();