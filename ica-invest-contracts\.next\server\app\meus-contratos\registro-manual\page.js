(()=>{var e={};e.id=3367,e.ids=[3367],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},51841:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>m});var t=a(73137),s=a(54647),i=a(4183),o=a.n(i),n=a(71775),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(r,l);let d=t.AppPageRouteModule,m=["",{children:["meus-contratos",{children:["registro-manual",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,18474)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\registro-manual\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\registro-manual\\page.tsx"],x="/meus-contratos/registro-manual/page",u={require:a,loadChunk:()=>Promise.resolve()},p=new d({definition:{kind:s.x.APP_PAGE,page:"/meus-contratos/registro-manual/page",pathname:"/meus-contratos/registro-manual",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},80620:(e,r,a)=>{Promise.resolve().then(a.bind(a,54029))},54029:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>Register});var t=a(60080),s=a(97669),i=a(47956),o=a(9885),n=a(32307),l=a(57086),d=a(66558),m=a(99986),c=a(96413),x=a(12107),u=a(27017),p=a(69145),g=a(85814),h=a(24577),b=a(34751),j=a(22796),f=a(32411),y=a(34635),v=a(40990),N=a(9993),w=a(42686),C=a(64731),M=a.n(C),Z=a(95081),D=a.n(Z),S=a(17871),I=a(90682),P=a(40509),A=a(34944),Y=a(51778),V=a(28303);let E=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}],useCreateContract=()=>(0,A.D)({mutationFn:async e=>await g.Z.post("/contract/manual",e),onSuccess:()=>{let e=D().tz.guess(),r=D()().tz(e),a=r.hour();a>=9&&a<18?b.Am.success("Conta pr\xe9 cadastrada com sucesso!"):b.Am.success("Contrato criado com sucesso. Informamos que, por ter sido realizado fora do hor\xe1rio comercial, o registro cont\xe1bil ser\xe1 processado somente no pr\xf3ximo dia \xfatil.",{delay:6e3})},onError:e=>{(0,h.Z)(e,"Tivemos um erro ao cadastrar a conta")}}),useGetAcessors=()=>{let e=(0,I.e)();return(0,Y.a)({queryKey:["acessors"],queryFn:async()=>await g.Z.get("/wallets/broker/advisors").then(e=>e.data),enabled:"broker"===e.name})};function BusinessRegister({modalityContract:e}){let[r,a]=(0,o.useState)(!1),[s,i]=(0,o.useState)(""),[g,h]=(0,o.useState)(""),[C,Z]=(0,o.useState)(""),[D,A]=(0,o.useState)(""),[T,O]=(0,o.useState)([{generatedId:crypto.randomUUID(),id:"",taxValue:""}]),q=(0,I.e)(),k=String(localStorage.getItem("typeCreateContract")),R=useCreateContract(),{data:_=[],isLoading:B}=useGetAcessors(),{register:z,handleSubmit:F,watch:U,setValue:L,reset:Q,formState:{errors:$,isValid:G}}=(0,d.cI)({defaultValues:{initDate:M()().format("YYYY-MM-DD")},resolver:(0,l.X)(x._R),mode:"all"}),K=U("term");(0,o.useEffect)(()=>{A(K)},[K]),(0,Y.a)({queryKey:["address",U("zipCode")],queryFn:async()=>{let e=await (0,P.x)((0,c.p4)(U("zipCode")));e&&(L("neighborhood",e.neighborhood,{shouldValidate:!0}),L("city",e.city,{shouldValidate:!0}),L("state",e.state,{shouldValidate:!0}),L("street",e.street,{shouldValidate:!0}))},enabled:U("zipCode")?.length===9}),(0,Y.a)({queryKey:["companyAddress",U("companyZipCode")],queryFn:async()=>{let e=await (0,P.x)((0,c.p4)(U("companyZipCode")));e&&(L("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),L("companyStreet",e.street,{shouldValidate:!0}),L("companyCity",e.city,{shouldValidate:!0}),L("companyState",e.state,{shouldValidate:!0}))},enabled:U("companyZipCode")?.length===9}),(0,o.useEffect)(()=>{L("isSCP","SCP"===e)},[e]);let W=(0,o.useMemo)(()=>{if(U("initDate")&&D){let e=(0,w.H)({investDate:D,startDate:U("initDate")});return M()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[U("initDate"),D]);(0,o.useEffect)(()=>{W&&L("endDate",W,{shouldValidate:!0})},[W,L]);let handleInputChange=(e,r)=>{O(a=>a.map(a=>a.generatedId===e?{...a,taxValue:r}:a))};return t.jsx("div",{children:(0,t.jsxs)("form",{action:"",onSubmit:F(a=>{if(!r)return b.Am.warn("Aceite os termos para liberar a cria\xe7\xe3o do contrato!");let t=Number(a.yield.replace(",",".")),s={amount:(0,S.Z)(a.value),monthlyRate:t,durationInMonths:Number(a.term),paymentMethod:a.purchaseWith,endDate:M()(W,"DD/MM/YYYY").format("YYYY-MM-DD"),profile:a.profile,quotaQuantity:"SCP"===e?Number(a.amountQuotes):void 0,isDebenture:"s"===a.isDebenture},i={bank:a.bank,agency:a.agency,account:a.accountNumber,pix:a.pix},o={street:a.street,city:a.city,state:a.state,neighborhood:a.neighborhood,postalCode:(0,c.p4)(a.zipCode),number:a.number,complement:a.complement},n={fullName:(0,V.Z)(a.ownerName),cpf:(0,c.p4)(a.ownerDocument),rg:a.rg,issuingAgency:a.issuer,nationality:a.placeOfBirth,occupation:(0,V.Z)(a.occupation),birthDate:a.dtBirth,email:a.email,phone:`55${(0,c.p4)(a.phoneNumber)}`,motherName:(0,V.Z)(a.motherName),address:o},l={street:a.companyStreet,city:a.companyCity,state:a.companyState,neighborhood:a.companyNeighborhood,postalCode:(0,c.p4)(a.companyZipCode),number:a.companyNumber,complement:a.companyComplement},d={corporateName:a.name,cnpj:(0,c.p4)(a.document),type:a.companyType,address:l,representative:n},m={personType:"PJ",contractType:e,advisors:"advisors"===k?T.map(e=>({advisorId:e.id,rate:Number(String(e.taxValue).replace(",","."))})):[],investment:s,bankAccount:i,company:d,role:q.name};R.mutate(m,{onSuccess:()=>{O([]),Q(),L("initDate",M()().format("YYYY-MM-DD"))}})}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[t.jsx(N.Z,{title:"Dados Pessoais - Representante",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(n.Z,{register:z,name:"ownerName",width:"300px",error:!!$.ownerName,errorMessage:$?.ownerName?.message,label:"Nome"}),t.jsx(n.Z,{register:z,name:"ownerDocument",width:"200px",error:!!$.ownerDocument,errorMessage:$?.ownerDocument?.message,label:"CPF",setValue:e=>L("ownerDocument",(0,c.VL)(e||""),{shouldValidate:!0})}),t.jsx(n.Z,{register:z,name:"rg",width:"200px",error:!!$.rg,errorMessage:$?.rg?.message,label:"RG"}),t.jsx(n.Z,{register:z,name:"issuer",width:"200px",error:!!$.issuer,errorMessage:$?.issuer?.message,label:"Org\xe3o emissor"}),t.jsx(n.Z,{register:z,name:"placeOfBirth",width:"200px",error:!!$.placeOfBirth,errorMessage:$?.placeOfBirth?.message,label:"Nacionalidade"}),t.jsx(n.Z,{register:z,name:"occupation",width:"200px",error:!!$.occupation,errorMessage:$?.occupation?.message,label:"Ocupa\xe7\xe3o"}),t.jsx(n.Z,{register:z,name:"motherName",width:"250px",error:!!$.motherName,errorMessage:$?.motherName?.message,label:"Nome da m\xe3e"}),(0,t.jsxs)("div",{style:{width:"200px"},children:[(0,t.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",t.jsx("b",{className:"text-red-500 font-light text-sm",children:$.dtBirth&&`- ${$.dtBirth.message}`})]}),t.jsx("input",{...z("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let r=e.target;r.value.length>10&&(r.value=r.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${$.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),t.jsx(n.Z,{width:"200px",register:z,name:"phoneNumber",error:!!$.phoneNumber,errorMessage:$?.phoneNumber?.message,label:"Celular",maxLength:15,setValue:e=>L("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})}),t.jsx(n.Z,{register:z,name:"email",width:"300px",error:!!$.email,errorMessage:$?.email?.message,label:"E-mail"}),t.jsx(n.Z,{register:z,name:"zipCode",width:"200px",error:!!$.zipCode,errorMessage:$?.zipCode?.message,label:"CEP",setValue:e=>{i(e),L("zipCode",(0,c.Tc)(e),{shouldValidate:!0})}}),t.jsx(n.Z,{register:z,name:"neighborhood",width:"300px",error:!!$.neighborhood,errorMessage:$?.neighborhood?.message,label:"Bairro"}),t.jsx(n.Z,{register:z,name:"street",width:"300px",error:!!$.street,errorMessage:$?.street?.message,label:"Rua"}),t.jsx(n.Z,{register:z,name:"city",width:"200px",error:!!$.city,errorMessage:$?.city?.message,label:"Cidade"}),t.jsx(n.Z,{register:z,maxLength:2,setValue:e=>L("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!$.state,errorMessage:$?.state?.message,label:"Estado"}),t.jsx(n.Z,{register:z,name:"number",width:"200px",error:!!$.number,errorMessage:$?.number?.message,label:"N\xfamero"}),t.jsx(n.Z,{register:z,name:"complement",width:"200px",error:!!$.complement,errorMessage:$?.complement?.message,label:"Complemento"})]})}),t.jsx(N.Z,{title:"Dados da empresa",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(n.Z,{register:z,name:"name",width:"400px",error:!!$.name,errorMessage:$?.name?.message,label:"Raz\xe3o Social"}),t.jsx(n.Z,{register:z,name:"document",width:"200px",error:!!$.document,errorMessage:$?.document?.message,label:"CNPJ",setValue:e=>L("document",(0,c.PK)(e||""),{shouldValidate:!0})}),t.jsx(u.Z,{width:"200px",name:"companyType",register:z,options:E,error:!!$.companyType,errorMessage:$?.companyType?.message,label:"Tipo"}),t.jsx(n.Z,{register:z,name:"companyZipCode",width:"200px",error:!!$.companyZipCode,errorMessage:$?.companyZipCode?.message,label:"CEP",setValue:e=>{h(e),L("companyZipCode",(0,c.Tc)(e),{shouldValidate:!0})}}),t.jsx(n.Z,{register:z,name:"companyNeighborhood",width:"300px",error:!!$.companyNeighborhood,errorMessage:$?.companyNeighborhood?.message,label:"Bairro"}),t.jsx(n.Z,{register:z,name:"companyStreet",width:"300px",error:!!$.companyStreet,errorMessage:$?.companyStreet?.message,label:"Rua"}),t.jsx(n.Z,{register:z,name:"companyCity",width:"200px",error:!!$.companyCity,errorMessage:$?.companyCity?.message,label:"Cidade"}),t.jsx(n.Z,{register:z,maxLength:2,setValue:e=>L("companyState",String(e).toUpperCase(),{shouldValidate:!0}),name:"companyState",width:"150px",error:!!$.companyState,errorMessage:$?.companyState?.message,label:"Estado"}),t.jsx(n.Z,{register:z,name:"companyNumber",width:"200px",error:!!$.companyNumber,errorMessage:$?.companyNumber?.message,label:"N\xfamero"}),t.jsx(n.Z,{register:z,name:"companyComplement",width:"200px",error:!!$.companyComplement,errorMessage:$?.companyComplement?.message,label:"Complemento"})]})}),t.jsx(N.Z,{title:"Dados de Investimento",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(n.Z,{register:z,name:"value",width:"200px",error:!!$.value,errorMessage:$?.value?.message,label:"Valor",setValue:e=>L("value",(0,c.Ht)(e||""),{shouldValidate:!0})}),t.jsx(n.Z,{register:z,type:"text",name:"term",width:"250px",error:!!$.term,errorMessage:$?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),t.jsx(n.Z,{register:z,type:"text",name:"yield",width:"250px",error:!!$.yield,errorMessage:$?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2,5"}),t.jsx(u.Z,{width:"200px",name:"purchaseWith",register:z,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!$?.purchaseWith,errorMessage:$?.purchaseWith?.message,label:"Comprar com"}),"SCP"===e&&t.jsx(t.Fragment,{children:t.jsx(n.Z,{register:z,type:"number",name:"amountQuotes",width:"150px",error:!!$.amountQuotes,errorMessage:$?.amountQuotes?.message,label:"Quantidade de cotas"})}),t.jsx(n.Z,{type:"date",register:z,disabled:!0,minDate:M()().format("YYYY-MM-DD"),name:"initDate",width:"200px",error:!!$.initDate,errorMessage:$?.initDate?.message,label:"Inicio do contrato"}),t.jsx(n.Z,{type:"text",register:z,name:"endDate",value:W,width:"200px",disabled:!0,label:"Final do contrato"}),t.jsx(u.Z,{width:"200px",name:"profile",register:z,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!$.profile,errorMessage:$?.profile?.message,label:"Perfil"}),t.jsx(u.Z,{width:"100px",name:"isDebenture",register:z,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!$.isDebenture,errorMessage:$?.isDebenture?.message,label:"Deb\xeanture"})]})}),"advisors"===k&&t.jsx(N.Z,{title:"Adicionar Assessor",children:(0,t.jsxs)("div",{children:[T.length>0?T?.map((e,r)=>t.jsxs("div",{className:"flex justify-between items-end gap-4 mb-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(j.Z,{label:"Selecione um Assessor",items:_,value:"",setValue:()=>{},loading:B,handleChange:a=>{let t=T.filter(r=>r.generatedId===e.generatedId)[0],s={generatedId:t.generatedId,id:a.id,taxValue:Number(a.rate)};T[r]=s,O([...T])}})}),t.jsx("div",{className:"flex-1",children:t.jsx(f.Z,{label:"Adicione a Taxa - em %",id:`advisor-${r}`,name:"",value:String(e.taxValue),type:"text",onChange:r=>handleInputChange(e.generatedId,r.target.value)})}),t.jsx("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=T.filter((e,a)=>a!==r);O(e)},children:t.jsx(y.Z,{width:20})})]},r)):t.jsx("div",{children:t.jsx("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),t.jsx("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:()=>{O([...T,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},children:t.jsx(v.Z,{width:25,color:"#fff"})})]})}),t.jsx(N.Z,{title:"Dados bancarios",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(n.Z,{register:z,name:"bank",width:"300px",error:!!$.bank,errorMessage:$?.bank?.message,label:"Banco"}),t.jsx(n.Z,{register:z,name:"agency",width:"200px",error:!!$.agency,errorMessage:$?.agency?.message,label:"Ag\xeancia"}),t.jsx(n.Z,{register:z,name:"accountNumber",width:"200px",error:!!$.accountNumber,errorMessage:$?.accountNumber?.message,label:"Conta"}),t.jsx(n.Z,{register:z,name:"pix",width:"250px",error:!!$.pix,errorMessage:$?.pix?.message,label:"Pix"})]})}),t.jsx(N.Z,{title:"Observa\xe7\xf5es",children:t.jsx(p.Z,{name:"observations",register:z})}),t.jsx(N.Z,{title:"Dados para Dep\xf3sito",children:(0,t.jsxs)("div",{className:"m-auto border-none",children:[t.jsx("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,t.jsxs)("div",{className:"mb-5",children:[t.jsx("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),t.jsx("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,t.jsxs)("div",{className:"flex mt-5",children:[t.jsx("input",{type:"checkbox",checked:r,className:"cursor-pointer",onChange:()=>a(!r)}),t.jsx("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),t.jsx("div",{className:"md:w-52 mb-10",children:t.jsx(m.Z,{label:"Enviar",size:"lg",loading:R.isPending,disabled:!r||R.isPending||!G||!W})})]})})}let PhysicalRegister_useCreateContract=()=>(0,A.D)({mutationFn:async e=>await g.Z.post("/contract/manual",e),onSuccess:()=>{let e=D().tz.guess(),r=D()().tz(e),a=r.hour();a>=9&&a<18?b.Am.success("Conta pr\xe9 cadastrada com sucesso!"):b.Am.success("Contrato criado com sucesso. Informamos que, por ter sido realizado fora do hor\xe1rio comercial, o registro cont\xe1bil sera processado somente no pr\xf3ximo dia \xfatil",{delay:6e3})},onError:e=>{(0,h.Z)(e,"Tivemos um erro ao cadastrar a conta")}}),PhysicalRegister_useGetAcessors=()=>{let e=(0,I.e)();return(0,Y.a)({queryKey:["acessors"],queryFn:()=>g.Z.get("/wallets/broker/advisors").then(e=>e.data),enabled:"broker"===e.name})};function PhysicalRegister({modalityContract:e}){let[r,a]=(0,o.useState)(!1),[s,i]=(0,o.useState)(""),[g,h]=(0,o.useState)(""),[C,Z]=(0,o.useState)([{generatedId:crypto.randomUUID(),id:"",taxValue:""}]),D=(0,I.e)(),A=String(localStorage.getItem("typeCreateContract")),E=PhysicalRegister_useCreateContract(),{data:T=[],isLoading:O}=PhysicalRegister_useGetAcessors(),{register:q,handleSubmit:k,watch:R,setValue:_,reset:B,formState:{errors:z,isValid:F}}=(0,d.cI)({defaultValues:{initDate:M()().format("YYYY-MM-DD")},resolver:(0,l.X)(x.$r),mode:"all"}),{isLoading:U}=(0,Y.a)({queryKey:["address",R("zipCode")],queryFn:async()=>{let e=await (0,P.x)((0,c.p4)(R("zipCode")));e&&(_("neighborhood",e.neighborhood,{shouldValidate:!0}),_("city",e.city,{shouldValidate:!0}),_("state",e.state,{shouldValidate:!0}),_("street",e.street,{shouldValidate:!0}))},enabled:R("zipCode")?.length===9}),L=R("term"),Q=(0,o.useMemo)(()=>"advisors"!==A?[]:C.map(e=>({advisorId:e.id,rate:Number(String(e.taxValue).replace(",","."))})),[C,A]);(0,o.useEffect)(()=>{_("isSCP","SCP"===e)},[e,_]);let handleAdvisorChange=(e,r)=>{Z(a=>a.map(a=>a.generatedId===e?{...a,taxValue:r}:a))},$=(0,o.useMemo)(()=>{if(R("initDate")&&L){let e=(0,w.H)({investDate:L,startDate:R("initDate")});return M()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[R("initDate"),L]);return(0,o.useEffect)(()=>{$&&_("endDate",$,{shouldValidate:!0})},[$,_]),t.jsx("div",{children:(0,t.jsxs)("form",{action:"",onSubmit:k(a=>{if(!r)return b.Am.error("Aceite os termos para liberar a cria\xe7\xe3o do contrato!");let t={personType:"PF",contractType:e,advisors:Q,investment:{amount:(0,S.Z)(a.value),monthlyRate:Number(a.yield.replace(",",".")),durationInMonths:Number(a.term),paymentMethod:a.purchaseWith,endDate:M()($,"DD/MM/YYYY").format("YYYY-MM-DD"),profile:a.profile,quotaQuantity:"SCP"===e?Number(a.amountQuotes):void 0,isDebenture:"s"===a.isDebenture},bankAccount:{bank:a.bank,agency:a.agency,account:a.accountNumber,pix:a.pix,type:"CORRENTE"},individual:{fullName:(0,V.Z)(a.name),cpf:(0,c.p4)(a.document),rg:a.rg,issuingAgency:a.issuer,nationality:a.placeOfBirth,occupation:(0,V.Z)(a.occupation),birthDate:a.dtBirth,email:a.email,phone:`55${(0,c.p4)(a.phoneNumber)}`,motherName:(0,V.Z)(a.motherName),address:{street:a.street,neighborhood:a.neighborhood,city:a.city,state:a.state,postalCode:(0,c.p4)(a.zipCode),number:a.number}},role:D.name};E.mutate(t,{onSuccess:()=>{Z([]),B(),_("initDate",M()().format("YYYY-MM-DD"))}})}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[t.jsx(N.Z,{title:"Dados Pessoais",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(n.Z,{register:q,name:"name",width:"300px",error:!!z.name,errorMessage:z?.name?.message,label:"Nome completo"}),t.jsx(n.Z,{register:q,name:"document",width:"200px",error:!!z.document,errorMessage:z?.document?.message,label:"CPF",setValue:e=>_("document",(0,c.VL)(e||""),{shouldValidate:!0})}),t.jsx(n.Z,{register:q,name:"rg",width:"200px",error:!!z.rg,errorMessage:z?.rg?.message,label:"Identidade"}),t.jsx(n.Z,{register:q,name:"issuer",width:"200px",error:!!z.issuer,errorMessage:z?.issuer?.message,label:"Org\xe3o emissor"}),t.jsx(n.Z,{register:q,name:"placeOfBirth",width:"200px",error:!!z.placeOfBirth,errorMessage:z?.placeOfBirth?.message,label:"Nacionalidade"}),t.jsx(n.Z,{register:q,name:"occupation",width:"200px",error:!!z.occupation,errorMessage:z?.occupation?.message,label:"Ocupa\xe7\xe3o"}),t.jsx(n.Z,{width:"200px",register:q,name:"phoneNumber",maxLength:15,error:!!z.phoneNumber,errorMessage:z?.phoneNumber?.message,label:"Celular",setValue:e=>_("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})}),(0,t.jsxs)("div",{style:{width:"200px"},children:[(0,t.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",t.jsx("b",{className:"text-red-500 font-light text-sm",children:z.dtBirth&&`- ${z.dtBirth.message}`})]}),t.jsx("input",{...q("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let r=e.target;r.value.length>10&&(r.value=r.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${z.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),t.jsx(n.Z,{register:q,name:"email",width:"300px",error:!!z.email,errorMessage:z?.email?.message,label:"E-mail"}),t.jsx(n.Z,{register:q,name:"motherName",width:"300px",error:!!z.motherName,errorMessage:z?.motherName?.message,label:"Nome da m\xe3e"}),t.jsx(n.Z,{register:q,name:"zipCode",width:"200px",error:!!z.zipCode,errorMessage:z?.zipCode?.message,label:"CEP",setValue:e=>{let r=(0,c.Tc)(e);i(r),_("zipCode",r,{shouldValidate:!0})}}),t.jsx(n.Z,{register:q,name:"neighborhood",width:"200px",error:!!z.neighborhood,errorMessage:z?.neighborhood?.message,label:"Bairro"}),t.jsx(n.Z,{register:q,name:"street",width:"300px",error:!!z.street,errorMessage:z?.street?.message,label:"Rua"}),t.jsx(n.Z,{register:q,name:"city",width:"200px",error:!!z.city,errorMessage:z?.city?.message,label:"Cidade"}),t.jsx(n.Z,{register:q,maxLength:2,setValue:e=>_("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!z.state,errorMessage:z?.state?.message,label:"Estado"}),t.jsx(n.Z,{register:q,name:"number",width:"200px",error:!!z.number,errorMessage:z?.number?.message,label:"N\xfamero"}),t.jsx(n.Z,{register:q,name:"complement",width:"200px",error:!!z.complement,errorMessage:z?.complement?.message,label:"Complemento"})]})}),t.jsx(N.Z,{title:"Dados de Investimento",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(n.Z,{register:q,name:"value",width:"200px",error:!!z.value,errorMessage:z?.value?.message,label:"Valor",setValue:e=>_("value",(0,c.Ht)(e||""),{shouldValidate:!0})}),t.jsx(n.Z,{register:q,type:"text",name:"term",width:"250px",error:!!z.term,errorMessage:z?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),t.jsx(n.Z,{register:q,type:"text",name:"yield",width:"250px",error:!!z.yield,errorMessage:z?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),t.jsx(u.Z,{width:"200px",name:"purchaseWith",register:q,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!z?.purchaseWith,errorMessage:z?.purchaseWith?.message,label:"Comprar com"}),"SCP"===e&&t.jsx(t.Fragment,{children:t.jsx(n.Z,{register:q,type:"number",name:"amountQuotes",width:"150px",error:!!z.amountQuotes,errorMessage:z?.amountQuotes?.message,label:"Quantidade de cotas"})}),t.jsx(n.Z,{type:"date",register:q,disabled:!0,minDate:M()().format("YYYY-MM-DD"),name:"initDate",width:"200px",error:!!z.initDate,errorMessage:z?.initDate?.message,label:"Inicio do contrato"}),t.jsx(n.Z,{type:"date",register:q,value:$?M()($,"DD/MM/YYYY").format("YYYY-MM-DD"):"",name:"endDate",width:"200px",disabled:!0,label:"Final do contrato"}),t.jsx(u.Z,{width:"200px",name:"profile",register:q,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!z.profile,errorMessage:z?.profile?.message,label:"Perfil"}),t.jsx(u.Z,{width:"100px",name:"isDebenture",register:q,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!z.isDebenture,errorMessage:z?.isDebenture?.message,label:"Deb\xeanture"})]})}),"advisors"===A&&t.jsx(N.Z,{title:"Adicionar Assessor",children:(0,t.jsxs)("div",{children:[C.length>0?C?.map((e,r)=>t.jsxs("div",{className:"flex justify-between items-end gap-4 mb-4",children:[t.jsx("div",{className:"flex-1",children:t.jsx(j.Z,{label:"Selecione um Assessor",items:T,value:"",setValue:()=>{},loading:O,handleChange:a=>{let t=C.filter(r=>r.generatedId===e.generatedId)[0],s={generatedId:t.generatedId,id:a.id,taxValue:Number(a.rate)};C[r]=s,Z([...C])}})}),t.jsx("div",{className:"flex-1",children:t.jsx(f.Z,{label:"Adicione a Taxa - em %",id:"",name:"",value:String(e.taxValue),type:"text",onChange:r=>handleAdvisorChange(e.generatedId,r.target.value)})}),t.jsx("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=C.filter((e,a)=>a!==r);Z(e)},children:t.jsx(y.Z,{width:20})})]},r)):t.jsx("div",{children:t.jsx("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),t.jsx("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:()=>{Z([...C,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},children:t.jsx(v.Z,{width:25,color:"#fff"})})]})}),t.jsx(N.Z,{title:"Dados bancarios",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(n.Z,{register:q,name:"bank",width:"300px",error:!!z.bank,errorMessage:z?.bank?.message,label:"Banco"}),t.jsx(n.Z,{register:q,name:"agency",width:"200px",error:!!z.agency,errorMessage:z?.agency?.message,label:"Ag\xeancia"}),t.jsx(n.Z,{register:q,name:"accountNumber",width:"200px",error:!!z.accountNumber,errorMessage:z?.accountNumber?.message,label:"Conta"}),t.jsx(n.Z,{register:q,name:"pix",width:"250px",error:!!z.pix,errorMessage:z?.pix?.message,label:"Pix"})]})}),t.jsx(N.Z,{title:"Observa\xe7\xf5es",children:t.jsx(p.Z,{name:"observations",register:q})}),t.jsx(N.Z,{title:"Dados para Dep\xf3sito",children:(0,t.jsxs)("div",{className:"m-auto border-none",children:[t.jsx("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,t.jsxs)("div",{className:"mb-5",children:[t.jsx("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),t.jsx("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,t.jsxs)("div",{className:"flex mt-5",children:[t.jsx("input",{type:"checkbox",checked:r,className:"cursor-pointer",onChange:()=>a(!r)}),t.jsx("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),t.jsx("div",{className:"md:w-52 mb-10",children:t.jsx(m.Z,{label:"Enviar",size:"lg",loading:E.isPending,disabled:!r||E.isPending||!F||!$})})]})})}var T=a(80223);function Register(){let[e,r]=(0,o.useState)("PF"),[a,n]=(0,o.useState)("MUTUO");return(0,t.jsxs)("div",{children:[t.jsx(s.Z,{}),t.jsx(i.Z,{children:(0,t.jsxs)("div",{className:"md:px-10",children:[t.jsx(N.Z,{title:"Tipo de contrato",children:(0,t.jsxs)("div",{className:"flex gap-4 items-end",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-white mb-1",children:"Tipo de contrato"}),t.jsx(T.Z,{value:e,onChange:e=>r(e.target.value),children:[{label:"PF",value:"PF"},{label:"PJ",value:"PJ"}].map((e,r)=>t.jsx("option",{value:e.value,children:e.label},r))})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-white mb-1",children:"Modalidade do Investimento"}),t.jsx(T.Z,{value:a,onChange:e=>n(e.target.value),children:[{label:"M\xfatuo",value:"MUTUO"},{label:"SCP",value:"SCP"}].map((e,r)=>t.jsx("option",{value:e.value,children:e.label},r))})]})]})}),(0,t.jsxs)("div",{children:["PF"===e&&t.jsx(PhysicalRegister,{modalityContract:a}),"PJ"===e&&t.jsx(BusinessRegister,{modalityContract:a})]})]})})]})}},69145:(e,r,a)=>{"use strict";a.d(r,{Z:()=>InputTextArea});var t=a(60080),s=a(9885);function InputTextArea({setValue:e,value:r,error:a,errorMessage:i,width:o="100%",register:n,name:l,placeholder:d="",className:m=""}){return(0,s.useEffect)(()=>{},[o]),t.jsx("div",{className:"input",style:{width:o},children:t.jsx("textarea",{...n&&{...n(l)},value:r&&r,placeholder:d,id:l,onChange:({target:r})=>{e&&e(r.value)},className:`${m} h-12 w-full min-h-40 p-2 text-white rounded-xl ${a?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})})}},54986:(e,r,a)=>{"use strict";a.d(r,{m:()=>isUnderage});var t=a(64731),s=a.n(t);function isUnderage(e){let r=s()(e),a=s()().diff(r,"years");return a<18}},18474:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>o,__esModule:()=>i,default:()=>l});var t=a(17536);let s=(0,t.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\meus-contratos\registro-manual\page.tsx`),{__esModule:i,$$typeof:o}=s,n=s.default,l=n}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),a=r.X(0,[4103,6426,4731,8813,5081,6558,3356,1808,7878,4944,7207,278,7669,2307,2411,2686,87,9327,2796,2434,8427],()=>__webpack_exec__(51841));module.exports=a})();