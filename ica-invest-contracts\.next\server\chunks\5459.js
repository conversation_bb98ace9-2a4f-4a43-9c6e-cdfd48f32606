"use strict";exports.id=5459,exports.ids=[5459],exports.modules={28168:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(9885);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"}))}),a=o},69888:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(9885);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}),a=o},25507:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(9885);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}),a=o},7589:(e,t,r)=>{r.d(t,{Dx:()=>J,VY:()=>G,aV:()=>Y,dk:()=>Q,fC:()=>K,h_:()=>X,x8:()=>ee,xz:()=>U});var n=r(9885),o=r(32840),a=r(80880),i=r(8718),l=r(76529),s=r(43408),d=r(75393),u=r(47699),c=r(53151),f=r(79752),p=r(43979),g=r(81046),m=r(32864),v=r(94348),h=r(71085),D=r(60080),x="Dialog",[w,b]=(0,i.b)(x),[y,j]=w(x),Dialog=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[f,p]=(0,s.T)({prop:o,defaultProp:a??!1,onChange:i,caller:x});return(0,D.jsx)(y,{scope:t,triggerRef:u,contentRef:c,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};Dialog.displayName=x;var R="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=j(R,r),l=(0,a.e)(t,i.triggerRef);return(0,D.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":getState(i.open),...n,ref:l,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});C.displayName=R;var E="DialogPortal",[k,I]=w(E,{forceMount:void 0}),DialogPortal=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=j(E,t);return(0,D.jsx)(k,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,D.jsx)(f.z,{present:r||i.open,children:(0,D.jsx)(c.h,{asChild:!0,container:a,children:e})}))})};DialogPortal.displayName=E;var M="DialogOverlay",O=n.forwardRef((e,t)=>{let r=I(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(M,e.__scopeDialog);return a.modal?(0,D.jsx)(f.z,{present:n||a.open,children:(0,D.jsx)(N,{...o,ref:t})}):null});O.displayName=M;var W=(0,h.Z8)("DialogOverlay.RemoveScroll"),N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(M,r);return(0,D.jsx)(m.f,{as:W,allowPinchZoom:!0,shards:[o.contentRef],children:(0,D.jsx)(p.WV.div,{"data-state":getState(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),F="DialogContent",_=n.forwardRef((e,t)=>{let r=I(F,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(F,e.__scopeDialog);return(0,D.jsx)(f.z,{present:n||a.open,children:a.modal?(0,D.jsx)(P,{...o,ref:t}):(0,D.jsx)(A,{...o,ref:t})})});_.displayName=F;var P=n.forwardRef((e,t)=>{let r=j(F,e.__scopeDialog),i=n.useRef(null),l=(0,a.e)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,v.Ry)(e)},[]),(0,D.jsx)(V,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;n&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),A=n.forwardRef((e,t)=>{let r=j(F,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,D.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let n=t.target,i=r.triggerRef.current?.contains(n);i&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),V=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,c=j(F,r),f=n.useRef(null),p=(0,a.e)(t,f);return(0,g.EW)(),(0,D.jsxs)(D.Fragment,{children:[(0,D.jsx)(u.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,D.jsx)(d.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":getState(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,D.jsxs)(D.Fragment,{children:[(0,D.jsx)(TitleWarning,{titleId:c.titleId}),(0,D.jsx)(DescriptionWarning,{contentRef:f,descriptionId:c.descriptionId})]})]})}),Z="DialogTitle",T=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(Z,r);return(0,D.jsx)(p.WV.h2,{id:o.titleId,...n,ref:t})});T.displayName=Z;var L="DialogDescription",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(L,r);return(0,D.jsx)(p.WV.p,{id:o.descriptionId,...n,ref:t})});S.displayName=L;var B="DialogClose",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(B,r);return(0,D.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})});function getState(e){return e?"open":"closed"}$.displayName=B;var z="DialogTitleWarning",[H,q]=(0,i.k)(z,{contentName:F,titleName:Z,docsSlug:"dialog"}),TitleWarning=({titleId:e})=>{let t=q(z),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{if(e){let t=document.getElementById(e);t||console.error(r)}},[r,e]),null},DescriptionWarning=({contentRef:e,descriptionId:t})=>{let r=q("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");if(t&&r){let e=document.getElementById(t);e||console.warn(o)}},[o,e,t]),null},K=Dialog,U=C,X=DialogPortal,Y=O,G=_,J=T,Q=S,ee=$}};