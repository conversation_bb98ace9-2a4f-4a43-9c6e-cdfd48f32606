"use strict";exports.id=2434,exports.ids=[2434],exports.modules={22434:(t,r,e)=>{function isValidateCPF(t){if(11!==(t=t.replace(/[^\d]+/g,"")).length||/^(\d)\1{10}$/.test(t))return!1;let calcularDigitoVerificador=t=>{let r=0,e=t.length+1;for(let i=0;i<t.length;i++)r+=parseInt(t.charAt(i),10)*e--;let i=11-r%11;return i>9?0:i},r=t.substring(0,9),e=calcularDigitoVerificador(r),i=calcularDigitoVerificador(r+e);return t===r+e+i}function isValidateCNPJ(t){if(14!==(t=t.replace(/[^\d]+/g,"")).length||/^(\d)\1{13}$/.test(t))return!1;let calcularDigitoVerificador=(t,r)=>{let e=0;for(let i=0;i<t.length;i++)e+=parseInt(t.charAt(i),10)*r[i];let i=11-e%11;return i>9?0:i},r=t.substring(0,12),e=calcularDigitoVerificador(r,[5,4,3,2,9,8,7,6,5,4,3,2]),i=calcularDigitoVerificador(r+e,[6,5,4,3,2,9,8,7,6,5,4,3,2]);return t===r+e+i}e.d(r,{p:()=>isValidateCPF,w:()=>isValidateCNPJ})}};