(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9440],{6085:function(e,t,a){!function(e){e.defineLocale("pt-br",{months:"jane<PERSON>_fevereiro_mar\xe7o_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_ter\xe7a-feira_quarta-feira_quinta-feira_sexta-feira_s\xe1bado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_s\xe1b".split("_"),weekdaysMin:"do_2\xaa_3\xaa_4\xaa_5\xaa_6\xaa_s\xe1".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [\xe0s] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [\xe0s] HH:mm"},calendar:{sameDay:"[Hoje \xe0s] LT",nextDay:"[Amanh\xe3 \xe0s] LT",nextWeek:"dddd [\xe0s] LT",lastDay:"[Ontem \xe0s] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[\xdaltimo] dddd [\xe0s] LT":"[\xdaltima] dddd [\xe0s] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"h\xe1 %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um m\xeas",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%d\xba",invalidDate:"Data inv\xe1lida"})}(a(2067))},2692:function(e,t,a){Promise.resolve().then(a.bind(a,7139))},7139:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return PagamentosPrevistos}});var r=a(7437),n=a(7059),s=a(3877),l=a(1980),o=a(8637),i=a(3401),d=a(5465),c=a(4568),m=a(6654),u=a(2067),x=a.n(u),f=a(2265);a(6085);var h=a(1458),p=a(5968),v=a(3014);let Y=[{label:"Janeiro",value:"1"},{label:"Fevereiro",value:"2"},{label:"Mar\xe7o",value:"3"},{label:"Abril",value:"4"},{label:"Maio",value:"5"},{label:"Junho",value:"6"},{label:"Julho",value:"7"},{label:"Agosto",value:"8"},{label:"Setembro",value:"9"},{label:"Outubro",value:"10"},{label:"Novembro",value:"11"},{label:"Dezembro",value:"12"}];var j=a(3256),g=a(4765),b=a(8700);function PagamentosPrevistos(){x().locale("pt-br");let[e,t]=(0,f.useState)(1),[a,u]=(0,f.useState)(!1),[w,M]=(0,f.useState)(""),[N,y]=(0,f.useState)(0),[D,_]=(0,f.useState)(x()().toString()),[P,S]=(0,f.useState)(String(x()().month()+1)),[L,k]=(0,f.useState)(String(x()().year())),[Z,C]=(0,f.useState)(x()().startOf("month").format("YYYY-MM-DD")),[F,O]=(0,f.useState)(x()().endOf("month").format("YYYY-MM-DD")),[E,A]=(0,f.useState)(!1),[T,I]=(0,f.useState)(!1),[R,B]=(0,f.useState)(),[H,z]=(0,f.useState)({total:0,lastPage:1,perPage:0}),V=(0,j.e)(),getPayments=()=>{A(!0),c.Z.get("income-payment/simulation",{params:{paymentStart:Z||x()().startOf("month").format("YYYY-MM-DD"),paymentEnd:F||Z,page:e,perPage:10,document:""!==w?(0,p.p4)(w):void 0}}).then(e=>{B(e.data.data),y(e.data.totalYieldSum),z({total:e.data.total,lastPage:e.data.lastPage,perPage:e.data.limit}),t(e.data.page)}).catch(e=>{(0,m.Z)(e,"Erro ao buscar pagamentos previstos")}).finally(()=>A(!1))};return(0,f.useEffect)(()=>{getPayments()},[e,P,L,Z,F]),(0,r.jsxs)("div",{children:[(0,r.jsx)(s.Z,{}),(0,r.jsx)(o.Z,{children:(0,r.jsxs)("div",{className:"text-white",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("h1",{className:"text-white text-center text-2xl",children:"Pagamentos Previstos"}),(0,r.jsxs)("div",{className:"border-[#FF9900] border rounded-2xl p-6 mt-5 text-center",children:[(0,r.jsx)("p",{className:"text-sm mb-2",children:"Total de Pagamentos"}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:(0,h.Z)(N)})]}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"border-[#FF9900] border rounded-2xl p-4 mt-5 text-center",children:(0,r.jsxs)("p",{className:"text-sm capitalize",children:[x()(D).format("MMMM")," / ",x()(D).format("YYYY")]})})})]}),(0,r.jsxs)(d.Z,{children:[(0,r.jsxs)("div",{className:"w-full p-2 flex justify-end gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-1 w-full justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"md:w-4/12",children:(0,r.jsx)(l.Z,{handleSearch:getPayments,placeholder:"Pesquisar por CPF/CNPJ",setValue:e=>{(0,p.p4)(e).length<=11?M((0,p.VL)(e)):M((0,p.PK)(e))},value:w})}),(0,r.jsx)("div",{children:(0,r.jsx)(b.z,{className:"bg-orange-linear",loading:T,size:"sm",onClick:()=>{I(!0),c.Z.post("income-payment/simulation/report",{},{params:{ownerRoleId:V.roleId,paymentStart:Z,paymentEnd:F,document:""!==w?(0,p.p4)(w):void 0}}).then(e=>{if(""===e.data.url)return v.Am.warning("N\xe3o foram encontrados dados dispon\xedveis para a gera\xe7\xe3o do relat\xf3rio.");window.open(e.data.url,"_blanck")}).catch(e=>{(0,m.Z)(e,"Tivemos um erro ao gerar o relat\xf3rio")}).finally(()=>I(!1))},children:"Relat\xf3rio"})})]}),(0,r.jsx)(n.Z,{activeModal:a,setActiveModal:u,isNew:!0,hidenButton:!0,handleSearch:()=>{getPayments(),_("".concat(L,"-").concat(1===P.length?"0".concat(P):D))},children:(0,r.jsxs)("div",{className:"w-[300px]",children:[(0,r.jsxs)("div",{className:"flex justify-between gap-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm mb-1",children:"M\xeas"}),(0,r.jsx)(g.Z,{value:P,onChange:e=>{let{target:t}=e;S(t.value),C(x()("".concat(L,"-").concat(t.value),"YYYY-MM").startOf("month").format("YYYY-MM-DD")),O(x()("".concat(L,"-").concat(t.value),"YYYY-MM").endOf("month").format("YYYY-MM-DD"))},className:"w-36 pl-3",children:Y.map((e,t)=>(0,r.jsx)("option",{value:e.value,children:e.label},t))})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm mb-1",children:"Ano"}),(0,r.jsx)(g.Z,{value:L,onChange:e=>{let{target:t}=e;k(t.value),C(x()("".concat(t.value,"-").concat(P),"YYYY-MM").startOf("month").format("YYYY-MM-DD")),O(x()("".concat(t.value,"-").concat(P),"YYYY-MM").endOf("month").format("YYYY-MM-DD"))},className:"w-36 pl-3",children:(function(e){let{currentYear:t,quantityYears:a}=e,r=t-a,n=Array.from({length:t+a-r+1},(e,t)=>{let a=(r+t).toString();return{label:a,value:a}});return n})({currentYear:x()().year(),quantityYears:10}).map((e,t)=>(0,r.jsx)("option",{value:e.value,children:e.label},t))})]})]}),""!==P&&(0,r.jsxs)("div",{className:"flex justify-between mt-3 gap-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-xs mb-1",children:"Data Inicial"}),(0,r.jsx)("input",{value:Z,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:e=>{let{target:t}=e;if(x()(t.value).isAfter(F))return v.Am.warning("A data inicial n\xe3o pode ser posterior \xe0 data final!");C(t.value)},type:"date",min:x()("".concat(L,"-").concat(P),"YYYY-MM").startOf("month").format("YYYY-MM-DD"),max:x()("".concat(L,"-").concat(P),"YYYY-MM").endOf("month").format("YYYY-MM-DD")})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-xs mb-1",children:"Data Final"}),(0,r.jsx)("input",{value:F,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:e=>{let{target:t}=e;if(x()(t.value,"YYYY-MM-DD").isBefore(x()(Z,"YYYY-MM-DD")))return v.Am.warning("A data final n\xe3o pode ser anterior \xe0 data inicial!");O(t.value)},type:"date",min:x()(Z,"YYYY-MM").startOf("month").format("YYYY-MM-DD"),max:x()("".concat(L,"-").concat(P),"YYYY-MM").endOf("month").format("YYYY-MM-DD")})]})]}),(0,r.jsx)("div",{className:"my-3",children:!!(Z!==x()().startOf("month").format("YYYY-MM-DD")||F!==x()().endOf("month").format("YYYY-MM-DD"))&&(0,r.jsx)(b.z,{variant:"secondary",onClick:()=>{u(!1),t(1),C(x()().startOf("month").format("YYYY-MM-DD")),O(x()().endOf("month").format("YYYY-MM-DD")),S(String(x()().month()+1)),k(String(x()().year()))},className:"w-full",children:"Resetar"})})]})})]}),(0,r.jsx)(i.Z,{loading:E,pagination:{lastPage:H.lastPage,page:e,perPage:H.perPage,setPage:t,totalItems:String(H.total)},data:R||[],headers:[{title:"Nome",component:"investor",width:"200px"},{title:"CPF/CNPJ",component:"document",render:e=>(0,r.jsx)("p",{children:String(e).length<=11?(0,p.VL)(String(e)):(0,p.PK)(String(e))})},{title:"E-mail",component:"email",width:"250px"},{title:"Rentabilidade",component:"totalYield",render:e=>(0,r.jsx)("p",{children:(0,h.Z)(Number(e))})},{title:"Valor do Aporte",component:"currentInvested",render:e=>(0,r.jsx)("p",{children:(0,h.Z)(Number(e))})},{title:"Data pagamento",component:"paymentDate",render:e=>(0,r.jsx)("p",{children:x()(e).format("DD/MM/YYYY")})}]})]})]})})]})}},9715:function(e,t,a){"use strict";a.d(t,{Z:function(){return Pagination}});var r=a(7437),n=a(13),s=a(3217);function Pagination(e){let{lastPage:t,page:a,setPage:l,totalItems:o,perPage:i,loading:d=!1}=e;return d?(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:"Carregando..."})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(n.Z,{color:"#555",width:15})}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818]",children:"..."}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(s.Z,{color:"#555",width:15})})]})]}):(isNaN(t),(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:(()=>{let e=parseInt(o,10);if(0===e)return"Nenhum resultado";let t=(a-1)*i+1,r=Math.min(a*i,e);return"Exibindo ".concat(t," a ").concat(r," de ").concat(e," resultados")})()})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(a>1?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>a>1&&l(a-1),children:(0,r.jsx)(n.Z,{color:"#fff",width:15})}),(0,r.jsx)("div",{className:"flex gap-2 select-none",children:(()=>{let e=new Set;e.add(1),e.add(t),a>1&&e.add(a-1),e.add(a),a<t&&e.add(a+1);let n=Array.from(e).sort((e,t)=>e-t);return n.map((e,t,n)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[t>0&&n[t-1]!==e-1&&(0,r.jsx)("div",{className:"flex items-center justify-center",children:"..."}),Number.isFinite(e)&&(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer bg-[#262626] ".concat(a===e?"text-[#FF9900]":"text-white"),onClick:()=>l(e),children:e})]},e))})()}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(a<t?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>a<t&&l(a+1),children:(0,r.jsx)(s.Z,{color:"#fff",width:15})})]})]}))}},5465:function(e,t,a){"use strict";a.d(t,{Z:function(){return TableFormat}});var r=a(7437);function TableFormat(e){let{children:t}=e;return(0,r.jsx)("div",{className:"rounded-t-md bg-[#1C1C1C] w-full text-white mt-5 overflow-x-auto rounded-b-md border border-[#FF9900] min-h-[400px] flex flex-col mb-10",children:t})}},3401:function(e,t,a){"use strict";var r=a(7437);a(2265);var n=a(8689),s=a(9715),l=a(7805);t.Z=e=>{var t;let{data:a,headers:o,loading:i,pagination:d,selectable:c=!1,checked:m=!1,setChecked:u=()=>{}}=e;return(0,r.jsxs)("div",{className:"bg-[#1C1C1C] w-full text-white min-h-[350px] h-full relative flex flex-col",children:[(0,r.jsx)("div",{className:"flex-1 mb-1 h-full",children:(0,r.jsx)("div",{className:"w-full overflow-auto",children:(0,r.jsxs)("table",{className:"min-w-[700px] w-full table-auto",children:[(0,r.jsxs)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900] relative",children:[c&&(0,r.jsx)("div",{className:"absolute p-1 pl-5 md:pt-2 pt-4 md:pl-4 z-10 select-none",children:(0,r.jsx)("div",{className:"w-5 h-5 border rounded-md border-[#FF9900] cursor-pointer flex items-center justify-center",onClick:()=>{u(!m)},children:m&&(0,r.jsx)(l.Z,{width:20,color:"#1EF97C"})})}),(0,r.jsx)("tr",{className:"w-full",children:o.map((e,t)=>(0,r.jsx)("th",{className:"py-2 pl-4 min-w-[".concat(null==e?void 0:e.width,"]"),children:(0,r.jsx)("p",{className:"font-bold text-sm ".concat(e.position&&"text-".concat(e.position)),children:e.title})},t))})]}),(0,r.jsx)("tbody",{className:"w-full",children:i?(0,r.jsx)("tr",{className:"",children:o.map(e=>(0,r.jsx)("td",{className:"p-1",children:(0,r.jsx)(n.j,{height:"25px"})},e.component))}):(null!==(t=null==a?void 0:a.length)&&void 0!==t?t:0)>0?a.map((e,t)=>(0,r.jsx)("tr",{className:"",children:o.map((t,a)=>(0,r.jsx)("td",{style:{width:t.width},className:"text-sm py-2 pl-4",children:(null==t?void 0:t.render)?t.render(e[t.component],e):(0,r.jsx)("p",{style:{width:t.width},className:"".concat(t.position&&"text-".concat(t.position)," truncate whitespace-nowrap overflow-hidden"),children:e[t.component]})},t.component))},t)):(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("p",{className:"py-2 px-4 text-center pt-10 absolute left-[50%] translate-x-[-50%]",children:"Nenhum dado encontrado"})})})]})})}),d&&(0,r.jsx)(s.Z,{lastPage:d.lastPage,page:d.page,perPage:d.perPage,setPage:d.setPage,totalItems:d.totalItems,loading:i})]})}},6654:function(e,t,a){"use strict";a.d(t,{Z:function(){return returnError}});var r=a(3014);function returnError(e,t){var a,n,s,l;let o=(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.message)||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.error);if(Array.isArray(o))return o.forEach(e=>{r.Am.error(e,{toastId:e})}),o.join("\n");if("string"==typeof o)return r.Am.error(o,{toastId:o}),o;if("object"==typeof o&&null!==o){let e=Object.values(o).flat().join("\n");return r.Am.error(e,{toastId:e}),e}return r.Am.error(t,{toastId:t}),t}},1458:function(e,t,a){"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}a.d(t,{A:function(){return cleanValue},Z:function(){return formatValue}})},7805:function(e,t,a){"use strict";var r=a(2265);let n=r.forwardRef(function({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))});t.Z=n},13:function(e,t,a){"use strict";var r=a(2265);let n=r.forwardRef(function({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))});t.Z=n},3217:function(e,t,a){"use strict";var r=a(2265);let n=r.forwardRef(function({title:e,titleId:t,...a},n){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))});t.Z=n}},function(e){e.O(0,[6990,8276,5371,6946,3151,2971,7864,1744],function(){return e(e.s=2692)}),_N_E=e.O()}]);