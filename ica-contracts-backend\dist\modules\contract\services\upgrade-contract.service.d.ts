import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { Repository } from 'typeorm';
import { EditNewContractDto } from '../dto/edit-new-contract.dto';
import { CreateNewContractService } from 'src/apis/ica-contract-service/services/create-new-contract.service';
import { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
export declare class UpgradeContractService {
    private readonly contractRepository;
    private readonly ownerRoleRelationRepository;
    private readonly createNewContractService;
    private readonly createNotificationService;
    private readonly logger;
    constructor(contractRepository: Repository<ContractEntity>, ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, createNewContractService: CreateNewContractService, createNotificationService: CreateNotificationService);
    perform(contractId: string, data: EditNewContractDto, userId: string): Promise<{
        id: string;
        status: string;
    }>;
}
