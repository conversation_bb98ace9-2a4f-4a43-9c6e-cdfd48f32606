{"version": 3, "file": "notification.entity.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/entities/notification.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAUiB;AAEjB,6EAAuE;AACvE,uDAAmD;AACnD,uDAAmD;AAEnD,IAAY,oBAIX;AAJD,WAAY,oBAAoB;IAC9B,mEAA2C,CAAA;IAC3C,qDAA6B,CAAA;IAC7B,6DAAqC,CAAA;AACvC,CAAC,EAJW,oBAAoB,oCAApB,oBAAoB,QAI/B;AAGM,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;CAyF9B,CAAA;AAzFY,gDAAkB;AAE7B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;8CACpB;AAMX;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,oBAAoB;KAC3B,CAAC;;gDACyB;AAG3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;iDACZ;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;;uDACZ;AAMpB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,KAAK;KACf,CAAC;;kDACc;AAKhB;IAHC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,aAAa;KACpB,CAAC;;sDACiB;AAMnB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAC,aAAa;QAClB,QAAQ,EAAE,IAAI;KACf,CAAC;;sDACiB;AAMnB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAC,aAAa;QAClB,QAAQ,EAAE,IAAI;KACf,CAAC;;sDACgB;AAOlB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,SAAS;KAChB,CAAC;;yDAC2B;AAG7B;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;qDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;qDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BAC9B,IAAI;qDAAC;AAOhB;IALC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oDAAuB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC;IAC1E,IAAA,oBAAU,EAAC;QACV,IAAI,EAAE,WAAW;QACjB,oBAAoB,EAAE,IAAI;KAC3B,CAAC;;mEACyD;AAO3D;IALC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oDAAuB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC;IAC9E,IAAA,oBAAU,EAAC;QACV,IAAI,EAAE,aAAa;QACnB,oBAAoB,EAAE,IAAI;KAC3B,CAAC;;oDAC0C;AAO5C;IALC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oDAAuB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC;IAC3E,IAAA,oBAAU,EAAC;QACV,IAAI,EAAE,8BAA8B;QACpC,oBAAoB,EAAE,IAAI;KAC3B,CAAC;;kEACwD;AAO1D;IALC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,gCAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;IAC7D,IAAA,oBAAU,EAAC;QACV,IAAI,EAAE,aAAa;QACnB,oBAAoB,EAAE,IAAI;KAC3B,CAAC;;oDACiC;AAOnC;IALC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,gCAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC;IACrE,IAAA,oBAAU,EAAC;QACV,IAAI,EAAE,aAAa;QACnB,oBAAoB,EAAE,IAAI;KAC3B,CAAC;;oDACiC;6BAxFxB,kBAAkB;IAD9B,IAAA,gBAAM,EAAC,cAAc,CAAC;GACV,kBAAkB,CAyF9B", "sourcesContent": ["import {\r\n  Column,\r\n  CreateDate<PERSON><PERSON>umn,\r\n  DeleteDateColumn,\r\n  Entity,\r\n  JoinColumn,\r\n  ManyToOne,\r\n  PrimaryGeneratedColumn,\r\n  Relation,\r\n  UpdateDateColumn,\r\n} from 'typeorm';\r\n\r\nimport { OwnerRoleRelationEntity } from './owner-role-relation.entity';\r\nimport { ContractEntity } from './contract.entity';\r\nimport { AddendumEntity } from './addendum.entity';\r\n\r\nexport enum NotificationTypeEnum {\r\n  DUPLICATED_DOCUMENT = 'DUPLICATED_DOCUMENT',\r\n  NEW_CONTRACT = 'NEW_CONTRACT',\r\n  INCLUDE_ADDITIVE = 'INCLUDE_ADDITIVE',\r\n}\r\n\r\n@Entity('notification')\r\nexport class NotificationEntity {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({\r\n    name: 'type',\r\n    enum: NotificationTypeEnum,\r\n  })\r\n  type: NotificationTypeEnum;\r\n\r\n  @Column({ name: 'title' })\r\n  title: string;\r\n\r\n  @Column({ name: 'description' })\r\n  description: string;\r\n\r\n  @Column({\r\n    name: 'viewed',\r\n    default: false,\r\n  })\r\n  viewed: boolean;\r\n\r\n  @Column({\r\n    name: 'contract_id',\r\n  })\r\n  contractId: string;\r\n\r\n  @Column({\r\n    name:'addendum_id',\r\n    nullable: true,\r\n  })\r\n  addendumId: number;\r\n\r\n  @Column({\r\n    name:'investor_id',\r\n    nullable: true,\r\n  })\r\n  investorId: string\r\n\r\n  @Column({\r\n    name: 'contract_value',\r\n    nullable: true,\r\n    type: 'decimal',\r\n  })\r\n  contractValue: number | null;\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  @DeleteDateColumn({ name: 'deleted_at' })\r\n  deletedAt: Date;\r\n\r\n  @ManyToOne(() => OwnerRoleRelationEntity, (item) => item.userNotifications)\r\n  @JoinColumn({\r\n    name: 'broker_id',\r\n    referencedColumnName: 'id',\r\n  })\r\n  brokerOwnerRoleRelation: Relation<OwnerRoleRelationEntity>;\r\n\r\n  @ManyToOne(() => OwnerRoleRelationEntity, (item) => item.investorNotifications)\r\n  @JoinColumn({\r\n    name: 'investor_id',\r\n    referencedColumnName: 'id',\r\n  })\r\n  investor: Relation<OwnerRoleRelationEntity>;\r\n\r\n  @ManyToOne(() => OwnerRoleRelationEntity, (item) => item.adminNotifications)\r\n  @JoinColumn({\r\n    name: 'admin_owner_role_relation_id',\r\n    referencedColumnName: 'id',\r\n  })\r\n  adminOwnerRoleRelation: Relation<OwnerRoleRelationEntity>;\r\n\r\n  @ManyToOne(() => ContractEntity, (item) => item.notifications)\r\n  @JoinColumn({\r\n    name: 'contract_id',\r\n    referencedColumnName: 'id',\r\n  })\r\n  contract: Relation<ContractEntity>;\r\n\r\n  @ManyToOne(() => AddendumEntity, (item) => item.addendumNotifications)\r\n  @JoinColumn({\r\n    name: 'addendum_id',\r\n    referencedColumnName: 'id',\r\n  })\r\n  addendum: Relation<AddendumEntity>;\r\n}\r\n"]}