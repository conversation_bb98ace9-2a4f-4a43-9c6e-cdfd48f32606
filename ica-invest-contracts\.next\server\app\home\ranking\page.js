(()=>{var e={};e.id=141,e.ids=[141],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},89245:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>x,routeModule:()=>m,tree:()=>d});var r=s(73137),a=s(54647),i=s(4183),n=s.n(i),o=s(71775),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=r.AppPageRouteModule,d=["",{children:["home",{children:["ranking",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,16091)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\page.tsx"],p="/home/<USER>/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new c({definition:{kind:a.x.APP_PAGE,page:"/home/<USER>/page",pathname:"/home/<USER>",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},42766:(e,t,s)=>{Promise.resolve().then(s.bind(s,97697))},97697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Ranking});var r=s(60080),a=s(74352),i=s(97669),n=s(18109),o=s(47956),l=s(74644),c=s(85814),d=s(24577),x=s(45682),p=s(73294),u=s(57114),m=s(9885);function Ranking(){let e=localStorage.getItem("searchRanking"),{notificationModal:t}=(0,m.useContext)(x.Z),[s,h]=(0,m.useState)(1),g=(0,u.useRouter)(),[j,f]=(0,m.useState)(!1),[b,v]=(0,m.useState)(!1),[k,N]=(0,m.useState)({startData:"",endData:"",input:"",type:"",filterOptions:[{label:"Todos",value:""},{label:"Contratos SCP",value:"SCP"},{label:"Contratos M\xfatuo",value:"P2P"}]}),[y,P]=(0,m.useState)(),[_,q]=(0,m.useState)({total:0,lastPage:1,perPage:0}),getBrokers=()=>{f(!0),c.Z.get("/super-admin/active-brokers",{params:{page:s,limit:10,dateFrom:""!==k.startData?k.startData:void 0,dateTo:""!==k.endData?k.endData:void 0}}).then(e=>{q(e.data.meta),P(e.data.data)}).catch(e=>{(0,d.Z)(e,"N\xe3o foi possivel buscar o ranking de brokers")}).finally(()=>{f(!1)})},getAdvisors=()=>{f(!0),c.Z.get("/super-admin/active-advisors",{params:{page:s,limit:10,dateFrom:""!==k.startData?k.startData:void 0,dateTo:""!==k.endData?k.endData:void 0,typeContract:""!==k.type?k.type:void 0}}).then(e=>{q(e.data.meta),P(e.data.data)}).catch(e=>{(0,d.Z)(e,"N\xe3o foi possivel buscar o ranking de assessores")}).finally(()=>{f(!1)})};return(0,m.useEffect)(()=>{"broker"===e?getBrokers():getAdvisors()},[s]),(0,r.jsxs)("div",{className:`${t?"fixed w-full":"relative"}`,children:[r.jsx(i.Z,{}),r.jsx(o.Z,{children:(0,r.jsxs)("div",{className:"",children:[r.jsx("div",{children:(0,r.jsxs)("p",{className:"capitalize text-white text-center text-xl",children:["broker"===e?"Brokers":"Assessores"," Ativos - Ranking de Capta\xe7\xe3o"]})}),(0,r.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] w-full text-white mt-10 overflow-x-auto rounded-b-md border border-[#FF9900] min-h-[400px] flex flex-col",children:[r.jsx("div",{className:"flex w-full justify-end p-2",children:r.jsx(a.Z,{inputPlaceholder:"Pesquisar por nome",handleSearch:"broker"===e?getBrokers:getAdvisors,filterData:k,setFilterData:N,activeModal:b,setActiveModal:v})}),r.jsx("div",{className:"flex-1",children:(0,r.jsxs)("table",{className:"w-full",children:[r.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,r.jsxs)("tr",{className:"w-full",children:[r.jsx("th",{className:"py-2 max-w-[350px] text-center",children:r.jsx("p",{className:"font-bold text-sm ",children:"Nome"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Valor captado"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Ranking"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Perfil"})}),r.jsx("th",{className:""})]})}),r.jsx(r.Fragment,{children:r.jsx("tbody",{className:"w-full",children:j?(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[r.jsx("td",{className:"",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})})]}):y?.map((t,s)=>r.jsxs("tr",{className:"",children:[r.jsx("td",{className:"max-w-[350px]",children:r.jsx("p",{className:"text-sm text-center py-1 ",children:t.name})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:p.Z(t.totalValue||0)})}),r.jsx("td",{children:r.jsxs("p",{className:"text-sm text-center",children:[t.rank,"\xb0"]})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:"broker"===e?"Broker":"Assessor"})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center text-blue-500 select-none cursor-pointer",onClick:()=>{g.push(`/home/<USER>/${t.brokerId||t.advisorId}`)},children:"Ver mais"})})]},t.brokerId))})})]})}),r.jsx(n.Z,{lastPage:_.lastPage,page:s,perPage:_.perPage,setPage:h,totalItems:String(_.total)})]})]})})]})}},73294:(e,t,s)=>{"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}s.d(t,{A:()=>cleanValue,Z:()=>formatValue})},16091:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=s(17536);let a=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\home\ranking\page.tsx`),{__esModule:i,$$typeof:n}=a,o=a.default,l=o}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[4103,6426,4731,8813,8394,7207,278,7669,8109],()=>__webpack_exec__(89245));module.exports=s})();