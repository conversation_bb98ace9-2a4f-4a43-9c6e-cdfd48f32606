(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9834],{4279:function(e,t,r){(e.exports=r(1223)).tz.load(r(6564))},1223:function(e,t,r){var o,n,a;a=function(e){"use strict";void 0===e.version&&e.default&&(e=e.default);var t,r,o={},n={},a={},i={},s={};e&&"string"==typeof e.version||logError("Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/");var d=e.version.split("."),l=+d[0],u=+d[1];function charCodeToInt(e){return e>96?e-87:e>64?e-29:e-48}function unpackBase60(e){var t,r=0,o=e.split("."),n=o[0],a=o[1]||"",i=1,s=0,d=1;for(45===e.charCodeAt(0)&&(r=1,d=-1);r<n.length;r++)s=60*s+(t=charCodeToInt(n.charCodeAt(r)));for(r=0;r<a.length;r++)i/=60,s+=(t=charCodeToInt(a.charCodeAt(r)))*i;return s*d}function arrayToInt(e){for(var t=0;t<e.length;t++)e[t]=unpackBase60(e[t])}function mapIndices(e,t){var r,o=[];for(r=0;r<t.length;r++)o[r]=e[t[r]];return o}function unpack(e){var t=e.split("|"),r=t[2].split(" "),o=t[3].split(""),n=t[4].split(" ");return arrayToInt(r),arrayToInt(o),arrayToInt(n),function(e,t){for(var r=0;r<t;r++)e[r]=Math.round((e[r-1]||0)+6e4*e[r]);e[t-1]=1/0}(n,o.length),{name:t[0],abbrs:mapIndices(t[1].split(" "),o),offsets:mapIndices(r,o),untils:n,population:0|t[5]}}function Zone(e){e&&this._set(unpack(e))}function Country(e,t){this.name=e,this.zones=t}function OffsetAt(e){var t=e.toTimeString(),r=t.match(/\([a-z ]+\)/i);"GMT"===(r=r&&r[0]?(r=r[0].match(/[A-Z]/g))?r.join(""):void 0:(r=t.match(/[A-Z]{3,5}/g))?r[0]:void 0)&&(r=void 0),this.at=+e,this.abbr=r,this.offset=e.getTimezoneOffset()}function ZoneScore(e){this.zone=e,this.offsetScore=0,this.abbrScore=0}function sortZoneScores(e,t){return e.offsetScore!==t.offsetScore?e.offsetScore-t.offsetScore:e.abbrScore!==t.abbrScore?e.abbrScore-t.abbrScore:e.zone.population!==t.zone.population?t.zone.population-e.zone.population:t.zone.name.localeCompare(e.zone.name)}function normalizeName(e){return(e||"").toLowerCase().replace(/\//g,"_")}function addZone(e){var t,r,n,a;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)o[a=normalizeName(r=(n=e[t].split("|"))[0])]=e[t],i[a]=r,function(e,t){var r,o;for(arrayToInt(t),r=0;r<t.length;r++)s[o=t[r]]=s[o]||{},s[o][e]=!0}(a,n[2].split(" "))}function getZone(e,t){var r,a=o[e=normalizeName(e)];return a instanceof Zone?a:"string"==typeof a?(a=new Zone(a),o[e]=a,a):n[e]&&t!==getZone&&(r=getZone(n[e],getZone))?((a=o[e]=new Zone)._set(r),a.name=i[e],a):null}function addLink(e){var t,r,o,a;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)o=normalizeName((r=e[t].split("|"))[0]),a=normalizeName(r[1]),n[o]=a,i[o]=r[0],n[a]=o,i[a]=r[1]}function zoneExists(e){return zoneExists.didShowError||(zoneExists.didShowError=!0,logError("moment.tz.zoneExists('"+e+"') has been deprecated in favor of !moment.tz.zone('"+e+"')")),!!getZone(e)}function needsOffset(e){var t="X"===e._f||"x"===e._f;return!!(e._a&&void 0===e._tzm&&!t)}function logError(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}function tz(t){var r,o=Array.prototype.slice.call(arguments,0,-1),n=arguments[arguments.length-1],a=e.utc.apply(null,o);return!e.isMoment(t)&&needsOffset(a)&&(r=getZone(n))&&a.add(r.parse(a),"minutes"),a.tz(n),a}(l<2||2===l&&u<6)&&logError("Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js "+e.version+". See momentjs.com"),Zone.prototype={_set:function(e){this.name=e.name,this.abbrs=e.abbrs,this.untils=e.untils,this.offsets=e.offsets,this.population=e.population},_index:function(e){var t;if((t=function(e,t){var r,o=t.length;if(e<t[0])return 0;if(o>1&&t[o-1]===1/0&&e>=t[o-2])return o-1;if(e>=t[o-1])return -1;for(var n=0,a=o-1;a-n>1;)t[r=Math.floor((n+a)/2)]<=e?n=r:a=r;return a}(+e,this.untils))>=0)return t},countries:function(){var e=this.name;return Object.keys(a).filter(function(t){return -1!==a[t].zones.indexOf(e)})},parse:function(e){var t,r,o,n,a=+e,i=this.offsets,s=this.untils,d=s.length-1;for(n=0;n<d;n++)if(t=i[n],r=i[n+1],o=i[n?n-1:n],t<r&&tz.moveAmbiguousForward?t=r:t>o&&tz.moveInvalidForward&&(t=o),a<s[n]-6e4*t)return i[n];return i[d]},abbr:function(e){return this.abbrs[this._index(e)]},offset:function(e){return logError("zone.offset has been deprecated in favor of zone.utcOffset"),this.offsets[this._index(e)]},utcOffset:function(e){return this.offsets[this._index(e)]}},ZoneScore.prototype.scoreOffsetAt=function(e){this.offsetScore+=Math.abs(this.zone.utcOffset(e.at)-e.offset),this.zone.abbr(e.at).replace(/[^A-Z]/g,"")!==e.abbr&&this.abbrScore++},tz.version="0.5.48",tz.dataVersion="",tz._zones=o,tz._links=n,tz._names=i,tz._countries=a,tz.add=addZone,tz.link=addLink,tz.load=function(e){addZone(e.zones),addLink(e.links),function(e){var t,r,o,n;if(e&&e.length)for(t=0;t<e.length;t++)r=(n=e[t].split("|"))[0].toUpperCase(),o=n[1].split(" "),a[r]=new Country(r,o)}(e.countries),tz.dataVersion=e.version},tz.zone=getZone,tz.zoneExists=zoneExists,tz.guess=function(e){return(!r||e)&&(r=function(){try{var e=Intl.DateTimeFormat().resolvedOptions().timeZone;if(e&&e.length>3){var t=i[normalizeName(e)];if(t)return t;logError("Moment Timezone found "+e+" from the Intl api, but did not have that data loaded.")}}catch(e){}var r,o,n,a=function(){var e,t,r,o,n=new Date().getFullYear()-2,a=new OffsetAt(new Date(n,0,1)),i=a.offset,s=[a];for(o=1;o<48;o++)(r=new Date(n,o,1).getTimezoneOffset())!==i&&(s.push(e=function(e,t){for(var r,o;o=((t.at-e.at)/12e4|0)*6e4;)(r=new OffsetAt(new Date(e.at+o))).offset===e.offset?e=r:t=r;return e}(a,t=new OffsetAt(new Date(n,o,1)))),s.push(new OffsetAt(new Date(e.at+6e4))),a=t,i=r);for(o=0;o<4;o++)s.push(new OffsetAt(new Date(n+o,0,1))),s.push(new OffsetAt(new Date(n+o,6,1)));return s}(),d=a.length,l=function(e){var t,r,o,n,a=e.length,d={},l=[],u={};for(t=0;t<a;t++)if(o=e[t].offset,!u.hasOwnProperty(o)){for(r in n=s[o]||{})n.hasOwnProperty(r)&&(d[r]=!0);u[o]=!0}for(t in d)d.hasOwnProperty(t)&&l.push(i[t]);return l}(a),u=[];for(o=0;o<l.length;o++){for(n=0,r=new ZoneScore(getZone(l[o]),d);n<d;n++)r.scoreOffsetAt(a[n]);u.push(r)}return u.sort(sortZoneScores),u.length>0?u[0].zone.name:void 0}()),r},tz.names=function(){var e,t=[];for(e in i)i.hasOwnProperty(e)&&(o[e]||o[n[e]])&&i[e]&&t.push(i[e]);return t.sort()},tz.Zone=Zone,tz.unpack=unpack,tz.unpackBase60=unpackBase60,tz.needsOffset=needsOffset,tz.moveInvalidForward=!0,tz.moveAmbiguousForward=!1,tz.countries=function(){return Object.keys(a)},tz.zonesForCountry=function(e,t){if(!(e=a[e.toUpperCase()]||null))return null;var r=e.zones.sort();return t?r.map(function(e){var t=getZone(e);return{name:e,offset:t.utcOffset(new Date)}}):r};var c=e.fn;function abbrWrap(e){return function(){return this._z?this._z.abbr(this):e.call(this)}}function resetZoneWrap(e){return function(){return this._z=null,e.apply(this,arguments)}}e.tz=tz,e.defaultZone=null,e.updateOffset=function(t,r){var o,n=e.defaultZone;if(void 0===t._z&&(n&&needsOffset(t)&&!t._isUTC&&t.isValid()&&(t._d=e.utc(t._a)._d,t.utc().add(n.parse(t),"minutes")),t._z=n),t._z){if(16>Math.abs(o=t._z.utcOffset(t))&&(o/=60),void 0!==t.utcOffset){var a=t._z;t.utcOffset(-o,r),t._z=a}else t.zone(o,r)}},c.tz=function(t,r){if(t){if("string"!=typeof t)throw Error("Time zone name must be a string, got "+t+" ["+typeof t+"]");return this._z=getZone(t),this._z?e.updateOffset(this,r):logError("Moment Timezone has no data for "+t+". See http://momentjs.com/timezone/docs/#/data-loading/."),this}if(this._z)return this._z.name},c.zoneName=abbrWrap(c.zoneName),c.zoneAbbr=abbrWrap(c.zoneAbbr),c.utc=resetZoneWrap(c.utc),c.local=resetZoneWrap(c.local),c.utcOffset=(t=c.utcOffset,function(){return arguments.length>0&&(this._z=null),t.apply(this,arguments)}),e.tz.setDefault=function(t){return(l<2||2===l&&u<9)&&logError("Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js "+e.version+"."),e.defaultZone=t?getZone(t):null,e};var m=e.momentProperties;return"[object Array]"===Object.prototype.toString.call(m)?(m.push("_z"),m.push("_a")):m&&(m._z=null),e},e.exports?e.exports=a(r(2067)):(o=[r(2067)],void 0===(n=a.apply(t,o))||(e.exports=n))},9900:function(e,t,r){Promise.resolve().then(r.bind(r,9332))},9332:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return EditContract}});var o=r(7437),n=r(2265),a=r(8038),i=r(9891),s=r(3877),d=r(8637),l=r(5233),u=r(4033),c=r(4568),m=r(6654),p=r(4207),v=r(9701),h=r(1865),f=r(2875),g=r(5968),b=r(5554),x=r(3014),y=r(2067),Z=r.n(y),M=r(3277),w=r(3256),j=r(4984),C=r(5691);let D=C.Ry().shape({isSCP:C.O7(),name:C.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:C.Z_().required("Campo obrigat\xf3rio"),email:C.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),rg:C.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),phoneNumber:C.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:C.Z_().required("Campo obrigat\xf3rio"),motherName:C.Z_().min(3,"Nome da m\xe3e deve conter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),zipCode:C.Z_().required("Obrigat\xf3rio"),neighborhood:C.Z_().required("Obrigat\xf3rio"),state:C.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:C.Z_().required("Obrigat\xf3rio"),complement:C.Z_().default("").notRequired(),number:C.Z_().required("Obrigat\xf3rio"),street:C.Z_().required("Obrigat\xf3rio"),value:C.Z_().required("Obrigat\xf3rio"),term:C.Z_().required("Obrigat\xf3rio"),yield:C.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchasedWith:C.Z_().required("Obrigat\xf3rio"),isDebenture:C.Z_().required("Obrigat\xf3rio"),initDate:C.Z_().required("Obrigat\xf3rio"),endDate:C.Z_().required("Obrigat\xf3rio"),issuer:C.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:C.Z_().required("Campo obrigat\xf3rio"),occupation:C.Z_().required("Campo obrigat\xf3rio"),amountQuotes:C.Z_().when("isSCP",(e,t)=>!0===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),documentPdf:C.Z_().notRequired(),proofPayment:C.Z_().notRequired(),proofOfResidence:C.Z_().notRequired(),contract:C.Z_().notRequired()}).required(),E=C.Ry().shape({isSCP:C.O7(),email:C.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),phoneNumber:C.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:C.Z_().required("Campo obrigat\xf3rio"),rg:C.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Obrigat\xf3rio"),ownerName:C.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),ownerDocument:C.Z_().min(3,"O nome da m\xe3e deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),motherName:C.Z_().required("Campo obrigat\xf3rio"),issuer:C.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:C.Z_().required("Campo obrigat\xf3rio"),occupation:C.Z_().required("Campo obrigat\xf3rio"),name:C.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:C.Z_().required("Campo obrigat\xf3rio"),companyType:C.Z_().required("Obrigat\xf3rio"),zipCode:C.Z_().required("Obrigat\xf3rio"),neighborhood:C.Z_().required("Obrigat\xf3rio"),state:C.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:C.Z_().required("Obrigat\xf3rio"),complement:C.Z_().default("").notRequired(),number:C.Z_().required("Obrigat\xf3rio"),street:C.Z_().required("Obrigat\xf3rio"),value:C.Z_().required("Obrigat\xf3rio"),term:C.Z_().required("Obrigat\xf3rio"),yield:C.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchasedWith:C.Z_().required("Obrigat\xf3rio"),initDate:C.Z_().required("Obrigat\xf3rio"),endDate:C.Z_().required("Obrigat\xf3rio"),isDebenture:C.Z_().required("Obrigat\xf3rio"),amountQuotes:C.Z_().when("isSCP",(e,t)=>!0===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),companyNumber:C.Z_().required("Campo obrigat\xf3rio"),companyComplement:C.Z_().default("").notRequired(),companyCity:C.Z_().required("Campo obrigat\xf3rio"),companyState:C.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Campo obrigat\xf3rio"),companyStreet:C.Z_().required("Campo obrigat\xf3rio"),companyZipCode:C.Z_().required("Campo obrigat\xf3rio"),companyNeighborhood:C.Z_().required("Campo obrigat\xf3rio"),documentPdf:C.Z_().notRequired(),proofPayment:C.Z_().notRequired(),proofOfResidence:C.Z_().notRequired(),contract:C.Z_().notRequired()}).required();var O=r(7412),N=r(6121),S=r(1458),z=r(8610),_=r(3588),R=r(919);let q=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}];function BusinessEditing(e){var t,r,a,s,d,y,C,D,Y,F,P,A,T,I,k,B,V,L,U,W,Q,G,K,H,X,J,$,ee,et,er,eo,en,ea,ei,es,ed,el,eu,ec,em,ep,ev,eh;let{modalityContract:ef,contractData:eg}=e,{id:eb}=(0,u.useParams)(),[ex,ey]=(0,n.useState)(""),[eZ,eM]=(0,n.useState)(),[ew,ej]=(0,n.useState)(),[eC,eD]=(0,n.useState)(),[eE,eO]=(0,n.useState)(),{navigation:eN}=(0,z.H)(),[eS,ez]=(0,n.useState)([]),[e_,eR]=(0,n.useState)(),[eq,eY]=(0,n.useState)(),[eF,eP]=(0,n.useState)(),[eA,eT]=(0,n.useState)(),eI=(0,w.e)(),{register:ek,handleSubmit:eB,watch:eV,setError:eL,setValue:eU,reset:eW,formState:{errors:eQ}}=(0,h.cI)({resolver:(0,v.X)(E)}),eG=eV("term"),eK=(0,_.D)({mutationFn:async e=>{let t=await c.Z.put("/account/resubmit-contract/",e);return t.data},onSuccess:()=>{x.Am.success("Contrato editado com sucesso!"),eN("/meus-contratos")},onError:e=>{(0,m.Z)(e,"Erro ao editar o contrato!")}}),eH=(0,n.useCallback)(()=>{if(!(null==eg?void 0:eg.investment))return"";let e=Z()(eg.investment.start),t=Z()(eg.investment.end);return String(t.diff(e,"months"))},[eg]),eX=(0,n.useCallback)(()=>{if(null==eg?void 0:eg.investor){var e,t,r,o,n,a;eU("name",eg.investor.companyName),eU("document",eg.investor.document),eU("companyType",eg.investor.businessType),eU("companyZipCode",eg.investor.address.zipcode),eU("companyNeighborhood",eg.investor.address.neighborhood),eU("companyStreet",eg.investor.address.street),eU("companyCity",eg.investor.address.city),eU("companyState",eg.investor.address.state),eU("companyNumber",eg.investor.address.number),eU("companyComplement",null!==(a=null==eg?void 0:null===(t=eg.investor)||void 0===t?void 0:null===(e=t.address)||void 0===e?void 0:e.complement)&&void 0!==a?a:""),eU("ownerName",eg.investor.responsibleOwner.name),eU("ownerDocument",eg.investor.responsibleOwner.document),eU("rg",eg.investor.responsibleOwner.rg),eU("issuer",eg.investor.responsibleOwner.issuingAgency),eU("placeOfBirth",eg.investor.responsibleOwner.nationality),eU("occupation",eg.investor.responsibleOwner.occupation),eU("phoneNumber",(0,g.gP)(eg.investor.responsibleOwner.phone)),eU("dtBirth",eg.investor.responsibleOwner.birthDate),eU("email",eg.investor.responsibleOwner.email),eU("motherName",eg.investor.responsibleOwner.motherName),eU("zipCode",eg.investor.responsibleOwner.address.zipcode),eU("neighborhood",eg.investor.responsibleOwner.address.neighborhood),eU("street",eg.investor.responsibleOwner.address.street),eU("city",eg.investor.responsibleOwner.address.city),eU("state",eg.investor.responsibleOwner.address.state),eU("number",eg.investor.responsibleOwner.address.number),eU("complement",eg.investor.responsibleOwner.address.complement),eU("value",String((0,S.A)(Number(eg.investment.value)))),eU("yield",Number(eg.investment.yield)),eU("term",eH()),eU("initDate",eg.investment.start),eU("endDate",eg.investment.end),eU("amountQuotes",(null==eg?void 0:null===(r=eg.investment)||void 0===r?void 0:r.quotesAmount)||""),eU("isDebenture",(null==eg?void 0:null===(o=eg.investment)||void 0===o?void 0:o.isdebenture)?"s":"n"),eU("purchasedWith",(null==eg?void 0:null===(n=eg.investment)||void 0===n?void 0:n.purchasedWith)||"")}},[eg,eH,eU]);(0,n.useEffect)(()=>{eX()},[eX]),(0,n.useEffect)(()=>{eU("isSCP","scp"===ef)},[ef]);let eJ=(0,n.useMemo)(()=>{if(eV("initDate")&&eG){let e=(0,R.H)({investDate:eG,startDate:eV("initDate")});return Z()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[eV("initDate"),eG]);(0,n.useEffect)(()=>{eJ&&eU("endDate",eJ,{shouldValidate:!0})},[eJ,eU]);let e$=(0,n.useCallback)(e=>{var t;let r=new FormData;r.append("personType","PJ"),r.append("contractType","scp"===ef?"SCP":"MUTUO"),r.append("role",eI.name),r.append("contractId",eb),r.append("bankAccount[accountType]","CORRENTE"),r.append("investment[amount]",String((0,M.Z)(e.value))),r.append("investment[monthlyRate]",String(e.yield)),r.append("investment[durationInMonths]",e.term),r.append("investment[startDate]","".concat((0,N.l)(e.initDate))),r.append("investment[endDate]","".concat(eJ?Z()(eJ,"DD/MM/YYYY").format("YYYY-MM-DD"):"")),r.append("investment[isDebenture]",String("s"===e.isDebenture)),"scp"===ef&&r.append("investment[quotaQuantity]",e.amountQuotes||""),r.append("company[corporateName]",e.name),r.append("company[cnpj]",(0,g.p4)(e.document)),r.append("company[type]",e.companyType),r.append("investment[paymentMethod]",e.purchasedWith),r.append("investment[profile]","moderate"),r.append("company[address][street]",e.companyStreet),r.append("company[address][city]",e.companyCity),r.append("company[address][state]",e.companyState),r.append("company[address][neighborhood]",e.companyNeighborhood),r.append("company[address][postalCode]",(0,g.p4)(e.companyZipCode)),r.append("company[address][number]",e.companyNumber),r.append("company[address][complement]",null!==(t=e.companyComplement)&&void 0!==t?t:""),r.append("company[representative][fullName]",e.ownerName),r.append("company[representative][cpf]",(0,g.p4)(e.ownerDocument)),r.append("company[representative][rg]",e.rg),r.append("company[representative][issuingAgency]",e.issuer),r.append("company[representative][nationality]",e.placeOfBirth),r.append("company[representative][occupation]",e.occupation),r.append("company[representative][birthDate]",e.dtBirth),r.append("company[representative][email]",e.email),r.append("company[representative][phone]","55".concat((0,g.p4)(e.phoneNumber))),r.append("company[representative][motherName]",e.motherName),r.append("company[representative][address][street]",e.street),r.append("company[representative][address][city]",e.city),r.append("company[representative][address][state]",e.state),r.append("company[representative][address][neighborhood]",e.neighborhood),r.append("company[representative][address][postalCode]",(0,g.p4)(e.zipCode)),r.append("company[representative][address][number]",e.number),r.append("company[representative][address][complement]",e.complement||""),eZ&&r.append("contract",eZ[0]),ew&&r.append("proofOfPayment",ew[0]),eC&&r.append("personalDocument",eC[0]),eE&&r.append("proofOfResidence",eE[0]),eK.mutateAsync(r)},[eZ,ew,eC,eE,eU,eb,ef,eI]);(0,i.a)({queryKey:["company-address",eV("companyZipCode")],queryFn:async()=>{let e=await (0,O.x)((0,g.p4)(eV("companyZipCode")));e&&(eU("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),eU("companyCity",e.city,{shouldValidate:!0}),eU("companyState",e.state,{shouldValidate:!0}),eU("companyStreet",e.street,{shouldValidate:!0}))},enabled:(null===(t=eV("companyZipCode"))||void 0===t?void 0:t.length)===9});let e0=(0,n.useCallback)(()=>{x.Am.info("Buscando dados da rejei\xe7\xe3o.",{toastId:"searchReasons"}),c.Z.get("/audit/contract/".concat(eb)).then(e=>{let t=e.data[0].rejectionReasons.reasons;ez(t)}).catch(e=>(0,m.Z)(e,"Erro ao buscar os motivos da rejei\xe7\xe3o")).finally(()=>x.Am.dismiss("searchReasons"))},[eb]),e3=(0,n.useCallback)(e=>{(null==e?void 0:e.length)>0&&e.forEach(e=>{eL(e.field,{type:"required",message:e.reason})})},[eL]);(0,n.useEffect)(()=>{e0()},[e0]),(0,n.useEffect)(()=>{e3(eS)},[eS,e3]);let e1=(0,n.useCallback)(e=>eS.find(t=>(null==t?void 0:t.field)===e),[eS]);return(0,o.jsx)("div",{children:(0,o.jsxs)("form",{action:"",onSubmit:eB(e$),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,o.jsx)(l.Z,{title:"Dados Pessoais - Representanteeeeee",children:(0,o.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"ownerName",width:"300px",error:!!eQ.ownerName,errorMessage:null==eQ?void 0:null===(r=eQ.ownerName)||void 0===r?void 0:r.message,label:"Nome"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"ownerDocument",width:"200px",error:!!eQ.ownerDocument,errorMessage:null==eQ?void 0:null===(a=eQ.ownerDocument)||void 0===a?void 0:a.message,label:"CPF",setValue:e=>eU("ownerDocument",(0,g.VL)(e||""))}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"rg",width:"200px",error:!!eQ.rg,errorMessage:null==eQ?void 0:null===(s=eQ.rg)||void 0===s?void 0:s.message,label:"RG"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"issuer",width:"200px",error:!!eQ.issuer,errorMessage:null==eQ?void 0:null===(d=eQ.issuer)||void 0===d?void 0:d.message,label:"Org\xe3o emissor"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"placeOfBirth",width:"200px",error:!!eQ.placeOfBirth,errorMessage:null==eQ?void 0:null===(y=eQ.placeOfBirth)||void 0===y?void 0:y.message,label:"Nacionalidade"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"occupation",width:"200px",error:!!eQ.occupation,errorMessage:null==eQ?void 0:null===(C=eQ.occupation)||void 0===C?void 0:C.message,label:"Ocupa\xe7\xe3o"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"motherName",width:"250px",error:!!eQ.motherName,errorMessage:null==eQ?void 0:null===(D=eQ.motherName)||void 0===D?void 0:D.message,label:"Nome da m\xe3e"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,type:"date",register:ek,name:"dtBirth",width:"200px",error:!!eQ.dtBirth,errorMessage:null==eQ?void 0:null===(Y=eQ.dtBirth)||void 0===Y?void 0:Y.message,label:"Data de Nascimento"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,width:"200px",register:ek,name:"phoneNumber",error:!!eQ.phoneNumber,errorMessage:null==eQ?void 0:null===(F=eQ.phoneNumber)||void 0===F?void 0:F.message,label:"Celular",maxLength:15,setValue:e=>eU("phoneNumber",(0,g.gP)(e||""))}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"email",width:"300px",error:!!eQ.email,errorMessage:null==eQ?void 0:null===(P=eQ.email)||void 0===P?void 0:P.message,label:"E-mail"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"zipCode",width:"200px",error:!!eQ.zipCode,errorMessage:null==eQ?void 0:null===(A=eQ.zipCode)||void 0===A?void 0:A.message,label:"CEP",setValue:e=>{eU("zipCode",(0,g.Tc)(e))}}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"neighborhood",width:"300px",error:!!eQ.neighborhood,errorMessage:null==eQ?void 0:null===(T=eQ.neighborhood)||void 0===T?void 0:T.message,label:"Bairro"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"street",width:"300px",error:!!eQ.street,errorMessage:null==eQ?void 0:null===(I=eQ.street)||void 0===I?void 0:I.message,label:"Rua"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"city",width:"200px",error:!!eQ.city,errorMessage:null==eQ?void 0:null===(k=eQ.city)||void 0===k?void 0:k.message,label:"Cidade"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,maxLength:2,setValue:e=>eU("state",String(e).toUpperCase()),name:"state",width:"150px",error:!!eQ.state,errorMessage:null==eQ?void 0:null===(B=eQ.state)||void 0===B?void 0:B.message,label:"Estado"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"number",width:"200px",error:!!eQ.number,errorMessage:null==eQ?void 0:null===(V=eQ.number)||void 0===V?void 0:V.message,label:"N\xfamero"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"complement",width:"200px",error:!!eQ.complement,errorMessage:null==eQ?void 0:null===(L=eQ.complement)||void 0===L?void 0:L.message,label:"Complemento"})]})}),(0,o.jsx)(l.Z,{title:"Dados da empresa",children:(0,o.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"name",width:"400px",error:!!eQ.name,errorMessage:null==eQ?void 0:null===(U=eQ.name)||void 0===U?void 0:U.message,label:"Raz\xe3o Social"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"document",width:"200px",error:!!eQ.document,errorMessage:null==eQ?void 0:null===(W=eQ.document)||void 0===W?void 0:W.message,label:"CNPJ",setValue:e=>eU("document",(0,g.PK)(e||""))}),(0,o.jsx)(b.Z,{width:"200px",name:"companyType",register:ek,options:q,error:!!eQ.companyType,errorMessage:null==eQ?void 0:null===(Q=eQ.companyType)||void 0===Q?void 0:Q.message,label:"Tipo"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"companyZipCode",width:"200px",error:!!eQ.companyZipCode,errorMessage:null==eQ?void 0:null===(G=eQ.companyZipCode)||void 0===G?void 0:G.message,label:"CEP",setValue:e=>{eU("companyZipCode",(0,g.Tc)(e))}}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"companyNeighborhood",width:"300px",error:!!eQ.companyNeighborhood,errorMessage:null==eQ?void 0:null===(K=eQ.companyNeighborhood)||void 0===K?void 0:K.message,label:"Bairro"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"companyStreet",width:"300px",error:!!eQ.companyStreet,errorMessage:null==eQ?void 0:null===(H=eQ.companyStreet)||void 0===H?void 0:H.message,label:"Rua"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"companyCity",width:"200px",error:!!eQ.companyCity,errorMessage:null==eQ?void 0:null===(X=eQ.companyCity)||void 0===X?void 0:X.message,label:"Cidade"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,maxLength:2,setValue:e=>eU("companyState",String(e).toUpperCase()),name:"companyState",width:"150px",error:!!eQ.companyState,errorMessage:null==eQ?void 0:null===(J=eQ.companyState)||void 0===J?void 0:J.message,label:"Estado"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"companyNumber",width:"200px",error:!!eQ.companyNumber,errorMessage:null==eQ?void 0:null===($=eQ.companyNumber)||void 0===$?void 0:$.message,label:"N\xfamero"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"companyComplement",width:"200px",error:!!eQ.companyComplement,errorMessage:null==eQ?void 0:null===(ee=eQ.companyComplement)||void 0===ee?void 0:ee.message,label:"Complemento"})]})}),(0,o.jsx)(l.Z,{title:"Dados de Investimento",children:(0,o.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,name:"value",width:"200px",error:!!eQ.value,errorMessage:null==eQ?void 0:null===(et=eQ.value)||void 0===et?void 0:et.message,label:"Valor",setValue:e=>eU("value",(0,g.Ht)(e||""))}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,type:"number",name:"term",width:"250px",error:!!eQ.term,errorMessage:null==eQ?void 0:null===(er=eQ.term)||void 0===er?void 0:er.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,type:"text",name:"yield",width:"250px",error:!!eQ.yield,errorMessage:null==eQ?void 0:null===(eo=eQ.yield)||void 0===eo?void 0:eo.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),(0,o.jsx)(b.Z,{width:"200px",name:"purchasedWith",register:ek,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!(null==eQ?void 0:eQ.purchasedWith),errorMessage:null==eQ?void 0:null===(en=eQ.purchasedWith)||void 0===en?void 0:en.message,label:"Comprar com"}),"scp"===ef&&(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:ek,type:"number",name:"amountQuotes",width:"150px",error:!!eQ.amountQuotes,errorMessage:null==eQ?void 0:null===(ea=eQ.amountQuotes)||void 0===ea?void 0:ea.message,label:"Quantidade de cotas"})}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,type:"date",register:ek,maxDate:Z()().format("YYYY-MM-DD"),name:"initDate",width:"200px",error:!!eQ.initDate,errorMessage:null==eQ?void 0:null===(ei=eQ.initDate)||void 0===ei?void 0:ei.message,label:"Inicio do contrato"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,type:"date",register:ek,name:"endDate",value:eJ?Z()(eJ,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"200px",label:"Final do contrato",disabled:!0}),(0,o.jsx)(b.Z,{width:"100px",name:"isDebenture",register:ek,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!eQ.isDebenture,errorMessage:null==eQ?void 0:null===(es=eQ.isDebenture)||void 0===es?void 0:es.message,label:"Deb\xeanture"})," "]})}),(0,o.jsx)(l.Z,{title:"Anexo de documentos",children:(0,o.jsx)("div",{children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Contrato"}),(0,o.jsx)(j.Z,{errorMessage:null===(ed=e1("contract"))||void 0===ed?void 0:ed.reason,disable:!(null===(el=e1("contract"))||void 0===el?void 0:el.field),onFileUploaded:eM,fileName:eA,onRemoveFile:()=>{eM(void 0),eT(void 0)}})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Comprovante"}),(0,o.jsx)("div",{children:(0,o.jsx)(j.Z,{disable:!(null===(eu=e1("proofPayment"))||void 0===eu?void 0:eu.field),errorMessage:null===(ec=e1("proofPayment"))||void 0===ec?void 0:ec.reason,onFileUploaded:ej,fileName:e_,onRemoveFile:()=>{ej(void 0),eR(void 0)}})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,o.jsx)("div",{children:(0,o.jsx)(j.Z,{errorMessage:null===(em=e1("documentPdf"))||void 0===em?void 0:em.reason,disable:!(null===(ep=e1("documentPdf"))||void 0===ep?void 0:ep.field),onFileUploaded:eD,fileName:eq,onRemoveFile:()=>{eD(void 0),eY(void 0)}})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,o.jsx)("div",{children:(0,o.jsx)(j.Z,{errorMessage:null===(ev=e1("proofOfResidence"))||void 0===ev?void 0:ev.reason,disable:!(null===(eh=e1("proofOfResidence"))||void 0===eh?void 0:eh.field),onFileUploaded:eO,fileName:eF,onRemoveFile:()=>{eO(void 0),eP(void 0)}})})]})]})})}),(0,o.jsx)("div",{className:"md:w-52 mb-10",children:(0,o.jsx)(f.Z,{label:"Enviar",size:"lg",loading:eK.isPending,disabled:eK.isPending||!eJ})})]})})}var functions_formatName=function(e){return e.toLowerCase().split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")};function PhysicalEditing(e){var t,r,a,i,s,d,y,C,E,_,R,q,Y,F,P,A,T,I,k,B,V,L,U,W,Q,G,K,H,X,J,$,ee,et;let{modalityContract:er,contractData:eo}=e,{id:en}=(0,u.useParams)(),[ea,ei]=(0,n.useState)(!1),[es,ed]=(0,n.useState)(""),[el,eu]=(0,n.useState)(""),[ec,em]=(0,n.useState)(""),[ep,ev]=(0,n.useState)([]),{navigation:eh}=(0,z.H)(),[ef,eg]=(0,n.useState)([]),[eb,ex]=(0,n.useState)([]),[ey,eZ]=(0,n.useState)(""),[eM,ew]=(0,n.useState)(!1),[ej,eC]=(0,n.useState)([]),[eD,eE]=(0,n.useState)(),[eO,eN]=(0,n.useState)(),[eS,ez]=(0,n.useState)(),[e_,eR]=(0,n.useState)(),[eq,eY]=(0,n.useState)(),[eF,eP]=(0,n.useState)(),[eA,eT]=(0,n.useState)(),[eI,ek]=(0,n.useState)(),eB=(0,w.e)(),{register:eV,handleSubmit:eL,setValue:eU,reset:eW,setError:eQ,formState:{errors:eG}}=(0,h.cI)({resolver:(0,v.X)(D)}),eK=(0,n.useCallback)(()=>{if(!(null==eo?void 0:eo.investment))return"";let e=Z()(eo.investment.start),t=Z()(eo.investment.end);return String(t.diff(e,"months"))},[eo]);(0,n.useEffect)(()=>{if(null==eo?void 0:eo.investor){var e;eU("name",eo.investor.name),eU("document",(0,g.VL)(eo.investor.document)),eU("rg",eo.investor.rg),eU("issuer",eo.investor.issuingAgency),eU("placeOfBirth",eo.investor.nationality),eU("occupation",eo.investor.occupation),eU("phoneNumber",(0,g.gP)(eo.investor.phone)),eU("dtBirth",eo.investor.birthDate),eU("email",eo.investor.email),eU("motherName",eo.investor.motherName),eU("zipCode",eo.investor.address.zipcode),eU("neighborhood",eo.investor.address.neighborhood),eU("street",eo.investor.address.street),eU("city",eo.investor.address.city),eU("state",eo.investor.address.state),eU("number",eo.investor.address.number),eU("complement",eo.investor.address.complement),eU("value",(0,S.A)(Number(eo.investment.value))),eU("yield",Number(eo.investment.yield)),eU("term",eK()),eU("initDate",eo.investment.start),eU("endDate",eo.investment.end),eU("amountQuotes",(null==eo?void 0:null===(e=eo.investment)||void 0===e?void 0:e.quotesAmount)||""),eU("purchasedWith",eo.investment.purchasedWith),eU("isDebenture",eo.investment.isdebenture?"s":"n")}},[eo]),(0,n.useEffect)(()=>{eU("isSCP","scp"===er)},[er]),(0,n.useEffect)(()=>{getReasons()},[]),(0,n.useEffect)(()=>{getCep(es||"")},[es]);let getCep=async e=>{let t=await (0,O.x)((0,g.p4)(e));null!==t&&(eU("neighborhood",t.neighborhood),eU("city",t.city),eU("state",t.state),eU("street",t.street))},getReasons=()=>{x.Am.info("Buscando dados da rejei\xe7\xe3o.",{toastId:"searchReasons"}),c.Z.get("/audit/contract/".concat(en)).then(e=>{let t=e.data[0].rejectionReasons.reasons;ev(t)}).catch(e=>(0,m.Z)(e,"Erro ao buscar os motivos da rejei\xe7\xe3o")).finally(()=>x.Am.dismiss("searchReasons"))};function getFieldDocument(e){let t=(null==ep?void 0:ep.find(t=>(null==t?void 0:t.field)===e))||void 0;return t}return(0,n.useEffect)(()=>{console.log(ep),(null==ep?void 0:ep.length)>0&&ep.map(e=>{eQ(e.field,{type:"required",message:e.reason})})},[ep]),(0,n.useEffect)(()=>{"broker"!==eB.name&&"advisor"!==eB.name&&c.Z.get("superadmin"===eB.name?"/wallets/list-brokers":"/wallets/admin/brokers").then(e=>{ex(e.data)}).catch(e=>{(0,m.Z)(e,"Erro ao buscar a lista de brokers")})},[]),(0,o.jsx)("div",{children:(0,o.jsxs)("form",{action:"",onSubmit:eL(e=>{ei(!0);let t=new FormData;t.append("role",eB.name),t.append("personType","PF"),t.append("contractType","scp"===er?"SCP":"MUTUO"),t.append("contractId",en),t.append("individual[fullName]",functions_formatName(String(e.name).trim())),t.append("individual[cpf]",(0,g.p4)(e.document).trim()),t.append("individual[rg]",e.rg),t.append("individual[issuingAgency]",e.issuer),t.append("individual[nationality]",e.placeOfBirth),t.append("individual[occupation]",functions_formatName(e.occupation)),t.append("individual[birthDate]",e.dtBirth),t.append("individual[email]",e.email),t.append("individual[phone]","55".concat((0,g.p4)(e.phoneNumber))),t.append("individual[motherName]",functions_formatName(e.motherName)),t.append("individual[address][street]",e.street),t.append("individual[address][neighborhood]",e.neighborhood),t.append("individual[address][city]",e.city),t.append("individual[address][state]",e.state),t.append("individual[address][postalCode]",(0,g.p4)(e.zipCode)),t.append("individual[address][number]",e.number),t.append("individual[address][complement]",e.complement),t.append("investment[profile]","moderate"),t.append("investment[amount]",String((0,M.Z)(e.value))),t.append("investment[monthlyRate]",e.yield),t.append("investment[durationInMonths]",e.term),t.append("investment[paymentMethod]",e.purchasedWith),t.append("investment[startDate]","".concat((0,N.l)(e.initDate))),t.append("investment[endDate]","".concat((0,N.l)(e.endDate))),t.append("investment[isDebenture]",String("s"===e.isDebenture)),"scp"===er&&t.append("investment[quotaQuantity]",e.amountQuotes),eD&&t.append("contract",eD[0]),eO&&t.append("proofOfPayment",eO[0]),eS&&t.append("personalDocument",eS[0]),e_&&t.append("proofOfResidence",e_[0]),c.Z.put("/account/resubmit-contract/",t).then(e=>{x.Am.success("Contrato editado com sucesso!"),eh("/meus-contratos")}).catch(e=>{(0,m.Z)(e,"Erro ao cadastrar o contrato!")}).finally(()=>ei(!1))}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,o.jsx)(l.Z,{title:"Dados Pessoais",children:(0,o.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"name",width:"300px",error:!!eG.name,errorMessage:null==eG?void 0:null===(t=eG.name)||void 0===t?void 0:t.message,label:"Nome completo"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"document",width:"200px",error:!!eG.document,errorMessage:null==eG?void 0:null===(r=eG.document)||void 0===r?void 0:r.message,label:"CPF",setValue:e=>eU("document",(0,g.VL)(e||""))}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"rg",width:"200px",error:!!eG.rg,errorMessage:null==eG?void 0:null===(a=eG.rg)||void 0===a?void 0:a.message,label:"Identidade"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"issuer",width:"200px",error:!!eG.issuer,errorMessage:null==eG?void 0:null===(i=eG.issuer)||void 0===i?void 0:i.message,label:"Org\xe3o emissor"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"placeOfBirth",width:"200px",error:!!eG.placeOfBirth,errorMessage:null==eG?void 0:null===(s=eG.placeOfBirth)||void 0===s?void 0:s.message,label:"Nacionalidade"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"occupation",width:"200px",error:!!eG.occupation,errorMessage:null==eG?void 0:null===(d=eG.occupation)||void 0===d?void 0:d.message,label:"Ocupa\xe7\xe3o"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,width:"200px",register:eV,name:"phoneNumber",maxLength:15,error:!!eG.phoneNumber,errorMessage:null==eG?void 0:null===(y=eG.phoneNumber)||void 0===y?void 0:y.message,label:"Celular",setValue:e=>eU("phoneNumber",(0,g.gP)(e||""))}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,type:"date",register:eV,name:"dtBirth",width:"200px",error:!!eG.dtBirth,errorMessage:null==eG?void 0:null===(C=eG.dtBirth)||void 0===C?void 0:C.message,label:"Data de Nascimento"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"email",width:"300px",error:!!eG.email,errorMessage:null==eG?void 0:null===(E=eG.email)||void 0===E?void 0:E.message,label:"E-mail"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"motherName",width:"300px",error:!!eG.motherName,errorMessage:null==eG?void 0:null===(_=eG.motherName)||void 0===_?void 0:_.message,label:"Nome da m\xe3e"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"zipCode",width:"200px",error:!!eG.zipCode,errorMessage:null==eG?void 0:null===(R=eG.zipCode)||void 0===R?void 0:R.message,label:"CEP",setValue:e=>{ed(e),eU("zipCode",(0,g.Tc)(e))}}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"neighborhood",width:"200px",error:!!eG.neighborhood,errorMessage:null==eG?void 0:null===(q=eG.neighborhood)||void 0===q?void 0:q.message,label:"Bairro"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"street",width:"300px",error:!!eG.street,errorMessage:null==eG?void 0:null===(Y=eG.street)||void 0===Y?void 0:Y.message,label:"Rua"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"city",width:"200px",error:!!eG.city,errorMessage:null==eG?void 0:null===(F=eG.city)||void 0===F?void 0:F.message,label:"Cidade"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,maxLength:2,setValue:e=>eU("state",String(e).toUpperCase()),name:"state",width:"150px",error:!!eG.state,errorMessage:null==eG?void 0:null===(P=eG.state)||void 0===P?void 0:P.message,label:"Estado"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"number",width:"200px",error:!!eG.number,errorMessage:null==eG?void 0:null===(A=eG.number)||void 0===A?void 0:A.message,label:"N\xfamero"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"complement",width:"200px",error:!!eG.complement,errorMessage:null==eG?void 0:null===(T=eG.complement)||void 0===T?void 0:T.message,label:"Complemento"})]})}),(0,o.jsx)(l.Z,{title:"Dados de Investimento",children:(0,o.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,name:"value",width:"200px",error:!!eG.value,errorMessage:null==eG?void 0:null===(I=eG.value)||void 0===I?void 0:I.message,label:"Valor",setValue:e=>eU("value",(0,g.Ht)(e))}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,type:"number",name:"term",width:"250px",error:!!eG.term,errorMessage:null==eG?void 0:null===(k=eG.term)||void 0===k?void 0:k.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,type:"text",name:"yield",width:"250px",error:!!eG.yield,errorMessage:null==eG?void 0:null===(B=eG.yield)||void 0===B?void 0:B.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),(0,o.jsx)(b.Z,{width:"200px",name:"purchasedWith",register:eV,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!(null==eG?void 0:eG.purchasedWith),errorMessage:null==eG?void 0:null===(V=eG.purchasedWith)||void 0===V?void 0:V.message,label:"Comprar com"}),"scp"===er&&(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(p.Z,{disableErrorMessage:!0,register:eV,type:"number",name:"amountQuotes",width:"150px",error:!!eG.amountQuotes,errorMessage:null==eG?void 0:null===(L=eG.amountQuotes)||void 0===L?void 0:L.message,label:"Quantidade de cotas"})}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,type:"date",register:eV,name:"initDate",width:"200px",error:!!eG.initDate,errorMessage:null==eG?void 0:null===(U=eG.initDate)||void 0===U?void 0:U.message,label:"Inicio do contrato"}),(0,o.jsx)(p.Z,{disableErrorMessage:!0,type:"date",register:eV,name:"endDate",width:"200px",error:!!eG.endDate,errorMessage:null==eG?void 0:null===(W=eG.endDate)||void 0===W?void 0:W.message,label:"Final do contrato"}),(0,o.jsx)(b.Z,{width:"100px",name:"isDebenture",register:eV,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!eG.isDebenture,errorMessage:null==eG?void 0:null===(Q=eG.isDebenture)||void 0===Q?void 0:Q.message,label:"Deb\xeanture"})]})}),(0,o.jsx)(l.Z,{title:"Anexo de documentos",children:(0,o.jsx)("div",{children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Contrato"}),(0,o.jsx)(j.Z,{errorMessage:null===(G=getFieldDocument("contract"))||void 0===G?void 0:G.reason,disable:!(null===(K=getFieldDocument("contract"))||void 0===K?void 0:K.field),onFileUploaded:eE,fileName:eI,onRemoveFile:()=>{eE(void 0),ek(void 0)}})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Comprovante"}),(0,o.jsx)("div",{children:(0,o.jsx)(j.Z,{disable:!(null===(H=getFieldDocument("proofPayment"))||void 0===H?void 0:H.field),errorMessage:null===(X=getFieldDocument("proofPayment"))||void 0===X?void 0:X.reason,onFileUploaded:eN,fileName:eq,onRemoveFile:()=>{eN(void 0),eY(void 0)}})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,o.jsx)("div",{children:(0,o.jsx)(j.Z,{errorMessage:null===(J=getFieldDocument("documentPdf"))||void 0===J?void 0:J.reason,disable:!(null===($=getFieldDocument("documentPdf"))||void 0===$?void 0:$.field),onFileUploaded:ez,fileName:eF,onRemoveFile:()=>{ez(void 0),eP(void 0)}})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,o.jsx)("div",{children:(0,o.jsx)(j.Z,{errorMessage:null===(ee=getFieldDocument("proofOfResidence"))||void 0===ee?void 0:ee.reason,disable:!(null===(et=getFieldDocument("proofOfResidence"))||void 0===et?void 0:et.field),onFileUploaded:eR,fileName:eA,onRemoveFile:()=>{eR(void 0),eT(void 0)}})})]})]})})}),(0,o.jsx)("div",{className:"md:w-52 mb-10",children:(0,o.jsx)(f.Z,{label:"Enviar",loading:ea,size:"lg",disabled:ea})})]})})}var Y=r(3729);function EditContract(){let{id:e}=(0,u.useParams)(),t=(0,u.useRouter)(),r=(0,a.NL)(),[p,v]=(0,n.useState)("pf"),[h,f]=(0,n.useState)("mutuo"),[g,b]=(0,n.useState)(),[y,Z]=(0,n.useState)(),[M,w]=(0,n.useState)("");(0,n.useEffect)(()=>()=>{v("pf"),f("mutuo"),b(void 0),Z(void 0),w(""),r.removeQueries({queryKey:["contract",e]})},[r,e]);let{isLoading:j,error:C}=(0,i.a)({queryKey:["contract",e],queryFn:async()=>{var r,o;let n=await c.Z.get("/contract/get-detail/".concat(e));if(!n.data)return x.Am.error("Dados do contrato n\xe3o encontrados."),t.push("/meus-contratos"),null;let a=n.data.status||n.data.statusContrato||(null===(r=n.data.contract)||void 0===r?void 0:r.status)||(null===(o=n.data.investment)||void 0===o?void 0:o.status)||"";return Y.z.includes(a.toUpperCase())?(w(a),n.data.investment&&n.data.investment.type&&f(n.data.investment.type),n.data.investor&&n.data.investor.type&&("business"===n.data.investor.type?(b(n.data),v("pj")):(Z(n.data),v("pf"))),n.data):(x.Am.error("Este contrato n\xe3o pode ser editado devido ao seu status atual."),t.push("/meus-contratos"),null)},enabled:!!e,staleTime:0});return((0,n.useEffect)(()=>{C&&(console.error("Erro completo:",C),(0,m.Z)(C,"Erro ao buscar dados do contrato."),t.push("/meus-contratos"))},[C,t]),j)?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(s.Z,{}),(0,o.jsx)(d.Z,{children:(0,o.jsx)("div",{className:"m-3 flex justify-center items-center h-[80vh]",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF9900] mx-auto mb-4"}),(0,o.jsx)("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})]}):C?null:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(s.Z,{}),(0,o.jsx)(d.Z,{children:(0,o.jsxs)("div",{className:"m-3",children:[(0,o.jsx)(l.Z,{title:"Edi\xe7\xe3o de contrato",children:(0,o.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,o.jsxs)("select",{value:p,disabled:!0,onChange:e=>{let{target:t}=e;return v(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[(0,o.jsx)("option",{value:"pf",selected:!0,children:"Pessoa F\xedsica"}),(0,o.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-white mb-1",children:"Tipo de contrato"}),(0,o.jsxs)("select",{value:h,disabled:!0,onChange:e=>{let{target:t}=e;return f("mutuo"===t.value?"mutuo":"scp")},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[(0,o.jsx)("option",{selected:!0,value:"mutuo",children:"M\xfatuo"}),(0,o.jsx)("option",{value:"scp",children:"SCP"})]})]})]})}),"pj"===p?(0,o.jsx)(BusinessEditing,{contractData:g,modalityContract:h}):(0,o.jsx)(PhysicalEditing,{contractData:y,modalityContract:h})]})})]})}},2875:function(e,t,r){"use strict";r.d(t,{Z:function(){return Button}});var o=r(7437),n=r(8700);function Button(e){let{handleSubmit:t,loading:r,label:a,disabled:i,className:s,...d}=e;return(0,o.jsx)(n.z,{...d,onClick:t,loading:r,disabled:i,className:s,children:a})}},5233:function(e,t,r){"use strict";r.d(t,{Z:function(){return CoverForm}});var o=r(7437);function CoverForm(e){let{title:t,children:r,width:n="auto",color:a="withe"}=e;return(0,o.jsxs)("div",{className:"md:w-auto w-full",children:[(0,o.jsx)("p",{className:"text-xl text-white mb-2",children:t}),(0,o.jsx)("div",{className:"mb-10 m-auto bg-opacity-90 rounded-2xl shadow-current ".concat("withe"===a?"bg-zinc-900 border border-[#FF9900] p-5":"pt-1"),children:r})]})}},5554:function(e,t,r){"use strict";r.d(t,{Z:function(){return InputSelect}});var o=r(7437);function InputSelect(e){let{optionSelected:t,options:r,setOptionSelected:n,label:a,placeHolder:i="",width:s="100%",register:d=()=>{},error:l,errorMessage:u,name:c="",disableErrorMessage:m=!1,disabled:p=!1}=e;return(0,o.jsxs)("div",{className:"inputSelect relative group",style:{width:s},children:[(0,o.jsx)("p",{className:"text-white mb-1 text-sm",children:a}),(0,o.jsxs)("select",{disabled:m&&!u,...d(c),value:t,onChange:e=>{let{target:t}=e;n&&n(t.value)},id:c,className:"h-12 w-full px-4 ".concat(p?"text-zinc-400":"text-white"," rounded-xl ").concat(l?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),children:[i&&(0,o.jsx)("option",{selected:!0,disabled:!0,value:"",children:i}),r.map((e,t)=>(0,o.jsx)("option",{className:"cursor-pointer",value:e.value,children:e.label},t))]}),l&&(0,o.jsx)("div",{className:" absolute gr max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[90%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:u})]})}},4207:function(e,t,r){"use strict";r.d(t,{Z:function(){return InputText}});var o=r(7437);function InputText(e){let{label:t,setValue:r,error:n,errorMessage:a,width:i="auto",register:s,name:d,placeholder:l="",type:u="text",disabled:c=!1,minDate:m,minLength:p,maxLength:v,maxDate:h,disableErrorMessage:f=!1,onBlur:g,value:b,onChange:x}=e;return(0,o.jsxs)("div",{className:"input relative group",style:{width:i},children:[(0,o.jsxs)("p",{className:"text-white mb-1 text-sm",children:[t,n&&!f&&(0,o.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",a]})]}),(0,o.jsx)("input",{...s(d),placeholder:l,type:u,id:d,disabled:c,min:m,max:h,minLength:p,maxLength:v,...r?{onChange:e=>{let{target:t}=e;return r(t.value)}}:{},onBlur:g,className:"h-12 w-full px-4 ".concat(c?"text-zinc-400":"text-white"," rounded-xl ").concat(n?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),...void 0!==b?{value:b}:{},...x?{onChange:x}:{}}),n&&(0,o.jsx)("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:a})]})}r(9514)},3729:function(e,t,r){"use strict";r.d(t,{f:function(){return a},z:function(){return n}});var o=r(8440);let n=[o.rd.REJECTED_BY_AUDIT,o.rd.AWAITING_AUDIT],a=[o.rd.AWAITING_INVESTOR_SIGNATURE]},6121:function(e,t,r){"use strict";r.d(t,{Z:function(){return formatDate},l:function(){return formatDateToEnglishType}});var o=r(4279),n=r.n(o);function formatDate(e){return n().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?n().utc(e).format("YYYY-MM-DD"):n().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},919:function(e,t,r){"use strict";r.d(t,{H:function(){return getFinalDataWithMount},Z:function(){return getDataFilter}});var o=r(2067),n=r.n(o);function getDataFilter(e){switch(e){case"TODAY":{let e=n()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}case"WEEK":{let e=n()().startOf("isoWeek").format("YYYY-MM-DD"),t=n()().endOf("isoWeek").format("YYYY-MM-DD");return{startDate:e,endDate:t}}case"MONTH":{let e=n()().startOf("month").format("YYYY-MM-DD"),t=n()().endOf("month").format("YYYY-MM-DD");return{startDate:e,endDate:t}}default:{let e=n()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}}}function getFinalDataWithMount(e){let{investDate:t,startDate:r}=e,o=n()(r).format("DD/MM/YYYY"),a=Number(t),i=n()(o,"DD/MM/YYYY").add(a,"months").format("DD/MM/YYYY");return i}},7412:function(e,t,r){"use strict";r.d(t,{x:function(){return getZipCode}});var o=r(4829);async function getZipCode(e){let t=e.replace(/\D/g,"");if(8!==t.length)return null;try{let e=await o.Z.get("https://viacep.com.br/ws/".concat(t,"/json/")),r=e.data;if(r&&!r.erro)return{neighborhood:r.bairro||"",street:r.logradouro||"",city:r.localidade||"",state:r.uf||""};return null}catch(e){return console.error("Erro ao buscar o CEP:",e),null}}},6654:function(e,t,r){"use strict";r.d(t,{Z:function(){return returnError}});var o=r(3014);function returnError(e,t){var r,n,a,i;let s=(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.message)||(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.error);if(Array.isArray(s))return s.forEach(e=>{o.Am.error(e,{toastId:e})}),s.join("\n");if("string"==typeof s)return o.Am.error(s,{toastId:s}),s;if("object"==typeof s&&null!==s){let e=Object.values(s).flat().join("\n");return o.Am.error(e,{toastId:e}),e}return o.Am.error(t,{toastId:t}),t}},8610:function(e,t,r){"use strict";r.d(t,{H:function(){return useNavigation}});var o=r(4033);let useNavigation=()=>{let e=(0,o.useRouter)();return{navigation:t=>e.push(t)}}},3277:function(e,t,r){"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}r.d(t,{Z:function(){return formatNumberValue}})},1458:function(e,t,r){"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}r.d(t,{A:function(){return cleanValue},Z:function(){return formatValue}})},9514:function(){},7470:function(e,t,r){"use strict";r.d(t,{R:function(){return getDefaultState},m:function(){return i}});var o=r(7987),n=r(9024),a=r(1640),i=class extends n.F{#e;#t;#r;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||getDefaultState(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(e){let onContinue=()=>{this.#o({type:"continue"})};this.#r=(0,a.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let t="pending"===this.state.status,r=!this.#r.canStart();try{if(t)onContinue();else{this.#o({type:"pending",variables:e,isPaused:r}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#o({type:"pending",context:t,variables:e,isPaused:r})}let o=await this.#r.start();return await this.#t.config.onSuccess?.(o,e,this.state.context,this),await this.options.onSuccess?.(o,e,this.state.context),await this.#t.config.onSettled?.(o,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(o,null,e,this.state.context),this.#o({type:"success",data:o}),o}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#o({type:"error",error:t})}}finally{this.#t.runNext(this)}}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),o.Vr.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},3588:function(e,t,r){"use strict";r.d(t,{D:function(){return useMutation}});var o=r(2265),n=r(7470),a=r(7987),i=r(2996),s=r(300),d=class extends i.l{#n;#a=void 0;#i;#s;constructor(e,t){super(),this.#n=e,this.setOptions(t),this.bindMethods(),this.#d()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#n.defaultMutationOptions(e),(0,s.VS)(this.options,t)||this.#n.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#i,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,s.Ym)(t.mutationKey)!==(0,s.Ym)(this.options.mutationKey)?this.reset():this.#i?.state.status==="pending"&&this.#i.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#i?.removeObserver(this)}onMutationUpdate(e){this.#d(),this.#l(e)}getCurrentResult(){return this.#a}reset(){this.#i?.removeObserver(this),this.#i=void 0,this.#d(),this.#l()}mutate(e,t){return this.#s=t,this.#i?.removeObserver(this),this.#i=this.#n.getMutationCache().build(this.#n,this.options),this.#i.addObserver(this),this.#i.execute(e)}#d(){let e=this.#i?.state??(0,n.R)();this.#a={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#l(e){a.Vr.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#a.variables,r=this.#a.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#a)})})}},l=r(8038);function useMutation(e,t){let r=(0,l.NL)(t),[n]=o.useState(()=>new d(r,e));o.useEffect(()=>{n.setOptions(e)},[n,e]);let i=o.useSyncExternalStore(o.useCallback(e=>n.subscribe(a.Vr.batchCalls(e)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),u=o.useCallback((e,t)=>{n.mutate(e,t).catch(s.ZT)},[n]);if(i.error&&(0,s.L3)(n.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:u,mutateAsync:i.mutate}}}},function(e){e.O(0,[6990,7326,8276,5371,6946,1865,3964,9891,3151,2971,7864,1744],function(){return e(e.s=9900)}),_N_E=e.O()}]);