"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationEntity = exports.NotificationTypeEnum = void 0;
const typeorm_1 = require("typeorm");
const owner_role_relation_entity_1 = require("./owner-role-relation.entity");
const contract_entity_1 = require("./contract.entity");
const addendum_entity_1 = require("./addendum.entity");
var NotificationTypeEnum;
(function (NotificationTypeEnum) {
    NotificationTypeEnum["DUPLICATED_DOCUMENT"] = "DUPLICATED_DOCUMENT";
    NotificationTypeEnum["NEW_CONTRACT"] = "NEW_CONTRACT";
    NotificationTypeEnum["INCLUDE_ADDITIVE"] = "INCLUDE_ADDITIVE";
})(NotificationTypeEnum || (exports.NotificationTypeEnum = NotificationTypeEnum = {}));
let NotificationEntity = class NotificationEntity {
};
exports.NotificationEntity = NotificationEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], NotificationEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        enum: NotificationTypeEnum,
    }),
    __metadata("design:type", String)
], NotificationEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'title' }),
    __metadata("design:type", String)
], NotificationEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'description' }),
    __metadata("design:type", String)
], NotificationEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'viewed',
        default: false,
    }),
    __metadata("design:type", Boolean)
], NotificationEntity.prototype, "viewed", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'contract_id',
    }),
    __metadata("design:type", String)
], NotificationEntity.prototype, "contractId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'addendum_id',
        nullable: true,
    }),
    __metadata("design:type", Number)
], NotificationEntity.prototype, "addendumId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'investor_id',
        nullable: true,
    }),
    __metadata("design:type", String)
], NotificationEntity.prototype, "investorId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'contract_value',
        nullable: true,
        type: 'decimal',
    }),
    __metadata("design:type", Number)
], NotificationEntity.prototype, "contractValue", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], NotificationEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], NotificationEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)({ name: 'deleted_at' }),
    __metadata("design:type", Date)
], NotificationEntity.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => owner_role_relation_entity_1.OwnerRoleRelationEntity, (item) => item.userNotifications),
    (0, typeorm_1.JoinColumn)({
        name: 'broker_id',
        referencedColumnName: 'id',
    }),
    __metadata("design:type", Object)
], NotificationEntity.prototype, "brokerOwnerRoleRelation", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => owner_role_relation_entity_1.OwnerRoleRelationEntity, (item) => item.investorNotifications),
    (0, typeorm_1.JoinColumn)({
        name: 'investor_id',
        referencedColumnName: 'id',
    }),
    __metadata("design:type", Object)
], NotificationEntity.prototype, "investor", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => owner_role_relation_entity_1.OwnerRoleRelationEntity, (item) => item.adminNotifications),
    (0, typeorm_1.JoinColumn)({
        name: 'admin_owner_role_relation_id',
        referencedColumnName: 'id',
    }),
    __metadata("design:type", Object)
], NotificationEntity.prototype, "adminOwnerRoleRelation", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => contract_entity_1.ContractEntity, (item) => item.notifications),
    (0, typeorm_1.JoinColumn)({
        name: 'contract_id',
        referencedColumnName: 'id',
    }),
    __metadata("design:type", Object)
], NotificationEntity.prototype, "contract", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => addendum_entity_1.AddendumEntity, (item) => item.addendumNotifications),
    (0, typeorm_1.JoinColumn)({
        name: 'addendum_id',
        referencedColumnName: 'id',
    }),
    __metadata("design:type", Object)
], NotificationEntity.prototype, "addendum", void 0);
exports.NotificationEntity = NotificationEntity = __decorate([
    (0, typeorm_1.Entity)('notification')
], NotificationEntity);
//# sourceMappingURL=notification.entity.js.map