"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7050],{8089:function(e,t,s){s.d(t,{Z:function(){return ModalBroker}});var a=s(7437),n=s(2265),l=s(4568),r=s(3014),d=s(5968);function BrokerData(e){let{broker:t,edit:s,setBroker:n}=e;return(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"<PERSON><PERSON><PERSON> <PERSON> Car<PERSON>ira Respons\xe1vel"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.upper,disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Tipo de Conta"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:"PHYSICAL"===t.accountType?"Pessoa F\xedsica":"Pessoa Juridica",disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Nome Completo"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.name,disabled:!s,onChange:e=>{let{target:s}=e;return n({...t,name:s.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:t.document.length<=11?"CPF":"CNPJ"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.document.length<=11?(0,d.VL)(t.document):(0,d.PK)(t.document),disabled:!s,onChange:e=>{let{target:s}=e;return n({...t,document:s.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Taxa"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.fee?"Sim":"N\xe3o",disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Data de Nascimento"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.birthDate,disabled:!s,onChange:e=>{let{target:s}=e;return n({...t,birthDate:s.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Telefone"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.phone&&(0,d.gP)(t.phone.replace("+55",""))||"",disabled:!s,onChange:e=>{let{target:s}=e;return n({...t,phone:s.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Nome da M\xe3e"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.motherName,disabled:!s,onChange:e=>{let{target:s}=e;return n({...t,motherName:s.value})}})]})]})}var o=s(4829);function BrokerAddress(e){let{broker:t,edit:s,setBroker:l}=e,[r,d]=(0,n.useState)("");async function handleGetByCep(e){let s=e.replace(/[^0-9]/g,"");8!==s.length||await o.Z.get("https://viacep.com.br/ws/".concat(s,"/json/")).then(e=>{e&&e.data&&(e.data.erro||l({...t,address:{neighbor:e.data.bairro,street:e.data.logradouro,city:e.data.localidade,uf:e.data.uf,complement:"",number:"",zipcode:s}}))}).catch(()=>{}).finally(()=>{})}return(0,n.useEffect)(()=>{(function(e){let t=e.replace(/[^0-9]/g,"");8===t.length&&handleGetByCep(t)})(r||"")},[r]),(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"ID"}),(0,a.jsx)("p",{className:"text-xs",children:null==t?void 0:t.id})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"CEP"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.address.zipcode||"",disabled:!s,onChange:e=>{let{target:s}=e;l({...t,address:{...t.address,zipcode:s.value}}),d(s.value)}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Cidade"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.address.city||"",disabled:!s,onChange:e=>{let{target:s}=e;return l({...t,address:{...t.address,city:s.value}})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Estado"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.address.uf||"",disabled:!s,onChange:e=>{let{target:s}=e;return l({...t,address:{...t.address,uf:s.value}})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Endere\xe7o"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.address.street||"",disabled:!s,onChange:e=>{let{target:s}=e;return l({...t,address:{...t.address,street:s.value}})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"N\xfamero"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.address.number||"",disabled:!s,onChange:e=>{let{target:s}=e;return l({...t,address:{...t.address,number:s.value}})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Bairro"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.address.neighbor||"",disabled:!s,onChange:e=>{let{target:s}=e;return l({...t,address:{...t.address,neighbor:s.value}})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Complemento"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:t.address.complement||"",disabled:!s,onChange:e=>{let{target:s}=e;return l({...t,address:{...t.address,complement:s.value}})}})]})]})}var i=s(4984);let returnTitleDocument=e=>{switch(e){case"CONTRACT":return"Contrato de parceria";case"CARD_CNPJ":return"Cart\xe3o CNPJ";case"RG":return"Documento de identidade";case"PROOF_RESIDENCE":return"Comprovante de resid\xeancia";case"SOCIAL_CONTRACT":return"Contrato social";case"MEI":return"Certificado de mei"}};function BrokerDocuments(e){let{broker:t,edit:s,setBroker:n}=e;return(0,a.jsx)("div",{className:"flex gap-y-3 mt-5 flex-wrap gap-4",children:t.uploads.map(e=>(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:returnTitleDocument(e.type)}),(0,a.jsx)("div",{className:"w-44",children:(0,a.jsx)("div",{onClick:()=>{window.open(e.url,"_blank")},children:(0,a.jsx)(i.Z,{disable:!0,onFileUploaded:()=>{}})})})]},e.id))})}var c=s(2067),x=s.n(c),m=s(6654),u=s(3256),h=s(8700);function ModalBroker(e){let{setModal:t,broker:s,typeModal:d}=e,[o,i]=(0,n.useState)({...s}),[c,p]=(0,n.useState)(0),[f,b]=(0,n.useState)(!1),[N,j]=(0,n.useState)(!1),v=(0,u.e)(),editData=()=>JSON.stringify(o)===JSON.stringify(s)?"old":"new",editBroker=()=>{j(!0);let{document:e,name:a,phone:n,email:i,motherName:c,birthDate:u,address:h,participation:p}=o,{city:f,complement:N,neighbor:v,number:g,street:w,uf:C,zipcode:y}=h,k={cep:y!==s.address.zipcode?y:void 0,street:w!==s.address.street?w:void 0,number:g!==s.address.number?g:void 0,complement:N!==s.address.complement?N:void 0,neighborhood:v!==s.address.neighbor?v:void 0,city:f!==s.address.city?f:void 0,state:C!==s.address.uf?C:void 0},D={owner:{document:e!==s.document?e:void 0,name:a!==s.name?a:void 0,phone:n!==s.phone?n:void 0,email:i!==s.email?i:void 0,motherName:c!==s.motherName?c:void 0,dtBirth:u!==s.birthDate?x()(u).format("YYYY-MM-DD"):void 0,Address:0===Object.keys(k).length?void 0:k},ownerRoleRelationId:s.id,rate:p!==s.participation?p:void 0};"broker"===d?l.Z.patch("/super-admin/broker",D).then(e=>{r.Am.success("Dados alterados com sucesso!"),t(!1),b(!1)}).catch(e=>{(0,m.Z)(e,"Tivemos um problema ao editar os dados do usu\xe1rio")}).finally(()=>j(!1)):l.Z.patch("/super-admin/advisor",D).then(e=>{r.Am.success("Dados alterados com sucesso!"),t(!1),b(!1)}).catch(e=>{(0,m.Z)(e,"Tivemos um problema ao editar os dados do usu\xe1rio")}).finally(()=>j(!1))};return(0,a.jsx)("div",{className:"",children:(0,a.jsxs)("div",{className:"w-full text-white h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5"}),(0,a.jsxs)("div",{className:"gap-y-1 flex flex-col",children:[(0,a.jsx)("p",{className:"font-bold text-xs",children:null==o?void 0:o.name}),(0,a.jsx)("p",{className:"text-xs",children:"broker"===d?"Broker":"Assessor"})]})]}),(0,a.jsxs)("div",{className:"w-full flex flex-2 text-center mt-5 mb-4 gap-2 justify-between",children:[(0,a.jsx)("div",{className:"cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ".concat(0===c?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>p(0),children:(0,a.jsx)("p",{className:"md:text-sm text-xs text-center",children:"Dados Pessoais"})}),(0,a.jsx)("div",{className:"cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ".concat(1===c?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>p(1),children:(0,a.jsx)("p",{className:"md:text-sm text-xs",children:"Endere\xe7o"})}),(0,a.jsx)("div",{className:"cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ".concat(2===c?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>p(2),children:(0,a.jsx)("p",{className:"md:text-sm text-xs",children:"Participa\xe7\xe3o"})}),(0,a.jsx)("div",{className:"cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ".concat(3===c?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>p(3),children:(0,a.jsx)("p",{className:"md:text-sm text-xs",children:"Anexos"})})]}),(0,a.jsx)("div",{className:"w-full flex-1",children:(()=>{switch(c){case 0:return(0,a.jsx)(BrokerData,{broker:o,setBroker:i,edit:f});case 1:return(0,a.jsx)(BrokerAddress,{broker:o,setBroker:i,edit:f});case 2:return(0,a.jsxs)("div",{className:"flex gap-y-3 mt-5",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Participa\xe7\xe3o"}),(0,a.jsxs)("div",{className:"flex items-center justify-end",children:[(0,a.jsx)("input",{type:"number",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:String(Number(o.participation))||"",disabled:!f,onChange:e=>{let{target:t}=e;return i({...o,participation:t.value})}}),(0,a.jsx)("p",{className:"ml-[1px]",children:"%"})]})]});case 3:return(0,a.jsx)(BrokerDocuments,{broker:o,setBroker:i,edit:f})}})()}),(0,a.jsx)("div",{className:"md:w-9/12 w-full flex flex-col md:flex-row justify-start gap-4 mt-5 select-none",children:"superadmin"===v.name&&(0,a.jsx)(h.z,{onClick:()=>{N||(!0===f&&"new"===editData()?editBroker():b(!f))},children:f?"new"!==editData()?"Cancelar edi\xe7\xe3o":"Confirmar edi\xe7\xe3o":"Editar dados"})})]})})}},1481:function(e,t,s){s.d(t,{Z:function(){return Modal}});var a=s(7437),n=s(5968),l=s(4829),r=s(2265);function UserData(e){var t,s;let{investor:d,edit:o,setInvestor:i}=e;async function handleGetByCep(e){let t=e.replace(/[^0-9]/g,"");8!==t.length||await l.Z.get("https://viacep.com.br/ws/".concat(t,"/json/")).then(e=>{e&&e.data&&(e.data.erro||i({...d,neighborhood:e.data.bairro,address:e.data.logradouro,city:e.data.localidade,state:e.data.uf}))}).catch(()=>{}).finally(()=>{})}return(0,r.useEffect)(()=>{(function(e){let t=e.replace(/[^0-9]/g,"");8===t.length&&handleGetByCep(t)})((null==d?void 0:d.zipCode)||"")},[null==d?void 0:d.zipCode]),(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"ID"}),(0,a.jsx)("p",{className:"text-xs",children:null==d?void 0:d.id})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Nome"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.name,disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,name:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"E-mail"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.email,disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,email:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Consultor Respons\xe1vel"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none ",placeholder:"N\xe3o informado",value:d.advisor,disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Telefone"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",value:(0,n.gP)((null==d?void 0:null===(t=d.phone)||void 0===t?void 0:t.replace("+55",""))||(null==d?void 0:null===(s=d.phone)||void 0===s?void 0:s.replace("55",""))||""),disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,phone:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Perfil"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.profile||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,profile:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"RG"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.rg||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,rg:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"CEP"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.zipCode||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,zipCode:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Cidade"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.city||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,city:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Estado"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.state||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,state:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Endere\xe7o"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.address||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,address:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"N\xfamero"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.addressNumber||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,addressNumber:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Bairro"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.neighborhood||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,neighborhood:t.value})}})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Complemento"}),(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:d.complement||"",disabled:!o,onChange:e=>{let{target:t}=e;return i({...d,complement:t.value})}})]})]})}var d=s(4209),o=s(8440),i=s(3042),c=s(5838),x=s(9367),m=s(6689),u=s(4279),h=s.n(u);function UserContracts(e){let{investor:t}=e,[s,n]=(0,r.useState)(),[l,u]=(0,r.useState)([]),returnUrl=(e,t)=>{if(e.files.length>0){let s=Array.isArray(t)?t:[t],n=e.files.filter(e=>s.includes(e.type));if(n.length>0)return(0,a.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,a.jsx)(i.Z,{className:"cursor-pointer",onClick:()=>{window.open(n[0].url,"_blank")},color:"#fff",width:20})})}return(0,a.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,a.jsx)(c.Z,{className:"",color:"#FF9900",width:20})})};return(0,a.jsxs)("div",{className:"mt-5",children:[(0,a.jsxs)("table",{className:"w-full relative min-h-20",children:[(0,a.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,a.jsxs)("tr",{className:"w-full py-2",children:[(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Valor"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Rendimento"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Consultor"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Criado em"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Status"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Contrato"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Comprovante"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Aditivos"})})]})}),t.contracts.length>=1?(0,a.jsx)("tbody",{className:"w-full",children:t.contracts.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[(0,a.jsx)("td",{children:(0,a.jsx)("p",{className:"text-xs text-center",children:Number(e.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),(0,a.jsx)("td",{children:(0,a.jsxs)("p",{className:"text-xs text-center",children:[e.investmentYield||"0","%"]})}),(0,a.jsx)("td",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-center max-w-44 truncate",children:null==e?void 0:e.responsibleConsultant})}),(0,a.jsx)("td",{children:(0,a.jsx)("p",{className:"text-xs text-center",children:h()(e.contractStart||"").format("DD/MM/YYYY")})}),(0,a.jsx)("td",{children:(0,a.jsx)(d.Z,{description:(0,o.mP)(e.contractStatus).description,text:(0,o.mP)(e.contractStatus).title,textColor:(0,o.mP)(e.contractStatus).textColor})}),(0,a.jsx)("td",{children:(null==e?void 0:e.pdfDocument)?(0,a.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,a.jsx)(i.Z,{className:"cursor-pointer",onClick:()=>{window.open(null==e?void 0:e.pdfDocument,"_blank")},color:"#fff",width:20})}):(0,a.jsx)("p",{className:"text-xs text-center",children:"N\xe3o possui"})}),(0,a.jsx)("td",{children:(null==e?void 0:e.proofPaymentPdf)?(0,a.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,a.jsx)(i.Z,{className:"cursor-pointer",onClick:()=>{window.open(null==e?void 0:e.proofPaymentPdf,"_blank")},color:"#fff",width:20})}):(0,a.jsx)("p",{className:"text-xs text-center",children:"N\xe3o possui"})}),(0,a.jsx)("td",{children:(null==e?void 0:e.addendum.length)>0?(0,a.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,a.jsx)(x.Z,{className:"cursor-pointer",onClick:()=>u(e.addendum),color:"#fff",width:20})}):(0,a.jsx)("p",{className:"text-xs text-center",children:"N\xe3o possui"})})]},t))}):(0,a.jsx)("div",{className:"text-center mt-5 absolute w-full",children:(0,a.jsx)("p",{children:"Nenhum dado encontrado"})})]}),l.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-1 mt-7 flex w-full items-center justify-between",children:[(0,a.jsx)("p",{children:"Aditivos do contrato selecionado"}),(0,a.jsx)(m.Z,{width:20,className:"cursor-pointer",onClick:()=>u([])})]}),(0,a.jsxs)("table",{className:"w-full relative min-h-20 ",children:[(0,a.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,a.jsxs)("tr",{className:"w-full py-2",children:[(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Valor"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Rendimento"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Consultor"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Criado em"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Assinatura"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Contrato"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm",children:"Comprovante"})}),(0,a.jsx)("th",{children:(0,a.jsx)("p",{className:"font-bold text-sm"})})]})}),(null==l?void 0:l.length)>=1?(0,a.jsx)("tbody",{className:"w-full",children:l.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[(0,a.jsx)("td",{children:(0,a.jsx)("p",{className:"text-xs text-center",children:Number(e.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),(0,a.jsx)("td",{children:(0,a.jsxs)("p",{className:"text-xs text-center",children:[e.investmentYield||"0","%"]})}),(0,a.jsx)("td",{className:"max-w-28",children:(0,a.jsx)("p",{className:"text-xs text-center max-w-48 truncate",children:null==e?void 0:e.responsibleConsultant})}),(0,a.jsx)("td",{children:(0,a.jsx)("p",{className:"text-xs text-center",children:h()(e.contractStart).format("DD/MM/YYYY")})}),(0,a.jsx)("td",{children:(0,a.jsx)(d.Z,{description:(0,o.XW)(e.contractStatus).description,text:(0,o.XW)(e.contractStatus).title,textColor:(0,o.XW)(e.contractStatus).textColor})}),(0,a.jsx)("td",{children:returnUrl(e,["CONTRACT","ADDENDUM"])}),(0,a.jsx)("td",{children:returnUrl(e,"PAYMENT")})]},t))}):(0,a.jsx)("div",{className:"text-center mt-5 absolute w-full",children:(0,a.jsx)("p",{children:"Nenhum dado encontrado"})})]})]})]})}var p=s(1865);function UserDataBank(e){let{investor:t,edit:s,setInvestor:n}=e,{setValue:l,watch:d}=(0,p.cI)(),[o,i]=(0,r.useState)(t||{});(0,r.useEffect)(()=>{t&&(i(t),l("bank",t.bank||""),l("accountNumber",t.accountNumber||""),l("branch",t.branch||""))},[t,l]);let c=d("bank"),x=d("accountNumber"),m=d("branch"),handleChange=(e,t)=>{l(e,t);let s={...o,[e]:t};i(s),n(s)};return(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Banco"}),s?(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:c,disabled:!s,onChange:e=>{let{target:t}=e;return handleChange("bank",t.value)}}):(0,a.jsx)("p",{className:"text-xs",children:c||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"N\xfamero da conta"}),s?(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:x,disabled:!s,onChange:e=>{let{target:t}=e;return handleChange("accountNumber",t.value)}}):(0,a.jsx)("p",{className:"text-xs",children:x||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Ag\xeancia"}),s?(0,a.jsx)("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:m,disabled:!s,onChange:e=>{let{target:t}=e;return handleChange("branch",t.value)}}):(0,a.jsx)("p",{className:"text-xs",children:m||"N\xe3o informado"})]})]})}var f=s(4568),b=s(3014),N=s(3256),j=s(8700),v=s(4033),g=s(5531);let w=(0,g.Z)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function CardUpgradeOpt(e){let{title:t,value:s,onClick:n}=e;return(0,a.jsx)("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:n,children:(0,a.jsx)("div",{className:"bg-[#1C1C1C] md:w-[100%] p-5 rounded-lg border-[#FF9900] border my-5",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4",children:t}),(0,a.jsx)("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px]",children:s})]}),(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("div",{className:"",children:(0,a.jsx)("p",{className:"text-white text-lg font-bold",children:(0,a.jsx)(w,{color:"#fff",width:20})})})})]})})})}function Modal(e){let{setModal:t,investor:s}=e,l=(0,v.useRouter)(),[d,o]=(0,r.useState)(s),[i,c]=(0,r.useState)(0),[x,m]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),w=(0,N.e)(),editData=()=>JSON.stringify(d)===JSON.stringify(s)?"owner":d.zipCode===s.zipCode&&d.address===s.address&&d.neighborhood===s.neighborhood&&d.addressNumber===s.addressNumber&&d.city===s.city&&d.state===s.state&&d.complement===s.complement?"address":void 0,editBank=()=>{if(d.bank===s.bank&&d.accountNumber===s.accountNumber&&d.branch===s.branch)return"bank"},editUser=()=>{if("owner"===editData())return m(!1);h(!0);let e={owner:"owner"!==editData()?{name:d.name!==s.name?d.name:void 0,document:d.document!==s.document?(0,n.VL)(d.document):void 0,email:d.email!==s.email?d.email:void 0,phone:d.phone!==s.phone?"+55".concat((0,n.p4)(d.phone)):void 0,rg:d.rg!==s.rg?d.rg:void 0}:void 0,bank:"bank"!==editBank()&&"superadmin"===w.name?{bank:d.bank,accountNumber:d.accountNumber,branch:d.branch}:void 0,address:"address"!==editData()?{cep:(0,n.Tc)(d.zipCode),street:d.address,neighborhood:d.neighborhood,number:d.addressNumber,city:d.city,state:d.state,complement:d.complement}:void 0,ownerRoleRelationId:d.id};f.Z.put("/".concat(w.name,"/investor"),e).then(e=>{b.Am.success("Dados alterados com sucesso!"),m(!1),t(!1),window.location.reload()}).catch(e=>{b.Am.error(e.response.data.message||"N\xe3o foi possivel alterar os dados do investidor")}).finally(()=>h(!1))},handleRentabilityClick=()=>{l.push("/contratos/alterar?tipo=rentabilidade&investorId=".concat(s.id))},handleModalityClick=()=>{l.push("/contratos/alterar?tipo=modalidade&investorId=".concat(s.id))},upgradeContractOptions=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(CardUpgradeOpt,{title:"Mudan\xe7a de Rentabilidade",value:"Clique aqui para mudar a rentabilidade do contrato do investidor",onClick:handleRentabilityClick}),(0,a.jsx)(CardUpgradeOpt,{title:"Mudan\xe7a de Modalidade",value:"Clique aqui para mudar a modalidade do contrato do investidor",onClick:handleModalityClick})]});return(0,a.jsx)("div",{className:"z-10 fixed top-0 left-0 w-screen h-screen bg-[#1c1c1c71]",children:(0,a.jsxs)("div",{className:"z-20 w-full ".concat(2!==i||p?"md:w-5/12":"md:w-6/12"," bg-[#1C1C1C] min-h-screen fixed top-0 right-0 border-l border-t border-[#FF9900] p-10 text-white overflow-auto h-full flex flex-col"),children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5"}),(0,a.jsxs)("div",{className:"gap-y-1 flex flex-col",children:[(0,a.jsx)("p",{className:"font-bold text-xs",children:null==d?void 0:d.name}),(0,a.jsx)("p",{className:"text-xs",children:"Investidor"})]})]}),(0,a.jsxs)("div",{className:"flex gap-x-2 mt-5",children:[(0,a.jsx)("div",{className:"cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ".concat(0===i?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>c(0),children:(0,a.jsx)("p",{className:"md:text-sm text-xs",children:"Dados Pessoais"})}),(0,a.jsx)("div",{className:"cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ".concat(1===i?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>c(1),children:(0,a.jsx)("p",{className:"md:text-sm text-xs",children:"Dados Banc\xe1rios"})}),(0,a.jsx)("div",{className:"cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ".concat(2===i?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>c(2),children:(0,a.jsx)("p",{className:"md:text-sm text-xs",children:"Contratos"})})]}),(0,a.jsx)("div",{className:"w-full flex-1",children:(()=>{switch(i){case 0:return(0,a.jsx)(UserData,{investor:d,setInvestor:o,edit:x});case 1:return(0,a.jsx)(UserDataBank,{investor:d,edit:x&&"superadmin"===w.name,setInvestor:o});case 2:return p?upgradeContractOptions():(0,a.jsx)(UserContracts,{investor:d})}})()}),(0,a.jsxs)("div",{className:"md:w-9/12 w-full flex flex-col md:flex-row justify-start gap-4 mt-5",children:[(0===i||1===i&&"superadmin"===w.name)&&["superadmin","admin","broker"].includes(w.name)&&(0,a.jsx)(j.z,{onClick:()=>{x?editUser():m(!0)},children:x?"owner"===editData()?"Cancelar edi\xe7\xe3o":"Confirmar edi\xe7\xe3o":"Editar dados"}),2===i&&["superadmin","admin","broker","advisor"].includes(w.name)&&null!=d&&d.contracts&&0!==d.contracts.length&&d.contracts.some(e=>{var t;let s=null===(t=e.contractStatus)||void 0===t?void 0:t.toUpperCase();return"ACTIVE"===s||"ATIVO"===s})?(0,a.jsx)(j.z,{variant:"default",onClick:()=>g(!0),children:"Alterar Contrato"}):null,(0,a.jsx)(j.z,{variant:"secondary",onClick:e=>p?g(!1):t(!1),children:"Fechar"})]})]})})}},4209:function(e,t,s){s.d(t,{Z:function(){return StatusWithDescription}});var a=s(7437),n=s(7057);function StatusWithDescription(e){let{description:t,text:s,textColor:l}=e;return(0,a.jsxs)("div",{className:"relative group select-none",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,a.jsx)("p",{className:"text-xs text-center ".concat(l),children:s}),(0,a.jsx)(n.Z,{width:15,color:"#FF9900"})]}),(0,a.jsx)("div",{className:"absolute max-w-[250px] text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[100%] hidden w-max bg-gray-700 text-white text-xs rounded px-2 py-1 group-hover:block group-hover:mb-[-100%]",children:t})]})}},2359:function(e,t,s){s.d(t,{aM:function(){return c},ue:function(){return h},yo:function(){return i}});var a=s(7437),n=s(2265),l=s(8712),r=s(6061),d=s(2549),o=s(992);let i=l.fC,c=l.xz;l.x8;let x=l.h_,m=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(l.aV,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n,ref:t})});m.displayName=l.aV.displayName;let u=(0,r.j)("fixed z-50 gap-4 bg-[#1C1C1C] border-l overflow-auto border-[#FF9900] p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right"}},defaultVariants:{side:"right"}}),h=n.forwardRef((e,t)=>{let{side:s="right",className:n,children:r,...i}=e;return(0,a.jsxs)(x,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(l.VY,{ref:t,className:(0,o.cn)(u({side:s}),n),...i,children:[(0,a.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(d.Z,{className:"h-4 w-4 text-white"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]}),r]})]})});h.displayName=l.VY.displayName;let p=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(l.Dx,{ref:t,className:(0,o.cn)("text-lg font-semibold text-foreground",s),...n})});p.displayName=l.Dx.displayName;let f=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(l.dk,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",s),...n})});f.displayName=l.dk.displayName},6654:function(e,t,s){s.d(t,{Z:function(){return returnError}});var a=s(3014);function returnError(e,t){var s,n,l,r;let d=(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.message)||(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(l=r.data)||void 0===l?void 0:l.error);if(Array.isArray(d))return d.forEach(e=>{a.Am.error(e,{toastId:e})}),d.join("\n");if("string"==typeof d)return a.Am.error(d,{toastId:d}),d;if("object"==typeof d&&null!==d){let e=Object.values(d).flat().join("\n");return a.Am.error(e,{toastId:e}),e}return a.Am.error(t,{toastId:t}),t}},9367:function(e,t,s){var a=s(2265);let n=a.forwardRef(function({title:e,titleId:t,...s},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=n}}]);