(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2903],{6085:function(e,t,a){!function(e){e.defineLocale("pt-br",{months:"jane<PERSON>_fevereiro_mar\xe7o_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_ter\xe7a-feira_quarta-feira_quinta-feira_sexta-feira_s\xe1bado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_s\xe1b".split("_"),weekdaysMin:"do_2\xaa_3\xaa_4\xaa_5\xaa_6\xaa_s\xe1".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [\xe0s] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [\xe0s] HH:mm"},calendar:{sameDay:"[Hoje \xe0s] LT",nextDay:"[Amanh\xe3 \xe0s] LT",nextWeek:"dddd [\xe0s] LT",lastDay:"[Ontem \xe0s] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[\xdaltimo] dddd [\xe0s] LT":"[\xdaltima] dddd [\xe0s] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"h\xe1 %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um m\xeas",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%d\xba",invalidDate:"Data inv\xe1lida"})}(a(2067))},8725:function(e,t,a){Promise.resolve().then(a.bind(a,5089))},5089:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return Metas}});var r=a(7437),s=a(3877),n=a(8637);function ProgressTable(e){let{goals:t,filters:a,filteredType:s}=e;function ProgressBar(e){let{goal:t}=e,a=Number(t.targetAmount),n=Number(t.perspectiveAmount),l="goals"===s?t.percentAchieved||0:n/a*100||0;return(0,r.jsxs)("div",{className:"text-white my-5 flex items-center w-10/12",children:[(0,r.jsx)("p",{className:"w-[30%]",children:t.brokerName}),(0,r.jsxs)("div",{className:"flex-1 bg-[#1C1C1C] rounded-lg ml-10 h-9 relative",children:[(0,r.jsx)("div",{style:{width:"".concat(l<=100?l:100,"%")},className:"relative text-center bg-orange-linear rounded-l-lg ".concat(l?"rounded-r-lg":""," h-full flex items-center justify-center"),children:(0,r.jsxs)("p",{className:" font-extrabold ".concat(l<5?"absolute right-[-5px] translate-x-[100%]":"text-[#282828]"),children:[Math.floor(l||0),"%"]})}),(0,r.jsxs)("p",{className:"absolute right-5 top-[50%] translate-y-[-50%] text-xs",children:[Number("goals"===s?t.totalAchieved:t.perspectiveAmount||0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})," / ",Number(t.targetAmount).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})]})]})]})}return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-[20px] h-[20px] bg-orange-linear"}),(0,r.jsx)("p",{className:"ml-1 text-sm",children:a[0].label})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-[20px] h-[20px] bg-[#3A3A3A]"}),(0,r.jsx)("p",{className:"ml-1 text-sm",children:a[1].label})]})]}),(0,r.jsx)("div",{children:t&&(null==t?void 0:t.length)>0?null==t?void 0:t.map(e=>(0,r.jsx)(ProgressBar,{goal:e},e.goalName)):(0,r.jsx)("div",{className:"mt-5 text-center",children:(0,r.jsx)("p",{children:"Nenhuma meta cadastrada."})})})]})}var l=a(2265),o=a(4568),c=a(3014),d=a(3336),i=a(3220),m=a(5968),x=a(2067),u=a.n(x),h=a(3256),p=a(8700);function AddGoals(e){let{brokerAdvisor:t,setAddgoalModal:a,golsData:s,brokerGoalId:n}=e,[x,g]=(0,l.useState)(t),[v,f]=(0,l.useState)(""),[b,j]=(0,l.useState)([]),[N,w]=(0,l.useState)(!1),y=(0,h.e)();return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"md:w-3/4 mb-5 m-auto",children:t&&(0,r.jsx)(d.Z,{selectable:!0,label:"",items:x,value:v,setValue:e=>{let t=String(e).split(" - ")[1]||"",a=String(e).split(" - ")[0]||"",r=s?s.split("-")[0]:String(u()().year()),l=s?s.split("-")[1]:String(u()().month()+1),o=x.filter(e=>e.id!==t);g(o);let c="".concat(r,"-").concat(l.padStart(2,"0"),"-01"),d="".concat(r,"-").concat(l.padStart(2,"0"),"-").concat(u()("".concat(r,"-").concat(l),"YYYY-MM").endOf("month").date());j([...b,{ownerRelationId:t,name:a,dateFrom:c,dateTo:d,observations:"",targetAmount:"",adminOwnerRelationId:"superadmin"===y.name?y.roleId:void 0,brokerGoalId:"broker"===y.name?n:void 0}])}})})}),(0,r.jsxs)("div",{className:"flex gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-[20px] h-[20px] bg-orange-linear"}),(0,r.jsx)("p",{className:"ml-1 text-sm",children:"Meta Atingida"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-[20px] h-[20px] bg-[#3A3A3A]"}),(0,r.jsx)("p",{className:"ml-1 text-sm",children:"Meta Restante"})]})]}),(0,r.jsx)("div",{children:b.map(e=>(0,r.jsxs)("div",{className:"text-white my-5 flex items-center",children:[(0,r.jsx)("p",{className:"w-3/12",children:e.name}),(0,r.jsxs)("div",{className:"flex-1 rounded-lg ml-10 h-9 flex items-center gap-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)(i.Z,{id:"",label:"",placeholder:"R$",name:"",type:"text",value:e.targetAmount,onChange:t=>{let a=b.filter(t=>t.ownerRelationId!==e.ownerRelationId);j([...a,{...e,targetAmount:(0,m.Ht)(t.target.value)}])}})}),(0,r.jsx)("div",{className:"w-7/12",children:(0,r.jsx)(i.Z,{id:"",label:"",placeholder:"Observa\xe7\xf5es",name:"",type:"text",value:e.observations,onChange:t=>{let a=b.filter(t=>t.ownerRelationId!==e.ownerRelationId);j([...a,{...e,observations:t.target.value}])}})})]})]},e.ownerRelationId))}),(0,r.jsx)("div",{className:"flex w-full justify-end mt-10 mb-5",children:(0,r.jsx)("div",{children:(0,r.jsx)(p.z,{onClick:function(){w(!0);let e=b.map(e=>({...e,targetAmount:Number(e.targetAmount.replaceAll(".","").replace(",","."))}));o.Z.post("/goals/".concat("superadmin"===y.name?"broker":"advisor"),e).then(e=>{c.Am.success("Metas cadastrada com sucesso!"),a(!1)}).catch(e=>{c.Am.error(e.response.data.message||"N\xe3o foi possivel cadastrar as metas")}).finally(()=>w(!1))},loading:N,children:"Concluir"})})})]})}var g=a(6691),v=a.n(g),f=a(1535),b={src:"/_next/static/media/graphic.6e8f2042.svg",height:31,width:31,blurWidth:0,blurHeight:0};function AdminDashboard(e){let{data:t}=e;return(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 mt-5",children:[(0,r.jsx)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,r.jsx)("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:(0,r.jsx)(v(),{alt:"",src:f.Z,width:20})}),(0,r.jsx)("p",{className:"text-sm",children:"Meta de capta\xe7\xe3o"}),(0,r.jsx)("p",{className:"text-xs font-extralight capitalize",children:t.monthName}),(0,r.jsx)("p",{className:"mt-3 text-2xl font-bold",children:Number(null==t?void 0:t.targetAmount).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})})]})}),(0,r.jsx)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 pb-2 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,r.jsx)("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:(0,r.jsx)(v(),{alt:"",src:b,width:20})}),(0,r.jsx)("p",{className:"text-sm",children:"Progresso"}),(0,r.jsx)("p",{className:"text-xs font-extralight",children:"Porcentagem"}),(0,r.jsxs)("p",{className:"mt-3 text-2xl font-bold",children:[Math.floor(t.percentage||0),"%"]}),(0,r.jsxs)("p",{className:"text-xs font-extralight",children:["*",Number(null==t?void 0:t.amountAchieved).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})]})]})}),(0,r.jsx)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,r.jsx)("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:(0,r.jsx)(v(),{alt:"",src:b,width:20})}),(0,r.jsx)("p",{className:"text-sm",children:t.over<0?"Valor excedido":"Valor \xe0 Captar"}),(0,r.jsx)("p",{className:"text-xs font-extralight",children:t.over<0?"Valor excedido da meta":"Valor Faltante"}),(0,r.jsx)("p",{className:"mt-3 text-2xl font-bold",children:t.over<0?(-1*t.over).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):t.over.toLocaleString("pt-BR",{style:"currency",currency:"BRL"})})]})})]})}function GoalDashboard(e){let{data:t}=e,a=(0,h.e)();return(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 mt-5",children:[(0,r.jsx)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,r.jsx)("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:(0,r.jsx)(v(),{alt:"",src:f.Z,width:20})}),(0,r.jsx)("p",{className:"text-sm",children:"Meta de capta\xe7\xe3o"}),(0,r.jsx)("p",{className:"text-xs font-extralight capitalize",children:t.monthName}),(0,r.jsx)("p",{className:"mt-3 text-2xl font-bold",children:Number(null==t?void 0:t.targetAmount).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})})]})}),(0,r.jsx)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[(0,r.jsx)("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:(0,r.jsx)(v(),{alt:"",src:b,width:20})}),(0,r.jsx)("p",{className:"text-sm",children:"Per\xedodo"}),(0,r.jsx)("p",{className:"text-xs font-extralight capitalize",children:t.monthName}),(0,r.jsx)("p",{className:"mt-3 text-2xl font-bold",children:t.month})]})}),("broker"===a.name||"advisor"===a.name)&&(0,r.jsx)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col w-full",children:[(0,r.jsx)("p",{className:"text-sm text-center",children:"Observa\xe7\xf5es"}),(0,r.jsx)("p",{className:"text-xs font-extralight mt-5 text-start",children:t.observation})]})})]})}a(6085);var j=a(5969),N=a(2307),w=a(4164);function newFormatDate(e){let t=e.split("T")[0],a=t.split("-");return{dateFormated:"".concat(a[2],"/").concat(a[1]),month:u()().locale("pt-br").month(Number(a[1])-1).format("MMMM")}}var y=a(6654);function AddPerspective(e){let{brokerGoalId:t}=e,[a,s]=(0,l.useState)(""),[n,d]=(0,l.useState)(""),[x,u]=(0,l.useState)(!1);return(0,r.jsxs)("div",{className:"mt-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-white mb-2",children:"Adicionar o valor"}),(0,r.jsx)("div",{className:"w-72",children:(0,r.jsx)(i.Z,{bg:"fill",id:"",label:"",placeholder:"R$",name:"",type:"text",value:a,onChange:e=>{s((0,m.Ht)(e.target.value))}})})]}),(0,r.jsxs)("div",{className:"mt-5",children:[(0,r.jsx)("p",{className:"text-white mb-2",children:"Adicione Observa\xe7\xf5es (caso necess\xe1rio)"}),(0,r.jsx)("div",{className:"w-[500px]",children:(0,r.jsx)("textarea",{value:n,placeholder:"Observa\xe7\xf5es",className:"bg-[#1C1C1C] h-32 w-full p-2 text-white rounded-xl ring-1 ring-inset flex-1 ring-[#FF9900]",onChange:e=>{let{target:t}=e;return d(t.value)}})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"flex justify-end mt-10",children:(0,r.jsx)("div",{children:(0,r.jsx)(p.z,{onClick:()=>{u(!0);let e={brokerGoalId:t,observation:n,amount:Number(a.replaceAll(".","").replace(",","."))};o.Z.patch("/goals/perspective-broker-amount",e).then(e=>{c.Am.success("Perspectiva cadastrada com sucesso!"),window.location.reload()}).catch(e=>{(0,y.Z)(e,"Erro ao criar perspectiva!")}).finally(()=>u(!1))},loading:x,children:"Concluir"})})})})]})}var A=a(5215);function Metas(){let[e,t]=(0,l.useState)(),[a,d]=(0,l.useState)(""),[i,m]=(0,l.useState)(!1),[x,g]=(0,l.useState)(!1),[v,f]=(0,l.useState)(!1),[b,D]=(0,l.useState)(""),[k,C]=(0,l.useState)(),[M,S]=(0,l.useState)("goals"),[_,L]=(0,l.useState)("goals"),[F,Z]=(0,l.useState)({percentage:0,month:"",monthName:"",observation:"",over:0,amountAchieved:0,targetAmount:0});function getDate(){let e="",t="";return""===b?(t=String(u()().year()),e=String(u()().month()+1)):(t=b.split("-")[0],e=b.split("-")[1]),{month:e,year:t}}let Y=(0,h.e)();function handleSelectTypeGoals(){"broker"===Y.name&&(o.Z.get("/goals/broker/".concat(Y.roleId),{params:{year:getDate().year,month:getDate().month,type:M}}).then(e=>{let a=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-01")).dateFormated,r=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-").concat(u()("".concat(getDate().year,"-").concat(getDate().month),"YYYY-MM").endOf("month").date())).dateFormated,s=e.data.advisorMetrics.map(e=>({goalName:e.goalName,brokerName:e.advisorName,brokerDocument:e.advisorDocument,targetAmount:e.targetAmount,totalAchieved:e.totalAchieved,percentAchieved:e.percentAchieved}));t({results:s,totalBrokerAchieved:0,totalGoalAmount:0}),d(e.data.id),Z({amountAchieved:e.data.totalAchieved,month:"".concat(a," \xe0 ").concat(r),monthName:newFormatDate("".concat(getDate().year,"-").concat(getDate().month,"-01")).month,observation:e.data.observations,over:Number(e.data.targetAmount)-Number(e.data.totalAchieved),percentage:e.data.percentAchieved,targetAmount:e.data.targetAmount})}).catch(e=>{(0,y.Z)(e,"N\xe3o conseguimos encontrar as metas!")}),getAcessors()),"advisor"===Y.name&&o.Z.get("/goals/advisor/".concat(Y.roleId),{params:{ownerRelationId:Y.roleId,year:getDate().year,month:getDate().month}}).then(e=>{let a=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-01")).dateFormated,r=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-").concat(u()("".concat(getDate().year,"-").concat(getDate().month),"YYYY-MM").endOf("month").date())).dateFormated;t({totalBrokerAchieved:0,totalGoalAmount:0,results:[{goalName:e.data.goalName,brokerName:e.data.advisorName,brokerDocument:e.data.advisorDocument,percentAchieved:e.data.percentAchieved,perspectiveAmount:e.data.perspectiveAmount,targetAmount:e.data.targetAmount,totalAchieved:e.data.totalAchieved}]}),Z({amountAchieved:e.data.totalAchieved,month:"".concat(a," \xe0 ").concat(r),monthName:newFormatDate("".concat(getDate().year,"-").concat(getDate().month,"-01")).month,observation:"",over:Number(e.data.targetAmount)-Number(e.data.totalAchieved),percentage:e.data.percentAchieved,targetAmount:e.data.targetAmount})}).catch(e=>{(0,y.Z)(e,"N\xe3o conseguimos encontrar as metas!")}),"admin"===Y.name&&getGoalsadmin(),"superadmin"===Y.name&&(o.Z.get("/goals/superadmin/broker",{params:{year:getDate().year,month:getDate().month,type:M}}).then(e=>{let a=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-01")).dateFormated,r=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-").concat(u()("".concat(getDate().year,"-").concat(getDate().month),"YYYY-MM").endOf("month").date())).dateFormated;t(e.data);let s=Number(e.data.totalGoalAmount),n=Number(e.data.totalBrokerAchieved);Z({amountAchieved:e.data.totalBrokerAchieved,month:"".concat(a," \xe0 ").concat(r),monthName:newFormatDate("".concat(getDate().year,"-").concat(getDate().month,"-01")).month,observation:"",over:Number(e.data.totalGoalAmount)-Number(e.data.totalBrokerAchieved),percentage:n/s*100,targetAmount:e.data.totalGoalAmount})}).catch(e=>{(0,y.Z)(e,"N\xe3o conseguimos encontrar as metas!")}),getBrokers())}(0,l.useEffect)(()=>{handleSelectTypeGoals()},[i]);let getGoalsadmin=()=>{o.Z.get("/goals/admin/".concat(Y.roleId,"/broker"),{params:{year:getDate().year,month:getDate().month}}).then(e=>{let a=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-01")).dateFormated,r=newFormatDate("".concat(getDate().year,"-").concat(getDate().month.padStart(2,"0"),"-").concat(u()("".concat(getDate().year,"-").concat(getDate().month),"YYYY-MM").endOf("month").date())).dateFormated,s=Number(e.data.totalGoalAmount),n=Number(e.data.totalBrokerAchieved);t(e.data),Z({amountAchieved:e.data.totalBrokerAchieved,month:"".concat(a," \xe0 ").concat(r),monthName:newFormatDate("".concat(getDate().year,"-").concat(getDate().month,"-01")).month,observation:"",over:Number(e.data.totalGoalAmount)-Number(e.data.totalBrokerAchieved),percentage:n/s*100,targetAmount:e.data.totalGoalAmount})}).catch(e=>{(0,y.Z)(e,"N\xe3o conseguimos encontrar as metas!")})},getAcessors=async()=>{try{let{data:e}=await o.Z.get("/wallets/broker/advisors",{params:{adviserId:Y.roleId}});C(e)}catch(e){(0,y.Z)(e,"N\xe3o conseguimos encontrar as metas!")}},getBrokers=async()=>{try{let{data:e}=await o.Z.get("/wallets/list-brokers");C(e)}catch(e){(0,y.Z)(e,"N\xe3o conseguimos encontrar as metas!")}},returnTypePage=()=>i?{title:"Nova Meta",component:(0,r.jsx)(GoalDashboard,{data:F}),subtitle:(0,r.jsxs)("h2",{className:"text-2xl text-center",children:["Adicionar Meta para ","admin"===Y.name||"superadmin"===Y.name?"Brokers":"Colaboradores"]}),page:(0,r.jsx)(AddGoals,{brokerAdvisor:k,setAddgoalModal:m,brokerGoalId:a,golsData:b})}:x?{title:"Nova Perspectiva",component:(0,r.jsx)("div",{}),subtitle:(0,r.jsx)("p",{}),page:(0,r.jsx)(AddPerspective,{brokerGoalId:a})}:{title:"Metas",component:(0,r.jsx)(AdminDashboard,{data:F}),subtitle:(0,r.jsx)("h2",{className:"text-2xl text-center",children:"perspective"===_?"Capta\xe7\xe3o da Perspectiva dos Brokers":"Meta de Capta\xe7\xe3o por ".concat("admin"===Y.name||"superadmin"===Y.name?"Brokers":"Colaborador")}),page:e&&(0,r.jsx)(ProgressTable,{filteredType:_,filters:[{label:"perspective"===_?"Perspectiva":"Meta atingida"},{label:"perspective"===_?"Meta Total":"Meta Restante"}],goals:e.results})};return(0,r.jsxs)("div",{children:[(0,r.jsx)(s.Z,{}),(0,r.jsx)(n.Z,{children:(0,r.jsx)("div",{className:"text-white",children:(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("p",{className:"text-2xl text-center",children:returnTypePage().title}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-start gap-2 md:gap-4 max-w-full overflow-hidden",children:[(0,r.jsxs)("div",{className:"relative",children:[!i&&!x&&(0,r.jsxs)(p.z,{onClick:()=>f(!v),className:"flex items-center gap-1 text-[9px] px-2 py-1 whitespace-nowrap bg-[#3A3A3A] hover:bg-[#4a4a4a] text-white rounded-md md:text-sm md:px-4 md:py-2",children:[(0,r.jsx)(j.Z,{className:"w-3 h-3 md:w-4 md:h-4",color:"#fff"}),(0,r.jsx)("span",{className:"text-[9px] md:text-sm",children:"Filtro"}),v?(0,r.jsx)(N.Z,{className:"w-3 h-3 md:w-4 md:h-4",color:"#fff"}):(0,r.jsx)(w.Z,{className:"w-3 h-3 md:w-4 md:h-4",color:"#fff"})]}),v&&!i&&!x&&(0,r.jsx)("div",{className:"absolute w-[300px] bg-[#3A3A3A] p-5 top-10 rounded-tr-lg rounded-b-lg z-10",children:(0,r.jsxs)("div",{className:"flex flex-col justify-between",children:[(0,r.jsxs)("div",{className:"",children:[(0,r.jsx)("p",{className:"text-sm mb-1",children:"Data"}),(0,r.jsx)("input",{value:b,className:"p-1 w-full rounded-md text-xs bg-transparent border placeholder-white text-white [color-scheme:dark] ",onChange:e=>{let{target:t}=e;D(t.value)},type:"month"})]}),("broker"===Y.name||"superadmin"===Y.name)&&(0,r.jsx)("div",{className:"my-2",children:(0,r.jsx)(A.Z,{selected:M,setSelected:S,options:[{label:"Metas Internas",value:"goals"},{label:"Perspectiva dos Brokers",value:"perspective"}]})}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(p.z,{className:"text-[9px] px-2 py-1 whitespace-nowrap md:text-sm md:px-4 md:py-2",onClick:()=>{if("broker"!==Y.name&&"superadmin"!==Y.name&&""===b)return c.Am.info("Selecione um m\xeas para poder aplicar o filtro");c.Am.info("Filtrando resultados!"),handleSelectTypeGoals(),f(!1),L(M)},children:"Aplicar"})})]})})]}),("broker"===Y.name||"superadmin"===Y.name)&&!x&&(0,r.jsx)("div",{children:(0,r.jsx)(p.z,{className:"text-[9px] px-2 py-1 whitespace-nowrap md:text-sm md:px-4 md:py-2",variant:i?"secondary":"default",onClick:()=>m(!i),children:i?"Cancelar":"Adicionar Meta"})}),"broker"===Y.name&&!i&&(0,r.jsx)(p.z,{className:"text-[9px] px-2 py-1 whitespace-nowrap md:text-sm md:px-4 md:py-2",variant:x?"secondary":"default",onClick:()=>g(!x),children:x?"Cancelar":"Adicionar Perspectiva"})]}),returnTypePage().component,(0,r.jsx)("div",{className:"my-5",children:returnTypePage().subtitle}),(0,r.jsx)("div",{children:returnTypePage().page})]})})})]})}},3220:function(e,t,a){"use strict";var r=a(7437),s=a(2265),n=a(1543),l=a(9367);let o=(0,s.forwardRef)((e,t)=>{let{label:a,bg:o,type:c,...d}=e,[i,m]=(0,s.useState)(!1);return(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-white mb-1",children:a}),(0,r.jsxs)("div",{className:"custom-input-wrapper h-12 w-full text-white rounded-xl ring-[#FF9900] ring-1 ".concat("month"===c?"":"ring-inset"," ").concat("transparent"===o?"bg-black":"bg-[#1C1C1C]"," flex-1 flex relative"),children:[(0,r.jsx)("input",{ref:t,type:i&&"password"===c?"text":c,...d,className:"w-full h-12 flex-1 px-4 bg-transparent rounded-xl"}),"password"===c&&(0,r.jsx)("div",{className:"mr-2 cursor-pointer absolute right-0 top-[50%] translate-y-[-50%]",onClick:()=>m(!i),children:i?(0,r.jsx)(n.Z,{width:20}):(0,r.jsx)(l.Z,{width:20})})]})]})});o.displayName="Input",t.Z=o},3336:function(e,t,a){"use strict";a.d(t,{Z:function(){return SelectSearch}});var r=a(7437),s=a(4164),n=a(2307),l=a(2265),o=a(8689);function SelectSearch(e){let{items:t,setValue:a,label:c,selectable:d=!1,disabled:i=!1,handleChange:m,loading:x=!1}=e,[u,h]=(0,l.useState)(!1),[p,g]=(0,l.useState)(""),[v,f]=(0,l.useState)([]),b=(0,l.useRef)(null),j=(0,l.useRef)(null);return(0,l.useEffect)(()=>{g("")},[x]),(0,l.useEffect)(()=>{if(""===p)return f(t);let e=null==t?void 0:t.filter(e=>e.name.toLowerCase().includes(p.toLowerCase()));f(e)},[p]),(0,l.useEffect)(()=>{f(t)},[t]),(0,l.useEffect)(()=>{let handleFocus=()=>{h(!0)},e=b.current;return e&&e.addEventListener("focus",handleFocus),()=>{e&&e.removeEventListener("focus",handleFocus)}},[]),(0,l.useEffect)(()=>{let handleClickOutside=e=>{j.current&&!j.current.contains(e.target)&&b.current&&!b.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]),(0,r.jsx)("div",{className:"w-full",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("p",{className:"text-white mb-1",children:c}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{disabled:i||x,ref:b,type:"text",value:x?"Carregando...":p,onChange:e=>{let{target:t}=e;return g(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer"}),(0,r.jsx)("div",{className:"absolute right-2 top-[50%] translate-y-[-50%]",children:u?(0,r.jsx)(n.Z,{width:20,color:"#fff"}):(0,r.jsx)(s.Z,{width:20,color:"#fff"})})]}),u&&(0,r.jsx)("div",{ref:j,className:"text-white absolute w-full border-b border-x px-2 top-[98%] z-40 bg-black max-h-52 overflow-y-auto scroll-",children:x?(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsx)(o.j,{height:"25px",className:"my-1"}),(0,r.jsx)(o.j,{height:"25px",className:"my-1"})]}):(null==v?void 0:v.length)>0?null==v?void 0:v.map(e=>(0,r.jsx)("div",{onClick:()=>{h(!1),m&&m(e),d?(g(""),a("".concat(e.name," - ").concat(e.id))):(a(e.id),g(e.name))},className:"cursor-pointer my-1 hover:bg-zinc-900",children:e.name},e.id)):(0,r.jsx)("div",{className:"py-3",children:(0,r.jsx)("p",{className:"text-zinc-400",children:"Nenhum dado encontrado!"})})})]})})}},6654:function(e,t,a){"use strict";a.d(t,{Z:function(){return returnError}});var r=a(3014);function returnError(e,t){var a,s,n,l;let o=(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.error);if(Array.isArray(o))return o.forEach(e=>{r.Am.error(e,{toastId:e})}),o.join("\n");if("string"==typeof o)return r.Am.error(o,{toastId:o}),o;if("object"==typeof o&&null!==o){let e=Object.values(o).flat().join("\n");return r.Am.error(e,{toastId:e}),e}return r.Am.error(t,{toastId:t}),t}},9367:function(e,t,a){"use strict";var r=a(2265);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=s},1543:function(e,t,a){"use strict";var r=a(2265);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});t.Z=s}},function(e){e.O(0,[6990,8276,5371,6946,3151,2971,7864,1744],function(){return e(e.s=8725)}),_N_E=e.O()}]);