(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9356],{2480:function(){},5864:function(e,r,t){Promise.resolve().then(t.bind(t,1900))},1900:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return Registro}});var i=t(7437),a=t(4568),n=t(2265),s=t(3014),o=t(2875),l=t(1865),d=t(9701),c=t(4829),x=t(7934),m=t(5968),u=t(7395),f=t(7157);function Registro(e){let{params:r}=e,[t,h]=(0,n.useState)(!1),[g,p]=(0,n.useState)(!1),[b,N]=(0,n.useState)(),[w,v]=(0,n.useState)(void 0),{register:y,handleSubmit:j,watch:_,setValue:Z,reset:O,formState:{errors:q}}=(0,l.cI)({resolver:(0,d.X)(x._n)}),C=_("zipCode");(0,n.useEffect)(()=>{(function(e){let r=e.replace(/[^0-9]/g,"");8===r.length&&handleGetByCep(r)})(C||"")},[C]),(0,n.useEffect)(()=>{validateToken()},[]);let validateToken=()=>{a.Z.get("/pre-register/token-validate",{headers:{token:r.id}}).then(e=>{N(e.data.investment),Z("email",e.data.email),Z("document",e.data.document),v(e.data.owner)}).catch(e=>{s.Am.error("Tivemos um erro ao buscar os dados")})};async function handleGetByCep(e){let r=e.replace(/[^0-9]/g,"");8!==r.length||await c.Z.get("https://viacep.com.br/ws/".concat(r,"/json/")).then(e=>{e&&e.data&&(e.data.erro||(Z("neighborhood",""!==e.data.bairro?"".concat(e.data.bairro," - ").concat(e.data.logradouro):""),Z("city",e.data.localidade),Z("state",e.data.uf)))}).catch(()=>{}).finally(()=>{})}return(0,i.jsxs)("div",{className:"px-10",children:[(0,i.jsxs)("div",{className:"mt-10",children:[(0,i.jsx)("img",{className:"mx-auto h-10 w-auto",src:"/logo.svg",alt:"Your Company"}),(0,i.jsx)("p",{className:"text-2xl text-white text-center my-10",children:"Preencha os Dados Necess\xe1rios"})]}),(0,i.jsx)("form",{action:"",onSubmit:j(e=>{if(p(!0),(0,f.m)(e.dtBirth))return s.Am.warn("O investidor n\xe3o pode ser menor de idade.");let t={name:e.name,rg:e.rg,document:(0,m.p4)(e.document||""),phoneNumber:"+55".concat((0,m.p4)(e.phoneNumber||"")),dtBirth:e.dtBirth,email:e.email,signIca:u.l,occupation:e.occupation,placeOfBirth:e.placeOfBirth,issuer:e.issuer,motherName:e.motherName,address:{zipCode:e.zipCode,neighborhood:e.neighborhood,city:e.city,complement:e.complement,number:e.number,state:e.state},investment:b,observations:"",accountBank:{bank:e.bank,accountNumber:e.accountNumber,agency:e.agency,pix:e.pix},accountType:w?"BUSINESS":"PHYSICAL",owner:w||void 0,testify:[{name:(null==e?void 0:e.testifyPrimaryName)||"",cpf:(0,m.p4)(e.testifyPrimaryCpf||""),sign:(null==e?void 0:e.testifyPrimaryName)||""},{name:e.testifySecondaryName||"",cpf:(0,m.p4)(e.testifySecondaryCpf||""),sign:e.testifySecondaryName||""}]};a.Z.post("/contract",t,{headers:{token:r.id}}).then(e=>{s.Am.success("Conta pr\xe9 cadastrada com sucesso!"),O(),window.close()}).catch(e=>{s.Am.error(e.response.data.message||"Tivemos um erro ao cadastrar a conta")}).finally(()=>p(!1))}),children:(0,i.jsxs)("div",{className:"md:w-9/12 m-auto",children:[(0,i.jsx)("p",{className:"text-xl text-white",children:"Dados Pessoais"}),(0,i.jsxs)("div",{className:"mb-10 p-7 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.name&&"- ".concat(q.name.message)})]}),(0,i.jsx)("input",{...y("name"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.name?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Identidade ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.rg&&"- ".concat(q.rg.message)})]}),(0,i.jsx)("input",{...y("rg"),type:"number",className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.rg?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(null==b?void 0:b.modality)==="SCP"&&(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Org\xe3o emissor",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.issuer&&"- ".concat(q.issuer.message)})]}),(0,i.jsx)("input",{...y("issuer"),type:"text",className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.issuer?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Celular ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.phoneNumber&&"- ".concat(q.phoneNumber.message)})]}),(0,i.jsx)("input",{...y("phoneNumber"),onChange:e=>{let{target:r}=e;return Z("phoneNumber",(0,m.gP)(r.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(null==b?void 0:b.modality)==="SCP"&&(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Ocupa\xe7\xe3o ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.occupation&&"- ".concat(q.occupation.message)})]}),(0,i.jsx)("input",{...y("occupation"),type:"text",className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.occupation?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CPF/CNPJ ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.document&&"- ".concat(q.document.message)})]}),(0,i.jsx)("input",{...y("document"),disabled:!0,className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.document?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.dtBirth&&"- ".concat(q.dtBirth.message)})]}),(0,i.jsx)("input",{...y("dtBirth"),type:"date",className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(null==b?void 0:b.modality)==="SCP"&&(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nacionalidade",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.placeOfBirth&&"- ".concat(q.placeOfBirth.message)})]}),(0,i.jsx)("input",{...y("placeOfBirth"),type:"text",className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.placeOfBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),w&&(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white mb-1",children:"Nome do representante"}),(0,i.jsx)("input",{disabled:!0,value:null==w?void 0:w.name,className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1"})]})}),(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white mb-1",children:"CPF do representante"}),(0,i.jsx)("input",{type:"text",disabled:!0,value:(0,m.VL)((null==w?void 0:w.cpf)||""),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1"})]})})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["E-mail ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.email&&"- ".concat(q.email.message)})]}),(0,i.jsx)("input",{...y("email"),disabled:!0,className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.motherName&&"- ".concat(q.motherName.message)})]}),(0,i.jsx)("input",{...y("motherName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.motherName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]}),(0,i.jsx)("div",{className:"flex items-center",children:(0,i.jsx)("p",{className:"text-xl text-white mr-1",children:"Informa\xe7\xf5es de Endere\xe7o"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CEP ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.zipCode&&"- ".concat(q.zipCode.message)})]}),(0,i.jsx)("input",{...y("zipCode"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.zipCode?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Endere\xe7o ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.neighborhood&&"- ".concat(q.neighborhood.message)})]}),(0,i.jsx)("input",{...y("neighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.neighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.number&&"- ".concat(q.number.message)})]}),(0,i.jsx)("input",{...y("number"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.number?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Cidade ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.city&&"- ".concat(q.city.message)})]}),(0,i.jsx)("input",{...y("city"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.city?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Estado ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.state&&"- ".concat(q.state.message)})]}),(0,i.jsx)("input",{...y("state"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.state?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Complemento ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.complement&&"- ".concat(q.complement.message)})]}),(0,i.jsx)("input",{...y("complement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.complement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]}),(0,i.jsx)("div",{className:"flex items-center",children:(0,i.jsx)("p",{className:"text-xl text-white mr-1",children:"Informa\xe7\xf5es Bancarias"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Banco ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.bank&&"- ".concat(q.bank.message)})]}),(0,i.jsx)("input",{...y("bank"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.bank?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Ag\xeancia ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.agency&&"- ".concat(q.agency.message)})]}),(0,i.jsx)("input",{...y("agency"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.agency?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Conta ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.accountNumber&&"- ".concat(q.accountNumber.message)})]}),(0,i.jsx)("input",{...y("accountNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.accountNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,i.jsx)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:(0,i.jsx)("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Chave PIX ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.pix&&"- ".concat(q.pix.message)})]}),(0,i.jsx)("input",{...y("pix"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.pix?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})})]}),(null==b?void 0:b.modality)==="SCP"&&(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsxs)("div",{className:"w-2/4",children:[(0,i.jsx)("div",{className:"flex items-center",children:(0,i.jsx)("p",{className:"text-xl text-white mr-1",children:"Testemunha 1"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsx)("div",{children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.testifyPrimaryName&&"- ".concat(q.testifyPrimaryName.message)})]}),(0,i.jsx)("input",{...y("testifyPrimaryName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.testifyPrimaryName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CPF ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.testifyPrimaryCpf&&"- ".concat(q.testifyPrimaryCpf.message)})]}),(0,i.jsx)("input",{...y("testifyPrimaryCpf"),onChange:e=>{let{target:r}=e;Z("testifyPrimaryCpf",(0,m.VL)(r.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.testifyPrimaryCpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]}),(0,i.jsxs)("div",{className:"w-2/4",children:[(0,i.jsx)("div",{className:"flex items-center",children:(0,i.jsx)("p",{className:"text-xl text-white mr-1",children:"Testemunha 2"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsx)("div",{children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.testifySecondaryName&&"- ".concat(q.testifySecondaryName.message)})]}),(0,i.jsx)("input",{...y("testifySecondaryName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.testifySecondaryName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CPF ",(0,i.jsx)("b",{className:"text-red-500 font-light text-sm",children:q.testifySecondaryCpf&&"- ".concat(q.testifySecondaryCpf.message)})]}),(0,i.jsx)("input",{...y("testifySecondaryCpf"),onChange:e=>{let{target:r}=e;Z("testifySecondaryCpf",(0,m.VL)(r.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(q.testifySecondaryCpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,i.jsx)("p",{className:"text-xl text-white",children:"Dados para Dep\xf3sito"}),(0,i.jsxs)("div",{className:"mb-10 p-7 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsx)("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),(0,i.jsx)("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),(0,i.jsx)("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),(0,i.jsx)("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),(0,i.jsx)("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),(0,i.jsx)("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]}),(0,i.jsxs)("div",{className:"mb-5",children:[(0,i.jsx)("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),(0,i.jsx)("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,i.jsxs)("div",{className:"flex mt-5",children:[(0,i.jsx)("input",{type:"checkbox",checked:t,className:"cursor-pointer",onChange:()=>h(!t)}),(0,i.jsx)("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),(0,i.jsx)("div",{className:"md:w-52 mb-10",children:(0,i.jsx)(o.Z,{label:"ENVIAR",loading:g,disabled:!t||g})})]})})]})}},2875:function(e,r,t){"use strict";t.d(r,{Z:function(){return Button}});var i=t(7437),a=t(8700);function Button(e){let{handleSubmit:r,loading:t,label:n,disabled:s,className:o,...l}=e;return(0,i.jsx)(a.z,{...l,onClick:r,loading:t,disabled:s,className:o,children:n})}},8700:function(e,r,t){"use strict";t.d(r,{d:function(){return d},z:function(){return c}});var i=t(7437),a=t(2265),n=t(7256),s=t(6061),o=t(3715),l=t(992);let d=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-orange-linear text-white shadow hover:bg-orange-black-linear",destructive:"bg-[#BC4C4C] text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"bg-black text-white rounded-xl ring-[#FF9900] ring-1 ring-inset",secondary:"bg-primary text-primary-foreground shadow hover:bg-primary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,r)=>{let{className:t,variant:a,size:s,asChild:c=!1,loading:x=!1,...m}=e,u=c?n.g7:"button";return(0,i.jsxs)(u,{className:(0,l.cn)(d({variant:a,size:s,className:t}),"flex items-center gap-2 select-none"),ref:r,disabled:x||m.disabled,...m,children:[x&&(0,i.jsx)(o.Z,{className:"h-4 w-4 animate-spin"}),m.children]})});c.displayName="Button"},7395:function(e,r,t){"use strict";t.d(r,{l:function(){return i}});let i="Shayra Madalena Lyra de Pinho"},4568:function(e,r,t){"use strict";t.d(r,{o:function(){return setToken}});var i=t(3256),a=t(4829),n=t(3014);let s=a.Z.create({baseURL:"http://localhost:3001"}),setToken=e=>{s.defaults.headers.common.Authorization="Bearer ".concat(e)},o=!1,l=[],processQueue=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;l.forEach(t=>{r?t.resolve(r):t.reject(e)}),l=[]};s.interceptors.response.use(e=>e,async e=>{var r;let t=e.config;if((null===(r=e.response)||void 0===r?void 0:r.status)===401&&!t._retry){let r=(0,i.P)(),d=sessionStorage.getItem("refreshToken");if(!d)return n.Am.error("Sess\xe3o expirada. Fa\xe7a login novamente!"),sessionStorage.clear(),window.location.href="/",Promise.reject(e);if(t._retry=!0,o)return new Promise(function(e,r){l.push({resolve:e,reject:r})}).then(e=>(t.headers.Authorization="Bearer ".concat(e),(0,a.Z)(t))).catch(e=>Promise.reject(e));o=!0;try{let{data:e}=await s.post("/auth-backoffice/login",{document:r.document,refreshToken:d}),i=e.accessToken;return setToken(i),sessionStorage.setItem("token",i),processQueue(null,i),t.headers.Authorization="Bearer ".concat(i),(0,a.Z)(t)}catch(e){n.Am.error("Erro ao validar token, fa\xe7a login novamente!"),setTimeout(()=>(processQueue(e,null),sessionStorage.clear(),window.location.href="/",Promise.reject(e)),1500)}finally{o=!1}}return Promise.reject(e)}),r.Z=s},3256:function(e,r,t){"use strict";t.d(r,{P:function(){return getUser},e:function(){return getUserProfile}});var i=t(9588);function getUserProfile(){(0,i.Uh)();let e=(0,i.aA)();return e||(console.warn("Perfil n\xe3o encontrado, usando padr\xe3o"),{name:"investor",roleId:""})}function getUser(){(0,i.Uh)();let e=(0,i.mo)();return e||{name:"investor",id:"",document:"",roles:[]}}},9588:function(e,r,t){"use strict";t.d(r,{mo:function(){return getDecryptedUser},aA:function(){return getDecryptedUserProfile},zO:function(){return persistSessionData},Uh:function(){return restoreSessionData},Tl:function(){return setEncryptedUser},jE:function(){return setEncryptedUserProfile}});var i=t(4155),a=t.n(i);let n="f5beb0e0-8b97-4440-9ef2-cd6b2557345a";if(!n)throw Error("Chave de criptografia n\xe3o definida em NEXT_PUBLIC_CRYPTO_SECRET_KEY");function encryptData(e){let r=JSON.stringify(e);return a().AES.encrypt(r,n).toString()}function decryptData(e){try{let r=a().AES.decrypt(e,n),t=r.toString(a().enc.Utf8);return JSON.parse(t)}catch(e){return console.error("Erro ao descriptografar dados:",e),null}}var s=t(2601);let o=null,l=null,d=s.env.NEXT_PUBLIC_MEMORY_KEY||"secure-memory-key",c="session_token";function setEncryptedUser(e){let r=encryptData(e);o=a().AES.encrypt(r,d).toString()}function getDecryptedUser(){if(!o)return null;try{let e=a().AES.decrypt(o,d).toString(a().enc.Utf8);return decryptData(e)}catch(e){return console.error("Erro ao descriptografar usu\xe1rio:",e),null}}function setEncryptedUserProfile(e){let r=encryptData(e);l=a().AES.encrypt(r,d).toString()}function getDecryptedUserProfile(){if(!l)return null;try{let e=a().AES.decrypt(l,d).toString(a().enc.Utf8);return decryptData(e)}catch(e){return console.error("Erro ao descriptografar perfil de usu\xe1rio:",e),null}}function persistSessionData(){if(!o||!l)return;let e={user:o,profile:l},r=a().AES.encrypt(JSON.stringify(e),d).toString();sessionStorage.setItem(c,r)}function restoreSessionData(){let e=sessionStorage.getItem(c);if(!e)return!1;try{let r=a().AES.decrypt(e,d).toString(a().enc.Utf8),t=JSON.parse(r);return o=t.user,l=t.profile,!0}catch(e){return console.error("Erro ao restaurar dados da sess\xe3o:",e),!1}}},992:function(e,r,t){"use strict";t.d(r,{cn:function(){return cn}});var i=t(7042),a=t(4769);function cn(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.m6)((0,i.W)(r))}},7157:function(e,r,t){"use strict";t.d(r,{m:function(){return isUnderage}});var i=t(2067),a=t.n(i);function isUnderage(e){let r=a()(e),t=a()().diff(r,"years");return t<18}},5968:function(e,r,t){"use strict";t.d(r,{Ht:function(){return valueMask},PK:function(){return cnpjMask},Tc:function(){return cepMask},VL:function(){return cpfMask},gP:function(){return phoneMask},p4:function(){return clearLetters}});let cpfMask=e=>e.replace(/\D/g,"").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d{1,2})/,"$1-$2").replace(/(-\d{2})\d+?$/,"$1"),cnpjMask=e=>e.replace(/\D/g,"").replace(/^(\d{2})(\d)/,"$1.$2").replace(/^(\d{2})\.(\d{3})(\d)/,"$1.$2.$3").replace(/\.(\d{3})(\d)/,".$1/$2").replace(/(\d{4})(\d)/,"$1-$2").replace(/(-\d{2})\d+?$/,"$1"),valueMask=e=>e.replace(/\D/g,"").replace(/(\d{1})(\d{14})$/,"$1.$2").replace(/(\d{1})(\d{11})$/,"$1.$2").replace(/(\d{1})(\d{8})$/,"$1.$2").replace(/(\d{1})(\d{5})$/,"$1.$2").replace(/(\d{1})(\d{1,2})$/,"$1,$2"),phoneMask=e=>e.replace(/\D/g,"").replace(/^55/,"").replace(/^(\d{2})(\d)/g,"($1) $2").replace(/(\d)(\d{4})$/,"$1-$2"),clearLetters=e=>e.replace(/\D/g,""),cepMask=e=>e.replace(/\D/g,"").replace(/(\d{5})(\d)/,"$1-$2").replace(/(-\d{3})\d+?$/,"$1")},7934:function(e,r,t){"use strict";t.d(r,{WF:function(){return s},_n:function(){return o},bs:function(){return n}});var i=t(5691),a=t(5968);let n=i.Ry().shape({document:i.Z_().required("Obrigat\xf3rio"),email:i.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),value:i.Z_().required("Obrigat\xf3rio"),term:i.Z_().required("Obrigat\xf3rio"),modality:i.Z_().required("Obrigat\xf3rio"),yield:i.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchaseWith:i.Z_().required("Obrigat\xf3rio"),amountQuotes:i.Z_().default(""),startContract:i.Z_().required("Obrigat\xf3rio"),endContract:i.Z_().required("Obrigat\xf3rio"),profile:i.Z_().required("Obrigat\xf3rio"),details:i.Z_().notRequired(),debenture:i.Z_().required("Obrigat\xf3rio")}).required();i.Ry().shape({name:i.Z_().required("Obrigat\xf3rio"),rg:i.Z_().required("Obrigat\xf3rio"),document:i.Z_().required("Obrigat\xf3rio"),phoneNumber:i.Z_().min(15,"N\xfamero de telefone inv\xe1lido!").max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:i.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,i,a]=e.split("-");if(!r||!i||!a||4!==r.length)return!1;let n=Number(r);if(isNaN(n)||n<1900||n>new Date().getFullYear())return!1;let s=new Date(e);return!(isNaN(s.getTime())||t(7157).m(e))}),email:i.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:i.Z_().required("Obrigat\xf3rio"),neighborhood:i.Z_().required("Obrigat\xf3rio"),state:i.Z_().required("Obrigat\xf3rio"),city:i.Z_().required("Obrigat\xf3rio"),complement:i.Z_().default(""),number:i.Z_().required("Obrigat\xf3rio"),value:i.Z_().required("Obrigat\xf3rio"),term:i.Z_().required("Obrigat\xf3rio"),modality:i.Z_().required("Obrigat\xf3rio"),yield:i.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),observations:i.Z_().default(""),purchaseWith:i.Z_().required("Obrigat\xf3rio"),amountQuotes:i.Z_(),initDate:i.Z_().required("Obrigat\xf3rio"),endDate:i.Z_().required("Obrigat\xf3rio"),gracePeriod:i.Z_().required("Obrigat\xf3rio"),profile:i.Z_().required("Obrigat\xf3rio"),bank:i.Z_().required("Obrigat\xf3rio"),accountNumber:i.Z_().required("Obrigat\xf3rio"),agency:i.Z_().required("Obrigat\xf3rio"),pix:i.Z_().required("Obrigat\xf3rio"),debenture:i.Z_().required("Obrigat\xf3rio"),motherName:i.Z_(),placeOfBirth:i.Z_(),occupation:i.Z_(),issuer:i.Z_(),testifyPrimaryName:i.Z_(),testifyPrimaryCpf:i.Z_(),testifySecondaryName:i.Z_(),testifySecondaryCpf:i.Z_(),companyAddress:i.Z_(),companyCity:i.Z_(),companyUF:i.Z_(),companyType:i.Z_()}).required(),i.Ry().shape({value:i.Z_().required("Obrigat\xf3rio"),profile:i.Z_().required("Obrigat\xf3rio"),yield:i.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),date:i.Z_().required("Obrigat\xf3rio"),bank:i.Z_().required("Obrigat\xf3rio"),accountNumber:i.Z_().required("Obrigat\xf3rio"),agency:i.Z_().required("Obrigat\xf3rio"),pix:i.Z_().required("Obrigat\xf3rio")}),i.Ry().shape({name:i.Z_().required("Obrigat\xf3rio"),document:i.Z_().required("Obrigat\xf3rio"),phoneNumber:i.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:i.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,i,a]=e.split("-");if(!r||!i||!a||4!==r.length)return!1;let n=Number(r);if(isNaN(n)||n<1900||n>new Date().getFullYear())return!1;let s=new Date(e);return!(isNaN(s.getTime())||t(7157).m(e))}),email:i.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:i.Z_().required("Obrigat\xf3rio"),neighborhood:i.Z_().required("Obrigat\xf3rio"),state:i.Z_().required("Obrigat\xf3rio"),city:i.Z_().required("Obrigat\xf3rio"),complement:i.Z_().default(""),number:i.Z_().required("Obrigat\xf3rio"),profile:i.Z_().required("Obrigat\xf3rio"),term:i.Z_().required("Obrigat\xf3rio"),yield:i.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),value:i.Z_().required("Obrigat\xf3rio"),bank:i.Z_().required("Obrigat\xf3rio"),agency:i.Z_().required("Obrigat\xf3rio"),accountNumber:i.Z_().required("Obrigat\xf3rio"),pix:i.Z_().required("Obrigat\xf3rio"),debenture:i.Z_().required("Obrigat\xf3rio"),observations:i.Z_().default(""),details:i.Z_().default(""),initDate:i.Z_().required("Obrigat\xf3rio"),endDate:i.Z_().required("Obrigat\xf3rio"),amountQuotes:i.Z_().default(""),modality:i.Z_().required("Obrigat\xf3rio"),purchaseWith:i.Z_().required("Obrigat\xf3rio"),motherName:i.Z_().when("document",(e,r)=>e[0]&&(0,a.p4)(e[0]).length<=11?r.required("Campo obrigat\xf3rio"):r.notRequired())}).required();let s=i.Ry().shape({isPf:i.O7().default(!1),birthDate:i.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return!1;let r=new Date(e),i=r.getFullYear();return!(isNaN(i)||i<1900||i>new Date().getFullYear()||r>new Date||t(7157).m(e))}),socialName:i.Z_(),isTaxable:i.Z_().required("Obrigat\xf3rio"),fullName:i.Z_().required("Obrigat\xf3rio"),cpf:i.Z_().required("Obrigat\xf3rio"),email:i.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),phoneNumber:i.Z_().required("Obrigat\xf3rio"),motherName:i.Z_().required("Obrigat\xf3rio"),pep:i.Z_().required("Obrigat\xf3rio"),ownerCep:i.Z_().required("Obrigat\xf3rio"),ownerCity:i.Z_().required("Obrigat\xf3rio"),ownerState:i.Z_().required("Obrigat\xf3rio"),ownerNeighborhood:i.Z_().required("Obrigat\xf3rio"),ownerStreet:i.Z_().required("Obrigat\xf3rio"),ownerComplement:i.Z_().notRequired(),ownerNumber:i.Z_().required("Obrigat\xf3rio"),fantasyName:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),cnpj:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),companyName:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessPhoneNumber:i.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),dtOpening:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessEmail:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"):r.notRequired()),type:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),size:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessCep:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessCity:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessState:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessNeighborhood:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessStreet:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessComplement:i.Z_().when("isPf",(e,r)=>(e[0],r.notRequired())),businessNumber:i.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),participationPercentage:i.Rx().min(0,"O valor m\xednimo \xe9 0").max(15,"O valor m\xe1ximo \xe9 15").typeError("O valor deve ser um n\xfamero v\xe1lido").when("$hide",{is:!0,then:e=>e.notRequired(),otherwise:e=>e.required("Obrigat\xf3rio")})}).required(),o=i.Ry().shape({name:i.Z_().required("Obrigat\xf3rio"),rg:i.Z_().required("Obrigat\xf3rio"),document:i.Z_().required("Obrigat\xf3rio"),phoneNumber:i.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:i.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,i,a]=e.split("-");if(!r||!i||!a||4!==r.length)return!1;let n=Number(r);if(isNaN(n)||n<1900||n>new Date().getFullYear())return!1;let s=new Date(e);return!(isNaN(s.getTime())||t(7157).m(e))}),email:i.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:i.Z_().required("Obrigat\xf3rio"),neighborhood:i.Z_().required("Obrigat\xf3rio"),state:i.Z_().required("Obrigat\xf3rio"),city:i.Z_().required("Obrigat\xf3rio"),complement:i.Z_().default(""),number:i.Z_().required("Obrigat\xf3rio"),bank:i.Z_().required("Obrigat\xf3rio"),accountNumber:i.Z_().required("Obrigat\xf3rio"),agency:i.Z_().required("Obrigat\xf3rio"),pix:i.Z_().required("Obrigat\xf3rio"),motherName:i.Z_().required("Obrigat\xf3rio"),placeOfBirth:i.Z_(),occupation:i.Z_(),issuer:i.Z_(),testifyPrimaryName:i.Z_(),testifyPrimaryCpf:i.Z_(),testifySecondaryName:i.Z_(),testifySecondaryCpf:i.Z_()}).required()}},function(e){e.O(0,[6990,8276,5371,1865,3964,2971,7864,1744],function(){return e(e.s=5864)}),_N_E=e.O()}]);