"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoritePixController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const add_favorite_pix_dto_1 = require("../dto/add-favorite-pix.dto");
const delete_favorite_pix_dto_1 = require("../dto/delete-favorite-pix.dto");
const get_favorite_pix_account_dto_1 = require("../dto/get-favorite-pix-account.dto");
const add_favorite_pix_service_1 = require("../service/add-favorite-pix.service");
const delete_favorite_pix_service_1 = require("../service/delete-favorite-pix.service");
const get_favorite_pix_account_service_1 = require("../service/get-favorite-pix-account.service");
let FavoritePixController = class FavoritePixController {
    constructor(addFavoriteService, getFavoriteService, deleteFavoriteService) {
        this.addFavoriteService = addFavoriteService;
        this.getFavoriteService = getFavoriteService;
        this.deleteFavoriteService = deleteFavoriteService;
    }
    async addFavorite(body, request) {
        try {
            const data = await this.addFavoriteService.perform(body, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async deleteFavorite(query, request) {
        try {
            const data = await this.deleteFavoriteService.perform(query, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getFavoriteAccount(query, request) {
        try {
            const data = await this.getFavoriteService.perform(request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
};
exports.FavoritePixController = FavoritePixController;
__decorate([
    (0, common_1.Post)('add'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [add_favorite_pix_dto_1.AddFavoritePixDto, Object]),
    __metadata("design:returntype", Promise)
], FavoritePixController.prototype, "addFavorite", null);
__decorate([
    (0, common_1.Delete)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [delete_favorite_pix_dto_1.DeleteFavoritePixDto, Object]),
    __metadata("design:returntype", Promise)
], FavoritePixController.prototype, "deleteFavorite", null);
__decorate([
    (0, common_1.Get)('account'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_favorite_pix_account_dto_1.GetFavoritePixAccountDto, Object]),
    __metadata("design:returntype", Promise)
], FavoritePixController.prototype, "getFavoriteAccount", null);
exports.FavoritePixController = FavoritePixController = __decorate([
    (0, common_1.Controller)('pix/favorite'),
    __param(0, (0, common_1.Inject)(add_favorite_pix_service_1.AddFavoritePixService)),
    __param(1, (0, common_1.Inject)(get_favorite_pix_account_service_1.GetFavoritePixAccountService)),
    __param(2, (0, common_1.Inject)(delete_favorite_pix_service_1.DeleteFavoritePixService)),
    __metadata("design:paramtypes", [add_favorite_pix_service_1.AddFavoritePixService,
        get_favorite_pix_account_service_1.GetFavoritePixAccountService,
        delete_favorite_pix_service_1.DeleteFavoritePixService])
], FavoritePixController);
//# sourceMappingURL=favorite-pix.controller.js.map