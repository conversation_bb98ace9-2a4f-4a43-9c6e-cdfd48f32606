(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7548],{9756:function(e,t,i){Promise.resolve().then(i.bind(i,973))},973:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return Register}});var r=i(7437),a=i(3877),s=i(8637),o=i(2265),n=i(4207),l=i(9701),d=i(1865),u=i(2875),c=i(5968),m=i(5691);let v=m.Ry().shape({isSCP:m.O7(),name:m.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:m.Z_().required("Campo obrigat\xf3rio"),email:m.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),rg:m.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),phoneNumber:m.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:m.Z_().required("Data de nascimento obrigat\xf3ria").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[t,r,a]=e.split("-");if(!t||!r||!a||4!==t.length)return!1;let s=Number(t);if(isNaN(s)||s<1900||s>new Date().getFullYear())return!1;let o=new Date(e);return!(o>new Date||isNaN(o.getTime())||i(7157).m(e))}),motherName:m.Z_().min(3,"Nome da m\xe3e deve conter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),zipCode:m.Z_().required("Obrigat\xf3rio"),neighborhood:m.Z_().required("Obrigat\xf3rio"),state:m.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:m.Z_().required("Obrigat\xf3rio"),complement:m.Z_().default("").notRequired(),number:m.Z_().required("Obrigat\xf3rio"),street:m.Z_().required("Obrigat\xf3rio"),value:m.Z_().required("Obrigat\xf3rio"),term:m.Z_().required("Obrigat\xf3rio"),yield:m.Z_().required("Obrigat\xf3rio"),purchasedWith:m.Z_().required("Obrigat\xf3rio"),isDebenture:m.Z_().required("Obrigat\xf3rio"),initDate:m.Z_().required("Obrigat\xf3rio"),endDate:m.Z_().required("Obrigat\xf3rio"),profile:m.Z_().required("Obrigat\xf3rio"),bank:m.Z_().required("Obrigat\xf3rio"),agency:m.Z_().required("Obrigat\xf3rio"),accountNumber:m.Z_().required("Obrigat\xf3rio"),pix:m.Z_().notRequired(),issuer:m.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:m.Z_().required("Campo obrigat\xf3rio"),occupation:m.Z_().required("Campo obrigat\xf3rio"),amountQuotes:m.Z_().when("isSCP",(e,t)=>!0===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),observations:m.Z_().notRequired()}).required(),h=m.Ry().shape({isSCP:m.O7(),email:m.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),phoneNumber:m.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:m.Z_().required("Campo obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[t,r,a]=e.split("-");if(!t||!r||!a||4!==t.length)return!1;let s=Number(t);if(isNaN(s)||s<1900||s>new Date().getFullYear())return!1;let o=new Date(e);return!(o>new Date||isNaN(o.getTime())||i(7157).m(e))}),rg:m.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Obrigat\xf3rio"),ownerName:m.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),ownerDocument:m.Z_().min(3,"O nome da m\xe3e deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),motherName:m.Z_().required("Campo obrigat\xf3rio"),issuer:m.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:m.Z_().required("Campo obrigat\xf3rio"),occupation:m.Z_().required("Campo obrigat\xf3rio"),name:m.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:m.Z_().required("Campo obrigat\xf3rio"),companyType:m.Z_().required("Obrigat\xf3rio"),zipCode:m.Z_().required("Obrigat\xf3rio"),neighborhood:m.Z_().required("Obrigat\xf3rio"),state:m.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:m.Z_().required("Obrigat\xf3rio"),complement:m.Z_().default(""),number:m.Z_().required("Obrigat\xf3rio"),street:m.Z_().required("Obrigat\xf3rio"),value:m.Z_().required("Obrigat\xf3rio"),term:m.Z_().required("Obrigat\xf3rio"),yield:m.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchasedWith:m.Z_().required("Obrigat\xf3rio"),initDate:m.Z_().required("Obrigat\xf3rio"),endDate:m.Z_().required("Obrigat\xf3rio"),profile:m.Z_().required("Obrigat\xf3rio"),isDebenture:m.Z_().required("Obrigat\xf3rio"),amountQuotes:m.Z_().when("isSCP",(e,t)=>!0===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),bank:m.Z_().required("Obrigat\xf3rio"),agency:m.Z_().required("Obrigat\xf3rio"),accountNumber:m.Z_().required("Obrigat\xf3rio"),pix:m.Z_().notRequired(),companyNumber:m.Z_().required("Campo obrigat\xf3rio"),companyComplement:m.Z_().default(""),companyCity:m.Z_().required("Campo obrigat\xf3rio"),companyState:m.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Campo obrigat\xf3rio"),companyStreet:m.Z_().required("Campo obrigat\xf3rio"),companyZipCode:m.Z_().required("Campo obrigat\xf3rio"),companyNeighborhood:m.Z_().required("Campo obrigat\xf3rio"),observations:m.Z_().notRequired()}).required();var x=i(5554),g=i(7152),p=i(6654),b=i(3014),f=i(5233),N=i(2067),y=i.n(N),j=i(3277),w=i(3256),Z=i(7412),C=i(3588),D=i(9891),O=i(7014),S=i(4568);let editContract=async(e,t)=>await S.Z.put("/contract/".concat(e,"/edit"),t);var M=i(4033);let q=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}];function BusinessRegister(e){var t,i,a,s,m,v,N,S,A,_,E,P,R,I,T,k,V,B,z,F,L,U,W,Q,Y,K,G,J,X,H,$,ee,et,ei,er,ea,es,eo,en,el;let{modalityContract:ed,contractData:eu,contractId:ec}=e,[em,ev]=(0,o.useState)(!1),eh=(0,w.e)(),ex=(0,M.useRouter)(),eg=(0,o.useMemo)(()=>{var e,t,i,r,a,s,o,n,l,d,u,m,v,h,x,g,p,b,f,N,y,j,w,Z,C,D,O,S,M,q,A,_,E,P,R,I,T,k,V,B,z,F,L,U,W,Q;if(!eu)return{};let Y=eu.investor&&"business"===eu.investor.type?eu.investor:void 0;return{ownerName:(null==Y?void 0:null===(e=Y.responsibleOwner)||void 0===e?void 0:e.name)||"",ownerDocument:(null==Y?void 0:null===(t=Y.responsibleOwner)||void 0===t?void 0:t.document)||"",rg:(null==Y?void 0:null===(i=Y.responsibleOwner)||void 0===i?void 0:i.rg)||"",issuer:(null==Y?void 0:null===(r=Y.responsibleOwner)||void 0===r?void 0:r.issuingAgency)||"",placeOfBirth:(null==Y?void 0:null===(a=Y.responsibleOwner)||void 0===a?void 0:a.nationality)||"",occupation:(null==Y?void 0:null===(s=Y.responsibleOwner)||void 0===s?void 0:s.occupation)||"",motherName:(null==Y?void 0:null===(o=Y.responsibleOwner)||void 0===o?void 0:o.motherName)||"",dtBirth:(null==Y?void 0:null===(n=Y.responsibleOwner)||void 0===n?void 0:n.birthDate)||"",phoneNumber:(null==Y?void 0:null===(d=Y.responsibleOwner)||void 0===d?void 0:null===(l=d.phone)||void 0===l?void 0:l.startsWith("55"))?(0,c.gP)(Y.responsibleOwner.phone.replace("55","")):(null==Y?void 0:null===(u=Y.responsibleOwner)||void 0===u?void 0:u.phone)?(0,c.gP)(Y.responsibleOwner.phone):"",email:(null==Y?void 0:null===(m=Y.responsibleOwner)||void 0===m?void 0:m.email)||"",zipCode:(null==Y?void 0:null===(h=Y.responsibleOwner)||void 0===h?void 0:null===(v=h.address)||void 0===v?void 0:v.zipcode)||"",neighborhood:(null==Y?void 0:null===(g=Y.responsibleOwner)||void 0===g?void 0:null===(x=g.address)||void 0===x?void 0:x.neighborhood)||"",street:(null==Y?void 0:null===(b=Y.responsibleOwner)||void 0===b?void 0:null===(p=b.address)||void 0===p?void 0:p.street)||"",city:(null==Y?void 0:null===(N=Y.responsibleOwner)||void 0===N?void 0:null===(f=N.address)||void 0===f?void 0:f.city)||"",state:(null==Y?void 0:null===(j=Y.responsibleOwner)||void 0===j?void 0:null===(y=j.address)||void 0===y?void 0:y.state)||"",number:(null==Y?void 0:null===(Z=Y.responsibleOwner)||void 0===Z?void 0:null===(w=Z.address)||void 0===w?void 0:w.number)||"",complement:(null==Y?void 0:null===(D=Y.responsibleOwner)||void 0===D?void 0:null===(C=D.address)||void 0===C?void 0:C.complement)||"",name:(null==Y?void 0:Y.name)||"",document:(null==Y?void 0:Y.document)||"",companyType:(null==Y?void 0:Y.businessType)||"",companyZipCode:(null==Y?void 0:null===(O=Y.address)||void 0===O?void 0:O.zipcode)||"",companyNeighborhood:(null==Y?void 0:null===(S=Y.address)||void 0===S?void 0:S.neighborhood)||"",companyStreet:(null==Y?void 0:null===(M=Y.address)||void 0===M?void 0:M.street)||"",companyCity:(null==Y?void 0:null===(q=Y.address)||void 0===q?void 0:q.city)||"",companyState:(null==Y?void 0:null===(A=Y.address)||void 0===A?void 0:A.state)||"",companyNumber:(null==Y?void 0:null===(_=Y.address)||void 0===_?void 0:_.number)||"",companyComplement:(null==Y?void 0:null===(E=Y.address)||void 0===E?void 0:E.complement)||"",value:(null===(P=eu.investment)||void 0===P?void 0:P.value)?String(eu.investment.value):"",yield:(null===(R=eu.investment)||void 0===R?void 0:R.yield)||0,term:(null===(I=eu.investment)||void 0===I?void 0:I.term)?String(eu.investment.term):"",initDate:(null===(T=eu.investment)||void 0===T?void 0:T.start)?String(eu.investment.start).slice(0,10):"",endDate:(null===(k=eu.investment)||void 0===k?void 0:k.end)?String(eu.investment.end).slice(0,10):"",amountQuotes:(null===(V=eu.investment)||void 0===V?void 0:V.quotesAmount)?String(eu.investment.quotesAmount):"",purchasedWith:(null===(B=eu.investment)||void 0===B?void 0:B.purchasedWith)||"",isDebenture:(null===(z=eu.investment)||void 0===z?void 0:z.isDebenture)?"s":"n",isSCP:"SCP"===ed,profile:"moderate",bank:(null===(F=eu.bankAccount)||void 0===F?void 0:F.bank)||"",agency:(null===(L=eu.bankAccount)||void 0===L?void 0:L.agency)||"",accountNumber:(null===(U=eu.bankAccount)||void 0===U?void 0:U.account)||"",pix:(null===(Q=eu.bankAccount)||void 0===Q?void 0:null===(W=Q.pix)||void 0===W?void 0:W.replace("+",""))||""}},[eu,ed]),{register:ep,handleSubmit:eb,watch:ef,setValue:eN,reset:ey,formState:{errors:ej,isValid:ew}}=(0,d.cI)({defaultValues:eg,resolver:(0,l.X)(h),mode:"all"}),eZ=(0,C.D)({mutationFn:async e=>{let{contractId:t,data:i}=e;return await editContract(t,i)},onSuccess:()=>{b.Am.success("Contrato atualizado com sucesso!"),ex.push("/meus-contratos"),ey()},onError:e=>(0,p.Z)(e,"Tivemos um erro ao atualizar o contrato")});(0,D.a)({queryKey:["address",ef("zipCode")],queryFn:async()=>{let e=await (0,Z.x)((0,c.p4)(ef("zipCode")||""));e&&(eN("neighborhood",e.neighborhood,{shouldValidate:!0}),eN("city",e.city,{shouldValidate:!0}),eN("state",e.state,{shouldValidate:!0}),eN("street",e.street,{shouldValidate:!0}))},enabled:(null===(t=ef("zipCode"))||void 0===t?void 0:t.length)===9}),(0,D.a)({queryKey:["companyAddress",ef("companyZipCode")],queryFn:async()=>{let e=await (0,Z.x)((0,c.p4)(ef("companyZipCode")||""));e&&(eN("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),eN("companyStreet",e.street,{shouldValidate:!0}),eN("companyCity",e.city,{shouldValidate:!0}),eN("companyState",e.state,{shouldValidate:!0}))},enabled:(null===(i=ef("companyZipCode"))||void 0===i?void 0:i.length)===9});let onSubmit=async e=>{if(!em)return b.Am.error("Aceite os termos para liberar a atualiza\xe7\xe3o do contrato!");let t={role:eh.name,personType:"PJ",contractType:ed,investment:{amount:(0,j.Z)(e.value),monthlyRate:Number(e.yield),durationInMonths:Number(e.term),paymentMethod:e.purchasedWith,endDate:e.endDate,startDate:e.initDate,profile:e.profile,quotaQuantity:"SCP"===ed?Number(e.amountQuotes):void 0,isDebenture:"s"===e.isDebenture},bankAccount:{bank:e.bank,agency:e.agency,account:e.accountNumber,pix:e.pix||void 0},company:{corporateName:e.name,cnpj:(0,c.p4)(e.document),type:e.companyType,address:{street:e.companyStreet,neighborhood:e.companyNeighborhood,city:e.companyCity,state:e.companyState,postalCode:(0,c.p4)(e.companyZipCode),number:e.companyNumber,complement:e.companyComplement||""},representative:{fullName:(0,O.Z)(e.ownerName),cpf:(0,c.p4)(e.ownerDocument),rg:e.rg,issuingAgency:e.issuer,nationality:e.placeOfBirth,occupation:(0,O.Z)(e.occupation),birthDate:e.dtBirth,email:e.email,phone:"55".concat((0,c.p4)(e.phoneNumber)),motherName:(0,O.Z)(e.motherName),address:{street:e.street,neighborhood:e.neighborhood,city:e.city,state:e.state,postalCode:(0,c.p4)(e.zipCode),number:e.number,complement:e.complement||""}}}};await eZ.mutateAsync({contractId:ec,data:t},{onSuccess:()=>{ey(),eN("initDate",y()().format("YYYY-MM-DD"))}})};return(0,o.useEffect)(()=>{if(ef("initDate")&&ef("term")&&Number(ef("term"))>0){let e=y()(ef("initDate")),t=e.add(Number(ef("term")),"months").format("YYYY-MM-DD");eN("endDate",t)}},[ef("term"),eN]),(0,o.useEffect)(()=>{if(ef("initDate")&&ef("endDate")){let e=y()(ef("initDate")),t=y()(ef("endDate")),i=t.diff(e,"months");i>0&&eN("term",String(i))}},[ef("initDate"),ef("endDate"),eN]),(0,r.jsx)("div",{children:(0,r.jsxs)("form",{action:"",onSubmit:eb(onSubmit),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,r.jsx)(f.Z,{title:"Dados Pessoais - Representante",children:(0,r.jsxs)("div",{className:"flex flex-col gap-5",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"ownerName",width:"100%",error:!!ej.ownerName,errorMessage:null==ej?void 0:null===(a=ej.ownerName)||void 0===a?void 0:a.message,label:"Nome"})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"ownerDocument",width:"100%",error:!!ej.ownerDocument,errorMessage:null==ej?void 0:null===(s=ej.ownerDocument)||void 0===s?void 0:s.message,label:"CPF",setValue:e=>eN("ownerDocument",(0,c.VL)(e||""),{shouldValidate:!0})})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"rg",width:"100%",error:!!ej.rg,errorMessage:null==ej?void 0:null===(m=ej.rg)||void 0===m?void 0:m.message,label:"RG"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"issuer",width:"100%",error:!!ej.issuer,errorMessage:null==ej?void 0:null===(v=ej.issuer)||void 0===v?void 0:v.message,label:"Org\xe3o emissor"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"placeOfBirth",width:"100%",error:!!ej.placeOfBirth,errorMessage:null==ej?void 0:null===(N=ej.placeOfBirth)||void 0===N?void 0:N.message,label:"Nacionalidade"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"occupation",width:"100%",error:!!ej.occupation,errorMessage:null==ej?void 0:null===(S=ej.occupation)||void 0===S?void 0:S.message,label:"Ocupa\xe7\xe3o"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"motherName",width:"100%",error:!!ej.motherName,errorMessage:null==ej?void 0:null===(A=ej.motherName)||void 0===A?void 0:A.message,label:"Nome da m\xe3e"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsxs)("div",{style:{width:"200px"},children:[(0,r.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,r.jsx)("b",{className:"text-red-500 font-light text-sm",children:ej.dtBirth&&"- ".concat(ej.dtBirth.message)})]}),(0,r.jsx)("input",{...ep("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",className:"h-12 w-full px-4 text-white rounded-xl ".concat(ej.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{width:"100%",register:ep,name:"phoneNumber",error:!!ej.phoneNumber,errorMessage:null==ej?void 0:null===(_=ej.phoneNumber)||void 0===_?void 0:_.message,label:"Celular",maxLength:15,setValue:e=>eN("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})})})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"email",width:"100%",error:!!ej.email,errorMessage:null==ej?void 0:null===(E=ej.email)||void 0===E?void 0:E.message,label:"E-mail"})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"zipCode",width:"100%",error:!!ej.zipCode,errorMessage:null==ej?void 0:null===(P=ej.zipCode)||void 0===P?void 0:P.message,label:"CEP",setValue:e=>{eN("zipCode",(0,c.Tc)(e),{shouldValidate:!0})}})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,maxLength:2,setValue:e=>eN("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"100%",error:!!ej.state,errorMessage:null==ej?void 0:null===(R=ej.state)||void 0===R?void 0:R.message,label:"Estado"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"city",width:"100%",error:!!ej.city,errorMessage:null==ej?void 0:null===(I=ej.city)||void 0===I?void 0:I.message,label:"Cidade"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"neighborhood",width:"100%",error:!!ej.neighborhood,errorMessage:null==ej?void 0:null===(T=ej.neighborhood)||void 0===T?void 0:T.message,label:"Bairro"})})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"street",width:"100%",error:!!ej.street,errorMessage:null==ej?void 0:null===(k=ej.street)||void 0===k?void 0:k.message,label:"Rua"})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"number",width:"100%",error:!!ej.number,errorMessage:null==ej?void 0:null===(V=ej.number)||void 0===V?void 0:V.message,label:"N\xfamero"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"complement",width:"100%",error:!!ej.complement,errorMessage:null==ej?void 0:null===(B=ej.complement)||void 0===B?void 0:B.message,label:"Complemento"})})]})]})}),(0,r.jsx)(f.Z,{title:"Dados da empresa",children:(0,r.jsxs)("div",{className:"flex flex-col gap-5",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"name",width:"100%",error:!!ej.name,errorMessage:null==ej?void 0:null===(z=ej.name)||void 0===z?void 0:z.message,label:"Raz\xe3o Social"})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"document",width:"100%",error:!!ej.document,errorMessage:null==ej?void 0:null===(F=ej.document)||void 0===F?void 0:F.message,label:"CNPJ",setValue:e=>eN("document",(0,c.PK)(e||""),{shouldValidate:!0})})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.Z,{width:"100%",name:"companyType",register:ep,options:q,error:!!ej.companyType,errorMessage:null==ej?void 0:null===(L=ej.companyType)||void 0===L?void 0:L.message,label:"Tipo"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"companyZipCode",width:"100%",error:!!ej.companyZipCode,errorMessage:null==ej?void 0:null===(U=ej.companyZipCode)||void 0===U?void 0:U.message,label:"CEP",setValue:e=>{eN("companyZipCode",(0,c.Tc)(e),{shouldValidate:!0})}})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,maxLength:2,setValue:e=>eN("companyState",String(e).toUpperCase(),{shouldValidate:!0}),name:"companyState",width:"100%",error:!!ej.companyState,errorMessage:null==ej?void 0:null===(W=ej.companyState)||void 0===W?void 0:W.message,label:"Estado"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"companyCity",width:"100%",error:!!ej.companyCity,errorMessage:null==ej?void 0:null===(Q=ej.companyCity)||void 0===Q?void 0:Q.message,label:"Cidade"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"companyNeighborhood",width:"100%",error:!!ej.companyNeighborhood,errorMessage:null==ej?void 0:null===(Y=ej.companyNeighborhood)||void 0===Y?void 0:Y.message,label:"Bairro"})})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"companyStreet",width:"100%",error:!!ej.companyStreet,errorMessage:null==ej?void 0:null===(K=ej.companyStreet)||void 0===K?void 0:K.message,label:"Rua"})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"companyNumber",width:"100%",error:!!ej.companyNumber,errorMessage:null==ej?void 0:null===(G=ej.companyNumber)||void 0===G?void 0:G.message,label:"N\xfamero"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"companyComplement",width:"100%",error:!!ej.companyComplement,errorMessage:null==ej?void 0:null===(J=ej.companyComplement)||void 0===J?void 0:J.message,label:"Complemento"})})]})]})}),(0,r.jsx)(f.Z,{title:"Dados de Investimento",children:(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"value",width:"100%",error:!!ej.value,errorMessage:null==ej?void 0:null===(X=ej.value)||void 0===X?void 0:X.message,label:"Valor",setValue:e=>eN("value",(0,c.Ht)(e||""),{shouldValidate:!0})})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,type:"number",name:"term",width:"100%",error:!!ej.term,errorMessage:null==ej?void 0:null===(H=ej.term)||void 0===H?void 0:H.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,type:"number",name:"yield",width:"100%",error:!!ej.yield,errorMessage:null==ej?void 0:null===($=ej.yield)||void 0===$?void 0:$.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2,5",setValue:e=>eN("yield",Number(e)||0,{shouldValidate:!0})})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.Z,{width:"100%",name:"purchasedWith",register:ep,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!(null==ej?void 0:ej.purchasedWith),errorMessage:null==ej?void 0:null===(ee=ej.purchasedWith)||void 0===ee?void 0:ee.message,label:"Comprar com"})}),"SCP"===ed&&(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,type:"number",name:"amountQuotes",width:"100%",error:!!ej.amountQuotes,errorMessage:null==ej?void 0:null===(et=ej.amountQuotes)||void 0===et?void 0:et.message,label:"Quantidade de cotas"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{type:"date",register:ep,name:"initDate",width:"100%",error:!!ej.initDate,errorMessage:null==ej?void 0:null===(ei=ej.initDate)||void 0===ei?void 0:ei.message,label:"Inicio do contrato"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{type:"date",register:ep,value:ef("endDate")||"",name:"endDate",width:"100%",disabled:!0,label:"Final do contrato"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.Z,{width:"100%",name:"profile",register:ep,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!ej.profile,errorMessage:null==ej?void 0:null===(er=ej.profile)||void 0===er?void 0:er.message,label:"Perfil"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.Z,{width:"100%",name:"isDebenture",register:ep,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!ej.isDebenture,errorMessage:null==ej?void 0:null===(ea=ej.isDebenture)||void 0===ea?void 0:ea.message,label:"Deb\xeanture"})})]})}),(0,r.jsx)(f.Z,{title:"Dados bancarios",children:(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"bank",width:"100%",error:!!ej.bank,errorMessage:null==ej?void 0:null===(es=ej.bank)||void 0===es?void 0:es.message,label:"Banco"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"agency",width:"100%",error:!!ej.agency,errorMessage:null==ej?void 0:null===(eo=ej.agency)||void 0===eo?void 0:eo.message,label:"Ag\xeancia"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"accountNumber",width:"100%",error:!!ej.accountNumber,errorMessage:null==ej?void 0:null===(en=ej.accountNumber)||void 0===en?void 0:en.message,label:"Conta"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:ep,name:"pix",width:"100%",error:!!ej.pix,errorMessage:null==ej?void 0:null===(el=ej.pix)||void 0===el?void 0:el.message,label:"Pix"})})]})}),(0,r.jsx)(f.Z,{title:"Observa\xe7\xf5es",children:(0,r.jsx)("div",{children:(0,r.jsx)(g.Z,{name:"observations",register:ep})})}),(0,r.jsx)(f.Z,{title:"Dados para Dep\xf3sito",children:(0,r.jsxs)("div",{className:"m-auto border-none",children:[(0,r.jsx)("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,r.jsxs)("div",{className:"mb-5",children:[(0,r.jsx)("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),(0,r.jsx)("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,r.jsxs)("div",{className:"flex mt-5",children:[(0,r.jsx)("input",{type:"checkbox",checked:em,className:"cursor-pointer",onChange:()=>ev(!em)}),(0,r.jsx)("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),(0,r.jsx)("div",{className:"md:w-52 mb-10",children:(0,r.jsx)(u.Z,{label:"Enviar",size:"lg",loading:eZ.isPending,disabled:!em||eZ.isPending||!ew||!ef("endDate")})})]})})}function PhysicalRegister(e){var t,i,a,s,m,h,N,S,q,A,_,E,P,R,I,T,k,V,B,z,F,L,U,W,Q,Y,K,G,J;let{modalityContract:X,contractData:H,contractId:$}=e,[ee,et]=(0,o.useState)(!1),ei=(0,w.e)(),er=(0,M.useRouter)(),ea=(0,o.useMemo)(()=>{var e,t,i,r,a,s,o,n,l,d,u,m,v,h,x,g,p,b,f,N,y,j;if(!H)return{};let w=H.investor&&"physical"===H.investor.type?H.investor:void 0;return{name:(null==w?void 0:w.name)||"",document:(null==w?void 0:w.document)||"",rg:(null==w?void 0:w.rg)||"",issuer:(null==w?void 0:w.issuingAgency)||"",placeOfBirth:(null==w?void 0:w.nationality)||"",occupation:(null==w?void 0:w.occupation)||"",phoneNumber:(null==w?void 0:null===(e=w.phone)||void 0===e?void 0:e.startsWith("55"))?(0,c.gP)(w.phone.replace("55","")):(null==w?void 0:w.phone)?(0,c.gP)(w.phone):"",dtBirth:(null==w?void 0:w.birthDate)||"",email:(null==w?void 0:w.email)||"",motherName:(null==w?void 0:w.motherName)||"",zipCode:(null==w?void 0:null===(t=w.address)||void 0===t?void 0:t.zipcode)||"",neighborhood:(null==w?void 0:null===(i=w.address)||void 0===i?void 0:i.neighborhood)||"",street:(null==w?void 0:null===(r=w.address)||void 0===r?void 0:r.street)||"",city:(null==w?void 0:null===(a=w.address)||void 0===a?void 0:a.city)||"",state:(null==w?void 0:null===(s=w.address)||void 0===s?void 0:s.state)||"",number:(null==w?void 0:null===(o=w.address)||void 0===o?void 0:o.number)||"",complement:(null==w?void 0:null===(n=w.address)||void 0===n?void 0:n.complement)||"",value:(null===(l=H.investment)||void 0===l?void 0:l.value)?String(H.investment.value):"",yield:(null===(d=H.investment)||void 0===d?void 0:d.yield)?String(H.investment.yield):"",term:(null===(u=H.investment)||void 0===u?void 0:u.term)?String(H.investment.term):"",initDate:(null===(m=H.investment)||void 0===m?void 0:m.start)?String(H.investment.start).slice(0,10):"",endDate:(null===(v=H.investment)||void 0===v?void 0:v.end)?String(H.investment.end).slice(0,10):"",amountQuotes:(null===(h=H.investment)||void 0===h?void 0:h.quotesAmount)?String(H.investment.quotesAmount):"",purchasedWith:(null===(x=H.investment)||void 0===x?void 0:x.purchasedWith)||"",isDebenture:(null===(g=H.investment)||void 0===g?void 0:g.isDebenture)?"s":"n",isSCP:(null===(p=H.investment)||void 0===p?void 0:p.modality)==="SCP",bank:(null===(b=H.bankAccount)||void 0===b?void 0:b.bank)||"",agency:(null===(f=H.bankAccount)||void 0===f?void 0:f.agency)||"",accountNumber:(null===(N=H.bankAccount)||void 0===N?void 0:N.account)||"",pix:(null===(j=H.bankAccount)||void 0===j?void 0:null===(y=j.pix)||void 0===y?void 0:y.replace("+",""))||""}},[H]),{register:es,handleSubmit:eo,watch:en,setValue:el,reset:ed,formState:{errors:eu,isValid:ec}}=(0,d.cI)({defaultValues:ea,resolver:(0,l.X)(v),mode:"all"}),em=(0,C.D)({mutationFn:async e=>{let{contractId:t,data:i}=e;return await editContract(t,i)},onSuccess:()=>{b.Am.success("Contrato atualizado com sucesso!"),er.push("/meus-contratos"),ed()},onError:e=>(0,p.Z)(e,"Tivemos um erro ao atualizar o contrato")});(0,D.a)({queryKey:["address",en("zipCode")],queryFn:async()=>{let e=await (0,Z.x)((0,c.p4)(en("zipCode")||""));e&&(el("neighborhood",e.neighborhood,{shouldValidate:!0}),el("city",e.city,{shouldValidate:!0}),el("state",e.state,{shouldValidate:!0}),el("street",e.street,{shouldValidate:!0}))},enabled:(null===(t=en("zipCode"))||void 0===t?void 0:t.length)===9});let onSubmit=async e=>{if(!ee)return b.Am.error("Aceite os termos para liberar a atualiza\xe7\xe3o do contrato!");let t={role:ei.name,personType:"PF",contractType:X,investment:{amount:(0,j.Z)(e.value),monthlyRate:Number(e.yield),durationInMonths:Number(e.term),paymentMethod:e.purchasedWith,endDate:e.endDate,startDate:e.initDate,profile:e.profile,quotaQuantity:"SCP"===X?Number(e.amountQuotes):void 0,isDebenture:"s"===e.isDebenture},bankAccount:{bank:e.bank,agency:e.agency,account:e.accountNumber,pix:e.pix||void 0},individual:{fullName:(0,O.Z)(e.name),cpf:(0,c.p4)(e.document),rg:e.rg,issuingAgency:e.issuer,nationality:e.placeOfBirth,occupation:(0,O.Z)(e.occupation),birthDate:e.dtBirth,email:e.email,phone:"55".concat((0,c.p4)(e.phoneNumber)),motherName:(0,O.Z)(e.motherName),address:{street:e.street,neighborhood:e.neighborhood,city:e.city,state:e.state,postalCode:(0,c.p4)(e.zipCode),number:e.number,complement:e.complement||""}}};await em.mutateAsync({contractId:$,data:t})};return(0,o.useEffect)(()=>{if(en("initDate")&&en("term")&&Number(en("term"))>0){let e=y()(en("initDate")),t=e.add(Number(en("term")),"months").format("YYYY-MM-DD");el("endDate",t)}},[en("term"),el]),(0,o.useEffect)(()=>{if(en("initDate")&&en("endDate")){let e=y()(en("initDate")),t=y()(en("endDate")),i=t.diff(e,"months");i>0&&el("term",String(i))}},[en("initDate"),en("endDate"),el]),(0,r.jsx)("div",{children:(0,r.jsxs)("form",{action:"",onSubmit:eo(onSubmit),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,r.jsx)(f.Z,{title:"Dados Pessoais",children:(0,r.jsxs)("div",{className:"flex flex-col gap-5",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"name",width:"100%",error:!!eu.name,errorMessage:null==eu?void 0:null===(i=eu.name)||void 0===i?void 0:i.message,label:"Nome completo"})}),(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"rg",width:"100%",error:!!eu.rg,errorMessage:null==eu?void 0:null===(a=eu.rg)||void 0===a?void 0:a.message,label:"Identidade"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{width:"100%",register:es,name:"phoneNumber",maxLength:15,error:!!eu.phoneNumber,errorMessage:null==eu?void 0:null===(s=eu.phoneNumber)||void 0===s?void 0:s.message,label:"Celular",setValue:e=>el("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})})})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"document",width:"100%",error:!!eu.document,errorMessage:null==eu?void 0:null===(m=eu.document)||void 0===m?void 0:m.message,label:"CPF",setValue:e=>el("document",(0,c.VL)(e||""),{shouldValidate:!0})})}),(0,r.jsxs)("div",{style:{width:"200px"},children:[(0,r.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,r.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.dtBirth&&"- ".concat(eu.dtBirth.message)})]}),(0,r.jsx)("input",{...es("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let t=e.target;t.value.length>10&&(t.value=t.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"motherName",width:"100%",error:!!eu.motherName,errorMessage:null==eu?void 0:null===(h=eu.motherName)||void 0===h?void 0:h.message,label:"Nome da m\xe3e"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"email",width:"100%",error:!!eu.email,errorMessage:null==eu?void 0:null===(N=eu.email)||void 0===N?void 0:N.message,label:"E-mail"})}),(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"issuer",width:"100%",error:!!eu.issuer,errorMessage:null==eu?void 0:null===(S=eu.issuer)||void 0===S?void 0:S.message,label:"Org\xe3o emissor"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"placeOfBirth",width:"100%",error:!!eu.placeOfBirth,errorMessage:null==eu?void 0:null===(q=eu.placeOfBirth)||void 0===q?void 0:q.message,label:"Nacionalidade"})})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"occupation",width:"100%",error:!!eu.occupation,errorMessage:null==eu?void 0:null===(A=eu.occupation)||void 0===A?void 0:A.message,label:"Ocupa\xe7\xe3o"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"zipCode",width:"100%",error:!!eu.zipCode,errorMessage:null==eu?void 0:null===(_=eu.zipCode)||void 0===_?void 0:_.message,label:"CEP",setValue:e=>{let t=(0,c.Tc)(e);el("zipCode",t,{shouldValidate:!0})}})})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,maxLength:2,setValue:e=>el("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"100%",error:!!eu.state,errorMessage:null==eu?void 0:null===(E=eu.state)||void 0===E?void 0:E.message,label:"Estado"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"city",width:"100%",error:!!eu.city,errorMessage:null==eu?void 0:null===(P=eu.city)||void 0===P?void 0:P.message,label:"Cidade"})})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"neighborhood",width:"100%",error:!!eu.neighborhood,errorMessage:null==eu?void 0:null===(R=eu.neighborhood)||void 0===R?void 0:R.message,label:"Bairro"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"street",width:"100%",error:!!eu.street,errorMessage:null==eu?void 0:null===(I=eu.street)||void 0===I?void 0:I.message,label:"Endere\xe7o"})}),(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"number",width:"100%",error:!!eu.number,errorMessage:null==eu?void 0:null===(T=eu.number)||void 0===T?void 0:T.message,label:"N\xfamero"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"complement",width:"100%",error:!!eu.complement,errorMessage:null==eu?void 0:null===(k=eu.complement)||void 0===k?void 0:k.message,label:"Complemento"})})]})]})}),(0,r.jsx)(f.Z,{title:"Dados de Investimento",children:(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"value",width:"100%",error:!!eu.value,errorMessage:null==eu?void 0:null===(V=eu.value)||void 0===V?void 0:V.message,label:"Valor",setValue:e=>el("value",(0,c.Ht)(e||""),{shouldValidate:!0})})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,type:"number",name:"term",width:"100%",error:!!eu.term,errorMessage:null==eu?void 0:null===(B=eu.term)||void 0===B?void 0:B.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,type:"text",name:"yield",width:"100%",error:!!eu.yield,errorMessage:null==eu?void 0:null===(z=eu.yield)||void 0===z?void 0:z.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.Z,{width:"100%",name:"purchasedWith",register:es,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!(null==eu?void 0:eu.purchasedWith),errorMessage:null==eu?void 0:null===(F=eu.purchasedWith)||void 0===F?void 0:F.message,label:"Comprar com"})}),"SCP"===X&&(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,type:"number",name:"amountQuotes",width:"100%",error:!!eu.amountQuotes,errorMessage:null==eu?void 0:null===(L=eu.amountQuotes)||void 0===L?void 0:L.message,label:"Quantidade de cotas"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{type:"date",register:es,name:"initDate",width:"100%",error:!!eu.initDate,errorMessage:null==eu?void 0:null===(U=eu.initDate)||void 0===U?void 0:U.message,label:"Inicio do contrato"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{type:"date",register:es,value:en("endDate")||"",name:"endDate",width:"100%",disabled:!0,label:"Final do contrato"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.Z,{width:"100%",name:"profile",register:es,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!eu.profile,errorMessage:null==eu?void 0:null===(W=eu.profile)||void 0===W?void 0:W.message,label:"Perfil"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.Z,{width:"100%",name:"isDebenture",register:es,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!eu.isDebenture,errorMessage:null==eu?void 0:null===(Q=eu.isDebenture)||void 0===Q?void 0:Q.message,label:"Deb\xeanture"})})]})}),(0,r.jsx)(f.Z,{title:"Dados bancarios",children:(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"bank",width:"100%",error:!!eu.bank,errorMessage:null==eu?void 0:null===(Y=eu.bank)||void 0===Y?void 0:Y.message,label:"Banco"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"agency",width:"100%",error:!!eu.agency,errorMessage:null==eu?void 0:null===(K=eu.agency)||void 0===K?void 0:K.message,label:"Ag\xeancia"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"accountNumber",width:"100%",error:!!eu.accountNumber,errorMessage:null==eu?void 0:null===(G=eu.accountNumber)||void 0===G?void 0:G.message,label:"Conta"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(n.Z,{register:es,name:"pix",width:"100%",error:!!eu.pix,errorMessage:null==eu?void 0:null===(J=eu.pix)||void 0===J?void 0:J.message,label:"Pix"})})]})}),(0,r.jsx)(f.Z,{title:"Observa\xe7\xf5es",children:(0,r.jsx)("div",{children:(0,r.jsx)(g.Z,{name:"observations",register:es})})}),(0,r.jsx)(f.Z,{title:"Dados para Dep\xf3sito",children:(0,r.jsxs)("div",{className:"m-auto border-none",children:[(0,r.jsx)("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),(0,r.jsx)("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,r.jsxs)("div",{className:"mb-5",children:[(0,r.jsx)("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),(0,r.jsx)("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,r.jsxs)("div",{className:"flex mt-5",children:[(0,r.jsx)("input",{type:"checkbox",checked:ee,className:"cursor-pointer",onChange:()=>et(!ee)}),(0,r.jsx)("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),(0,r.jsx)("div",{className:"md:w-52 mb-10",children:(0,r.jsx)(u.Z,{label:"Enviar",size:"lg",loading:em.isPending,disabled:!ee||em.isPending||!ec||!en("endDate")})})]})})}var A=i(3729),_=i(8038);let getContractDetail=async e=>{let t=await S.Z.get("/contract/get-detail/".concat(e));return t.data};var E=i(4765);function ReusableSelect(e){let{value:t,label:i,children:a}=e;return(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-white mb-1",children:i}),(0,r.jsx)(E.Z,{value:t,disabled:!0,onChange:()=>{},children:a})]})}function Register(){var e,t,i,n,l,d;let{id:u}=(0,M.useParams)(),c=(0,M.useRouter)(),m=(0,_.NL)();(0,o.useEffect)(()=>()=>m.removeQueries({queryKey:["contract",u]}),[m,u]);let{data:v,isLoading:h,error:x}=(0,D.a)({queryKey:["new-contract",u],queryFn:async()=>{var e;let t=await getContractDetail(u);if(!t)return b.Am.error("Dados do contrato n\xe3o encontrados."),c.push("/meus-contratos"),null;let i=(null===(e=t.investment)||void 0===e?void 0:e.status)||"";return A.f.includes(i)?t:(b.Am.error("Este contrato n\xe3o pode ser editado devido ao seu status atual."),c.push("/meus-contratos"),null)},enabled:!!u,staleTime:0,refetchOnMount:"always"});return((0,o.useEffect)(()=>{x&&(console.error("Erro completo:",x),(0,p.Z)(x,"Erro ao buscar dados do contrato."),c.push("/meus-contratos"))},[x,c]),h)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.Z,{}),(0,r.jsx)(s.Z,{children:(0,r.jsx)("div",{className:"m-3 flex justify-center items-center h-[80vh]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF9900] mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})]}):x?null:(0,r.jsxs)("div",{children:[(0,r.jsx)(a.Z,{}),(0,r.jsx)(s.Z,{children:(0,r.jsxs)("div",{className:"md:px-10",children:[(0,r.jsx)(f.Z,{title:"Tipo de contrato",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between lg:gap-24 gap-5",children:[(0,r.jsxs)(ReusableSelect,{value:(null==v?void 0:null===(e=v.investor)||void 0===e?void 0:e.type)||"physical",label:"Tipo de conta",children:[(0,r.jsx)("option",{value:"physical",children:"Pessoa F\xedsica"}),(0,r.jsx)("option",{value:"business",children:"Pessoa Jur\xeddica"})]}),(0,r.jsxs)(ReusableSelect,{value:(null==v?void 0:null===(t=v.investment)||void 0===t?void 0:t.type)==="scp"?"SCP":"MUTUO",label:"Tipo de contrato",children:[(0,r.jsx)("option",{value:"MUTUO",children:"M\xfatuo"}),(0,r.jsx)("option",{value:"SCP",children:"SCP"})]})]})}),(0,r.jsxs)("div",{children:[(null==v?void 0:null===(i=v.investor)||void 0===i?void 0:i.type)==="physical"&&(0,r.jsx)(PhysicalRegister,{modalityContract:(null==v?void 0:null===(n=v.investment)||void 0===n?void 0:n.modality)||"MUTUO",contractData:v,contractId:u}),(null==v?void 0:null===(l=v.investor)||void 0===l?void 0:l.type)==="business"&&(0,r.jsx)(BusinessRegister,{modalityContract:(null==v?void 0:null===(d=v.investment)||void 0===d?void 0:d.modality)||"MUTUO",contractData:v,contractId:u})]})]})})]})}},2875:function(e,t,i){"use strict";i.d(t,{Z:function(){return Button}});var r=i(7437),a=i(8700);function Button(e){let{handleSubmit:t,loading:i,label:s,disabled:o,className:n,...l}=e;return(0,r.jsx)(a.z,{...l,onClick:t,loading:i,disabled:o,className:n,children:s})}},5233:function(e,t,i){"use strict";i.d(t,{Z:function(){return CoverForm}});var r=i(7437);function CoverForm(e){let{title:t,children:i,width:a="auto",color:s="withe"}=e;return(0,r.jsxs)("div",{className:"md:w-auto w-full",children:[(0,r.jsx)("p",{className:"text-xl text-white mb-2",children:t}),(0,r.jsx)("div",{className:"mb-10 m-auto bg-opacity-90 rounded-2xl shadow-current ".concat("withe"===s?"bg-zinc-900 border border-[#FF9900] p-5":"pt-1"),children:i})]})}},5554:function(e,t,i){"use strict";i.d(t,{Z:function(){return InputSelect}});var r=i(7437);function InputSelect(e){let{optionSelected:t,options:i,setOptionSelected:a,label:s,placeHolder:o="",width:n="100%",register:l=()=>{},error:d,errorMessage:u,name:c="",disableErrorMessage:m=!1,disabled:v=!1}=e;return(0,r.jsxs)("div",{className:"inputSelect relative group",style:{width:n},children:[(0,r.jsx)("p",{className:"text-white mb-1 text-sm",children:s}),(0,r.jsxs)("select",{disabled:m&&!u,...l(c),value:t,onChange:e=>{let{target:t}=e;a&&a(t.value)},id:c,className:"h-12 w-full px-4 ".concat(v?"text-zinc-400":"text-white"," rounded-xl ").concat(d?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),children:[o&&(0,r.jsx)("option",{selected:!0,disabled:!0,value:"",children:o}),i.map((e,t)=>(0,r.jsx)("option",{className:"cursor-pointer",value:e.value,children:e.label},t))]}),d&&(0,r.jsx)("div",{className:" absolute gr max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[90%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:u})]})}},7152:function(e,t,i){"use strict";i.d(t,{Z:function(){return InputTextArea}});var r=i(7437),a=i(2265);function InputTextArea(e){let{setValue:t,value:i,error:s,errorMessage:o,width:n="100%",register:l,name:d,placeholder:u="",className:c=""}=e;return(0,a.useEffect)(()=>{},[n]),(0,r.jsx)("div",{className:"input",style:{width:n},children:(0,r.jsx)("textarea",{...l&&{...l(d)},value:i&&i,placeholder:u,id:d,onChange:e=>{let{target:i}=e;t&&t(i.value)},className:"".concat(c," h-12 w-full min-h-40 p-2 text-white rounded-xl ").concat(s?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})})}},4207:function(e,t,i){"use strict";i.d(t,{Z:function(){return InputText}});var r=i(7437);function InputText(e){let{label:t,setValue:i,error:a,errorMessage:s,width:o="auto",register:n,name:l,placeholder:d="",type:u="text",disabled:c=!1,minDate:m,minLength:v,maxLength:h,maxDate:x,disableErrorMessage:g=!1,onBlur:p,value:b,onChange:f}=e;return(0,r.jsxs)("div",{className:"input relative group",style:{width:o},children:[(0,r.jsxs)("p",{className:"text-white mb-1 text-sm",children:[t,a&&!g&&(0,r.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",s]})]}),(0,r.jsx)("input",{...n(l),placeholder:d,type:u,id:l,disabled:c,min:m,max:x,minLength:v,maxLength:h,...i?{onChange:e=>{let{target:t}=e;return i(t.value)}}:{},onBlur:p,className:"h-12 w-full px-4 ".concat(c?"text-zinc-400":"text-white"," rounded-xl ").concat(a?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),...void 0!==b?{value:b}:{},...f?{onChange:f}:{}}),a&&(0,r.jsx)("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:s})]})}i(9514)},3729:function(e,t,i){"use strict";i.d(t,{f:function(){return s},z:function(){return a}});var r=i(8440);let a=[r.rd.REJECTED_BY_AUDIT,r.rd.AWAITING_AUDIT],s=[r.rd.AWAITING_INVESTOR_SIGNATURE]},7412:function(e,t,i){"use strict";i.d(t,{x:function(){return getZipCode}});var r=i(4829);async function getZipCode(e){let t=e.replace(/\D/g,"");if(8!==t.length)return null;try{let e=await r.Z.get("https://viacep.com.br/ws/".concat(t,"/json/")),i=e.data;if(i&&!i.erro)return{neighborhood:i.bairro||"",street:i.logradouro||"",city:i.localidade||"",state:i.uf||""};return null}catch(e){return console.error("Erro ao buscar o CEP:",e),null}}},6654:function(e,t,i){"use strict";i.d(t,{Z:function(){return returnError}});var r=i(3014);function returnError(e,t){var i,a,s,o;let n=(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(i=a.data)||void 0===i?void 0:i.message)||(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(s=o.data)||void 0===s?void 0:s.error);if(Array.isArray(n))return n.forEach(e=>{r.Am.error(e,{toastId:e})}),n.join("\n");if("string"==typeof n)return r.Am.error(n,{toastId:n}),n;if("object"==typeof n&&null!==n){let e=Object.values(n).flat().join("\n");return r.Am.error(e,{toastId:e}),e}return r.Am.error(t,{toastId:t}),t}},7014:function(e,t){"use strict";t.Z=function(e){return e.trim().replace(/\s+/g," ").toLowerCase().split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}},3277:function(e,t,i){"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}i.d(t,{Z:function(){return formatNumberValue}})},7157:function(e,t,i){"use strict";i.d(t,{m:function(){return isUnderage}});var r=i(2067),a=i.n(r);function isUnderage(e){let t=a()(e),i=a()().diff(t,"years");return i<18}},9514:function(){},7470:function(e,t,i){"use strict";i.d(t,{R:function(){return getDefaultState},m:function(){return o}});var r=i(7987),a=i(9024),s=i(1640),o=class extends a.F{#e;#t;#i;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||getDefaultState(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(e){let onContinue=()=>{this.#r({type:"continue"})};this.#i=(0,s.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#r({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#r({type:"pause"})},onContinue,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let t="pending"===this.state.status,i=!this.#i.canStart();try{if(t)onContinue();else{this.#r({type:"pending",variables:e,isPaused:i}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#r({type:"pending",context:t,variables:e,isPaused:i})}let r=await this.#i.start();return await this.#t.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#t.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#r({type:"success",data:r}),r}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#r({type:"error",error:t})}}finally{this.#t.runNext(this)}}#r(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),r.Vr.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},3588:function(e,t,i){"use strict";i.d(t,{D:function(){return useMutation}});var r=i(2265),a=i(7470),s=i(7987),o=i(2996),n=i(300),l=class extends o.l{#a;#s=void 0;#o;#n;constructor(e,t){super(),this.#a=e,this.setOptions(t),this.bindMethods(),this.#l()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#a.defaultMutationOptions(e),(0,n.VS)(this.options,t)||this.#a.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#o,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,n.Ym)(t.mutationKey)!==(0,n.Ym)(this.options.mutationKey)?this.reset():this.#o?.state.status==="pending"&&this.#o.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#o?.removeObserver(this)}onMutationUpdate(e){this.#l(),this.#d(e)}getCurrentResult(){return this.#s}reset(){this.#o?.removeObserver(this),this.#o=void 0,this.#l(),this.#d()}mutate(e,t){return this.#n=t,this.#o?.removeObserver(this),this.#o=this.#a.getMutationCache().build(this.#a,this.options),this.#o.addObserver(this),this.#o.execute(e)}#l(){let e=this.#o?.state??(0,a.R)();this.#s={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#d(e){s.Vr.batch(()=>{if(this.#n&&this.hasListeners()){let t=this.#s.variables,i=this.#s.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,i),this.#n.onSettled?.(e.data,null,t,i)):e?.type==="error"&&(this.#n.onError?.(e.error,t,i),this.#n.onSettled?.(void 0,e.error,t,i))}this.listeners.forEach(e=>{e(this.#s)})})}},d=i(8038);function useMutation(e,t){let i=(0,d.NL)(t),[a]=r.useState(()=>new l(i,e));r.useEffect(()=>{a.setOptions(e)},[a,e]);let o=r.useSyncExternalStore(r.useCallback(e=>a.subscribe(s.Vr.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),u=r.useCallback((e,t)=>{a.mutate(e,t).catch(n.ZT)},[a]);if(o.error&&(0,n.L3)(a.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:u,mutateAsync:o.mutate}}}},function(e){e.O(0,[6990,8276,5371,6946,1865,3964,9891,3151,2971,7864,1744],function(){return e(e.s=9756)}),_N_E=e.O()}]);