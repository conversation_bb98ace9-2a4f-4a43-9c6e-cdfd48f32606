"use strict";exports.id=9327,exports.ids=[9327],exports.modules={27017:(e,t,l)=>{l.d(t,{Z:()=>InputSelect});var r=l(60080);function InputSelect({optionSelected:e,options:t,setOptionSelected:l,label:a,placeHolder:s="",width:n="100%",register:i=()=>{},error:d,errorMessage:c,name:u="",disableErrorMessage:o=!1,disabled:x=!1}){return(0,r.jsxs)("div",{className:"inputSelect relative group",style:{width:n},children:[r.jsx("p",{className:"text-white mb-1 text-sm",children:a}),(0,r.jsxs)("select",{disabled:o&&!c,...i(u),value:e,onChange:({target:e})=>{l&&l(e.value)},id:u,className:`h-12 w-full px-4 ${x?"text-zinc-400":"text-white"} rounded-xl ${d?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`,children:[s&&r.jsx("option",{selected:!0,disabled:!0,value:"",children:s}),t.map((e,t)=>r.jsx("option",{className:"cursor-pointer",value:e.value,children:e.label},t))]}),d&&r.jsx("div",{className:" absolute gr max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[90%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:c})]})}},17871:(e,t,l)=>{l.d(t,{Z:()=>formatNumberValue});function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}}};