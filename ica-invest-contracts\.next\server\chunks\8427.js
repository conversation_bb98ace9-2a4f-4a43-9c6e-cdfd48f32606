"use strict";exports.id=8427,exports.ids=[8427],exports.modules={40509:(e,r,i)=>{i.d(r,{x:()=>getZipCode});var o=i(21145);async function getZipCode(e){let r=e.replace(/\D/g,"");if(8!==r.length)return null;try{let e=await o.Z.get(`https://viacep.com.br/ws/${r}/json/`),i=e.data;if(i&&!i.erro)return{neighborhood:i.bairro||"",street:i.logradouro||"",city:i.localidade||"",state:i.uf||""};return null}catch(e){return console.error("Erro ao buscar o CEP:",e),null}}},28303:(e,r,i)=>{i.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});let __WEBPACK_DEFAULT_EXPORT__=function(e){return e.trim().replace(/\s+/g," ").toLowerCase().split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}},70322:(e,r,i)=>{i.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});let __WEBPACK_DEFAULT_EXPORT__=function(e){return["AC","AL","AP","AM","BA","CE","DF","ES","GO","MA","MT","MS","MG","PA","PB","PR","PE","PI","RJ","RN","RS","RO","RR","SC","SP","SE","TO"].includes(e.toUpperCase())}},12107:(e,r,i)=>{i.d(r,{$r:()=>m,_R:()=>u});var o=i(50298),a=i(96413),t=i(22434),d=i(93543),n=i(70322),x=i(54986);let m=o.Ry().shape({isSCP:o.O7(),name:o.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:o.Z_().test("valid-document","CPF inv\xe1lido",e=>!!e&&(0,t.p)((0,a.p4)(e||""))).required("Campo obrigat\xf3rio"),email:o.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),rg:o.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),phoneNumber:o.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").test("test-phone","N\xfamero de telefone inv\xe1lido",e=>!!e&&(0,d.Z)((0,a.p4)(e))).required("Campo obrigat\xf3rio"),dtBirth:o.Z_().test("dtBirth-error","O investidor n\xe3o pode ser menor de idade.",e=>!!e&&!(0,x.m)(e)).required("Campo obrigat\xf3rio"),motherName:o.Z_().min(3,"Nome da m\xe3e deve conter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),zipCode:o.Z_().required("Obrigat\xf3rio"),neighborhood:o.Z_().required("Obrigat\xf3rio"),state:o.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").test("state-error","Estado inv\xe1lido!",e=>!!e&&(0,n.Z)(e)).required("Obrigat\xf3rio"),city:o.Z_().required("Obrigat\xf3rio"),complement:o.Z_().default("").notRequired(),number:o.Z_().required("Obrigat\xf3rio"),street:o.Z_().required("Obrigat\xf3rio"),value:o.Z_().required("Obrigat\xf3rio"),term:o.Z_().required("Obrigat\xf3rio"),yield:o.Z_().test("yield-error","Taxa Remunera\xe7\xe3o Mensal inv\xe1lida",e=>{if(!e)return!1;let r=Number(e.replace(",","."));return r>0&&r<=5}).required("Obrigat\xf3rio"),purchaseWith:o.Z_().required("Obrigat\xf3rio"),initDate:o.Z_().required("Obrigat\xf3rio"),endDate:o.Z_().required("Obrigat\xf3rio"),profile:o.Z_().required("Obrigat\xf3rio"),isDebenture:o.Z_().required("Obrigat\xf3rio"),bank:o.Z_().min(2,"Nome do banco muito curto").required("Obrigat\xf3rio"),accountNumber:o.Z_().min(3,"Conta inv\xe1lida").required("Obrigat\xf3rio"),agency:o.Z_().min(2,"Ag\xeancia inv\xe1lida").required("Obrigat\xf3rio"),pix:o.Z_().required("Obrigat\xf3rio"),observations:o.Z_().notRequired(),issuer:o.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:o.Z_().required("Campo obrigat\xf3rio"),occupation:o.Z_().required("Campo obrigat\xf3rio"),amountQuotes:o.Z_().when("isSCP",(e,r)=>!0===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired())}).required(),u=o.Ry().shape({isSCP:o.O7(),email:o.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),phoneNumber:o.Z_().test("test-phone","N\xfamero de telefone inv\xe1lido",e=>!!e&&(0,d.Z)((0,a.p4)(e))).max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:o.Z_().test("dtBirth-error","O representante n\xe3o pode ser menor de idade.",e=>!!e&&!(0,x.m)(e)).required("Campo obrigat\xf3rio"),rg:o.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Obrigat\xf3rio"),ownerName:o.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),ownerDocument:o.Z_().test("valid-document","CPF inv\xe1lido",e=>!!e&&(0,t.p)((0,a.p4)(e||""))).min(3,"O nome da m\xe3e deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),motherName:o.Z_().required("Campo obrigat\xf3rio"),issuer:o.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:o.Z_().required("Campo obrigat\xf3rio"),occupation:o.Z_().required("Campo obrigat\xf3rio"),name:o.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:o.Z_().test("valid-document-business","CNPJ inv\xe1lido",e=>!!e&&(0,t.w)((0,a.p4)(e||""))).required("Campo obrigat\xf3rio"),companyType:o.Z_().required("Obrigat\xf3rio"),zipCode:o.Z_().required("Obrigat\xf3rio"),neighborhood:o.Z_().required("Obrigat\xf3rio"),state:o.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").test("state-error","Estado inv\xe1lido!",e=>!!e&&(0,n.Z)(e)).required("Obrigat\xf3rio"),city:o.Z_().required("Obrigat\xf3rio"),complement:o.Z_().default("").notRequired(),number:o.Z_().required("Obrigat\xf3rio"),street:o.Z_().required("Obrigat\xf3rio"),value:o.Z_().required("Obrigat\xf3rio"),term:o.Z_().required("Obrigat\xf3rio"),yield:o.Z_().test("yield-error","Taxa Remunera\xe7\xe3o Mensal inv\xe1lida",e=>{if(!e)return!1;let r=Number(e.replace(",","."));return r>0&&r<=5}).required("Obrigat\xf3rio"),purchaseWith:o.Z_().required("Obrigat\xf3rio"),initDate:o.Z_().required("Obrigat\xf3rio"),endDate:o.Z_().required("Obrigat\xf3rio"),profile:o.Z_().required("Obrigat\xf3rio"),isDebenture:o.Z_().required("Obrigat\xf3rio"),amountQuotes:o.Z_().when("isSCP",(e,r)=>!0===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),bank:o.Z_().min(2,"Nome do banco muito curto").required("Obrigat\xf3rio"),accountNumber:o.Z_().min(3,"Conta inv\xe1lida").required("Obrigat\xf3rio"),agency:o.Z_().min(2,"Ag\xeancia inv\xe1lida").required("Obrigat\xf3rio"),pix:o.Z_().required("Obrigat\xf3rio"),observations:o.Z_().notRequired(),companyNumber:o.Z_().required("Campo obrigat\xf3rio"),companyComplement:o.Z_().default("").notRequired(),companyCity:o.Z_().required("Campo obrigat\xf3rio"),companyState:o.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").test("company-state-error","Estado inv\xe1lido!",e=>!!e&&(0,n.Z)(e)).required("Campo obrigat\xf3rio"),companyStreet:o.Z_().required("Campo obrigat\xf3rio"),companyZipCode:o.Z_().required("Campo obrigat\xf3rio"),companyNeighborhood:o.Z_().required("Campo obrigat\xf3rio")}).required();o.Ry().shape({tipoContrato:o.Z_().required("Tipo de Contrato \xe9 obrigat\xf3rio"),nomeCompleto:o.Z_().required("Nome Completo \xe9 obrigat\xf3rio"),identidade:o.Z_().required("Identidade \xe9 obrigat\xf3ria"),celular:o.Z_().required("Celular \xe9 obrigat\xf3rio").matches(/(\(\d{2}\)\s?\d{8,9})/,"Celular inv\xe1lido"),cpf:o.Z_().required("CPF \xe9 obrigat\xf3rio").matches(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/,"CPF inv\xe1lido"),dataNascimento:o.Z_().required("Data de Nascimento \xe9 obrigat\xf3ria").matches(/^\d{2}\/\d{2}\/\d{4}$/,"Data inv\xe1lida"),nomeMae:o.Z_().required("Nome da M\xe3e \xe9 obrigat\xf3rio"),email:o.Z_().email("E-mail inv\xe1lido").required("E-mail \xe9 obrigat\xf3rio"),cep:o.Z_().required("CEP \xe9 obrigat\xf3rio").matches(/^\d{8}$/,"CEP inv\xe1lido"),cidade:o.Z_().required("Cidade \xe9 obrigat\xf3ria"),endereco:o.Z_().required("Endere\xe7o \xe9 obrigat\xf3rio"),numero:o.Z_().required("N\xfamero \xe9 obrigat\xf3rio"),complemento:o.Z_().nullable(),banco:o.Z_().required("Nome do Banco \xe9 obrigat\xf3rio"),conta:o.Z_().required("Conta \xe9 obrigat\xf3ria"),agencia:o.Z_().required("Ag\xeancia \xe9 obrigat\xf3ria"),chavePix:o.Z_().required("Chave PIX \xe9 obrigat\xf3ria"),observacoes:o.Z_().nullable(),modalidade:o.Z_().required("Modalidade \xe9 obrigat\xf3ria"),valorInvestimento:o.Z_().required("Valor do Investimento \xe9 obrigat\xf3rio").matches(/^R?\$[\d.,]+$/,"Valor inv\xe1lido"),prazoInvestimento:o.Rx().typeError("Prazo deve ser um n\xfamero").integer("Prazo deve ser inteiro").required("Prazo do Investimento \xe9 obrigat\xf3rio"),taxaRemuneracao:o.Rx().typeError("Taxa deve ser um n\xfamero").required("Taxa de Remunera\xe7\xe3o Mensal \xe9 obrigat\xf3ria"),comprarCom:o.Z_().required("Forma de compra \xe9 obrigat\xf3ria"),inicioContrato:o.Z_().required("In\xedcio do Contrato \xe9 obrigat\xf3rio").matches(/^\d{2}\/\d{2}\/\d{4}$/,"Data inv\xe1lida"),fimContrato:o.Z_().required("Fim do Contrato \xe9 obrigat\xf3rio").matches(/^\d{2}\/\d{2}\/\d{4}$/,"Data inv\xe1lida"),perfil:o.Z_().required("Perfil \xe9 obrigat\xf3rio"),debenture:o.Z_().required("Deb\xeanture \xe9 obrigat\xf3ria")})},93543:(e,r,i)=>{i.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var o=i(96413);let __WEBPACK_DEFAULT_EXPORT__=function(e){return/^(\d{2})(9\d{8}|[2-8]\d{7})$/.test((0,o.p4)(e))}},40990:(e,r,i)=>{i.d(r,{Z:()=>t});var o=i(9885);let a=o.forwardRef(function({title:e,titleId:r,...i},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},i),e?o.createElement("title",{id:r},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}),t=a}};