{"version": 3, "file": "contract.module.js", "sourceRoot": "/", "sources": ["modules/contract/contract.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA2E;AAC3E,6CAAgD;AAChD,wDAAkD;AAClD,8GAAuG;AACvG,4FAAsF;AACtF,kHAA0G;AAC1G,kHAA0G;AAC1G,8DAAwD;AAExD,8EAA0E;AAC1E,0EAAsE;AACtE,gFAA2E;AAC3E,gHAAyG;AACzG,kGAA4F;AAC5F,2IAAqI;AACrI,qHAAgH;AAChH,6HAAuH;AACvH,qHAA+G;AAC/G,gFAA2E;AAC3E,gFAA2E;AAC3E,sFAA+E;AAC/E,kGAA4F;AAC5F,wFAAkF;AAClF,4EAAuE;AACvE,wGAAgG;AAChG,oFAA6E;AAC7E,oGAA8F;AAC9F,8EAAyE;AACzE,oFAAqF;AACrF,4GAAqG;AACrG,0FAAoF;AACpF,kFAA6E;AAuCtE,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,SAAS,CAAC,QAA4B;QACpC,QAAQ;aACL,KAAK,CAAC,kEAA8B,CAAC;aACrC,SAAS,CACR,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,sBAAa,CAAC,GAAG,EAAE,EAC/C,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,sBAAa,CAAC,IAAI,EAAE,EAChE,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,EAAE,sBAAa,CAAC,IAAI,EAAE,CACjE,CAAC;IACN,CAAC;CACF,CAAA;AAVY,wCAAc;yBAAd,cAAc;IArC1B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,4BAAY;YACZ,wBAAU;YACV,wCAAkB;YAClB,uBAAa,CAAC,UAAU,CAAC;gBACvB,gCAAc;gBACd,oDAAuB;gBACvB,iDAAsB;aACvB,CAAC;SACH;QACD,WAAW,EAAE,CAAC,wCAAkB,CAAC;QACjC,SAAS,EAAE;YACT,+CAAqB;YACrB,2CAAmB;YACnB,iDAAqB;YACrB,+CAAqB;YACrB,4DAA2B;YAC3B,6CAAoB;YACpB,gEAA6B;YAC7B,wDAAyB;YACzB,yDAA6B;YAC7B,gEAA6B;YAC7B,6EAAmC;YACnC,gEAA6B;YAC7B,2CAAmB;YACnB,kDAAsB;YACtB,kEAA8B;YAC9B,oEAA8B;YAC9B,yEAAiC;YACjC,sDAAwB;YACxB,+CAAqB;YACrB,mDAAsB;YACtB,iDAAsB;SACvB;QACD,OAAO,EAAE,CAAC,gEAA6B,CAAC;KACzC,CAAC;GACW,cAAc,CAU1B", "sourcesContent": ["import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ApisModule } from 'src/apis/apis.module';\r\nimport { ContractDeletionEntity } from 'src/shared/database/typeorm/entities/contract-deletion.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { ValidateAdviserOwnerMiddleware } from 'src/shared/middlewares/validate-adviser-owner.middleware';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { NotificationModule } from '../notifications/notification.module';\r\nimport { ContractController } from './controller/contract.controller';\r\nimport { AddSignatoriesService } from './services/add-signataries.service';\r\nimport { CreateContractAdditiveManualService } from './services/create-contract-additive-manual.service';\r\nimport { CreateContractAdditiveService } from './services/create-contract-additive.service';\r\nimport { CheckDuplicateContractHandler } from './services/create-contract-manual/concrete-handlers/check-duplicate-contract.handler';\r\nimport { SendContractHandler } from './services/create-contract-manual/concrete-handlers/send-contract.handler';\r\nimport { ValidateAdvisorHandler } from './services/create-contract-manual/concrete-handlers/validate-advisors.handler';\r\nimport { CreateContractManualService } from './services/create-contract-manual/create-contract-manual.service';\r\nimport { CreateContractService } from './services/create-contract.service';\r\nimport { DeleteContractService } from './services/delete-contract.service';\r\nimport { EditNewContractService } from './services/editt-new-contract.service';\r\nimport { GetContractsByInvestorService } from './services/get-contract-by-investor.service';\r\nimport { GetContractDetailService } from './services/get-contract-detail.service';\r\nimport { GetContractsService } from './services/get-contracts.service';\r\nimport { GetContratAddendumsByIdService } from './services/get-contrat-addendums-by-id.service';\r\nimport { GetOneContractService } from './services/get-one-contracts.service';\r\nimport { ListContractsSuperadminService } from './services/list-contracts-superadmin.service';\r\nimport { RenewContractService } from './services/renew-contract.service';\r\nimport { SendEmailNotificationContract } from './services/send-notification.service';\r\nimport { UploadProofPaymentAddendumService } from './services/upload-proof-payment-addendum.service';\r\nimport { UploadProofPaymentService } from './services/upload-proof-payment.service';\r\nimport { UpgradeContractService } from './services/upgrade-contract.service';\r\n\r\n@Module({\r\n  imports: [\r\n    SharedModule,\r\n    ApisModule,\r\n    NotificationModule,\r\n    TypeOrmModule.forFeature([\r\n      ContractEntity,\r\n      OwnerRoleRelationEntity,\r\n      ContractDeletionEntity,\r\n    ]),\r\n  ],\r\n  controllers: [ContractController],\r\n  providers: [\r\n    CreateContractService,\r\n    GetContractsService,\r\n    GetOneContractService,\r\n    AddSignatoriesService,\r\n    CreateContractManualService,\r\n    RenewContractService,\r\n    GetContractsByInvestorService,\r\n    UploadProofPaymentService,\r\n    SendEmailNotificationContract,\r\n    CreateContractAdditiveService,\r\n    CreateContractAdditiveManualService,\r\n    CheckDuplicateContractHandler,\r\n    SendContractHandler,\r\n    ValidateAdvisorHandler,\r\n    ListContractsSuperadminService,\r\n    GetContratAddendumsByIdService,\r\n    UploadProofPaymentAddendumService,\r\n    GetContractDetailService,\r\n    DeleteContractService,\r\n    EditNewContractService,\r\n    UpgradeContractService,\r\n  ],\r\n  exports: [GetContractsByInvestorService],\r\n})\r\nexport class ContractModule {\r\n  configure(consumer: MiddlewareConsumer) {\r\n    consumer\r\n      .apply(ValidateAdviserOwnerMiddleware)\r\n      .forRoutes(\r\n        { path: 'contract', method: RequestMethod.GET },\r\n        { path: 'contract/add-signatories', method: RequestMethod.POST },\r\n        { path: 'contract/add-signatories', method: RequestMethod.POST },\r\n      );\r\n  }\r\n}\r\n"]}