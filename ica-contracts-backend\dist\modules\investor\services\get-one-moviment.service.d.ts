import { MovementEntity } from 'src/shared/database/typeorm/entities/movement.entity';
import { Repository } from 'typeorm';
export declare class GetMovementService {
    private readonly movementRepository;
    constructor(movementRepository: Repository<MovementEntity>);
    perform(movementId: string, userId: string): Promise<{
        contract: {
            contractNumber: number;
            signataries: import("../../../shared/database/typeorm/entities/pre-register.entity").PreRegisterEntity;
            id: string;
            externalId: number;
            brokerId: string;
            investorId: string;
            startContract: Date;
            endContract: Date;
            contractPdf: string;
            oldContractPdf: string;
            proofPayment: string;
            signInvestor: string;
            signIca: string;
            status: string;
            type: string;
            isDebenture: boolean;
            durationInMonths: number;
            brokerParticipationPercentage: string;
            advisorParticipationPercentage: string;
            createdAt: Date;
            updatedAt: Date;
            ownerRoleRelation: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/owner-role-relation.entity").OwnerRoleRelationEntity>;
            investor: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/owner-role-relation.entity").OwnerRoleRelationEntity>;
            events: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/contract-event.entity").ContractEventEntity[]>;
            scheduledPayments: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/income-payment-scheduled.entity").IncomePaymentScheduledEntity[]>;
            addendum: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/addendum.entity").AddendumEntity[]>;
            contractAdvisors: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/contract-advisor.entity").ContractAdvisorEntity[]>;
            latestEvent?: import("../../../shared/database/typeorm/entities/contract-event.entity").ContractEventEntity;
            notifications: import("../../../shared/database/typeorm/entities/notification.entity").NotificationEntity[];
            incomeReportsContracts: import("../../../shared/database/typeorm/entities/income-reports-contracts.entity").IncomeReportsContractsEntity[];
            audits: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/contract-audit.entity").ContractAuditEntity[]>;
            deletions: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/contract-deletion.entity").ContractDeletionEntity[]>;
        };
        id: string;
        operationType: string;
        amount: number;
        description: string;
        updatedAt?: Date;
        createdAt?: Date;
        deletedAt?: Date;
        investor: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/owner-role-relation.entity").OwnerRoleRelationEntity>;
    }>;
}
