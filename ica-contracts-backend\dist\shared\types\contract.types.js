"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvisorAssignmentDto = exports.InvestmentDetailsDto = exports.BankAccountDto = exports.CompanyDto = exports.IndividualDto = exports.AddressDto = exports.CompanyLegalType = exports.InvestorProfile = exports.PaymentMethod = exports.ContractType = exports.PersonType = void 0;
const swagger_1 = require("@nestjs/swagger");
const brazilian_class_validator_1 = require("brazilian-class-validator");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const is_valid_name_decorator_1 = require("../decorators/is-valid-name.decorator");
const to_boolean_decorator_1 = require("../decorators/to-boolean.decorator");
const to_number_decorator_1 = require("../decorators/to-number.decorator");
const generate_date_1 = require("../functions/generate-date");
var PersonType;
(function (PersonType) {
    PersonType["PF"] = "PF";
    PersonType["PJ"] = "PJ";
})(PersonType || (exports.PersonType = PersonType = {}));
var ContractType;
(function (ContractType) {
    ContractType["SCP"] = "SCP";
    ContractType["MUTUO"] = "MUTUO";
})(ContractType || (exports.ContractType = ContractType = {}));
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["PIX"] = "pix";
    PaymentMethod["BANK_TRANSFER"] = "bank_transfer";
    PaymentMethod["BOLETO"] = "boleto";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var InvestorProfile;
(function (InvestorProfile) {
    InvestorProfile["CONSERVATIVE"] = "conservative";
    InvestorProfile["MODERATE"] = "moderate";
    InvestorProfile["AGGRESSIVE"] = "aggressive";
})(InvestorProfile || (exports.InvestorProfile = InvestorProfile = {}));
var CompanyLegalType;
(function (CompanyLegalType) {
    CompanyLegalType["MEI"] = "MEI";
    CompanyLegalType["EI"] = "EI";
    CompanyLegalType["EIRELI"] = "EIRELI";
    CompanyLegalType["LTDA"] = "LTDA";
    CompanyLegalType["SLU"] = "SLU";
    CompanyLegalType["SA"] = "SA";
    CompanyLegalType["SS"] = "SS";
    CompanyLegalType["CONSORCIO"] = "CONSORCIO";
})(CompanyLegalType || (exports.CompanyLegalType = CompanyLegalType = {}));
class AddressDto {
}
exports.AddressDto = AddressDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Rua (ex: Av. Paulista)',
        example: 'Av. Paulista',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Rua não pode estar vazia' }),
    (0, class_validator_1.MinLength)(3, { message: 'A rua deve ter no mínimo 3 caracteres' }),
    __metadata("design:type", String)
], AddressDto.prototype, "street", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Cidade (ex: São Paulo)', example: 'São Paulo' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Cidade não pode estar vazia' }),
    (0, class_validator_1.MinLength)(2, { message: 'A cidade deve ter no mínimo 2 caracteres' }),
    __metadata("design:type", String)
], AddressDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Estado (ex: SP)', example: 'SP' }),
    (0, class_validator_1.Matches)(/^[A-Z]{2}$/, {
        message: 'Estado inválido. Use o formato XX (ex: SP)',
    }),
    __metadata("design:type", String)
], AddressDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bairro (ex: Bela Vista)',
        example: 'Bela Vista',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Bairro não pode estar vazio' }),
    __metadata("design:type", String)
], AddressDto.prototype, "neighborhood", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CEP (ex: 01310-100 ou 01310100)',
        example: '01310100',
        pattern: '^\\d{5}-?\\d{3}$',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'CEP não pode estar vazio' }),
    (0, class_validator_1.Matches)(/^\d{8}$/, {
        message: 'CEP inválido. Use o formato XXXXXXXX (ex: 01310100)',
    }),
    __metadata("design:type", String)
], AddressDto.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Número (ex: 1000)', example: '1000' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Número não pode estar vazio' }),
    __metadata("design:type", String)
], AddressDto.prototype, "number", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Complemento (ex: Apt 101)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AddressDto.prototype, "complement", void 0);
class IndividualDto {
}
exports.IndividualDto = IndividualDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Nome completo (ex: João da Silva)' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome completo não pode estar vazio' }),
    (0, is_valid_name_decorator_1.IsValidName)({
        message: 'Nome completo inválido. Use apenas letras, espaços, hífens e caracteres acentuados',
    }),
    __metadata("design:type", String)
], IndividualDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'CPF (ex: 12345678900)', example: '' }),
    (0, brazilian_class_validator_1.IsCPF)({
        message: 'CPF inválido. Use o formato XXXXXXXXXXX (ex: 12345678900)',
    }),
    __metadata("design:type", String)
], IndividualDto.prototype, "cpf", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'RG (ex: 123456789)', example: '123456789' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'RG não pode estar vazio' }),
    __metadata("design:type", String)
], IndividualDto.prototype, "rg", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Órgão emissor (ex: SSP)' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Órgão emissor não pode estar vazio' }),
    __metadata("design:type", String)
], IndividualDto.prototype, "issuingAgency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Nacionalidade (ex: Brasileiro)' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nacionalidade não pode estar vazia' }),
    __metadata("design:type", String)
], IndividualDto.prototype, "nationality", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Ocupação (ex: Engenheiro)' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Ocupação não pode estar vazia' }),
    (0, is_valid_name_decorator_1.IsValidName)({
        message: 'Ocupação inválida. Use apenas letras, espaços, hífens e caracteres acentuados',
    }),
    __metadata("design:type", String)
], IndividualDto.prototype, "occupation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de nascimento (ex: 1990-01-01)',
        example: (0, generate_date_1.generateDate)(-(365 * 20)),
    }),
    (0, class_validator_1.IsDateString)({}, { message: 'Data de nascimento inválida' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Data de nascimento é obrigatória' }),
    __metadata("design:type", String)
], IndividualDto.prototype, "birthDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email (ex: <EMAIL>)',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)({}, { message: 'E-mail inválido' }),
    __metadata("design:type", String)
], IndividualDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone celular (ex: 5548999999999)',
        example: '48999999999',
    }),
    (0, class_validator_1.Matches)(/^55(\d{2})\d{8,9}$/, {
        message: 'Telefone inválido. (ex: 5548999999999)',
    }),
    __metadata("design:type", String)
], IndividualDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da mãe (ex: Maria da Silva)',
        example: 'Maria da Silva',
    }),
    (0, is_valid_name_decorator_1.IsValidName)({
        message: 'Nome da mãe inválido. Use apenas letras, espaços, hífens e caracteres acentuados',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome da mãe não pode estar vazio' }),
    __metadata("design:type", String)
], IndividualDto.prototype, "motherName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Endereço',
        type: AddressDto,
        example: {
            street: 'Av. Paulista',
            number: '1000',
            city: 'São Paulo',
            state: 'SP',
            postalCode: '01310100',
            neighborhood: 'Bela Vista',
            complement: 'Apt 101',
        },
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AddressDto),
    __metadata("design:type", AddressDto)
], IndividualDto.prototype, "address", void 0);
class CompanyDto {
}
exports.CompanyDto = CompanyDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Razão social',
        example: 'ACME Ltda',
    }),
    (0, class_validator_1.IsString)({ message: 'Razão Social é obrigatória' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Razão social não pode estar vazia' }),
    __metadata("design:type", String)
], CompanyDto.prototype, "corporateName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CNPJ (ex: 12345678000199)',
        example: '12345678000199',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'CNPJ é obrigatório' }),
    (0, brazilian_class_validator_1.IsCNPJ)({
        message: 'CNPJ inválido. Use o formato XXXXXXXXXXXXXXX (ex: 12345678000199)',
    }),
    __metadata("design:type", String)
], CompanyDto.prototype, "cnpj", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo jurídico da empresa',
        enum: CompanyLegalType,
        example: CompanyLegalType.LTDA,
    }),
    (0, class_validator_1.IsEnum)(CompanyLegalType, { message: 'Tipo de empresa inválido' }),
    __metadata("design:type", String)
], CompanyDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Endereço', type: AddressDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AddressDto),
    __metadata("design:type", AddressDto)
], CompanyDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Representante legal', type: IndividualDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => IndividualDto),
    __metadata("design:type", IndividualDto)
], CompanyDto.prototype, "representative", void 0);
class BankAccountDto {
}
exports.BankAccountDto = BankAccountDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome do banco (ex: ICA Bank)',
        example: 'ICA Bank',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome do banco não pode estar vazio' }),
    (0, class_validator_1.MinLength)(2, { message: 'Nome do banco muito curto' }),
    __metadata("design:type", String)
], BankAccountDto.prototype, "bank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Agência bancária (ex: 0001)',
        example: '0001',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Agência não pode estar vazia' }),
    (0, class_validator_1.MinLength)(2, { message: 'Agência inválida' }),
    __metadata("design:type", String)
], BankAccountDto.prototype, "agency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Conta bancária (ex: 1234567)',
        example: '1234567',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Conta não pode estar vazia' }),
    (0, class_validator_1.MinLength)(3, { message: 'Conta inválida' }),
    __metadata("design:type", String)
], BankAccountDto.prototype, "account", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Chave PIX (ex: <EMAIL>)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            if (value.match(/^\d{2}9\d{8}$/)) {
                return '+55' + value;
            }
            if (value.match(/^55\d{2}9\d{8}$/)) {
                return '+' + value;
            }
        }
        return value;
    }),
    (0, class_validator_1.Matches)(/^(\+55\d{2}9\d{8}|[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|\d{11}|\d{14}|[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})$/, {
        message: 'Chave PIX inválida. Use CPF (11 dígitos), CNPJ (14 dígitos), telefone (+55+DDD+Telefone), email ou chave aleatória.',
    }),
    __metadata("design:type", String)
], BankAccountDto.prototype, "pix", void 0);
class InvestmentDetailsDto {
}
exports.InvestmentDetailsDto = InvestmentDetailsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Valor do investimento',
        type: Number,
    }),
    (0, to_number_decorator_1.ToNumber)(),
    (0, class_validator_1.IsNumber)({ allowNaN: false }, { message: 'Valor do investimento é obrigatório' }),
    (0, class_validator_1.IsPositive)({ message: 'Valor deve ser maior que zero' }),
    __metadata("design:type", Number)
], InvestmentDetailsDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Taxa de remuneração',
        type: Number,
    }),
    (0, to_number_decorator_1.ToNumber)(),
    (0, class_validator_1.IsNumber)({ allowNaN: false }, { message: 'Taxa de remuneração é obrigatória' }),
    (0, class_validator_1.IsPositive)({ message: 'Taxa de remuneração deve ser maior que zero' }),
    __metadata("design:type", Number)
], InvestmentDetailsDto.prototype, "monthlyRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Prazo em meses',
        type: Number,
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'Prazo em meses é obrigatório' }),
    (0, class_validator_1.IsInt)({ message: 'O prazo deve ser um número inteiro' }),
    (0, class_validator_1.IsPositive)({ message: 'Prazo deve ser maior que zero' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Prazo em meses não pode ser vazio' }),
    __metadata("design:type", Number)
], InvestmentDetailsDto.prototype, "durationInMonths", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Método de pagamento',
        enum: PaymentMethod,
        example: PaymentMethod.PIX,
    }),
    (0, class_validator_1.IsEnum)(PaymentMethod, {
        message: 'Método de pagamento inválido. Tipos válidos: pix, bank_transfer, boleto',
    }),
    __metadata("design:type", String)
], InvestmentDetailsDto.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de início (ex: 2025-01-01)',
        example: (0, generate_date_1.generateDate)(0),
    }),
    (0, class_validator_1.IsDateString)({}, { message: 'Data de início inválida' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Data de início não pode ser vazia' }),
    __metadata("design:type", String)
], InvestmentDetailsDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de fim (ex: 2025-01-01)',
        example: (0, generate_date_1.generateDate)(365),
    }),
    (0, class_validator_1.IsDateString)({}, { message: 'Data de fim de contrato inválida' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Data de fim do contrato não pode ser vazia' }),
    __metadata("design:type", String)
], InvestmentDetailsDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Perfil do investidor',
        type: String,
        enum: InvestorProfile,
        example: 'conservative, moderate, aggressive',
    }),
    (0, class_validator_1.IsEnum)(InvestorProfile, {
        message: 'Perfil do investidor inválido. Tipos válidos: conservative, moderate, aggressive',
    }),
    __metadata("design:type", String)
], InvestmentDetailsDto.prototype, "profile", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Quantidade de cotas (ex: 1)',
        example: 2,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], InvestmentDetailsDto.prototype, "quotaQuantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'É debênture? (ex: false)',
        example: false,
    }),
    (0, to_boolean_decorator_1.ToBoolean)(),
    (0, class_validator_1.IsBoolean)({ message: 'Campo Debênture é obrigatório' }),
    __metadata("design:type", Boolean)
], InvestmentDetailsDto.prototype, "isDebenture", void 0);
class AdvisorAssignmentDto {
}
exports.AdvisorAssignmentDto = AdvisorAssignmentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do assessor (ex: advisor-uuid)',
        example: '',
    }),
    (0, class_validator_1.IsUUID)('all', { message: 'ID do assessor inválido' }),
    __metadata("design:type", String)
], AdvisorAssignmentDto.prototype, "advisorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Porcentagem do assessor (%) (ex: 10)',
        example: 10,
    }),
    (0, to_number_decorator_1.ToNumber)(),
    (0, class_validator_1.IsNumber)({ allowNaN: false }, { message: 'Taxa do assessor é obrigatória' }),
    (0, class_validator_1.Min)(0.01, { message: 'Porcentagem deve ser maior que zero' }),
    (0, class_validator_1.Max)(100, { message: 'Porcentagem não pode ser maior que 100' }),
    __metadata("design:type", Number)
], AdvisorAssignmentDto.prototype, "rate", void 0);
//# sourceMappingURL=contract.types.js.map