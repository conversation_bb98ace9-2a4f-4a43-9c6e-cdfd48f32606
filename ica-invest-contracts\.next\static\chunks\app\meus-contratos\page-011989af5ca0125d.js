(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[235],{1712:function(e,t,a){Promise.resolve().then(a.bind(a,4547))},4547:function(e,t,a){"use strict";a.r(t),a.d(t,{Screen:function(){return Screen}});var s=a(7437),n=a(3877),o=a(8637),r=a(2265),l=a(4568),i=a(3042),d=a(1122),c=a(1980),x=a(4033),m=a(5968),u=a(6121),p=a(4984),v=a(2067),h=a.n(v),f=a(3014);function RenewContract(e){let{contract:t,setOpenModal:a}=e,[n,o]=(0,r.useState)(),[i,d]=(0,r.useState)(),[c,x]=(0,r.useState)(h()().format("YYYY-MM-DD")),[m,u]=(0,r.useState)(h()().format("YYYY-MM-DD")),[v,j]=(0,r.useState)(),[g,N]=(0,r.useState)();return(0,s.jsx)("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"w-5/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[(0,s.jsx)("p",{className:"text-lg font-bold",children:"Renova\xe7\xe3o de contrato"}),(0,s.jsxs)("div",{className:"flex gap-4 mt-5",children:[(0,s.jsxs)("div",{className:"md:w-2/4",children:[(0,s.jsx)("p",{className:"text-white mb-1",children:"Inicio do contrato"}),(0,s.jsx)("input",{value:c,onChange:e=>{let{target:t}=e;return x(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]}),(0,s.jsxs)("div",{className:"md:w-2/4",children:[(0,s.jsx)("p",{className:"text-white mb-1",children:"Fim do contrato"}),(0,s.jsx)("input",{value:m,onChange:e=>{let{target:t}=e;return u(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 mb-10 text-white items-center justify-around mt-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Contrato antigo"}),(0,s.jsx)(p.Z,{onFileUploaded:o,fileName:v,onRemoveFile:()=>{o(void 0)}})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Novo Contrato"}),(0,s.jsx)(p.Z,{onFileUploaded:d,fileName:g,onRemoveFile:()=>{d(void 0)}})]})]}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[(0,s.jsx)("div",{className:"px-10 bg-orange-linear flex items-center cursor-pointer",onClick:()=>{if(null===c)return f.Am.warning("Precisa informar a data de inicio de contrato");let e=new FormData;e.append("newStartDate",c),e.append("newEndDate",m),n&&e.append("oldContractPdf",n[0]),i&&e.append("newContractPdf",i[0]),l.Z.patch("/contract/renew/".concat(t.id),e).then(e=>{f.Am.success("Contrato atualizado com sucesso!"),a(!1)}).catch(e=>{})},children:(0,s.jsx)("p",{className:"text-sm",children:"Renovar contrato"})}),(0,s.jsx)("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer",onClick:()=>a(!1),children:(0,s.jsx)("p",{className:"text-sm",children:"Fechar"})})]})]})})}var j=a(6610),g=a(4209),N=a(8440);function ContractData(e){var t;let{contract:a,setAditive:n,setModal:o,setModalPayment:r,setRenew:l,loading:i,resendContract:d}=e;return(0,s.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10 w-full",children:[a.statusContrato===N.rd.DELETED&&(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Motivo da exclus\xe3o"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.cancelledReason})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"ID"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.idContrato})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Investidor"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.nomeInvestidor})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"CPF/CNPJ"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(null==a?void 0:a.documentoInvestidor)!==void 0&&(null===(t=(0,m.p4)(null==a?void 0:a.documentoInvestidor))||void 0===t?void 0:t.length)<=11?(0,m.VL)(String(null==a?void 0:a.documentoInvestidor)):(0,m.PK)(String(null==a?void 0:a.documentoInvestidor))})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Consultor Respons\xe1vel"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Comprado com"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.compradoCom})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Assinaturas"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Valor"}),(0,s.jsx)("p",{className:"text-xs text-end",children:Number(null==a?void 0:a.valorInvestimento).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Contrato Solicitado em"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(0,u.Z)(null==a?void 0:a.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Final do contrato"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(0,u.Z)(null==a?void 0:a.fimContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Status de Contrato"}),(0,s.jsx)("div",{children:(0,s.jsx)(g.Z,{description:(0,N.mP)(a.statusContrato).description,text:(0,N.mP)(a.statusContrato).title,textColor:(0,N.mP)(a.statusContrato).title})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Qtd de Cotas/Participa\xe7\xf5es"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(null==a?void 0:a.cotas)||"N\xe3o encontrado"})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Data de Car\xeancia"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.periodoCarencia})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Porcentagens"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:[null==a?void 0:a.rendimentoInvestimento,"%"]})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Ativado em"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(0,u.Z)(null==a?void 0:a.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Tags"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:["#",(null==a?void 0:a.tags)==="P2P"?"M\xfatuo":(null==a?void 0:a.tags)||"NE"]})]}),a.contratoPdf&&(0,s.jsx)("div",{className:"flex w-full mt-3",children:(0,s.jsx)("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(a.contratoPdf,"_blank"),children:"Ver contrato"})}),a.comprovamentePagamento&&(0,s.jsx)("div",{className:"flex w-full mt-3",children:(0,s.jsx)("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(a.comprovamentePagamento,"_blank"),children:"Ver comprovante anexado"})})]})}var b=a(2875),w=a(5233),C=a(5554),y=a(4207),S=a(7152),F=a(7395),A=a(6654),D=a(3277),Z=a(5691);let k=Z.Ry().shape({isPF:Z.O7().default(!0),name:Z.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").when("isPF",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),document:Z.Z_().when("isPF",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),value:Z.Z_().required("Obrigat\xf3rio"),profile:Z.Z_().required("Obrigat\xf3rio"),initDate:Z.Z_().required("Obrigat\xf3rio"),bank:Z.Z_().min(2,"Nome do banco muito curto").required("Obrigat\xf3rio"),accountNumber:Z.Z_().min(3,"Conta inv\xe1lida").required("Obrigat\xf3rio"),agency:Z.Z_().min(2,"Ag\xeancia inv\xe1lida").required("Obrigat\xf3rio"),pix:Z.Z_().required("Obrigat\xf3rio"),observations:Z.Z_().notRequired()});var I=a(9701),P=a(4279),R=a.n(P),M=a(1865);function AditiveContract(e){var t,a,n,o,i,d,c,x,u;let{contract:v,setOpenModal:h}=e,[j,g]=(0,r.useState)(),[N,Z]=(0,r.useState)(),[P,E]=(0,r.useState)("new"),[T,Y]=(0,r.useState)(!1),[z,_]=(0,r.useState)(),[O,U]=(0,r.useState)(),q=(0,r.useRef)(null),{register:L,handleSubmit:V,watch:B,setValue:G,reset:W,formState:{errors:X}}=(0,M.cI)({resolver:(0,I.X)(k),mode:"onChange"});(0,r.useEffect)(()=>{let handClickOutside=e=>{q.current&&!q.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",handClickOutside),()=>{document.removeEventListener("mousedown",handClickOutside)}});let createAditive=e=>{Y(!0);let t={contractId:null==v?void 0:v.idContrato,investment:{value:(0,D.Z)(e.value),profile:e.profile,yield:Number(v.rendimentoInvestimento),date:R()(e.initDate).format("YYYY-MM-DD")},accountBank:{bank:e.bank,accountNumber:e.accountNumber,agency:e.agency,pix:e.pix},owner:v.documentoInvestidor.length>11?{name:e.name,cpf:(0,m.p4)(e.document||"")}:void 0,observations:e.observations,signIca:F.l};l.Z.post("/contract/additive",t).then(e=>{f.Am.success("Contrato de aditivo criado com sucesso!"),h(!1)}).catch(e=>{(0,A.Z)(e,"N\xe3o conseguimos criar o contrato de aditivo!")}).finally(()=>Y(!1))},registerAditiveExists=e=>{if("exist"===P&&(!N||!j))return f.Am.error("\xc9 necess\xe1rio anexar o contrato e o comprovante de pagamento");if("exist"===P&&R().utc(e.initDate).isSameOrAfter(R().utc(),"day"))return f.Am.warn("Data de in\xedcio do contrato n\xe3o pode ser igual ou maior que a data atual");Y(!0);let t=new FormData;N&&t.append("contractPdf",N[0]),j&&t.append("proofPayment",j[0]),t.append("contractId",null==v?void 0:v.idContrato),t.append("investment[value]",e.value.replace(".","").replace(",",".")),t.append("investment[date]",R()(String(e.initDate)).format("YYYY-MM-DD")),t.append("investment[yield]",v.rendimentoInvestimento),t.append("accountBank[bank]",e.bank),t.append("accountBank[accountNumber]",e.accountNumber),t.append("accountBank[agency]",e.agency),t.append("accountBank[pix]",e.pix),t.append("observations",String(e.observations)),l.Z.post("/contract/additive-manual",t).then(e=>{f.Am.success("Contrato de aditivo cadastrado com sucesso!"),h(!1)}).catch(e=>{(0,A.Z)(e,"N\xe3o foi poss\xedvel cadastrar o aditivo")}).finally(()=>Y(!1))};return(0,r.useEffect)(()=>{v.documentoInvestidor.length>11?G("isPF",!1):G("isPF",!0)},[]),(0,r.useEffect)(()=>{"exist"!==P?G("initDate",R()().format("YYYY-MM-DD")):G("initDate","")},[P]),(0,s.jsx)("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-20",children:(0,s.jsxs)("div",{ref:q,className:"md:w-6/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900] overflow-auto h-[88%]",children:[(0,s.jsx)("p",{className:"text-2xl font-bold",children:"Criar contrato aditivo"}),(0,s.jsx)("div",{className:"mt-5 w-full",children:(0,s.jsxs)("div",{className:"mb-5",children:[(0,s.jsxs)("div",{className:"flex gap-4 mb-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("input",{type:"checkbox",name:"",checked:"new"===P,onChange:()=>E("new"),id:"novo",className:"mr-2 cursor-pointer"}),(0,s.jsx)("label",{htmlFor:"novo",className:"cursor-pointer select-none",children:"Criar novo aditivo"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("input",{type:"checkbox",name:"",checked:"exist"===P,onChange:()=>E("exist"),id:"manual",className:"mr-2 cursor-pointer"}),(0,s.jsx)("label",{htmlFor:"manual",className:"cursor-pointer select-none",children:"Cadastrar aditivo existente"})]})]}),(0,s.jsxs)("form",{action:"",onSubmit:V(e=>{"new"===P?createAditive(e):registerAditiveExists(e)}),children:[v.documentoInvestidor.length>11&&(0,s.jsx)(w.Z,{color:"black",title:"Dados do Representante Legal",children:(0,s.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,s.jsx)(y.Z,{register:L,name:"name",width:"300px",error:!!X.name,errorMessage:null==X?void 0:null===(t=X.name)||void 0===t?void 0:t.message,label:"Nome completo"}),(0,s.jsx)(y.Z,{register:L,name:"document",width:"200px",error:!!X.document,errorMessage:null==X?void 0:null===(a=X.document)||void 0===a?void 0:a.message,label:"CPF",setValue:e=>G("document",(0,m.VL)(e||""))})]})}),(0,s.jsx)(w.Z,{color:"black",title:"Dados de Investimento",children:(0,s.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,s.jsx)(y.Z,{register:L,name:"value",width:"200px",error:!!X.value,errorMessage:null==X?void 0:null===(n=X.value)||void 0===n?void 0:n.message,label:"Valor",setValue:e=>G("value",(0,m.Ht)(e||""))}),(0,s.jsx)(C.Z,{width:"200px",name:"profile",register:L,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!X.profile,errorMessage:null==X?void 0:null===(o=X.profile)||void 0===o?void 0:o.message,label:"Perfil"}),(0,s.jsx)(y.Z,{type:"date",register:L,minDate:"exist"!==P?R().utc().format("YYYY-MM-DD"):void 0,maxDate:"exist"===P?R().utc().subtract(1,"day").format("YYYY-MM-DD"):void 0,disabled:"exist"!==P,name:"initDate",width:"200px",error:!!X.initDate,errorMessage:null==X?void 0:null===(i=X.initDate)||void 0===i?void 0:i.message,label:"Inicio do contrato"})]})}),(0,s.jsx)(w.Z,{color:"black",title:"Dados bancarios",children:(0,s.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,s.jsx)(y.Z,{register:L,name:"bank",width:"200px",error:!!X.bank,errorMessage:null==X?void 0:null===(d=X.bank)||void 0===d?void 0:d.message,label:"Banco"}),(0,s.jsx)(y.Z,{register:L,name:"agency",width:"200px",error:!!X.agency,errorMessage:null==X?void 0:null===(c=X.agency)||void 0===c?void 0:c.message,label:"Ag\xeancia"}),(0,s.jsx)(y.Z,{register:L,name:"accountNumber",width:"200px",error:!!X.accountNumber,errorMessage:null==X?void 0:null===(x=X.accountNumber)||void 0===x?void 0:x.message,label:"Conta"}),(0,s.jsx)(y.Z,{register:L,name:"pix",width:"200px",error:!!X.pix,errorMessage:null==X?void 0:null===(u=X.pix)||void 0===u?void 0:u.message,label:"Pix"})]})}),(0,s.jsx)(w.Z,{color:"black",title:"Observa\xe7\xf5es",children:(0,s.jsx)(S.Z,{name:"observations",register:L})}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[(0,s.jsx)("div",{children:(0,s.jsx)(b.Z,{label:"Criar aditivo",loading:T,className:"bg-orange-linear",disabled:T})}),(0,s.jsx)("div",{children:(0,s.jsx)(b.Z,{label:"Fechar",loading:!1,handleSubmit:()=>h(!1)})})]})]})]})}),"exist"===P&&(0,s.jsxs)("div",{className:"md:flex-row flex flex-col gap-2 mb-10 text-white jus  mt-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Aditivo"}),(0,s.jsx)(p.Z,{onFileUploaded:Z,fileName:z,onRemoveFile:()=>{Z(void 0)}})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Comprovante de pagamento"}),(0,s.jsx)(p.Z,{onFileUploaded:g,fileName:O,onRemoveFile:()=>{g(void 0)}})]})]})]})})}var E=a(3256),T=a(5838),Y=a(8647);function AditiveData(e){let{contract:t,aditives:a,getAditives:n}=e,[o,d]=(0,r.useState)(),[c,x]=(0,r.useState)(),[m,p]=(0,r.useState)(!1),v=(0,E.e)();(0,r.useEffect)(()=>{c&&o&&onUploadFile()},[c,o]);let h=(0,r.useCallback)(e=>{d(e)},[]),onUploadFile=()=>{if(!o)return f.Am.warning("Selecione um comprovante para anexar!");p(!0),f.Am.info("Enviando comprovante...");let e=new FormData;e.append("addendumId",String(c)),o&&e.append("proofPayment",o[0]),l.Z.post("/contract/addendum/proof-payment",e).then(e=>{f.Am.success("Comprovante anexado com sucesso!"),n(),d(void 0)}).catch(e=>{(0,A.Z)(e,"Erro ao enviar comprovante")}).finally(()=>{p(!1)})},{getRootProps:j,getInputProps:b}=(0,Y.uI)({onDrop:h,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&f.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{d(void 0)},disabled:m}),returnAddendumFiles=e=>{let{files:t,type:a}=e,n=t.filter(e=>e.type===a)[0];return n?(0,s.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,s.jsx)(i.Z,{className:"cursor-pointer",onClick:()=>{window.open(n.url,"_blank")},color:"#fff",width:20})}):n||"PAYMENT"!==a||"broker"!==v.name&&"advisor"!==v.name?(0,s.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,s.jsx)(T.Z,{color:"#FF9900",width:20})}):(0,s.jsxs)("div",{...j(),children:[(0,s.jsx)("input",{...b(),disabled:m,accept:".png,.jpg,.pdf"}),(0,s.jsx)("p",{className:"w-full flex items-center justify-center bg-orange-linear py-1 rounded-lg text-sm ".concat(m?"opacity-50":"cursor-pointer"),children:"Anexar"})]})};return(0,s.jsx)("div",{className:"mt-5",children:(0,s.jsx)("div",{className:"w-full overflow-x-auto pb-20",children:(0,s.jsxs)("table",{className:"w-full min-w-[700px] text-center",children:[(0,s.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900] h-10",children:(0,s.jsxs)("tr",{className:"w-full py-2",children:[(0,s.jsx)("th",{className:"min-w-[100px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Valor"})}),(0,s.jsx)("th",{className:"min-w-[150px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Rendimento"})}),(0,s.jsx)("th",{className:"min-w-[250px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Consultor"})}),(0,s.jsx)("th",{className:"min-w-[150px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Criado em"})}),(0,s.jsx)("th",{className:"min-w-[100px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Assinatura"})}),(0,s.jsx)("th",{className:"min-w-[100px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Contrato"})}),(0,s.jsx)("th",{className:"min-w-[120px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Comprovante"})})]})}),a.length>=1?(0,s.jsx)("tbody",{className:"w-full",children:a.map((e,a)=>(0,s.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-xs text-center",children:Number(e.value||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),(0,s.jsx)("td",{children:(0,s.jsxs)("p",{className:"text-xs text-center",children:[e.yieldRate||"0","%"]})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-xs text-center",children:t.consultorResponsavel})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-xs text-center",children:(0,u.Z)(e.applicationDate)})}),(0,s.jsx)("td",{className:"select-none",children:(0,s.jsx)(g.Z,{description:(0,N.XW)(e.status).description,text:(0,N.XW)(e.status).title,textColor:(0,N.XW)(e.status).textColor})}),(0,s.jsx)("td",{children:returnAddendumFiles({files:null==e?void 0:e.addendumFiles,type:"ADDENDUM"})}),(0,s.jsx)("td",{onClick:()=>x(e.id),children:returnAddendumFiles({files:null==e?void 0:e.addendumFiles,type:"PAYMENT",addendumId:e.id})})]},a))}):(0,s.jsx)("div",{className:"mt-5 absolute translate-x-[-50%] left-[50%]",children:(0,s.jsx)("p",{children:"Nenhum dado encontrado"})})]})})})}var z=a(8610),_=a(8928),O=a(8700),U=a(3729);function ModalContract(e){let{contract:t,setModal:a,setRenew:n,setModalPayment:o}=e,[i,d]=(0,r.useState)(0),[c,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)([]),[p,v]=(0,r.useState)(!1),{navigation:g}=(0,z.H)(),[b,w]=(0,r.useState)(!1),[C,y]=(0,r.useState)(""),[F,D]=(0,r.useState)(!1),Z=(0,E.e)(),resendContract=()=>{x(!0),l.Z.post("/contract/send-notification/".concat(t.idContrato)).then(e=>{f.Am.success("Contrato encaminhado novamente para o investidor.")}).catch(e=>{var t,a;f.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o conseguimos encaminhar o contrato para o investidor.")}).finally(()=>x(!1))},getAditives=()=>{l.Z.get("/contract/".concat(t.idContrato,"/addendum")).then(e=>{u(e.data.addendums)}).catch(e=>{var t,a;f.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o conseguimos carregar os aditivos do contrato.")})};return(0,r.useEffect)(()=>{!1===p&&getAditives()},[p]),(0,s.jsxs)("div",{className:"",children:[(0,s.jsxs)("div",{className:"w-full text-white overflow-auto",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center",children:(0,s.jsx)(j.Z,{color:"#000",width:20})}),(0,s.jsx)("div",{className:"gap-y-1 flex flex-col",children:(0,s.jsx)("p",{className:"font-bold text-xs",children:"Detalhes do Contrato"})})]}),(0,s.jsxs)("div",{className:"w-full flex flex-wrap mt-4 gap-4 justify-start",children:[(0,s.jsx)("div",{className:"cursor-pointer text-center w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ".concat(0===i?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>d(0),children:(0,s.jsx)("p",{className:"md:text-sm text-xs",children:"Dados do Contrato"})}),(0,s.jsx)("div",{className:"cursor-pointer text-center w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ".concat(1===i?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>d(1),children:(0,s.jsx)("p",{className:"md:text-sm text-xs",children:"Aditivos"})})]}),b?(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-2 mt-5",children:"Digite o motivo da exclus\xe3o do contrato"}),(0,s.jsx)(S.Z,{name:"",value:C,setValue:y,className:"h-20"})]}):0===i?(0,s.jsx)(ContractData,{contract:t,loading:c,resendContract:resendContract,setAditive:v,setModal:a,setModalPayment:o,setRenew:n}):(0,s.jsx)("div",{className:"min-h-[300px]",children:(0,s.jsx)(AditiveData,{contract:t,aditives:m,getAditives:getAditives})}),(0,s.jsxs)("div",{className:"w-full flex mt-10 flex-wrap gap-2 justify-start",children:[(null==t?void 0:t.statusContrato)===N.rd.EXPIRED&&"superadmin"!==Z.name&&(0,s.jsx)("div",{children:(0,s.jsx)(O.z,{loading:!1,onClick:()=>{n(!0),a(!1)},children:"Renovar contrato"})}),!(null==t?void 0:t.comprovamentePagamento)&&t.statusContrato===N.rd.AWAITING_DEPOSIT&&(0,s.jsx)("div",{children:(0,s.jsx)(O.z,{loading:!1,onClick:()=>{o(!0),a(!1)},children:"Anexar comprovante"})}),b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(O.z,{loading:F,disabled:F,variant:"destructive",onClick:()=>{if(""===C)return f.Am.warn("Digite o motivo para excluir o contrato");D(!0),l.Z.post("/contract/".concat(t.idContrato,"/delete"),{role:Z.roleId,reason:C}).then(e=>{f.Am.success("Contrato excluido com sucesso!"),y(""),w(!1),setTimeout(()=>{window.location.reload()},2e3)}).catch(e=>{(0,A.Z)(e,"Erro ao excluir contrato!")}).finally(()=>{D(!1)})},children:"Confirmar"}),(0,s.jsx)(O.z,{disabled:F,variant:"secondary",onClick:()=>{w(!1),y("")},children:"Fechar"})]}):(0,_.f)(t.statusContrato)?(0,s.jsx)(O.z,{variant:"destructive",onClick:()=>w(!0),children:"Excluir Contrato"}):void 0,(0,s.jsx)("div",{children:"superadmin"!==Z.name&&(t.statusContrato===N.rd.AWAITING_INVESTOR_SIGNATURE||t.statusContrato===N.rd.SIGNATURE_SENT)&&(0,s.jsx)(O.z,{loading:c,onClick:resendContract,children:"Reenviar Contrato"})}),t.statusContrato===N.rd.ACTIVE&&!h().utc(t.fimContrato).isBefore(h()(),"day")&&(0,s.jsx)("div",{children:(0,s.jsx)(O.z,{onClick:()=>{v(!0)},children:"Criar Aditivo"})}),U.z.includes(t.statusContrato.toUpperCase())&&(0,s.jsx)("div",{children:(0,s.jsx)(O.z,{onClick:()=>{g("/meus-contratos/contrato/".concat(t.idContrato))},children:"Editar contrato"})}),U.f.includes(t.statusContrato.toUpperCase())&&(0,s.jsx)("div",{children:(0,s.jsx)(O.z,{onClick:()=>{g("/meus-contratos/contrato/novo/".concat(t.idContrato))},children:"Editar contrato"})})]})]}),p&&t&&(0,s.jsx)(AditiveContract,{contract:t,setOpenModal:v})]})}function AddPayment(e){let{contract:t,setOpenModal:a,documentSearch:n,getContracts:o}=e,[i,d]=(0,r.useState)(),[c,x]=(0,r.useState)(!1),[m,u]=(0,r.useState)(),renewContract=async()=>{if(!i)return f.Am.warning("Selecione um comprovante para anexar!");x(!0);let e=new FormData;e.append("contractId",t.idContrato),i&&e.append("file",i[0]),l.Z.put("/contract/upload/proof-payment",e).then(e=>{f.Am.success("Comprovante anexado com sucesso!"),x(!1),a(!1),setTimeout(()=>{window.location.reload()},1e3)}).catch(e=>{var t,a;f.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o foi possivel anexar o comprovante a esse contrato"),x(!1)})};return(0,s.jsx)("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"md:w-3/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[(0,s.jsx)("p",{className:"text-lg font-bold",children:"Anexar comprovante"}),(0,s.jsx)("p",{className:"text-xs",children:"*Anexe neste campo o comprovante de Pagamento do seu\xa0Investidor"}),(0,s.jsx)("div",{className:"flex gap-2 mb-10 text-white items-start justify-center mt-5",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Comprovante"}),(0,s.jsx)(p.Z,{onFileUploaded:d,fileName:m,onRemoveFile:()=>{d(void 0)}})]})}),(0,s.jsxs)("div",{className:"flex w-full mt-10 justify-between gap-10",children:[(0,s.jsx)(b.Z,{label:"Anexar",loading:c,className:"bg-orange-linear",handleSubmit:renewContract}),(0,s.jsx)("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer flex items-center",onClick:()=>a(!1),children:(0,s.jsx)("p",{className:"text-sm",children:"Fechar"})})]})]})})}var q=a(5465),L=a(3401),V=a(9891),B=a(7626),G=a(439),W=a(6553),X=a(2359),J=a(9784);function Screen(e){var t,a,p;let{initialSignatarie:v,initialPage:h="1",initialType:f="all",initialStartData:j="",initialEndData:b="",initialStatus:w="Todos"}=e,C=(0,x.useRouter)(),y=(0,x.useSearchParams)(),[S,F]=(0,r.useState)(),[A,D]=(0,r.useState)(!1),[Z,k]=(0,r.useState)(!1),[I,P]=(0,r.useState)(!1),[R,M]=(0,r.useState)(!1),[T,Y]=(0,r.useState)(w),[z,_]=(0,r.useState)(Number(h)||1),[O,U]=(0,r.useState)(v||""),K=(0,G.Nr)(O,300),[H,Q]=(0,r.useState)({startData:j,endData:b,type:f,status:w}),[$,ee]=(0,r.useState)(!1),et=(0,E.e)(),{data:ea,isLoading:es}=(0,V.a)({queryKey:B.U.CONTRACTS(z,K,T,H,et.roleId),queryFn:async()=>{var e;console.log("API call dateTo:",H.endData);let t=await l.Z.get(returnRoute(),{params:{roleId:et.roleId,limit:"10",page:z,status:"Todos"===T?void 0:T,signatarie:K?(0,m.p4)(K):void 0,dateFrom:""===H.startData?void 0:H.startData,dateTo:""===H.endData?void 0:H.endData,contractType:"all"===H.type?void 0:H.type}}),a=Number(null===(e=t.data)||void 0===e?void 0:e.totalPaginas)||1;return z>a?(_(1),{data:{documentos:[],totalPaginas:a,total:0}}):t},staleTime:6e4});(0,r.useEffect)(()=>{if(localStorage.setItem("typeCreateContract",""),v){let e=new URLSearchParams(y.toString());e.set("signatarie",v),e.set("page","1"),C.replace("?".concat(e.toString()))}},[v,C,y]);let returnRoute=()=>{switch(et.name){case"broker":return"/contract/list-contracts";case"advisor":return"/advisor/contracts";case"superadmin":return"/contract/list-contracts/superadmin";default:return""}},translateTag=e=>(null==e?void 0:e.toUpperCase())==="P2P"?"MUTUO":e.toUpperCase(),handleSearch=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{...H,status:T},a=new URLSearchParams(y.toString());a.set("signatarie",e),a.set("page","1"),a.set("type",t.type||"all"),a.set("startData",t.startData||""),a.set("endData",t.endData||""),a.set("status",t.status||"Todos"),console.log("handleSearch params:",a.toString()),C.replace("?".concat(a.toString()))};return(0,s.jsxs)("div",{children:[(0,s.jsx)(n.Z,{}),(0,s.jsx)(o.Z,{children:(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(X.yo,{open:Z,onOpenChange:k,children:[(0,s.jsx)("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:(0,s.jsx)("h1",{className:"m-auto font-bold text-2xl",children:"Contratos"})}),(0,s.jsx)(q.Z,{children:(0,s.jsxs)("div",{className:"w-full p-2 gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row w-full justify-between items-stretch md:items-center gap-4 mb-2",children:[("advisor"===et.name||"broker"===et.name)&&(0,s.jsx)("div",{className:"w-32 h-10 bg-orange-linear px-10 flex items-center justify-center md:mr-5 mb-2 md:mb-0 rounded-lg cursor-pointer",onClick:()=>{"advisor"===et.name?(localStorage.setItem("typeCreateContract","broker"),C.push("/meus-contratos/registro-manual")):ee(!0)},children:(0,s.jsx)("p",{children:"Criar"})}),(0,s.jsxs)("div",{className:"flex items-center gap-4 w-full justify-end",children:[(0,s.jsx)("div",{className:"w-full md:w-3/12",children:(0,s.jsx)(c.Z,{isDocument:!0,handleSearch:()=>handleSearch(O),setValue:e=>{U(e);let t=new URLSearchParams(y.toString());""===e?t.delete("signatarie"):t.set("signatarie",e),t.set("page","1"),C.replace("?".concat(t.toString()))},placeholder:"Pesquisar por CPF/CNPJ",value:O})}),(0,s.jsx)(W.Z,{activeModal:A,setActiveModal:D,filterData:{...H,status:T},setFilterData:e=>{Q({startData:e.startData,endData:e.endData,type:e.type,status:e.status}),Y(e.status)},handleSearch:handleSearch,setPage:_,signatarie:O})]})]}),(0,s.jsx)(L.Z,{data:(null==ea?void 0:null===(t=ea.data)||void 0===t?void 0:t.documentos)||[],headers:[{title:"",component:"id",width:"30px",render:(e,t)=>(0,s.jsx)("div",{className:"cursor-pointer",onClick:()=>{k(!0),F(t)},children:(0,s.jsx)(X.aM,{children:(0,s.jsx)(i.Z,{color:"#fff",width:20})})})},{title:"Investidor",component:"nomeInvestidor",width:"150px"},{title:"CPF/CNPJ",component:"document",position:"center",width:"150px",render:(e,t)=>(0,s.jsx)("p",{className:"text-center",children:(0,m.p4)(t.documentoInvestidor||"").length<=11?(0,m.VL)(t.documentoInvestidor||""):(0,m.PK)(t.documentoInvestidor||"")})},{title:"Valor",component:"valorInvestimento",position:"center",render:(e,t)=>(0,s.jsx)("p",{className:"text-center",children:(0,J.F)(t)})},{title:"Rendimento",component:"rendimentoInvestimento",position:"center",render:e=>(0,s.jsxs)("p",{className:"text-center",children:[String(e)||"0","%"]})},{title:"Consultor",component:"consultorResponsavel",position:"center",width:"100px"},{title:"Criado em",component:"inicioContrato",position:"center",render:e=>(0,s.jsx)("p",{className:"text-center",children:(0,u.Z)(String(e))})},{title:"Status",component:"statusContrato",position:"center",width:"100px",render:(e,t)=>(0,s.jsx)(g.Z,{description:(0,N.mP)(t.statusContrato).description,text:(0,N.mP)(t.statusContrato).title,textColor:(0,N.mP)(t.statusContrato).textColor})},{title:"Modelo",component:"inicioContrato",position:"center",render:(e,t)=>(0,s.jsx)("div",{className:"px-2",children:(0,s.jsx)("div",{className:"bg-white py-[5px] px-[10px] rounded-md text-center",children:(0,s.jsx)("p",{className:"text-xs text-[#FF9900] font-bold",children:translateTag(t.tags)||"NE"})})})}],loading:es,pagination:{page:z,lastPage:Number(null==ea?void 0:null===(a=ea.data)||void 0===a?void 0:a.totalPaginas)||1,perPage:10,setPage:e=>{_(e);let t=new URLSearchParams(y.toString());t.set("page",String(e)),C.replace("?".concat(t.toString()))},totalItems:String((null==ea?void 0:null===(p=ea.data)||void 0===p?void 0:p.total)||0)}})]})}),(0,s.jsx)(X.ue,{className:"w-full md:w-[500px]",children:(0,s.jsx)(ModalContract,{contract:S,setRenew:M,setModal:k,setModalPayment:P})}),I&&S&&(0,s.jsx)(AddPayment,{contract:S,setOpenModal:P,documentSearch:O,getContracts:handleSearch}),R&&S&&(0,s.jsx)(RenewContract,{contract:S,setOpenModal:M}),$&&(0,s.jsx)("div",{className:"fixed z-40 top-0 left-0 w-full h-full bg-[#3A3A3AAB]",onClick:()=>ee(!1),children:(0,s.jsxs)("div",{className:"absolute w-[90%] md:w-3/6 bg-[#1C1C1C] z-50 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] border border-[#FF9900] rounded-lg",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center border-b border-[#FF9900]",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Individual"}),(0,s.jsx)("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com negocia\xe7\xe3o exclusiva para voc\xea."})]}),(0,s.jsx)("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer animate-moveXRight",onClick:()=>{localStorage.setItem("typeCreateContract","broker"),C.push("/meus-contratos/registro-manual")},children:(0,s.jsx)(d.Z,{width:20})})]}),(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Compartilhado"}),(0,s.jsx)("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com a negocia\xe7\xe3o personalizada de m\xfaltiplos assessores."})]}),(0,s.jsx)("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer",onClick:()=>{localStorage.setItem("typeCreateContract","advisors"),C.push("/meus-contratos/registro-manual")},children:(0,s.jsx)(d.Z,{width:20})})]})]})})]})})})]})}},5233:function(e,t,a){"use strict";a.d(t,{Z:function(){return CoverForm}});var s=a(7437);function CoverForm(e){let{title:t,children:a,width:n="auto",color:o="withe"}=e;return(0,s.jsxs)("div",{className:"md:w-auto w-full",children:[(0,s.jsx)("p",{className:"text-xl text-white mb-2",children:t}),(0,s.jsx)("div",{className:"mb-10 m-auto bg-opacity-90 rounded-2xl shadow-current ".concat("withe"===o?"bg-zinc-900 border border-[#FF9900] p-5":"pt-1"),children:a})]})}},5554:function(e,t,a){"use strict";a.d(t,{Z:function(){return InputSelect}});var s=a(7437);function InputSelect(e){let{optionSelected:t,options:a,setOptionSelected:n,label:o,placeHolder:r="",width:l="100%",register:i=()=>{},error:d,errorMessage:c,name:x="",disableErrorMessage:m=!1,disabled:u=!1}=e;return(0,s.jsxs)("div",{className:"inputSelect relative group",style:{width:l},children:[(0,s.jsx)("p",{className:"text-white mb-1 text-sm",children:o}),(0,s.jsxs)("select",{disabled:m&&!c,...i(x),value:t,onChange:e=>{let{target:t}=e;n&&n(t.value)},id:x,className:"h-12 w-full px-4 ".concat(u?"text-zinc-400":"text-white"," rounded-xl ").concat(d?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),children:[r&&(0,s.jsx)("option",{selected:!0,disabled:!0,value:"",children:r}),a.map((e,t)=>(0,s.jsx)("option",{className:"cursor-pointer",value:e.value,children:e.label},t))]}),d&&(0,s.jsx)("div",{className:" absolute gr max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[90%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:c})]})}},4207:function(e,t,a){"use strict";a.d(t,{Z:function(){return InputText}});var s=a(7437);function InputText(e){let{label:t,setValue:a,error:n,errorMessage:o,width:r="auto",register:l,name:i,placeholder:d="",type:c="text",disabled:x=!1,minDate:m,minLength:u,maxLength:p,maxDate:v,disableErrorMessage:h=!1,onBlur:f,value:j,onChange:g}=e;return(0,s.jsxs)("div",{className:"input relative group",style:{width:r},children:[(0,s.jsxs)("p",{className:"text-white mb-1 text-sm",children:[t,n&&!h&&(0,s.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",o]})]}),(0,s.jsx)("input",{...l(i),placeholder:d,type:c,id:i,disabled:x,min:m,max:v,minLength:u,maxLength:p,...a?{onChange:e=>{let{target:t}=e;return a(t.value)}}:{},onBlur:f,className:"h-12 w-full px-4 ".concat(x?"text-zinc-400":"text-white"," rounded-xl ").concat(n?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),...void 0!==j?{value:j}:{},...g?{onChange:g}:{}}),n&&(0,s.jsx)("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:o})]})}a(9514)},3729:function(e,t,a){"use strict";a.d(t,{f:function(){return o},z:function(){return n}});var s=a(8440);let n=[s.rd.REJECTED_BY_AUDIT,s.rd.AWAITING_AUDIT],o=[s.rd.AWAITING_INVESTOR_SIGNATURE]},9514:function(){}},function(e){e.O(0,[6990,7326,8276,5371,6946,1865,3964,9891,1306,6955,3151,46,2971,7864,1744],function(){return e(e.s=1712)}),_N_E=e.O()}]);