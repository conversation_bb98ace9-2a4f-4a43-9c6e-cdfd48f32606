(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8092],{983:function(e,s,t){Promise.resolve().then(t.bind(t,5996))},5996:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return Investidores}});var l=t(7437),a=t(3877),r=t(8637),d=t(2265),n=t(4568),o=t(3014),c=t(3042),i=t(9367),m=t(13),x=t(3217),h=t(6689),u=t(5968),p=t(1481),j=t(8689),f=t(8089),v=t(6654),N=t(3256),b=t(2359);function Investidores(){let[e,s]=(0,d.useState)(!1),[t,g]=(0,d.useState)(),[w,y]=(0,d.useState)(),[k,I]=(0,d.useState)(),[C,S]=(0,d.useState)(),[Z,F]=(0,d.useState)(!1),[A,P]=(0,d.useState)(!1),[E,_]=(0,d.useState)("Todos"),[K,L]=(0,d.useState)(1),[V,D]=(0,d.useState)(0),[M,O]=(0,d.useState)(),[B,J]=(0,d.useState)(),[T,U]=(0,d.useState)(),[q,z]=(0,d.useState)("broker"),[G,H]=(0,d.useState)(),Q=(0,N.e)();async function getAcessorsInvestors(){s(!0);try{let e=await n.Z.get("/wallets/broker/investors",{params:{adviserId:Q.roleId}}),t=await n.Z.get("/wallets/broker/advisors",{params:{adviserId:Q.roleId}}),l=e.data,a=t.data,r=[],d=[];l.map(e=>{r.push({...e,document:e.document.length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document),type:"investidor"})}),a.map(e=>{d.push({...e,document:e.document.length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document),type:"assessor"})}),g([...d,...r]),s(!1)}catch(e){o.Am.error("Erro ao buscar os assessores")}}let getInvestorData=e=>{o.Am.info("Buscando dados do investidor..."),n.Z.get("/contract/".concat(e.id)).then(s=>{J({...s.data,...e}),F(!0)}).catch(e=>{o.Am.error("N\xe3o foi possivel buscar o investidor")})};(0,d.useEffect)(()=>{"advisor"===Q.name?(s(!0),n.Z.get("/wallets/advisor/investors",{params:{adviserId:Q.roleId}}).then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e.document.length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document),type:"investidor"})}),g(s)}).catch(e=>{o.Am.error("Erro ao buscar os investidores")}).finally(()=>s(!1))):0===V?getAcessorsInvestors():1===V&&(s(!0),n.Z.get("/wallets/broker/investors-advisor",{params:{adviserId:Q.roleId,advisorId:null==C?void 0:C.id}}).then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e.document.length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document),type:"investidor"})}),g(s)}).catch(e=>{o.Am.error("Erro ao buscar os investidores")}).finally(()=>s(!1)))},[V]);let getAdvisorData=e=>{o.Am.info("Buscando dados do assessor...",{toastId:"advisor"}),n.Z.get("/wallets/advisor/one?advisorId=".concat(e.id)).then(e=>{o.Am.dismiss("advisor"),F(!0),H(e.data)}).catch(e=>{(0,v.Z)(e,"N\xe3o foi possivel buscar o investidor"),o.Am.dismiss("broadvisorker")})};return(0,l.jsx)("div",{children:(0,l.jsxs)(b.yo,{open:Z,onOpenChange:F,children:[(0,l.jsx)(a.Z,{}),(0,l.jsx)(r.Z,{children:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:(0,l.jsx)("h1",{className:"m-auto font-bold text-2xl",children:"Usu\xe1rios"})}),C&&(0,l.jsx)("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border text-white",children:(0,l.jsxs)("div",{className:"flex w-full",children:[(0,l.jsx)("div",{className:"w-[25px] h-[25px] bg-white rounded-full flex items-center justify-center",children:(0,l.jsx)("p",{className:"text-[#FF9900] text-xs font-bold",children:(e=>{let s=e.split(" ");return s.length>1?"".concat(null==C?void 0:C.name.split(" ")[0][0]).concat(null==C?void 0:C.name.split(" ")[1][0]):"".concat(null==C?void 0:C.name.split(" ")[0][0]).concat(null==C?void 0:C.name.split(" ")[0][1])})(C.name)})}),(0,l.jsx)("div",{className:"w-full",children:(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"ml-3 text-base",children:null==C?void 0:C.name}),(0,l.jsx)("p",{className:"ml-3 text-sm",children:null==C?void 0:C.type})]})}),(0,l.jsx)("div",{className:"cursor-pointer",onClick:()=>{D(0),S(void 0)},children:(0,l.jsx)(h.Z,{width:20})})]})}),(0,l.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] w-full text-white mt-10 overflow-x-auto rounded-b-md border border-[#FF9900]",children:[(0,l.jsx)("div",{className:"flex w-full justify-end p-2"}),(0,l.jsxs)("table",{className:"w-full relative min-h-20",children:[(0,l.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,l.jsxs)("tr",{className:"w-full",children:[(0,l.jsx)("th",{className:"w-10",children:(0,l.jsx)("p",{className:"font-bold text-sm"})}),(0,l.jsx)("th",{className:"w-10",children:(0,l.jsx)("p",{className:"font-bold text-sm"})}),(0,l.jsx)("th",{className:"py-2",children:(0,l.jsx)("p",{className:"font-bold text-sm",children:"Nome"})}),(0,l.jsx)("th",{children:(0,l.jsx)("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),(0,l.jsx)("th",{children:(0,l.jsx)("p",{className:"font-bold text-sm",children:"E-mail"})}),(0,l.jsx)("th",{children:(0,l.jsx)("p",{className:"font-bold text-sm",children:"Perfil"})})]})}),(0,l.jsx)(l.Fragment,{children:!1===e?(0,l.jsx)(l.Fragment,{children:(null==t?void 0:t.length)>=1?(0,l.jsx)("tbody",{className:"w-full",children:t.map((e,s)=>(0,l.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[(0,l.jsx)("td",{className:"w-10",children:"assessor"===e.type&&(0,l.jsx)("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{D(1),S(e)},children:(0,l.jsx)(c.Z,{color:"#fff",width:20})})}),(0,l.jsx)("td",{className:"w-10",children:("investidor"===e.type||"assessor"===e.type)&&(0,l.jsx)("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{"investidor"===e.type?(z("investor"),getInvestorData(e)):"assessor"===e.type&&(z("advisor"),getAdvisorData(e))},children:(0,l.jsx)(i.Z,{color:"#fff",width:20})})}),(0,l.jsx)("td",{children:(0,l.jsx)("p",{className:"text-sm text-center py-1",children:e.name||"N\xe3o encontrado"})}),(0,l.jsx)("td",{children:(0,l.jsx)("p",{className:"text-sm text-center",children:(0,u.p4)(e.document).length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document)})}),(0,l.jsx)("td",{children:(0,l.jsx)("p",{className:"text-sm text-center",children:e.email})}),(0,l.jsx)("td",{children:(0,l.jsx)("p",{className:"text-sm text-center",children:e.type})})]},s))}):(0,l.jsx)("tbody",{className:"w-full",children:(0,l.jsx)("tr",{children:(0,l.jsx)("td",{colSpan:6,className:"text-center py-4",children:"Nenhum dado encontrado"})})})}):(0,l.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[(0,l.jsx)("td",{className:"px-1",children:(0,l.jsx)(j.j,{height:"25px"})}),(0,l.jsx)("td",{className:"px-1",children:(0,l.jsx)(j.j,{height:"25px"})}),(0,l.jsx)("td",{className:"px-1",children:(0,l.jsx)(j.j,{height:"25px"})}),(0,l.jsx)("td",{className:"px-1",children:(0,l.jsx)(j.j,{height:"25px"})}),(0,l.jsx)("td",{className:"px-1",children:(0,l.jsx)(j.j,{height:"25px"})}),(0,l.jsx)("td",{className:"px-1",children:(0,l.jsx)(j.j,{height:"25px"})})]})})]}),(0,l.jsx)("div",{className:"w-full flex justify-end items-center pr-5 ",children:(0,l.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,l.jsx)("p",{className:"p-1 bg-[#262626] rounded-md ".concat(Number(K)>1?"cursor-pointer":""," flex items-center"),onClick:e=>{let{}=e;Number(K)>1&&L(K-1)},children:(0,l.jsx)(m.Z,{color:Number(K)>1?"#fff":"#424242",width:20})}),(0,l.jsx)("p",{className:"font-bold bg-[#262626] rounded-md py-1 px-2",children:K}),(0,l.jsx)("p",{className:"p-1 bg-[#262626] rounded-md ".concat(K<Number(null==M?void 0:M.lastPage)?"cursor-pointer":""," flex items-center"),onClick:e=>{let{}=e;K<Number(null==M?void 0:M.lastPage)&&L(K+1)},children:(0,l.jsx)(x.Z,{color:K<Number(null==M?void 0:M.lastPage)?"#fff":"#424242",width:20})})]})})]}),(0,l.jsx)(b.ue,{className:"w-full md:w-[500px]",children:Z?"investor"===q?(0,l.jsx)(p.Z,{investor:B,setModal:F}):G&&(0,l.jsx)(f.Z,{typeModal:q,broker:G,setModal:F}):(0,l.jsx)(l.Fragment,{})})]})})]})})}}},function(e){e.O(0,[6990,7326,8276,5371,6946,1865,1306,3151,7050,2971,7864,1744],function(){return e(e.s=983)}),_N_E=e.O()}]);