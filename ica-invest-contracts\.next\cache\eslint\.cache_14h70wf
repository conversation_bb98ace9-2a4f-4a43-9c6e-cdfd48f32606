[{"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\brokerCreate\\index.tsx": "1", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\brokerCreate\\registerForms\\BrokerRegisterPf.tsx": "2", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\brokerCreate\\registerForms\\BrokerRegisterPj.tsx": "3", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAdmin\\index.tsx": "4", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAdmin\\registerForms\\AdminRegisterPF.tsx": "5", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAdmin\\registerForms\\AdminRegisterPJ.tsx": "6", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAssessor\\index.tsx": "7", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAssessor\\registerForms\\AssessorRegisterPf.tsx": "8", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAssessor\\registerForms\\AssessorRegisterPj.tsx": "9", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateInvestor\\index.tsx": "10", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateInvestor\\registerForms\\BusinessRegister.tsx": "11", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateInvestor\\registerForms\\PhysicalRegister.tsx": "12", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\page.tsx": "13", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\alterar\\page.tsx": "14", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\AddPayment.tsx": "15", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\AditiveData.tsx": "16", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\AditiveModal.tsx": "17", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\ContractData.tsx": "18", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\FilterModal.tsx": "19", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\ModalContract.tsx": "20", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\RenewContract.tsx": "21", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\types.ts": "22", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\compose\\BusinessContract.tsx": "23", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\compose\\PhysicalContract.tsx": "24", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\page.tsx": "25", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\page.tsx": "26", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\_screen\\index.tsx": "27", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamento\\[id]\\page.tsx": "28", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamentos\\page.tsx": "29", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamentos\\types.ts": "30", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\page.tsx": "31", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\types.ts": "32", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\AdminData.tsx": "33", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\BrokerData.tsx": "34", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\InvestorData.tsx": "35", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\ModalRegister.tsx": "36", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\Retention\\compose\\ModalMonitoramento.tsx": "37", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\Retention\\index.tsx": "38", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\index.tsx": "39", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamento\\[id]\\page.tsx": "40", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamentos\\page.tsx": "41", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamentos\\types.ts": "42", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\page.tsx": "43", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\page.tsx": "44", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\[id]\\page.tsx": "45", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\informe-rendimentos\\page.tsx": "46", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\informe-rendimentos\\types.ts": "47", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\informe-rendimentos\\[id]\\[year]\\page.tsx": "48", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\investidores\\data.ts": "49", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\investidores\\page.tsx": "50", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx": "51", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\AddGoals.tsx": "52", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\AddPerspective.tsx": "53", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\AdminDashboard.tsx": "54", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\GoalDashboard.tsx": "55", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\ProgresTable.tsx": "56", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\page.tsx": "57", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\AddPayment.tsx": "58", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\AditiveData.tsx": "59", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\AditiveModal.tsx": "60", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\ContractData.tsx": "61", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\ModalContract.tsx": "62", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\registerForms\\BusinessRegister.tsx": "63", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\registerForms\\PhysicalRegister.tsx": "64", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\RenewContract.tsx": "65", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\page.tsx": "66", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\_components\\BusinessRegister.tsx": "67", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\_components\\PhysicalRegister.tsx": "68", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\_components\\ReusableSelect.tsx": "69", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\compose\\BusinessEditing.tsx": "70", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\compose\\PhysicalEditing.tsx": "71", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\page.tsx": "72", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\page.tsx": "73", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\registro-manual\\page.tsx": "74", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\_screen\\index.tsx": "75", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\compose\\ModalMonitoramento.tsx": "76", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\compose\\ModalTypeAction.tsx": "77", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\page.tsx": "78", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\movimentacoes\\page.tsx": "79", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\pagamentos-previstos\\page.tsx": "80", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\pagamentos-previstos\\types.ts": "81", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\page.tsx": "82", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\registro\\[id]\\page.tsx": "83", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\CardUpgradeOpt.tsx": "84", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\UserContracts.tsx": "85", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\UserData.tsx": "86", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\UserDataBank.tsx": "87", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\index.tsx": "88", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerAddress.tsx": "89", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerContracts.tsx": "90", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerData.tsx": "91", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerDataBank.tsx": "92", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerDocuments.tsx": "93", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\index.tsx": "94", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\page.tsx": "95", "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\types.ts": "96", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Button\\Button.tsx": "97", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\CoverForm\\index.tsx": "98", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Dropzone\\index.tsx": "99", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\FileView\\index.tsx": "100", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\FilterModal\\index.tsx": "101", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Header\\index.tsx": "102", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Input\\index.tsx": "103", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputSelect\\index.tsx": "104", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputSelectText\\index.tsx": "105", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputSelectText\\types.ts": "106", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputText\\index.tsx": "107", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputTextArea\\index.tsx": "108", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\InputSearch\\index.tsx": "109", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\List\\index.tsx": "110", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Map\\index.tsx": "111", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Modal\\index.tsx": "112", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Notifications\\index.tsx": "113", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\PageHeader\\index.tsx": "114", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Pagination\\index.tsx": "115", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\PaymentTable\\index.tsx": "116", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\PrivateRoute.tsx": "117", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Select\\index.tsx": "118", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\SelectCustom\\index.tsx": "119", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\SelectSearch\\index.tsx": "120", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Sidebar\\index.tsx": "121", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Skeleton\\intex.tsx": "122", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\StatusWithDescription\\index.tsx": "123", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Table\\components\\TableFormat.tsx": "124", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Table\\index.tsx": "125", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Table\\types.ts": "126", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\button.tsx": "127", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\calendar.tsx": "128", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\datePicker.tsx": "129", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\dropdown-menu.tsx": "130", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\popover.tsx": "131", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\select.tsx": "132", "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\sheet.tsx": "133", "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\AppRoutes.ts": "134", "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\Arrays.ts": "135", "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\editContractStatus.ts": "136", "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\filterStatusContract.ts": "137", "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\signIca.ts": "138", "C:\\projetos\\3\\ica-invest-contracts\\src\\core\\actions\\edit-new-contract.action.ts": "139", "C:\\projetos\\3\\ica-invest-contracts\\src\\core\\actions\\get-contract-detail.action.ts": "140", "C:\\projetos\\3\\ica-invest-contracts\\src\\core\\api.ts": "141", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\canDeleteContract.ts": "142", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\checkPrivateRoutes.ts": "143", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\crypto.ts": "144", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatDate.ts": "145", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatName.ts": "146", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatStatus.ts": "147", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatUserType.ts": "148", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\generateYearsArray.ts": "149", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\getDataFilter.ts": "150", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\getUserData.ts": "151", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\getZipCode.ts": "152", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\newFormatDate.ts": "153", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\pagination.ts": "154", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\RegionsEnum.ts": "155", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\returnError.ts": "156", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\sessionStorageSecure.ts": "157", "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\Unauthorized.ts": "158", "C:\\projetos\\3\\ica-invest-contracts\\src\\hooks\\navigation.ts": "159", "C:\\projetos\\3\\ica-invest-contracts\\src\\lib\\utils.ts": "160", "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\AuthModel.ts": "161", "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\contract.ts": "162", "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\moviments.ts": "163", "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\user.ts": "164", "C:\\projetos\\3\\ica-invest-contracts\\src\\provider\\AuthContext.ts": "165", "C:\\projetos\\3\\ica-invest-contracts\\src\\provider\\AuthProvider.tsx": "166", "C:\\projetos\\3\\ica-invest-contracts\\src\\provider\\ReactQueryClientProvider.tsx": "167", "C:\\projetos\\3\\ica-invest-contracts\\src\\services\\contracts.service.ts": "168", "C:\\projetos\\3\\ica-invest-contracts\\src\\styles\\chartTheme.ts": "169", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\calculateTotalValue.ts": "170", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\daysOfWeek.ts": "171", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\formatName.ts": "172", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\formatNumberValue.ts": "173", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\formatValue.ts": "174", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\getPurchaseData.ts": "175", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\isUnderage.ts": "176", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\isValidUf.ts": "177", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\masks.ts": "178", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\queries.ts": "179", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\createAdditive.ts": "180", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\createContract.ts": "181", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\editContract.ts": "182", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\editNewContract.ts": "183", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\schemasValidation.ts": "184", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\validate-documents.ts": "185", "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\validatePhoneNumber.ts": "186"}, {"size": 2437, "mtime": 1752600686082, "results": "187", "hashOfConfig": "188"}, {"size": 33886, "mtime": 1752672400683, "results": "189", "hashOfConfig": "188"}, {"size": 57167, "mtime": 1752672400684, "results": "190", "hashOfConfig": "188"}, {"size": 1534, "mtime": 1752600686075, "results": "191", "hashOfConfig": "188"}, {"size": 34687, "mtime": 1752672400673, "results": "192", "hashOfConfig": "188"}, {"size": 56887, "mtime": 1752672400674, "results": "193", "hashOfConfig": "188"}, {"size": 2889, "mtime": 1752676470503, "results": "194", "hashOfConfig": "188"}, {"size": 34373, "mtime": 1752672400677, "results": "195", "hashOfConfig": "188"}, {"size": 57260, "mtime": 1752672400678, "results": "196", "hashOfConfig": "188"}, {"size": 1543, "mtime": 1752232385227, "results": "197", "hashOfConfig": "188"}, {"size": 32679, "mtime": 1752676470505, "results": "198", "hashOfConfig": "188"}, {"size": 27293, "mtime": 1752676470507, "results": "199", "hashOfConfig": "188"}, {"size": 3407, "mtime": 1752600686084, "results": "200", "hashOfConfig": "188"}, {"size": 67630, "mtime": 1752694735693, "results": "201", "hashOfConfig": "188"}, {"size": 2447, "mtime": 1752514252502, "results": "202", "hashOfConfig": "188"}, {"size": 7210, "mtime": 1752232385235, "results": "203", "hashOfConfig": "188"}, {"size": 10886, "mtime": 1752232385235, "results": "204", "hashOfConfig": "188"}, {"size": 5688, "mtime": 1752232385236, "results": "205", "hashOfConfig": "188"}, {"size": 5784, "mtime": 1752232385237, "results": "206", "hashOfConfig": "188"}, {"size": 6263, "mtime": 1752232385237, "results": "207", "hashOfConfig": "188"}, {"size": 3436, "mtime": 1752672400688, "results": "208", "hashOfConfig": "188"}, {"size": 2521, "mtime": 1752232385241, "results": "209", "hashOfConfig": "188"}, {"size": 29575, "mtime": 1752232385239, "results": "210", "hashOfConfig": "188"}, {"size": 26465, "mtime": 1752232385240, "results": "211", "hashOfConfig": "188"}, {"size": 4745, "mtime": 1752232385240, "results": "212", "hashOfConfig": "188"}, {"size": 665, "mtime": 1752232385241, "results": "213", "hashOfConfig": "188"}, {"size": 17048, "mtime": 1752672400685, "results": "214", "hashOfConfig": "188"}, {"size": 19165, "mtime": 1752672400690, "results": "215", "hashOfConfig": "188"}, {"size": 11935, "mtime": 1752232385244, "results": "216", "hashOfConfig": "188"}, {"size": 179, "mtime": 1752232385245, "results": "217", "hashOfConfig": "188"}, {"size": 10383, "mtime": 1752232385246, "results": "218", "hashOfConfig": "188"}, {"size": 1764, "mtime": 1752232385247, "results": "219", "hashOfConfig": "188"}, {"size": 19164, "mtime": 1752232385249, "results": "220", "hashOfConfig": "188"}, {"size": 16612, "mtime": 1752232385250, "results": "221", "hashOfConfig": "188"}, {"size": 7950, "mtime": 1752232385250, "results": "222", "hashOfConfig": "188"}, {"size": 16529, "mtime": 1752232385251, "results": "223", "hashOfConfig": "188"}, {"size": 15304, "mtime": 1752232385252, "results": "224", "hashOfConfig": "188"}, {"size": 14890, "mtime": 1752232385253, "results": "225", "hashOfConfig": "188"}, {"size": 8624, "mtime": 1752232385253, "results": "226", "hashOfConfig": "188"}, {"size": 18396, "mtime": 1752232385255, "results": "227", "hashOfConfig": "188"}, {"size": 11790, "mtime": 1752232385255, "results": "228", "hashOfConfig": "188"}, {"size": 179, "mtime": 1752232385256, "results": "229", "hashOfConfig": "188"}, {"size": 2551, "mtime": 1752232385256, "results": "230", "hashOfConfig": "188"}, {"size": 6390, "mtime": 1752232385258, "results": "231", "hashOfConfig": "188"}, {"size": 6981, "mtime": 1752232385258, "results": "232", "hashOfConfig": "188"}, {"size": 14961, "mtime": 1752600686088, "results": "233", "hashOfConfig": "188"}, {"size": 1159, "mtime": 1752232385260, "results": "234", "hashOfConfig": "188"}, {"size": 13818, "mtime": 1752232385259, "results": "235", "hashOfConfig": "188"}, {"size": 1985, "mtime": 1752232385261, "results": "236", "hashOfConfig": "188"}, {"size": 14095, "mtime": 1752232385262, "results": "237", "hashOfConfig": "188"}, {"size": 1489, "mtime": 1752582402257, "results": "238", "hashOfConfig": "188"}, {"size": 5786, "mtime": 1752232385263, "results": "239", "hashOfConfig": "188"}, {"size": 2283, "mtime": 1752232385265, "results": "240", "hashOfConfig": "188"}, {"size": 2690, "mtime": 1752232385265, "results": "241", "hashOfConfig": "188"}, {"size": 2347, "mtime": 1752232385266, "results": "242", "hashOfConfig": "188"}, {"size": 2498, "mtime": 1752232385267, "results": "243", "hashOfConfig": "188"}, {"size": 16163, "mtime": 1752232385267, "results": "244", "hashOfConfig": "188"}, {"size": 2680, "mtime": 1752514252503, "results": "245", "hashOfConfig": "188"}, {"size": 7304, "mtime": 1752232385269, "results": "246", "hashOfConfig": "188"}, {"size": 13038, "mtime": 1752232385270, "results": "247", "hashOfConfig": "188"}, {"size": 5857, "mtime": 1752232385270, "results": "248", "hashOfConfig": "188"}, {"size": 9636, "mtime": 1752232385271, "results": "249", "hashOfConfig": "188"}, {"size": 30262, "mtime": 1752672400692, "results": "250", "hashOfConfig": "188"}, {"size": 24496, "mtime": 1752672400694, "results": "251", "hashOfConfig": "188"}, {"size": 3979, "mtime": 1752232385271, "results": "252", "hashOfConfig": "188"}, {"size": 4277, "mtime": 1752232385279, "results": "253", "hashOfConfig": "188"}, {"size": 32177, "mtime": 1752672400696, "results": "254", "hashOfConfig": "188"}, {"size": 24849, "mtime": 1752672400698, "results": "255", "hashOfConfig": "188"}, {"size": 456, "mtime": 1752232385278, "results": "256", "hashOfConfig": "188"}, {"size": 30124, "mtime": 1752600686093, "results": "257", "hashOfConfig": "188"}, {"size": 23850, "mtime": 1752672400695, "results": "258", "hashOfConfig": "188"}, {"size": 5874, "mtime": 1752600686095, "results": "259", "hashOfConfig": "188"}, {"size": 682, "mtime": 1752232385279, "results": "260", "hashOfConfig": "188"}, {"size": 2363, "mtime": 1752232385280, "results": "261", "hashOfConfig": "188"}, {"size": 17322, "mtime": 1752672400691, "results": "262", "hashOfConfig": "188"}, {"size": 5821, "mtime": 1752232385281, "results": "263", "hashOfConfig": "188"}, {"size": 4261, "mtime": 1752232385282, "results": "264", "hashOfConfig": "188"}, {"size": 14135, "mtime": 1752232385282, "results": "265", "hashOfConfig": "188"}, {"size": 10469, "mtime": 1752232385283, "results": "266", "hashOfConfig": "188"}, {"size": 13171, "mtime": 1752600686099, "results": "267", "hashOfConfig": "188"}, {"size": 183, "mtime": 1752232385284, "results": "268", "hashOfConfig": "188"}, {"size": 4884, "mtime": 1752232385285, "results": "269", "hashOfConfig": "188"}, {"size": 24683, "mtime": 1752232385286, "results": "270", "hashOfConfig": "188"}, {"size": 1158, "mtime": 1752672400699, "results": "271", "hashOfConfig": "188"}, {"size": 10257, "mtime": 1752600686100, "results": "272", "hashOfConfig": "188"}, {"size": 7598, "mtime": 1752232385288, "results": "273", "hashOfConfig": "188"}, {"size": 2843, "mtime": 1752232385289, "results": "274", "hashOfConfig": "188"}, {"size": 9259, "mtime": 1752681583729, "results": "275", "hashOfConfig": "188"}, {"size": 4509, "mtime": 1752232385291, "results": "276", "hashOfConfig": "188"}, {"size": 9238, "mtime": 1752232385291, "results": "277", "hashOfConfig": "188"}, {"size": 3275, "mtime": 1752232385292, "results": "278", "hashOfConfig": "188"}, {"size": 831, "mtime": 1752232385292, "results": "279", "hashOfConfig": "188"}, {"size": 1410, "mtime": 1752232385293, "results": "280", "hashOfConfig": "188"}, {"size": 6686, "mtime": 1752232385293, "results": "281", "hashOfConfig": "188"}, {"size": 17558, "mtime": 1752600686101, "results": "282", "hashOfConfig": "188"}, {"size": 2217, "mtime": 1752232385295, "results": "283", "hashOfConfig": "188"}, {"size": 601, "mtime": 1752232385308, "results": "284", "hashOfConfig": "188"}, {"size": 588, "mtime": 1752232385309, "results": "285", "hashOfConfig": "188"}, {"size": 5122, "mtime": 1752232385310, "results": "286", "hashOfConfig": "188"}, {"size": 733, "mtime": 1752232385311, "results": "287", "hashOfConfig": "188"}, {"size": 7919, "mtime": 1752672400701, "results": "288", "hashOfConfig": "188"}, {"size": 9376, "mtime": 1752672400702, "results": "289", "hashOfConfig": "188"}, {"size": 1344, "mtime": 1752232385313, "results": "290", "hashOfConfig": "188"}, {"size": 2014, "mtime": 1752232385316, "results": "291", "hashOfConfig": "188"}, {"size": 2006, "mtime": 1752232385318, "results": "292", "hashOfConfig": "188"}, {"size": 392, "mtime": 1752232385319, "results": "293", "hashOfConfig": "188"}, {"size": 2210, "mtime": 1752672400704, "results": "294", "hashOfConfig": "188"}, {"size": 1073, "mtime": 1752232385321, "results": "295", "hashOfConfig": "188"}, {"size": 1439, "mtime": 1752232385315, "results": "296", "hashOfConfig": "188"}, {"size": 2466, "mtime": 1752232385322, "results": "297", "hashOfConfig": "188"}, {"size": 2493, "mtime": 1752232385322, "results": "298", "hashOfConfig": "188"}, {"size": 930, "mtime": 1752232385323, "results": "299", "hashOfConfig": "188"}, {"size": 15778, "mtime": 1752232385323, "results": "300", "hashOfConfig": "188"}, {"size": 862, "mtime": 1752232385324, "results": "301", "hashOfConfig": "188"}, {"size": 4104, "mtime": 1752232385325, "results": "302", "hashOfConfig": "188"}, {"size": 8026, "mtime": 1752232385326, "results": "303", "hashOfConfig": "188"}, {"size": 3781, "mtime": 1752672400705, "results": "304", "hashOfConfig": "188"}, {"size": 3305, "mtime": 1752232385327, "results": "305", "hashOfConfig": "188"}, {"size": 1339, "mtime": 1752232385328, "results": "306", "hashOfConfig": "188"}, {"size": 4835, "mtime": 1752232385329, "results": "307", "hashOfConfig": "188"}, {"size": 5225, "mtime": 1752672400706, "results": "308", "hashOfConfig": "188"}, {"size": 1538, "mtime": 1752232385331, "results": "309", "hashOfConfig": "188"}, {"size": 813, "mtime": 1752232385332, "results": "310", "hashOfConfig": "188"}, {"size": 322, "mtime": 1752232385334, "results": "311", "hashOfConfig": "188"}, {"size": 3673, "mtime": 1752232385335, "results": "312", "hashOfConfig": "188"}, {"size": 580, "mtime": 1752232385335, "results": "313", "hashOfConfig": "188"}, {"size": 2195, "mtime": 1752232385336, "results": "314", "hashOfConfig": "188"}, {"size": 2983, "mtime": 1752232385337, "results": "315", "hashOfConfig": "188"}, {"size": 1418, "mtime": 1752232385337, "results": "316", "hashOfConfig": "188"}, {"size": 8535, "mtime": 1752232385338, "results": "317", "hashOfConfig": "188"}, {"size": 1389, "mtime": 1752232385338, "results": "318", "hashOfConfig": "188"}, {"size": 5904, "mtime": 1752232385339, "results": "319", "hashOfConfig": "188"}, {"size": 4446, "mtime": 1752232385339, "results": "320", "hashOfConfig": "188"}, {"size": 1074, "mtime": 1752232385340, "results": "321", "hashOfConfig": "188"}, {"size": 627, "mtime": 1752232385341, "results": "322", "hashOfConfig": "188"}, {"size": 267, "mtime": 1752232385341, "results": "323", "hashOfConfig": "188"}, {"size": 1659, "mtime": 1752232385342, "results": "324", "hashOfConfig": "188"}, {"size": 55, "mtime": 1752232385342, "results": "325", "hashOfConfig": "188"}, {"size": 1856, "mtime": 1752232385343, "results": "326", "hashOfConfig": "188"}, {"size": 1945, "mtime": 1752232385343, "results": "327", "hashOfConfig": "188"}, {"size": 2573, "mtime": 1752232385344, "results": "328", "hashOfConfig": "188"}, {"size": 1029, "mtime": 1752232385347, "results": "329", "hashOfConfig": "188"}, {"size": 204, "mtime": 1752232385347, "results": "330", "hashOfConfig": "188"}, {"size": 708, "mtime": 1752232385348, "results": "331", "hashOfConfig": "188"}, {"size": 362, "mtime": 1752232385348, "results": "332", "hashOfConfig": "188"}, {"size": 215, "mtime": 1752600686105, "results": "333", "hashOfConfig": "188"}, {"size": 7809, "mtime": 1752232385349, "results": "334", "hashOfConfig": "188"}, {"size": 344, "mtime": 1752232385350, "results": "335", "hashOfConfig": "188"}, {"size": 453, "mtime": 1752232385350, "results": "336", "hashOfConfig": "188"}, {"size": 1324, "mtime": 1752232385351, "results": "337", "hashOfConfig": "188"}, {"size": 940, "mtime": 1752232385351, "results": "338", "hashOfConfig": "188"}, {"size": 774, "mtime": 1752232385352, "results": "339", "hashOfConfig": "188"}, {"size": 691, "mtime": 1752232385352, "results": "340", "hashOfConfig": "188"}, {"size": 449, "mtime": 1752232385353, "results": "341", "hashOfConfig": "188"}, {"size": 189, "mtime": 1752232385346, "results": "342", "hashOfConfig": "188"}, {"size": 767, "mtime": 1752232385353, "results": "343", "hashOfConfig": "188"}, {"size": 3229, "mtime": 1752232385354, "results": "344", "hashOfConfig": "188"}, {"size": 261, "mtime": 1752232385346, "results": "345", "hashOfConfig": "188"}, {"size": 194, "mtime": 1752232385354, "results": "346", "hashOfConfig": "188"}, {"size": 172, "mtime": 1752232385355, "results": "347", "hashOfConfig": "188"}, {"size": 266, "mtime": 1752232385355, "results": "348", "hashOfConfig": "188"}, {"size": 2454, "mtime": 1752232385356, "results": "349", "hashOfConfig": "188"}, {"size": 594, "mtime": 1752232385356, "results": "350", "hashOfConfig": "188"}, {"size": 271, "mtime": 1752232385357, "results": "351", "hashOfConfig": "188"}, {"size": 447, "mtime": 1752232385358, "results": "352", "hashOfConfig": "188"}, {"size": 3647, "mtime": 1752232385358, "results": "353", "hashOfConfig": "188"}, {"size": 672, "mtime": 1752232385359, "results": "354", "hashOfConfig": "188"}, {"size": 1131, "mtime": 1752232385360, "results": "355", "hashOfConfig": "188"}, {"size": 172, "mtime": 1752232385360, "results": "356", "hashOfConfig": "188"}, {"size": 771, "mtime": 1752672400707, "results": "357", "hashOfConfig": "188"}, {"size": 252, "mtime": 1752232385362, "results": "358", "hashOfConfig": "188"}, {"size": 243, "mtime": 1752672400707, "results": "359", "hashOfConfig": "188"}, {"size": 126, "mtime": 1752232385363, "results": "360", "hashOfConfig": "188"}, {"size": 407, "mtime": 1752232385363, "results": "361", "hashOfConfig": "188"}, {"size": 657, "mtime": 1752232385364, "results": "362", "hashOfConfig": "188"}, {"size": 241, "mtime": 1752232385364, "results": "363", "hashOfConfig": "188"}, {"size": 325, "mtime": 1752232385365, "results": "364", "hashOfConfig": "188"}, {"size": 2118, "mtime": 1752232385365, "results": "365", "hashOfConfig": "188"}, {"size": 289, "mtime": 1752232385366, "results": "366", "hashOfConfig": "188"}, {"size": 1147, "mtime": 1752232385367, "results": "367", "hashOfConfig": "188"}, {"size": 12331, "mtime": 1752672400708, "results": "368", "hashOfConfig": "188"}, {"size": 6355, "mtime": 1752232385368, "results": "369", "hashOfConfig": "188"}, {"size": 8002, "mtime": 1752672400710, "results": "370", "hashOfConfig": "188"}, {"size": 15516, "mtime": 1752672400711, "results": "371", "hashOfConfig": "188"}, {"size": 1654, "mtime": 1752232385369, "results": "372", "hashOfConfig": "188"}, {"size": 244, "mtime": 1752232385370, "results": "373", "hashOfConfig": "188"}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wauv9c", {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\brokerCreate\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\brokerCreate\\registerForms\\BrokerRegisterPf.tsx", ["932"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\brokerCreate\\registerForms\\BrokerRegisterPj.tsx", ["933"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAdmin\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAdmin\\registerForms\\AdminRegisterPF.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAdmin\\registerForms\\AdminRegisterPJ.tsx", ["934"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAssessor\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAssessor\\registerForms\\AssessorRegisterPf.tsx", ["935"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateAssessor\\registerForms\\AssessorRegisterPj.tsx", ["936"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateInvestor\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateInvestor\\registerForms\\BusinessRegister.tsx", ["937", "938", "939", "940"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\compose\\CreateInvestor\\registerForms\\PhysicalRegister.tsx", ["941", "942", "943"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\page.tsx", ["944"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\alterar\\page.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\AddPayment.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\AditiveData.tsx", ["945"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\AditiveModal.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\ContractData.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\FilterModal.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\ModalContract.tsx", ["946"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\compose\\RenewContract.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\compose\\BusinessContract.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\compose\\PhysicalContract.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\page.tsx", ["947", "948"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\page.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\_screen\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamento\\[id]\\page.tsx", ["949"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamentos\\page.tsx", ["950", "951"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamentos\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\page.tsx", ["952"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\AdminData.tsx", ["953", "954", "955"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\BrokerData.tsx", ["956", "957", "958"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\InvestorData.tsx", ["959"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\ModalRegister.tsx", ["960", "961"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\Retention\\compose\\ModalMonitoramento.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\compose\\Retention\\index.tsx", ["962"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\index.tsx", ["963"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamento\\[id]\\page.tsx", ["964"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamentos\\page.tsx", ["965"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamentos\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\page.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\page.tsx", ["966"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\[id]\\page.tsx", ["967"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\informe-rendimentos\\page.tsx", ["968"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\informe-rendimentos\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\informe-rendimentos\\[id]\\[year]\\page.tsx", ["969"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\investidores\\data.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\investidores\\page.tsx", ["970"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\AddGoals.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\AddPerspective.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\AdminDashboard.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\GoalDashboard.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\compose\\ProgresTable.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\page.tsx", ["971"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\AddPayment.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\AditiveData.tsx", ["972"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\AditiveModal.tsx", ["973", "974"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\ContractData.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\ModalContract.tsx", ["975"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\registerForms\\BusinessRegister.tsx", ["976", "977", "978"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\registerForms\\PhysicalRegister.tsx", ["979", "980"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\compose\\RenewContract.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\page.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\_components\\BusinessRegister.tsx", [], ["981", "982", "983", "984", "985"], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\_components\\PhysicalRegister.tsx", [], ["986", "987", "988", "989", "990"], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\_components\\ReusableSelect.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\compose\\BusinessEditing.tsx", ["991", "992", "993", "994"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\compose\\PhysicalEditing.tsx", ["995", "996", "997", "998", "999", "1000"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\page.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\page.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\registro-manual\\page.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\_screen\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\compose\\ModalMonitoramento.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\compose\\ModalTypeAction.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\monitoramento\\page.tsx", ["1001"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\movimentacoes\\page.tsx", ["1002"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\pagamentos-previstos\\page.tsx", ["1003"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\pagamentos-previstos\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\page.tsx", [], ["1004"], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\registro\\[id]\\page.tsx", ["1005", "1006"], ["1007"], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\CardUpgradeOpt.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\UserContracts.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\UserData.tsx", ["1008"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\compose\\UserDataBank.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\Modal\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerAddress.tsx", ["1009"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerContracts.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerData.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerDataBank.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\compose\\BrokerDocuments.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\component\\ModalBroker\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\page.tsx", ["1010", "1011"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Button\\Button.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\CoverForm\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Dropzone\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\FileView\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\FilterModal\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Header\\index.tsx", [], ["1012"], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Input\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputSelect\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputSelectText\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputSelectText\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputText\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Inputs\\InputTextArea\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\InputSearch\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\List\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Map\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Modal\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Notifications\\index.tsx", ["1013"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\PageHeader\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Pagination\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\PaymentTable\\index.tsx", ["1014"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\PrivateRoute.tsx", ["1015"], ["1016", "1017"], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Select\\index.tsx", ["1018", "1019"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\SelectCustom\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\SelectSearch\\index.tsx", ["1020"], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Sidebar\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Skeleton\\intex.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\StatusWithDescription\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Table\\components\\TableFormat.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Table\\index.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\Table\\types.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\button.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\calendar.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\datePicker.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\popover.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\select.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\components\\ui\\sheet.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\AppRoutes.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\Arrays.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\editContractStatus.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\filterStatusContract.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\constants\\signIca.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\core\\actions\\edit-new-contract.action.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\core\\actions\\get-contract-detail.action.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\core\\api.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\canDeleteContract.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\checkPrivateRoutes.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\crypto.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatDate.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatName.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatStatus.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\formatUserType.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\generateYearsArray.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\getDataFilter.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\getUserData.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\getZipCode.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\newFormatDate.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\pagination.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\RegionsEnum.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\returnError.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\sessionStorageSecure.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\functions\\Unauthorized.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\hooks\\navigation.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\lib\\utils.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\AuthModel.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\contract.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\moviments.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\models\\user.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\provider\\AuthContext.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\provider\\AuthProvider.tsx", [], ["1021"], "C:\\projetos\\3\\ica-invest-contracts\\src\\provider\\ReactQueryClientProvider.tsx", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\services\\contracts.service.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\styles\\chartTheme.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\calculateTotalValue.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\daysOfWeek.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\formatName.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\formatNumberValue.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\formatValue.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\getPurchaseData.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\isUnderage.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\isValidUf.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\masks.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\queries.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\createAdditive.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\createContract.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\editContract.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\editNewContract.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\schemas\\schemasValidation.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\validate-documents.ts", [], [], "C:\\projetos\\3\\ica-invest-contracts\\src\\utils\\validatePhoneNumber.ts", [], [], {"ruleId": "1022", "severity": 1, "message": "1023", "line": 267, "column": 9, "nodeType": "1024", "endLine": 267, "endColumn": 90, "suggestions": "1025"}, {"ruleId": "1022", "severity": 1, "message": "1026", "line": 150, "column": 8, "nodeType": "1024", "endLine": 150, "endColumn": 53, "suggestions": "1027"}, {"ruleId": "1022", "severity": 1, "message": "1026", "line": 151, "column": 8, "nodeType": "1024", "endLine": 151, "endColumn": 53, "suggestions": "1028"}, {"ruleId": "1022", "severity": 1, "message": "1029", "line": 285, "column": 9, "nodeType": "1024", "endLine": 285, "endColumn": 90, "suggestions": "1030"}, {"ruleId": "1022", "severity": 1, "message": "1026", "line": 152, "column": 8, "nodeType": "1024", "endLine": 152, "endColumn": 53, "suggestions": "1031"}, {"ruleId": "1022", "severity": 1, "message": "1032", "line": 151, "column": 6, "nodeType": "1024", "endLine": 151, "endColumn": 31, "suggestions": "1033"}, {"ruleId": "1022", "severity": 1, "message": "1034", "line": 151, "column": 7, "nodeType": "1035", "endLine": 151, "endColumn": 24}, {"ruleId": "1022", "severity": 1, "message": "1036", "line": 175, "column": 6, "nodeType": "1024", "endLine": 175, "endColumn": 24, "suggestions": "1037"}, {"ruleId": "1022", "severity": 1, "message": "1038", "line": 325, "column": 5, "nodeType": "1024", "endLine": 335, "endColumn": 6, "suggestions": "1039"}, {"ruleId": "1022", "severity": 1, "message": "1032", "line": 132, "column": 6, "nodeType": "1024", "endLine": 132, "endColumn": 31, "suggestions": "1040"}, {"ruleId": "1022", "severity": 1, "message": "1034", "line": 132, "column": 7, "nodeType": "1035", "endLine": 132, "endColumn": 24}, {"ruleId": "1022", "severity": 1, "message": "1041", "line": 272, "column": 5, "nodeType": "1024", "endLine": 282, "endColumn": 6, "suggestions": "1042"}, {"ruleId": "1022", "severity": 1, "message": "1043", "line": 36, "column": 6, "nodeType": "1024", "endLine": 36, "endColumn": 8, "suggestions": "1044"}, {"ruleId": "1022", "severity": 1, "message": "1045", "line": 29, "column": 6, "nodeType": "1024", "endLine": 29, "endColumn": 32, "suggestions": "1046"}, {"ruleId": "1022", "severity": 1, "message": "1047", "line": 81, "column": 6, "nodeType": "1024", "endLine": 81, "endColumn": 15, "suggestions": "1048"}, {"ruleId": "1022", "severity": 1, "message": "1049", "line": 43, "column": 6, "nodeType": "1024", "endLine": 43, "endColumn": 8, "suggestions": "1050"}, {"ruleId": "1022", "severity": 1, "message": "1051", "line": 65, "column": 6, "nodeType": "1024", "endLine": 65, "endColumn": 8, "suggestions": "1052"}, {"ruleId": "1022", "severity": 1, "message": "1053", "line": 91, "column": 6, "nodeType": "1024", "endLine": 91, "endColumn": 8, "suggestions": "1054"}, {"ruleId": "1022", "severity": 1, "message": "1055", "line": 36, "column": 6, "nodeType": "1024", "endLine": 36, "endColumn": 8, "suggestions": "1056"}, {"ruleId": "1022", "severity": 1, "message": "1057", "line": 72, "column": 6, "nodeType": "1024", "endLine": 72, "endColumn": 25, "suggestions": "1058"}, {"ruleId": "1022", "severity": 1, "message": "1059", "line": 167, "column": 6, "nodeType": "1024", "endLine": 167, "endColumn": 8, "suggestions": "1060"}, {"ruleId": "1022", "severity": 1, "message": "1061", "line": 169, "column": 6, "nodeType": "1024", "endLine": 169, "endColumn": 23, "suggestions": "1062"}, {"ruleId": "1022", "severity": 1, "message": "1063", "line": 173, "column": 6, "nodeType": "1024", "endLine": 173, "endColumn": 21, "suggestions": "1064"}, {"ruleId": "1022", "severity": 1, "message": "1065", "line": 237, "column": 6, "nodeType": "1024", "endLine": 237, "endColumn": 24, "suggestions": "1066"}, {"ruleId": "1022", "severity": 1, "message": "1061", "line": 162, "column": 6, "nodeType": "1024", "endLine": 162, "endColumn": 23, "suggestions": "1067"}, {"ruleId": "1022", "severity": 1, "message": "1063", "line": 166, "column": 6, "nodeType": "1024", "endLine": 166, "endColumn": 21, "suggestions": "1068"}, {"ruleId": "1022", "severity": 1, "message": "1069", "line": 230, "column": 6, "nodeType": "1024", "endLine": 230, "endColumn": 8, "suggestions": "1070"}, {"ruleId": "1022", "severity": 1, "message": "1071", "line": 31, "column": 6, "nodeType": "1024", "endLine": 31, "endColumn": 8, "suggestions": "1072"}, {"ruleId": "1022", "severity": 1, "message": "1036", "line": 54, "column": 6, "nodeType": "1024", "endLine": 54, "endColumn": 29, "suggestions": "1073"}, {"ruleId": "1022", "severity": 1, "message": "1036", "line": 61, "column": 6, "nodeType": "1024", "endLine": 61, "endColumn": 8, "suggestions": "1074"}, {"ruleId": "1022", "severity": 1, "message": "1075", "line": 129, "column": 6, "nodeType": "1024", "endLine": 129, "endColumn": 25, "suggestions": "1076"}, {"ruleId": "1022", "severity": 1, "message": "1077", "line": 146, "column": 6, "nodeType": "1024", "endLine": 146, "endColumn": 8, "suggestions": "1078"}, {"ruleId": "1022", "severity": 1, "message": "1053", "line": 91, "column": 6, "nodeType": "1024", "endLine": 91, "endColumn": 8, "suggestions": "1079"}, {"ruleId": "1022", "severity": 1, "message": "1057", "line": 62, "column": 6, "nodeType": "1024", "endLine": 62, "endColumn": 25, "suggestions": "1080"}, {"ruleId": "1022", "severity": 1, "message": "1081", "line": 101, "column": 6, "nodeType": "1024", "endLine": 101, "endColumn": 12, "suggestions": "1082"}, {"ruleId": "1022", "severity": 1, "message": "1083", "line": 106, "column": 6, "nodeType": "1024", "endLine": 106, "endColumn": 12, "suggestions": "1084"}, {"ruleId": "1022", "severity": 1, "message": "1085", "line": 78, "column": 6, "nodeType": "1024", "endLine": 78, "endColumn": 19, "suggestions": "1086"}, {"ruleId": "1022", "severity": 1, "message": "1085", "line": 74, "column": 6, "nodeType": "1024", "endLine": 74, "endColumn": 8, "suggestions": "1087"}, {"ruleId": "1022", "severity": 1, "message": "1088", "line": 165, "column": 6, "nodeType": "1024", "endLine": 165, "endColumn": 12, "suggestions": "1089"}, {"ruleId": "1022", "severity": 1, "message": "1090", "line": 93, "column": 6, "nodeType": "1024", "endLine": 93, "endColumn": 15, "suggestions": "1091"}, {"ruleId": "1022", "severity": 1, "message": "1045", "line": 29, "column": 6, "nodeType": "1024", "endLine": 29, "endColumn": 32, "suggestions": "1092"}, {"ruleId": "1022", "severity": 1, "message": "1093", "line": 172, "column": 6, "nodeType": "1024", "endLine": 172, "endColumn": 8, "suggestions": "1094"}, {"ruleId": "1022", "severity": 1, "message": "1036", "line": 180, "column": 6, "nodeType": "1024", "endLine": 180, "endColumn": 12, "suggestions": "1095"}, {"ruleId": "1022", "severity": 1, "message": "1047", "line": 80, "column": 6, "nodeType": "1024", "endLine": 80, "endColumn": 15, "suggestions": "1096"}, {"ruleId": "1022", "severity": 1, "message": "1036", "line": 195, "column": 6, "nodeType": "1024", "endLine": 195, "endColumn": 24, "suggestions": "1097"}, {"ruleId": "1022", "severity": 1, "message": "1032", "line": 299, "column": 7, "nodeType": "1024", "endLine": 299, "endColumn": 38, "suggestions": "1098"}, {"ruleId": "1022", "severity": 1, "message": "1034", "line": 299, "column": 8, "nodeType": "1035", "endLine": 299, "endColumn": 25}, {"ruleId": "1022", "severity": 1, "message": "1032", "line": 229, "column": 6, "nodeType": "1024", "endLine": 229, "endColumn": 31, "suggestions": "1099"}, {"ruleId": "1022", "severity": 1, "message": "1034", "line": 229, "column": 7, "nodeType": "1035", "endLine": 229, "endColumn": 24}, {"ruleId": "1022", "severity": 1, "message": "1100", "line": 295, "column": 6, "nodeType": "1024", "endLine": 295, "endColumn": 31, "suggestions": "1101", "suppressions": "1102"}, {"ruleId": "1022", "severity": 1, "message": "1103", "line": 295, "column": 7, "nodeType": "1035", "endLine": 295, "endColumn": 20, "suppressions": "1104"}, {"ruleId": "1022", "severity": 1, "message": "1100", "line": 310, "column": 6, "nodeType": "1024", "endLine": 310, "endColumn": 53, "suggestions": "1105", "suppressions": "1106"}, {"ruleId": "1022", "severity": 1, "message": "1103", "line": 310, "column": 7, "nodeType": "1035", "endLine": 310, "endColumn": 24, "suppressions": "1107"}, {"ruleId": "1022", "severity": 1, "message": "1103", "line": 310, "column": 26, "nodeType": "1035", "endLine": 310, "endColumn": 42, "suppressions": "1108"}, {"ruleId": "1022", "severity": 1, "message": "1100", "line": 224, "column": 6, "nodeType": "1024", "endLine": 224, "endColumn": 31, "suggestions": "1109", "suppressions": "1110"}, {"ruleId": "1022", "severity": 1, "message": "1103", "line": 224, "column": 7, "nodeType": "1035", "endLine": 224, "endColumn": 20, "suppressions": "1111"}, {"ruleId": "1022", "severity": 1, "message": "1100", "line": 239, "column": 6, "nodeType": "1024", "endLine": 239, "endColumn": 53, "suggestions": "1112", "suppressions": "1113"}, {"ruleId": "1022", "severity": 1, "message": "1103", "line": 239, "column": 7, "nodeType": "1035", "endLine": 239, "endColumn": 24, "suppressions": "1114"}, {"ruleId": "1022", "severity": 1, "message": "1103", "line": 239, "column": 26, "nodeType": "1035", "endLine": 239, "endColumn": 42, "suppressions": "1115"}, {"ruleId": "1022", "severity": 1, "message": "1036", "line": 218, "column": 6, "nodeType": "1024", "endLine": 218, "endColumn": 24, "suggestions": "1116"}, {"ruleId": "1022", "severity": 1, "message": "1032", "line": 229, "column": 6, "nodeType": "1024", "endLine": 229, "endColumn": 31, "suggestions": "1117"}, {"ruleId": "1022", "severity": 1, "message": "1034", "line": 229, "column": 7, "nodeType": "1035", "endLine": 229, "endColumn": 24}, {"ruleId": "1022", "severity": 1, "message": "1118", "line": 333, "column": 6, "nodeType": "1024", "endLine": 333, "endColumn": 88, "suggestions": "1119"}, {"ruleId": "1022", "severity": 1, "message": "1120", "line": 147, "column": 6, "nodeType": "1024", "endLine": 147, "endColumn": 20, "suggestions": "1121"}, {"ruleId": "1022", "severity": 1, "message": "1036", "line": 151, "column": 6, "nodeType": "1024", "endLine": 151, "endColumn": 24, "suggestions": "1122"}, {"ruleId": "1022", "severity": 1, "message": "1123", "line": 155, "column": 6, "nodeType": "1024", "endLine": 155, "endColumn": 8, "suggestions": "1124"}, {"ruleId": "1022", "severity": 1, "message": "1125", "line": 238, "column": 6, "nodeType": "1024", "endLine": 238, "endColumn": 15, "suggestions": "1126"}, {"ruleId": "1022", "severity": 1, "message": "1127", "line": 275, "column": 6, "nodeType": "1024", "endLine": 275, "endColumn": 15, "suggestions": "1128"}, {"ruleId": "1022", "severity": 1, "message": "1129", "line": 281, "column": 6, "nodeType": "1024", "endLine": 281, "endColumn": 8, "suggestions": "1130"}, {"ruleId": "1022", "severity": 1, "message": "1075", "line": 129, "column": 6, "nodeType": "1024", "endLine": 129, "endColumn": 25, "suggestions": "1131"}, {"ruleId": "1022", "severity": 1, "message": "1132", "line": 87, "column": 6, "nodeType": "1024", "endLine": 87, "endColumn": 12, "suggestions": "1133"}, {"ruleId": "1022", "severity": 1, "message": "1057", "line": 103, "column": 6, "nodeType": "1024", "endLine": 103, "endColumn": 58, "suggestions": "1134"}, {"ruleId": "1135", "severity": 1, "message": "1136", "line": 77, "column": 9, "nodeType": "1137", "endLine": 81, "endColumn": 11, "suppressions": "1138"}, {"ruleId": "1022", "severity": 1, "message": "1125", "line": 121, "column": 6, "nodeType": "1024", "endLine": 121, "endColumn": 15, "suggestions": "1139"}, {"ruleId": "1022", "severity": 1, "message": "1140", "line": 125, "column": 6, "nodeType": "1024", "endLine": 125, "endColumn": 8, "suggestions": "1141"}, {"ruleId": "1135", "severity": 1, "message": "1136", "line": 180, "column": 9, "nodeType": "1137", "endLine": 184, "endColumn": 11, "suppressions": "1142"}, {"ruleId": "1022", "severity": 1, "message": "1125", "line": 15, "column": 6, "nodeType": "1024", "endLine": 15, "endColumn": 25, "suggestions": "1143"}, {"ruleId": "1022", "severity": 1, "message": "1125", "line": 18, "column": 6, "nodeType": "1024", "endLine": 18, "endColumn": 15, "suggestions": "1144"}, {"ruleId": "1022", "severity": 1, "message": "1043", "line": 214, "column": 6, "nodeType": "1024", "endLine": 214, "endColumn": 8, "suggestions": "1145"}, {"ruleId": "1022", "severity": 1, "message": "1146", "line": 226, "column": 6, "nodeType": "1024", "endLine": 226, "endColumn": 12, "suggestions": "1147"}, {"ruleId": "1135", "severity": 1, "message": "1136", "line": 112, "column": 19, "nodeType": "1137", "endLine": 116, "endColumn": 21, "suppressions": "1148"}, {"ruleId": "1022", "severity": 1, "message": "1149", "line": 230, "column": 6, "nodeType": "1024", "endLine": 230, "endColumn": 34, "suggestions": "1150"}, {"ruleId": "1022", "severity": 1, "message": "1151", "line": 106, "column": 6, "nodeType": "1024", "endLine": 106, "endColumn": 14, "suggestions": "1152"}, {"ruleId": "1022", "severity": 1, "message": "1153", "line": 75, "column": 6, "nodeType": "1024", "endLine": 75, "endColumn": 13, "suggestions": "1154"}, {"ruleId": "1022", "severity": 1, "message": "1155", "line": 51, "column": 6, "nodeType": "1024", "endLine": 51, "endColumn": 12, "suggestions": "1156", "suppressions": "1157"}, {"ruleId": "1022", "severity": 1, "message": "1155", "line": 64, "column": 6, "nodeType": "1024", "endLine": 64, "endColumn": 8, "suggestions": "1158", "suppressions": "1159"}, {"ruleId": "1022", "severity": 1, "message": "1160", "line": 37, "column": 6, "nodeType": "1024", "endLine": 37, "endColumn": 8, "suggestions": "1161"}, {"ruleId": "1022", "severity": 1, "message": "1162", "line": 41, "column": 6, "nodeType": "1024", "endLine": 41, "endColumn": 16, "suggestions": "1163"}, {"ruleId": "1022", "severity": 1, "message": "1164", "line": 39, "column": 6, "nodeType": "1024", "endLine": 39, "endColumn": 18, "suggestions": "1165"}, {"ruleId": "1022", "severity": 1, "message": "1166", "line": 63, "column": 9, "nodeType": "1167", "endLine": 101, "endColumn": 4, "suppressions": "1168"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'adminId'. Either include it or remove the dependency array.", "ArrayExpression", ["1169"], "React Hook useMemo has an unnecessary dependency: 'typeCreate'. Either exclude it or remove the dependency array.", ["1170"], ["1171"], "React Hook useCallback has a missing dependency: 'brokerId'. Either include it or remove the dependency array.", ["1172"], ["1173"], "React Hook useMemo has a missing dependency: 'watch'. Either include it or remove the dependency array.", ["1174"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.", ["1175"], "React Hook useCallback has a missing dependency: 'endDate'. Either include it or remove the dependency array.", ["1176"], ["1177"], "React Hook useCallback has missing dependencies: 'endDate' and 'reset'. Either include them or remove the dependency array.", ["1178"], "React Hook useEffect has a missing dependency: 'userProfile.name'. Either include it or remove the dependency array.", ["1179"], "React Hook useEffect has a missing dependency: 'onUploadFile'. Either include it or remove the dependency array.", ["1180"], "React Hook useEffect has a missing dependency: 'getAditives'. Either include it or remove the dependency array.", ["1181"], "React Hook useEffect has missing dependencies: 'navigation' and 'userProfile?.name'. Either include them or remove the dependency array.", ["1182"], "React Hook useEffect has a missing dependency: 'getContractData'. Either include it or remove the dependency array.", ["1183"], "React Hook useEffect has a missing dependency: 'getPaymentData'. Either include it or remove the dependency array.", ["1184"], "React Hook useEffect has missing dependencies: 'router' and 'userProfile.name'. Either include them or remove the dependency array.", ["1185"], "React Hook useEffect has a missing dependency: 'getPayments'. Either include it or remove the dependency array.", ["1186"], "React Hook useEffect has missing dependencies: 'getChartData', 'getQuotes', and 'isBroker'. Either include them or remove the dependency array.", ["1187"], "React Hook useEffect has a missing dependency: 'getChartContractsData'. Either include it or remove the dependency array.", ["1188"], "React Hook useEffect has a missing dependency: 'getChartResultsData'. Either include it or remove the dependency array.", ["1189"], "React Hook useEffect has missing dependencies: 'getAdminData' and 'loading'. Either include them or remove the dependency array.", ["1190"], ["1191"], ["1192"], "React Hook useEffect has a missing dependency: 'getAdvisorData'. Either include it or remove the dependency array.", ["1193"], "React Hook useEffect has a missing dependency: 'getInvestor'. Either include it or remove the dependency array.", ["1194"], ["1195"], ["1196"], "React Hook useEffect has a missing dependency: 'getContracts'. Either include it or remove the dependency array.", ["1197"], "React Hook useEffect has a missing dependency: 'getChartData'. Either include it or remove the dependency array.", ["1198"], ["1199"], ["1200"], "React Hook useEffect has missing dependencies: 'getAdvisors', 'getBrokers', and 'typeRanking'. Either include them or remove the dependency array.", ["1201"], "React Hook useEffect has missing dependencies: 'getAdvisor', 'getBroker', and 'typeRanking'. Either include them or remove the dependency array.", ["1202"], "React Hook useEffect has a missing dependency: 'getIncomeReport'. Either include it or remove the dependency array.", ["1203"], ["1204"], "React Hook useEffect has missing dependencies: 'getAcessorsInvestors', 'getInvestorsAdviser', 'getInvestorsAdvisorSelected', and 'userProfile.name'. Either include them or remove the dependency array.", ["1205"], "React Hook useEffect has a missing dependency: 'handleSelectTypeGoals'. Either include it or remove the dependency array.", ["1206"], ["1207"], "React Hook useEffect has missing dependencies: 'contract.documentoInvestidor.length' and 'setValue'. Either include them or remove the dependency array.", ["1208"], ["1209"], ["1210"], ["1211"], ["1212"], ["1213"], "React Hook useEffect has a missing dependency: 'watch'. Either include it or remove the dependency array.", ["1214"], ["1215"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["1216"], ["1217"], ["1218"], ["1219"], ["1220"], ["1221"], ["1222"], ["1223"], ["1224"], ["1225"], ["1226"], ["1227"], ["1228"], ["1229"], "React Hook useCallback has missing dependencies: 'contractMutation' and 'endDate'. Either include them or remove the dependency array.", ["1230"], "React Hook useEffect has missing dependencies: 'durationInMonths' and 'setValue'. Either include them or remove the dependency array.", ["1231"], ["1232"], "React Hook useEffect has a missing dependency: 'getReasons'. Either include it or remove the dependency array.", ["1233"], "React Hook useEffect has a missing dependency: 'getCep'. Either include it or remove the dependency array.", ["1234"], "React Hook useEffect has a missing dependency: 'setError'. Either include it or remove the dependency array.", ["1235"], "React Hook useEffect has missing dependencies: 'getBrokers' and 'userProfile.name'. Either include them or remove the dependency array.", ["1236"], ["1237"], "React Hook useEffect has a missing dependency: 'getMoviments'. Either include it or remove the dependency array.", ["1238"], ["1239"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1240"], ["1241"], "React Hook useEffect has a missing dependency: 'validateToken'. Either include it or remove the dependency array.", ["1242"], ["1243"], ["1244"], ["1245"], ["1246"], "React Hook useEffect has missing dependencies: 'getAcessorsInvestors', 'getBrokers', and 'getInvestors'. Either include them or remove the dependency array.", ["1247"], ["1248"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["1249"], "React Hook useEffect has a missing dependency: 'getSchedulePayments'. Either include it or remove the dependency array.", ["1250"], "React Hook useEffect has a missing dependency: 'queryClient'. Either include it or remove the dependency array.", ["1251"], "React Hook useEffect has a missing dependency: 'syncAuthFromSession'. Either include it or remove the dependency array.", ["1252"], ["1253"], ["1254"], ["1255"], "React Hook useEffect has missing dependencies: 'options' and 'selected'. Either include them or remove the dependency array. If 'setOptSelected' needs the current value of 'options', you can also switch to useReducer instead of useState and read 'options' in the reducer.", ["1256"], "React Hook useEffect has a missing dependency: 'options'. Either include it or remove the dependency array. If 'setOptSelected' needs the current value of 'options', you can also switch to useReducer instead of useState and read 'options' in the reducer.", ["1257"], "React Hook useEffect has a missing dependency: 'items'. Either include it or remove the dependency array. If 'setItemsFilter' needs the current value of 'items', you can also switch to useReducer instead of useState and read 'items' in the reducer.", ["1258"], "The 'handleSignInUser' function makes the dependencies of useMemo Hook (at line 113) change on every render. Move it inside the useMemo callback. Alternatively, wrap the definition of 'handleSignInUser' in its own useCallback() Hook.", "VariableDeclarator", ["1259"], {"desc": "1260", "fix": "1261"}, {"desc": "1262", "fix": "1263"}, {"desc": "1262", "fix": "1264"}, {"desc": "1265", "fix": "1266"}, {"desc": "1262", "fix": "1267"}, {"desc": "1268", "fix": "1269"}, {"desc": "1270", "fix": "1271"}, {"desc": "1272", "fix": "1273"}, {"desc": "1268", "fix": "1274"}, {"desc": "1275", "fix": "1276"}, {"desc": "1277", "fix": "1278"}, {"desc": "1279", "fix": "1280"}, {"desc": "1281", "fix": "1282"}, {"desc": "1283", "fix": "1284"}, {"desc": "1285", "fix": "1286"}, {"desc": "1287", "fix": "1288"}, {"desc": "1289", "fix": "1290"}, {"desc": "1291", "fix": "1292"}, {"desc": "1293", "fix": "1294"}, {"desc": "1295", "fix": "1296"}, {"desc": "1297", "fix": "1298"}, {"desc": "1299", "fix": "1300"}, {"desc": "1295", "fix": "1301"}, {"desc": "1297", "fix": "1302"}, {"desc": "1303", "fix": "1304"}, {"desc": "1305", "fix": "1306"}, {"desc": "1307", "fix": "1308"}, {"desc": "1309", "fix": "1310"}, {"desc": "1311", "fix": "1312"}, {"desc": "1313", "fix": "1314"}, {"desc": "1287", "fix": "1315"}, {"desc": "1291", "fix": "1316"}, {"desc": "1317", "fix": "1318"}, {"desc": "1319", "fix": "1320"}, {"desc": "1321", "fix": "1322"}, {"desc": "1323", "fix": "1324"}, {"desc": "1325", "fix": "1326"}, {"desc": "1327", "fix": "1328"}, {"desc": "1279", "fix": "1329"}, {"desc": "1330", "fix": "1331"}, {"desc": "1332", "fix": "1333"}, {"desc": "1281", "fix": "1334"}, {"desc": "1270", "fix": "1335"}, {"desc": "1336", "fix": "1337"}, {"desc": "1268", "fix": "1338"}, {"desc": "1339", "fix": "1340"}, {"kind": "1341", "justification": "1342"}, {"kind": "1341", "justification": "1342"}, {"desc": "1339", "fix": "1343"}, {"kind": "1341", "justification": "1342"}, {"kind": "1341", "justification": "1342"}, {"kind": "1341", "justification": "1342"}, {"desc": "1339", "fix": "1344"}, {"kind": "1341", "justification": "1342"}, {"kind": "1341", "justification": "1342"}, {"desc": "1339", "fix": "1345"}, {"kind": "1341", "justification": "1342"}, {"kind": "1341", "justification": "1342"}, {"kind": "1341", "justification": "1342"}, {"desc": "1270", "fix": "1346"}, {"desc": "1268", "fix": "1347"}, {"desc": "1348", "fix": "1349"}, {"desc": "1350", "fix": "1351"}, {"desc": "1270", "fix": "1352"}, {"desc": "1353", "fix": "1354"}, {"desc": "1355", "fix": "1356"}, {"desc": "1357", "fix": "1358"}, {"desc": "1359", "fix": "1360"}, {"desc": "1311", "fix": "1361"}, {"desc": "1362", "fix": "1363"}, {"desc": "1364", "fix": "1365"}, {"kind": "1341", "justification": "1342"}, {"desc": "1355", "fix": "1366"}, {"desc": "1367", "fix": "1368"}, {"kind": "1341", "justification": "1342"}, {"desc": "1369", "fix": "1370"}, {"desc": "1371", "fix": "1372"}, {"desc": "1277", "fix": "1373"}, {"desc": "1374", "fix": "1375"}, {"kind": "1341", "justification": "1342"}, {"desc": "1376", "fix": "1377"}, {"desc": "1378", "fix": "1379"}, {"desc": "1380", "fix": "1381"}, {"desc": "1382", "fix": "1383"}, {"kind": "1341", "justification": "1342"}, {"desc": "1384", "fix": "1385"}, {"kind": "1341", "justification": "1342"}, {"desc": "1386", "fix": "1387"}, {"desc": "1386", "fix": "1388"}, {"desc": "1389", "fix": "1390"}, {"kind": "1341", "justification": "1342"}, "Update the dependencies array to be: [accountCreated, password, adminId, createWalletMutation, sendDocuments, accountCreatedId]", {"range": "1391", "text": "1392"}, "Update the dependencies array to be: [typeAccount, typeBusiness, hide]", {"range": "1393", "text": "1394"}, {"range": "1395", "text": "1394"}, "Update the dependencies array to be: [accountCreated, password, brokerId, createWalletMutation, sendDocuments, accountCreatedId]", {"range": "1396", "text": "1397"}, {"range": "1398", "text": "1394"}, "Update the dependencies array to be: [term, watch]", {"range": "1399", "text": "1400"}, "Update the dependencies array to be: [modalityContract, setValue]", {"range": "1401", "text": "1402"}, "Update the dependencies array to be: [modalityContract, userProfile.name, endDate, advisors, contract, file, document, residence, createContractMutation, brokerIdSelected]", {"range": "1403", "text": "1404"}, {"range": "1405", "text": "1400"}, "Update the dependencies array to be: [userProfile.name, modalityContract, endDate, advisors, contract, file, document, residence, createInvestitorMutation, brokerIdSelected, reset]", {"range": "1406", "text": "1407"}, "Update the dependencies array to be: [userProfile.name]", {"range": "1408", "text": "1409"}, "Update the dependencies array to be: [addendumId, onUploadFile, selectedFile]", {"range": "1410", "text": "1411"}, "Update the dependencies array to be: [aditive, getAditives]", {"range": "1412", "text": "1413"}, "Update the dependencies array to be: [navigation, userProfile?.name]", {"range": "1414", "text": "1415"}, "Update the dependencies array to be: [getContractData]", {"range": "1416", "text": "1417"}, "Update the dependencies array to be: [getPaymentData]", {"range": "1418", "text": "1419"}, "Update the dependencies array to be: [router, userProfile.name]", {"range": "1420", "text": "1421"}, "Update the dependencies array to be: [page, optSelected, getPayments]", {"range": "1422", "text": "1423"}, "Update the dependencies array to be: [getChartData, getQuotes, isBroker]", {"range": "1424", "text": "1425"}, "Update the dependencies array to be: [filterContracts, getChartContractsData]", {"range": "1426", "text": "1427"}, "Update the dependencies array to be: [filterResults, getChartResultsData]", {"range": "1428", "text": "1429"}, "Update the dependencies array to be: [getAdminData, loading, userProfile.name]", {"range": "1430", "text": "1431"}, {"range": "1432", "text": "1427"}, {"range": "1433", "text": "1429"}, "Update the dependencies array to be: [getAdvisorData]", {"range": "1434", "text": "1435"}, "Update the dependencies array to be: [getInvestor]", {"range": "1436", "text": "1437"}, "Update the dependencies array to be: [startDate, investData, setValue]", {"range": "1438", "text": "1439"}, "Update the dependencies array to be: [setValue]", {"range": "1440", "text": "1441"}, "Update the dependencies array to be: [page, optSelected, getContracts]", {"range": "1442", "text": "1443"}, "Update the dependencies array to be: [getChartData]", {"range": "1444", "text": "1445"}, {"range": "1446", "text": "1419"}, {"range": "1447", "text": "1423"}, "Update the dependencies array to be: [getAdvisors, getBrokers, page, typeRanking]", {"range": "1448", "text": "1449"}, "Update the dependencies array to be: [getAdvisor, getBroker, page, typeRanking]", {"range": "1450", "text": "1451"}, "Update the dependencies array to be: [getIncomeReport, optSelected]", {"range": "1452", "text": "1453"}, "Update the dependencies array to be: [getIncomeReport]", {"range": "1454", "text": "1455"}, "Update the dependencies array to be: [getAcessorsInvestors, getInvestorsAdviser, getInvestorsAdvisorSelected, step, userProfile.name]", {"range": "1456", "text": "1457"}, "Update the dependencies array to be: [addGoal, handleSelectTypeGoals]", {"range": "1458", "text": "1459"}, {"range": "1460", "text": "1411"}, "Update the dependencies array to be: [contract.documentoInvestidor.length, setValue]", {"range": "1461", "text": "1462"}, "Update the dependencies array to be: [setValue, type]", {"range": "1463", "text": "1464"}, {"range": "1465", "text": "1413"}, {"range": "1466", "text": "1402"}, "Update the dependencies array to be: [investDate, watch]", {"range": "1467", "text": "1468"}, {"range": "1469", "text": "1400"}, "Update the dependencies array to be: [setValue, watch]", {"range": "1470", "text": "1471"}, "directive", "", {"range": "1472", "text": "1471"}, {"range": "1473", "text": "1471"}, {"range": "1474", "text": "1471"}, {"range": "1475", "text": "1402"}, {"range": "1476", "text": "1400"}, "Update the dependencies array to be: [modalityContract, userProfile.name, id, endDate, contract, file, document, residence, contractMutation]", {"range": "1477", "text": "1478"}, "Update the dependencies array to be: [contractData, durationInMonths, setValue]", {"range": "1479", "text": "1480"}, {"range": "1481", "text": "1402"}, "Update the dependencies array to be: [getReasons]", {"range": "1482", "text": "1483"}, "Update the dependencies array to be: [getCep, zipCode]", {"range": "1484", "text": "1485"}, "Update the dependencies array to be: [reasons, setError]", {"range": "1486", "text": "1487"}, "Update the dependencies array to be: [getBrokers, userProfile.name]", {"range": "1488", "text": "1489"}, {"range": "1490", "text": "1443"}, "Update the dependencies array to be: [getMoviments, page]", {"range": "1491", "text": "1492"}, "Update the dependencies array to be: [page, filteredData, filterYear, startDate, endDate, getPayments]", {"range": "1493", "text": "1494"}, {"range": "1495", "text": "1485"}, "Update the dependencies array to be: [validateToken]", {"range": "1496", "text": "1497"}, "Update the dependencies array to be: [getCep, investor?.zipCode]", {"range": "1498", "text": "1499"}, "Update the dependencies array to be: [getCep, zipcode]", {"range": "1500", "text": "1501"}, {"range": "1502", "text": "1409"}, "Update the dependencies array to be: [getAcessorsInvestors, getBrokers, getInvestors, step]", {"range": "1503", "text": "1504"}, "Update the dependencies array to be: [open, notificationSelected, handleClose]", {"range": "1505", "text": "1506"}, "Update the dependencies array to be: [filter, getSchedulePayments]", {"range": "1507", "text": "1508"}, "Update the dependencies array to be: [queryClient, token]", {"range": "1509", "text": "1510"}, "Update the dependencies array to be: [push, syncAuthFromSession]", {"range": "1511", "text": "1512"}, "Update the dependencies array to be: [syncAuthFromSession]", {"range": "1513", "text": "1514"}, "Update the dependencies array to be: [options, selected]", {"range": "1515", "text": "1516"}, {"range": "1517", "text": "1516"}, "Update the dependencies array to be: [itemSearch, items]", {"range": "1518", "text": "1519"}, [9244, 9325], "[accountCreated, password, adminId, createWalletMutation, sendDocuments, accountCreatedId]", [5037, 5082], "[typeAccount, typeBusiness, hide]", [5055, 5100], [10080, 10161], "[accountCreated, password, brokerId, createWalletMutation, sendDocuments, accountCreatedId]", [5141, 5186], [4256, 4281], "[term, watch]", [4904, 4922], "[modalityContract, setValue]", [10484, 10680], "[modalityContract, userProfile.name, endDate, advisors, contract, file, document, residence, createContractMutation, brokerIdSelected]", [4183, 4208], [9255, 9453], "[userProfile.name, modalityContract, endDate, advisors, contract, file, document, residence, createInvestitorMutation, brokerIdSelected, reset]", [1370, 1372], "[userProfile.name]", [1157, 1183], "[addendumId, onUploadFile, selectedFile]", [3017, 3026], "[aditive, getAditives]", [1626, 1628], "[navigation, userProfile?.name]", [2189, 2191], "[getContractData]", [3235, 3237], "[getPaymentData]", [1366, 1368], "[router, userProfile.name]", [2333, 2352], "[page, optSelected, getPayments]", [4212, 4214], "[get<PERSON><PERSON><PERSON><PERSON>, getQuotes, isBroker]", [5312, 5329], "[filterContracts, getChartContractsData]", [5387, 5402], "[filterR<PERSON><PERSON><PERSON>, getChartResultsData]", [7208, 7226], "[getAdminData, loading, userProfile.name]", [5046, 5063], [5121, 5136], [6852, 6854], "[getAdvisorData]", [934, 936], "[getInvestor]", [1846, 1869], "[startDate, investData, setValue]", [2011, 2013], "[setValue]", [3765, 3784], "[page, optSelected, getContracts]", [3608, 3610], "[getChartData]", [3237, 3239], [2123, 2142], [2977, 2983], "[getAdvisors, getBrokers, page, typeRanking]", [3230, 3236], "[getAdvisor, getBroker, page, typeRanking]", [2589, 2602], "[getIncomeReport, optSelected]", [2163, 2165], "[getIncomeReport]", [5403, 5409], "[getAcessorsInvestors, getInvestorsAdviser, getInvestorsAdvisorSelected, step, userProfile.name]", [2718, 2727], "[addGoal, handleSelectTypeGoals]", [1157, 1183], [5305, 5307], "[contract.documentoInvestidor.length, setValue]", [5482, 5488], "[setValue, type]", [2643, 2652], [5553, 5571], [8580, 8611], "[investDate, watch]", [7393, 7418], [10396, 10421], "[set<PERSON><PERSON><PERSON>, watch]", [10888, 10935], [7656, 7681], [8148, 8195], [7134, 7152], [7450, 7475], [11358, 11440], "[modalityContract, userProfile.name, id, endDate, contract, file, document, residence, contractMutation]", [5590, 5604], "[contractData, durationInMonths, setValue]", [5686, 5704], [5753, 5755], "[getReasons]", [8673, 8682], "[getCep, zipCode]", [9678, 9687], "[reasons, setError]", [9821, 9823], "[getBrokers, userProfile.name]", [3787, 3806], [2754, 2760], "[getMoviments, page]", [3884, 3936], "[page, filteredData, filterYear, startDate, endDate, getPayments]", [3597, 3606], [3658, 3660], "[validateToken]", [417, 436], "[getCep, investor?.zipCode]", [453, 462], "[getCep, zipcode]", [7702, 7704], [7943, 7949], "[getAcessorsInvestors, getBrokers, getInvestors, step]", [7256, 7284], "[open, notificationSelected, handleClose]", [2967, 2975], "[filter, getSchedulePayments]", [2380, 2387], "[queryClient, token]", [1638, 1644], "[push, syncAuthFromSession]", [2094, 2096], "[syncAuthFromSession]", [798, 800], "[options, selected]", [912, 922], [1157, 1169], "[itemSearch, items]"]