(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9153],{4193:function(){},9175:function(t){t.exports={style:{fontFamily:"'__Poppins_cc80f9', '__Poppins_Fallback_cc80f9'",fontStyle:"normal"},className:"__className_cc80f9",variable:"__variable_cc80f9"}},4033:function(t,e,s){t.exports=s(290)},7470:function(t,e,s){"use strict";s.d(e,{R:function(){return getDefaultState},m:function(){return n}});var i=s(7987),a=s(9024),r=s(1640),n=class extends a.F{#t;#e;#s;constructor(t){super(),this.mutationId=t.mutationId,this.#e=t.mutationCache,this.#t=[],this.state=t.state||getDefaultState(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#t.includes(t)||(this.#t.push(t),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#t=this.#t.filter(e=>e!==t),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#e.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(t){let onContinue=()=>{this.#i({type:"continue"})};this.#s=(0,r.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#i({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#i({type:"pause"})},onContinue,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});let e="pending"===this.state.status,s=!this.#s.canStart();try{if(e)onContinue();else{this.#i({type:"pending",variables:t,isPaused:s}),await this.#e.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#i({type:"pending",context:e,variables:t,isPaused:s})}let i=await this.#s.start();return await this.#e.config.onSuccess?.(i,t,this.state.context,this),await this.options.onSuccess?.(i,t,this.state.context),await this.#e.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,t,this.state.context),this.#i({type:"success",data:i}),i}catch(e){try{throw await this.#e.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#e.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#i({type:"error",error:e})}}finally{this.#e.runNext(this)}}#i(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),i.Vr.batch(()=>{this.#t.forEach(e=>{e.onMutationUpdate(t)}),this.#e.notify({mutation:this,type:"updated",action:t})})}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},8908:function(t,e,s){"use strict";s.d(e,{S:function(){return f}});var i=s(300),a=s(3002),r=s(7987),n=s(2996),u=class extends n.l{constructor(t={}){super(),this.config=t,this.#a=new Map}#a;build(t,e,s){let r=e.queryKey,n=e.queryHash??(0,i.Rm)(r,e),u=this.get(n);return u||(u=new a.A({client:t,queryKey:r,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(r)}),this.add(u)),u}add(t){this.#a.has(t.queryHash)||(this.#a.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let e=this.#a.get(t.queryHash);e&&(t.destroy(),e===t&&this.#a.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){r.Vr.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#a.get(t)}getAll(){return[...this.#a.values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i._x)(e,t))}findAll(t={}){let e=this.getAll();return Object.keys(t).length>0?e.filter(e=>(0,i._x)(t,e)):e}notify(t){r.Vr.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){r.Vr.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){r.Vr.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},o=s(7470),h=class extends n.l{constructor(t={}){super(),this.config=t,this.#r=new Set,this.#n=new Map,this.#u=0}#r;#n;#u;build(t,e,s){let i=new o.m({mutationCache:this,mutationId:++this.#u,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#r.add(t);let e=scopeFor(t);if("string"==typeof e){let s=this.#n.get(e);s?s.push(t):this.#n.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#r.delete(t)){let e=scopeFor(t);if("string"==typeof e){let s=this.#n.get(e);if(s){if(s.length>1){let e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#n.delete(e)}}}this.notify({type:"removed",mutation:t})}canRun(t){let e=scopeFor(t);if("string"!=typeof e)return!0;{let s=this.#n.get(e),i=s?.find(t=>"pending"===t.state.status);return!i||i===t}}runNext(t){let e=scopeFor(t);if("string"!=typeof e)return Promise.resolve();{let s=this.#n.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){r.Vr.batch(()=>{this.#r.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#r.clear(),this.#n.clear()})}getAll(){return Array.from(this.#r)}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i.X7)(e,t))}findAll(t={}){return this.getAll().filter(e=>(0,i.X7)(t,e))}notify(t){r.Vr.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return r.Vr.batch(()=>Promise.all(t.map(t=>t.continue().catch(i.ZT))))}};function scopeFor(t){return t.options.scope?.id}var l=s(9198),c=s(436);function infiniteQueryBehavior(t){return{onFetch:(e,s)=>{let a=e.options,r=e.fetchOptions?.meta?.fetchMore?.direction,n=e.state.data?.pages||[],u=e.state.data?.pageParams||[],o={pages:[],pageParams:[]},h=0,fetchFn=async()=>{let s=!1,addSignalProperty=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)})},l=(0,i.cG)(e.options,e.fetchOptions),fetchPage=async(t,a,r)=>{if(s)return Promise.reject();if(null==a&&t.pages.length)return Promise.resolve(t);let n=(()=>{let t={client:e.client,queryKey:e.queryKey,pageParam:a,direction:r?"backward":"forward",meta:e.options.meta};return addSignalProperty(t),t})(),u=await l(n),{maxPages:o}=e.options,h=r?i.Ht:i.VX;return{pages:h(t.pages,u,o),pageParams:h(t.pageParams,a,o)}};if(r&&n.length){let t="backward"===r,e=t?getPreviousPageParam:getNextPageParam,s={pages:n,pageParams:u},i=e(a,s);o=await fetchPage(s,i,t)}else{let e=t??n.length;do{let t=0===h?u[0]??a.initialPageParam:getNextPageParam(a,o);if(h>0&&null==t)break;o=await fetchPage(o,t),h++}while(h<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(fetchFn,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=fetchFn}}}function getNextPageParam(t,{pages:e,pageParams:s}){let i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function getPreviousPageParam(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}var f=class{#o;#e;#h;#l;#c;#f;#d;#p;constructor(t={}){this.#o=t.queryCache||new u,this.#e=t.mutationCache||new h,this.#h=t.defaultOptions||{},this.#l=new Map,this.#c=new Map,this.#f=0}mount(){this.#f++,1===this.#f&&(this.#d=l.j.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#o.onFocus())}),this.#p=c.N.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#o.onOnline())}))}unmount(){this.#f--,0===this.#f&&(this.#d?.(),this.#d=void 0,this.#p?.(),this.#p=void 0)}isFetching(t){return this.#o.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#e.findAll({...t,status:"pending"}).length}getQueryData(t){let e=this.defaultQueryOptions({queryKey:t});return this.#o.get(e.queryHash)?.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),s=this.#o.build(this,e),a=s.state.data;return void 0===a?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime((0,i.KC)(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(a))}getQueriesData(t){return this.#o.findAll(t).map(({queryKey:t,state:e})=>{let s=e.data;return[t,s]})}setQueryData(t,e,s){let a=this.defaultQueryOptions({queryKey:t}),r=this.#o.get(a.queryHash),n=r?.state.data,u=(0,i.SE)(e,n);if(void 0!==u)return this.#o.build(this,a).setData(u,{...s,manual:!0})}setQueriesData(t,e,s){return r.Vr.batch(()=>this.#o.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){let e=this.defaultQueryOptions({queryKey:t});return this.#o.get(e.queryHash)?.state}removeQueries(t){let e=this.#o;r.Vr.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let s=this.#o;return r.Vr.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){let s={revert:!0,...e},a=r.Vr.batch(()=>this.#o.findAll(t).map(t=>t.cancel(s)));return Promise.all(a).then(i.ZT).catch(i.ZT)}invalidateQueries(t,e={}){return r.Vr.batch(()=>(this.#o.findAll(t).forEach(t=>{t.invalidate()}),t?.refetchType==="none")?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))}refetchQueries(t,e={}){let s={...e,cancelRefetch:e.cancelRefetch??!0},a=r.Vr.batch(()=>this.#o.findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(i.ZT)),"paused"===t.state.fetchStatus?Promise.resolve():e}));return Promise.all(a).then(i.ZT)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let s=this.#o.build(this,e);return s.isStaleByTime((0,i.KC)(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(i.ZT).catch(i.ZT)}fetchInfiniteQuery(t){return t.behavior=infiniteQueryBehavior(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(i.ZT).catch(i.ZT)}ensureInfiniteQueryData(t){return t.behavior=infiniteQueryBehavior(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return c.N.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#o}getMutationCache(){return this.#e}getDefaultOptions(){return this.#h}setDefaultOptions(t){this.#h=t}setQueryDefaults(t,e){this.#l.set((0,i.Ym)(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...this.#l.values()],s={};return e.forEach(e=>{(0,i.to)(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#c.set((0,i.Ym)(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...this.#c.values()],s={};return e.forEach(e=>{(0,i.to)(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;let e={...this.#h.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=(0,i.Rm)(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===i.CN&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#h.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#o.clear(),this.#e.clear()}}}}]);