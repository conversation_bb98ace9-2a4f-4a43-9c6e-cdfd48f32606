"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetContractsByInvestorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const typeorm_2 = require("typeorm");
let GetContractsByInvestorService = class GetContractsByInvestorService {
    constructor(ownerRoleRelationRepository, contractRepository) {
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.contractRepository = contractRepository;
    }
    async perform(investorId) {
        const userProfile = await this.ownerRoleRelationRepository.findOne({
            where: { id: investorId },
            relations: {
                owner: { address: true, account: true },
                business: { address: true, account: true },
                role: true,
            },
        });
        if (!userProfile) {
            throw new common_1.NotFoundException(`Usuario nāo encontrado.`);
        }
        const contracts = await this.contractRepository.find({
            where: { investor: { id: userProfile.id } },
            relations: {
                ownerRoleRelation: { business: true, owner: true },
                signataries: true,
                addendum: { addendumFiles: { file: true } },
                contractAdvisors: {
                    advisor: { owner: true, business: true },
                },
            },
        });
        const result = contracts.map((contract) => {
            const signatary = contract.signataries[0];
            return {
                id: contract.id,
                investmentValue: signatary?.investmentValue,
                investmentTerm: signatary?.investmentTerm,
                investmentYield: signatary?.investmentYield,
                investorName: signatary?.name,
                investorDocument: signatary?.document,
                tags: signatary?.investmentModality,
                purchasedWith: signatary?.purchaseWith,
                quotesAmount: signatary?.amountQuotes,
                gracePeriod: signatary?.gracePeriod,
                responsibleConsultant: contract.ownerRoleRelation?.owner?.name ||
                    contract.ownerRoleRelation?.business?.fantasyName ||
                    'Nāo possui',
                contractStatus: contract.status,
                contractStart: contract.startContract,
                contractEnd: contract.endContract,
                finalizedAt: contract.updatedAt,
                pdfDocument: contract.contractPdf,
                proofPaymentPdf: contract.proofPayment,
                advisors: contract.contractAdvisors.map(({ advisor, rate }) => ({
                    id: advisor.id,
                    name: advisor.owner?.name ?? advisor.business?.fantasyName,
                    document: advisor.owner?.cpf ?? advisor.business?.cnpj,
                    rate,
                })),
                addendum: contract.addendum.map((addendum) => ({
                    id: addendum.id,
                    investmentValue: addendum.value,
                    investmentYield: addendum.yieldRate,
                    contractStart: addendum.applicationDate,
                    contractEnd: addendum.expiresIn,
                    contractStatus: addendum.status,
                    responsibleConsultant: contract.ownerRoleRelation?.owner?.name ||
                        contract.ownerRoleRelation?.business?.fantasyName,
                    files: addendum.addendumFiles.map((addendumFile) => ({
                        type: addendumFile.type,
                        url: addendumFile.file.url,
                    })),
                })),
            };
        });
        return {
            document: userProfile.owner?.cpf || userProfile.business?.cnpj,
            email: userProfile.owner?.email || userProfile.business?.email,
            phone: userProfile.owner?.phone,
            profile: userProfile.role?.name,
            advisor: contracts[0]?.ownerRoleRelation?.owner?.name ||
                contracts[0]?.ownerRoleRelation?.business?.fantasyName,
            rg: contracts[0]?.signataries[0]?.rg,
            motherName: userProfile.owner?.motherName,
            birthDate: userProfile.owner?.dtBirth ? (() => {
                try {
                    if (userProfile.owner.dtBirth instanceof Date) {
                        return userProfile.owner.dtBirth.toISOString().split('T')[0];
                    }
                    const birthDate = new Date(userProfile.owner.dtBirth);
                    if (!isNaN(birthDate.getTime())) {
                        return birthDate.toISOString().split('T')[0];
                    }
                    return undefined;
                }
                catch (e) {
                    console.error('Error parsing dtBirth', e);
                    return undefined;
                }
            })() : undefined,
            zipCode: userProfile?.owner?.address[0]?.cep ||
                userProfile?.business?.address[0]?.cep,
            city: userProfile.owner?.address[0]?.city ||
                userProfile.business?.address[0]?.city,
            address: userProfile.owner?.address[0]?.street ||
                userProfile.business?.address[0]?.street,
            addressNumber: userProfile.owner?.address[0]?.number ||
                userProfile.business?.address[0]?.number,
            complement: userProfile.owner?.address[0]?.complement ||
                userProfile.business?.address[0]?.complement,
            neighborhood: userProfile.owner?.address[0]?.neighborhood ||
                userProfile.business?.address[0]?.neighborhood,
            state: userProfile.owner?.address[0]?.state ||
                userProfile.business?.address[0]?.state,
            bank: userProfile.owner?.account[0]?.bank ||
                userProfile.business?.account[0]?.bank ||
                contracts[0]?.signataries[0]?.bank,
            accountNumber: userProfile.owner?.account[0]?.number ||
                userProfile.business?.account[0]?.number ||
                contracts[0]?.signataries[0]?.account,
            branch: userProfile.owner?.account[0]?.branch ||
                userProfile.business?.account[0]?.branch ||
                contracts[0]?.signataries[0]?.agency,
            contracts: result,
        };
    }
};
exports.GetContractsByInvestorService = GetContractsByInvestorService;
exports.GetContractsByInvestorService = GetContractsByInvestorService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], GetContractsByInvestorService);
//# sourceMappingURL=get-contract-by-investor.service.js.map