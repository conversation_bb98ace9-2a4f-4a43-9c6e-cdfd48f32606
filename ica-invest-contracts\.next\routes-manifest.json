{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/contratos/contrato/[id]", "regex": "^/contratos/contrato/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/contratos/contrato/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/financeiro/pagamento/[id]", "regex": "^/financeiro/pagamento/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/financeiro/pagamento/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/home/<USER>/pagamento/[id]", "regex": "^/home/<USER>/pagamento/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/home/<USER>/pagamento/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/home/<USER>/[id]", "regex": "^/home/<USER>/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/home/<USER>/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/informe-rendimentos/[id]/[year]", "regex": "^/informe\\-rendimentos/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPyear": "nxtPyear"}, "namedRegex": "^/informe\\-rendimentos/(?<nxtPid>[^/]+?)/(?<nxtPyear>[^/]+?)(?:/)?$"}, {"page": "/meus-contratos/contrato/novo/[id]", "regex": "^/meus\\-contratos/contrato/novo/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/meus\\-contratos/contrato/novo/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/meus-contratos/contrato/[id]", "regex": "^/meus\\-contratos/contrato/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/meus\\-contratos/contrato/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/registro/[id]", "regex": "^/registro/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/registro/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/cadastro-manual", "regex": "^/cadastro\\-manual(?:/)?$", "routeKeys": {}, "namedRegex": "^/cadastro\\-manual(?:/)?$"}, {"page": "/contratos", "regex": "^/contratos(?:/)?$", "routeKeys": {}, "namedRegex": "^/contratos(?:/)?$"}, {"page": "/contratos/alterar", "regex": "^/contratos/alterar(?:/)?$", "routeKeys": {}, "namedRegex": "^/contratos/alterar(?:/)?$"}, {"page": "/financeiro", "regex": "^/financeiro(?:/)?$", "routeKeys": {}, "namedRegex": "^/financeiro(?:/)?$"}, {"page": "/financeiro/pagamentos", "regex": "^/financeiro/pagamentos(?:/)?$", "routeKeys": {}, "namedRegex": "^/financeiro/pagamentos(?:/)?$"}, {"page": "/home", "regex": "^/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/home(?:/)?$"}, {"page": "/home/<USER>/pagamentos", "regex": "^/home/<USER>/pagamentos(?:/)?$", "routeKeys": {}, "namedRegex": "^/home/<USER>/pagamentos(?:/)?$"}, {"page": "/home/<USER>", "regex": "^/home/<USER>/)?$", "routeKeys": {}, "namedRegex": "^/home/<USER>/)?$"}, {"page": "/informe-rendimentos", "regex": "^/informe\\-rendimentos(?:/)?$", "routeKeys": {}, "namedRegex": "^/informe\\-rendimentos(?:/)?$"}, {"page": "/investidores", "regex": "^/investidores(?:/)?$", "routeKeys": {}, "namedRegex": "^/investidores(?:/)?$"}, {"page": "/metas", "regex": "^/metas(?:/)?$", "routeKeys": {}, "namedRegex": "^/metas(?:/)?$"}, {"page": "/meus-contratos", "regex": "^/meus\\-contratos(?:/)?$", "routeKeys": {}, "namedRegex": "^/meus\\-contratos(?:/)?$"}, {"page": "/meus-contratos/registro-manual", "regex": "^/meus\\-contratos/registro\\-manual(?:/)?$", "routeKeys": {}, "namedRegex": "^/meus\\-contratos/registro\\-manual(?:/)?$"}, {"page": "/monitoramento", "regex": "^/monitoramento(?:/)?$", "routeKeys": {}, "namedRegex": "^/monitoramento(?:/)?$"}, {"page": "/movimentacoes", "regex": "^/movimentacoes(?:/)?$", "routeKeys": {}, "namedRegex": "^/movimentacoes(?:/)?$"}, {"page": "/pagamentos-previstos", "regex": "^/pagamentos\\-previstos(?:/)?$", "routeKeys": {}, "namedRegex": "^/pagamentos\\-previstos(?:/)?$"}, {"page": "/usuarios", "regex": "^/usuarios(?:/)?$", "routeKeys": {}, "namedRegex": "^/usuarios(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}