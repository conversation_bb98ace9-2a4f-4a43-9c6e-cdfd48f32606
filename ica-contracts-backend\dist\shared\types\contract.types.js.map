{"version": 3, "file": "contract.types.js", "sourceRoot": "/", "sources": ["shared/types/contract.types.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAAmE;AACnE,yEAA0D;AAC1D,yDAAoD;AACpD,qDAiByB;AACzB,mFAA4E;AAC5E,6EAAuE;AACvE,2EAAqE;AACrE,8DAAkE;AAMlE,IAAY,UAGX;AAHD,WAAY,UAAU;IACpB,uBAAS,CAAA;IACT,uBAAS,CAAA;AACX,CAAC,EAHW,UAAU,0BAAV,UAAU,QAGrB;AAED,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,2BAAW,CAAA;IACX,+BAAe,CAAA;AACjB,CAAC,EAHW,YAAY,4BAAZ,YAAY,QAGvB;AAED,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,gDAA+B,CAAA;IAC/B,kCAAiB,CAAA;AACnB,CAAC,EAJW,aAAa,6BAAb,aAAa,QAIxB;AAED,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,gDAA6B,CAAA;IAC7B,wCAAqB,CAAA;IACrB,4CAAyB,CAAA;AAC3B,CAAC,EAJW,eAAe,+BAAf,eAAe,QAI1B;AAED,IAAY,gBASX;AATD,WAAY,gBAAgB;IAC1B,+BAAW,CAAA;IACX,6BAAS,CAAA;IACT,qCAAiB,CAAA;IACjB,iCAAa,CAAA;IACb,+BAAW,CAAA;IACX,6BAAS,CAAA;IACT,6BAAS,CAAA;IACT,2CAAuB,CAAA;AACzB,CAAC,EATW,gBAAgB,gCAAhB,gBAAgB,QAS3B;AAMD,MAAa,UAAU;CA8CtB;AA9CD,gCA8CC;AAvCC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE,cAAc;KACxB,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACnD,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;;0CACnD;AAKhB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAC5E,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACtD,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;;wCACxD;AAMd;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC9D,IAAA,yBAAO,EAAC,YAAY,EAAE;QACrB,OAAO,EAAE,4CAA4C;KACtD,CAAC;;yCACa;AAOf;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,yBAAyB;QACtC,OAAO,EAAE,YAAY;KACtB,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;;gDACjC;AAWtB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iCAAiC;QAC9C,OAAO,EAAE,UAAU;QACnB,OAAO,EAAE,kBAAkB;KAC5B,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACnD,IAAA,yBAAO,EAAC,SAAS,EAAE;QAClB,OAAO,EAAE,qDAAqD;KAC/D,CAAC;;8CACkB;AAIpB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAClE,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;;0CACvC;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACS;AAGtB,MAAa,aAAa;CAsFzB;AAtFD,sCAsFC;AA/EC;IANC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mCAAmC,EAAE,CAAC;IACjE,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC7D,IAAA,qCAAW,EAAC;QACX,OAAO,EACL,oFAAoF;KACvF,CAAC;;+CACgB;AAMlB;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAClE,IAAA,iCAAK,EAAC;QACL,OAAO,EAAE,2DAA2D;KACrE,CAAC;;0CACW;AAIb;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACxE,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;;yCACvC;AAIZ;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,yBAAyB,EAAE,CAAC;IACvD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;;oDACvC;AAIvB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IAC9D,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;;kDACzC;AAQrB;IANC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACzD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IACxD,IAAA,qCAAW,EAAC;QACX,OAAO,EACL,+EAA+E;KAClF,CAAC;;iDACkB;AAQpB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,IAAA,4BAAY,EAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;KACnC,CAAC;IACD,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IAC5D,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;;gDACzC;AAOnB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,eAAe;KACzB,CAAC;IACD,IAAA,yBAAO,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;;4CAC7B;AASf;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sCAAsC;QACnD,OAAO,EAAE,aAAa;KACvB,CAAC;IACD,IAAA,yBAAO,EAAC,oBAAoB,EAAE;QAC7B,OAAO,EAAE,wCAAwC;KAClD,CAAC;;4CACa;AAWf;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kCAAkC;QAC/C,OAAO,EAAE,gBAAgB;KAC1B,CAAC;IACD,IAAA,qCAAW,EAAC;QACX,OAAO,EACL,kFAAkF;KACrF,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;;iDACxC;AAiBpB;IAfC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE;YACP,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,IAAI;YACX,UAAU,EAAE,UAAU;YACtB,YAAY,EAAE,YAAY;YAC1B,UAAU,EAAE,SAAS;SACtB;KACF,CAAC;IACD,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACb,UAAU;8CAAC;AAGvB,MAAa,UAAU;CAqCtB;AArCD,gCAqCC;AA9BC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,WAAW;KACrB,CAAC;IACD,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACnD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;;iDACtC;AAWvB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,2BAA2B;QACxC,OAAO,EAAE,gBAAgB;KAC1B,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;IAC7C,IAAA,kCAAM,EAAC;QACN,OAAO,EACL,mEAAmE;KACtE,CAAC;;wCACY;AAQd;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE,gBAAgB,CAAC,IAAI;KAC/B,CAAC;IACD,IAAA,wBAAM,EAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;wCAC1C;AAKxB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;IAC1D,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACb,UAAU;2CAAC;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACxE,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,aAAa,CAAC;8BACT,aAAa;kDAAC;AAGjC,MAAa,cAAc;CAiD1B;AAjDD,wCAiDC;AA1CC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,UAAU;KACpB,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC7D,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;;4CACzC;AAQd;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,6BAA6B;QAC1C,OAAO,EAAE,MAAM;KAChB,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACvD,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;;8CAC9B;AAQhB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,SAAS;KACnB,CAAC;IACD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACrD,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;;+CAC3B;AAyBjB;IAvBC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IACtE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;QACvB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAE9B,IAAI,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;gBACjC,OAAO,KAAK,GAAG,KAAK,CAAC;YACvB,CAAC;YAED,IAAI,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACnC,OAAO,GAAG,GAAG,KAAK,CAAC;YACrB,CAAC;QACH,CAAC;QACD,OAAO,KAAe,CAAC;IACzB,CAAC,CAAC;IACD,IAAA,yBAAO,EACN,4JAA4J,EAC5J;QACE,OAAO,EACL,qHAAqH;KACxH,CACF;;2CACY;AAGf,MAAa,oBAAoB;CAwFhC;AAxFD,oDAwFC;AA7EC;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE,MAAM;KACb,CAAC;IACD,IAAA,8BAAQ,GAAE;IACV,IAAA,0BAAQ,EACP,EAAE,QAAQ,EAAE,KAAK,EAAE,EACnB,EAAE,OAAO,EAAE,qCAAqC,EAAE,CACnD;IACA,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;;oDACzC;AAYhB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,IAAI,EAAE,MAAM;KACb,CAAC;IACD,IAAA,8BAAQ,GAAE;IACV,IAAA,0BAAQ,EACP,EAAE,QAAQ,EAAE,KAAK,EAAE,EACnB,EAAE,OAAO,EAAE,mCAAmC,EAAE,CACjD;IACA,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,6CAA6C,EAAE,CAAC;;yDAClD;AAUrB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,IAAI,EAAE,MAAM;KACb,CAAC;IACD,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACzD,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IACxD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IACxD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;;8DACnC;AAW1B;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,aAAa,CAAC,GAAG;KAC3B,CAAC;IACD,IAAA,wBAAM,EAAC,aAAa,EAAE;QACrB,OAAO,EACL,yEAAyE;KAC5E,CAAC;;2DAC4B;AAQ9B;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iCAAiC;QAC9C,OAAO,EAAE,IAAA,4BAAY,EAAC,CAAC,CAAC;KACzB,CAAC;IACD,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;IACxD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;;uDAC1C;AAQnB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,IAAA,4BAAY,EAAC,GAAG,CAAC;KAC3B,CAAC;IACD,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IACjE,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,4CAA4C,EAAE,CAAC;;qDACrD;AAYjB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sBAAsB;QACnC,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,oCAAoC;KAC9C,CAAC;IACD,IAAA,wBAAM,EAAC,eAAe,EAAE;QACvB,OAAO,EACL,kFAAkF;KACrF,CAAC;;qDACwB;AAO1B;IALC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,6BAA6B;QAC1C,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,GAAE;;2DACU;AAQvB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,0BAA0B;QACvC,OAAO,EAAE,KAAK;KACf,CAAC;IACD,IAAA,gCAAS,GAAE;IACX,IAAA,2BAAS,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;;yDAClC;AAGxB,MAAa,oBAAoB;CAiBhC;AAjBD,oDAiBC;AAXC;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mCAAmC;QAChD,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,wBAAM,EAAC,KAAK,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;;uDACnC;AAUnB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sCAAsC;QACnD,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,8BAAQ,GAAE;IACV,IAAA,0BAAQ,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;IAC5E,IAAA,qBAAG,EAAC,IAAI,EAAE,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAC7D,IAAA,qBAAG,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,wCAAwC,EAAE,CAAC;;kDAClD", "sourcesContent": ["import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';\r\nimport { IsCNPJ, IsCPF } from 'brazilian-class-validator';\r\nimport { Transform, Type } from 'class-transformer';\r\nimport {\r\n  IsString,\r\n  IsNotEmpty,\r\n  <PERSON><PERSON><PERSON>th,\r\n  IsO<PERSON>al,\r\n  IsEnum,\r\n  IsEmail,\r\n  IsNumber,\r\n  IsPositive,\r\n  Min,\r\n  Max,\r\n  IsInt,\r\n  Matches,\r\n  ValidateNested,\r\n  IsBoolean,\r\n  IsUUID,\r\n  IsDateString,\r\n} from 'class-validator';\r\nimport { IsValidName } from 'src/shared/decorators/is-valid-name.decorator';\r\nimport { ToBoolean } from 'src/shared/decorators/to-boolean.decorator';\r\nimport { ToNumber } from 'src/shared/decorators/to-number.decorator';\r\nimport { generateDate } from 'src/shared/functions/generate-date';\r\n\r\n// ============================================================================\r\n// ENUMS\r\n// ============================================================================\r\n\r\nexport enum PersonType {\r\n  PF = 'PF',\r\n  PJ = 'PJ',\r\n}\r\n\r\nexport enum ContractType {\r\n  SCP = 'SCP',\r\n  MUTUO = 'MUTUO',\r\n}\r\n\r\nexport enum PaymentMethod {\r\n  PIX = 'pix',\r\n  BANK_TRANSFER = 'bank_transfer',\r\n  BOLETO = 'boleto',\r\n}\r\n\r\nexport enum InvestorProfile {\r\n  CONSERVATIVE = 'conservative',\r\n  MODERATE = 'moderate',\r\n  AGGRESSIVE = 'aggressive',\r\n}\r\n\r\nexport enum CompanyLegalType {\r\n  MEI = 'MEI',\r\n  EI = 'EI',\r\n  EIRELI = 'EIRELI',\r\n  LTDA = 'LTDA',\r\n  SLU = 'SLU',\r\n  SA = 'SA',\r\n  SS = 'SS',\r\n  CONSORCIO = 'CONSORCIO',\r\n}\r\n\r\n// ============================================================================\r\n// SHARED DTOs\r\n// ============================================================================\r\n\r\nexport class AddressDto {\r\n  @ApiProperty({\r\n    description: 'Rua (ex: Av. Paulista)',\r\n    example: 'Av. Paulista',\r\n  })\r\n  @IsNotEmpty({ message: 'Rua não pode estar vazia' })\r\n  @MinLength(3, { message: 'A rua deve ter no mínimo 3 caracteres' })\r\n  street!: string;\r\n\r\n  @ApiProperty({ description: 'Cidade (ex: São Paulo)', example: 'São Paulo' })\r\n  @IsNotEmpty({ message: 'Cidade não pode estar vazia' })\r\n  @MinLength(2, { message: 'A cidade deve ter no mínimo 2 caracteres' })\r\n  city!: string;\r\n\r\n  @ApiProperty({ description: 'Estado (ex: SP)', example: 'SP' })\r\n  @Matches(/^[A-Z]{2}$/, {\r\n    message: 'Estado inválido. Use o formato XX (ex: SP)',\r\n  })\r\n  state!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Bairro (ex: Bela Vista)',\r\n    example: 'Bela Vista',\r\n  })\r\n  @IsNotEmpty({ message: 'Bairro não pode estar vazio' })\r\n  neighborhood!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'CEP (ex: 01310-100 ou 01310100)',\r\n    example: '01310100',\r\n    pattern: '^\\\\d{5}-?\\\\d{3}$',\r\n  })\r\n  @IsNotEmpty({ message: 'CEP não pode estar vazio' })\r\n  @Matches(/^\\d{8}$/, {\r\n    message: 'CEP inválido. Use o formato XXXXXXXX (ex: 01310100)',\r\n  })\r\n  postalCode!: string;\r\n\r\n  @ApiProperty({ description: 'Número (ex: 1000)', example: '1000' })\r\n  @IsNotEmpty({ message: 'Número não pode estar vazio' })\r\n  number!: string;\r\n\r\n  @ApiPropertyOptional({ description: 'Complemento (ex: Apt 101)' })\r\n  @IsOptional()\r\n  @IsString()\r\n  complement?: string;\r\n}\r\n\r\nexport class IndividualDto {\r\n  @ApiProperty({ description: 'Nome completo (ex: João da Silva)' })\r\n  @IsNotEmpty({ message: 'Nome completo não pode estar vazio' })\r\n  @IsValidName({\r\n    message:\r\n      'Nome completo inválido. Use apenas letras, espaços, hífens e caracteres acentuados',\r\n  })\r\n  fullName!: string;\r\n\r\n  @ApiProperty({ description: 'CPF (ex: 12345678900)', example: '' })\r\n  @IsCPF({\r\n    message: 'CPF inválido. Use o formato XXXXXXXXXXX (ex: 12345678900)',\r\n  })\r\n  cpf!: string;\r\n\r\n  @ApiProperty({ description: 'RG (ex: 123456789)', example: '123456789' })\r\n  @IsNotEmpty({ message: 'RG não pode estar vazio' })\r\n  rg!: string;\r\n\r\n  @ApiProperty({ description: 'Órgão emissor (ex: SSP)' })\r\n  @IsNotEmpty({ message: 'Órgão emissor não pode estar vazio' })\r\n  issuingAgency!: string;\r\n\r\n  @ApiProperty({ description: 'Nacionalidade (ex: Brasileiro)' })\r\n  @IsNotEmpty({ message: 'Nacionalidade não pode estar vazia' })\r\n  nationality!: string;\r\n\r\n  @ApiProperty({ description: 'Ocupação (ex: Engenheiro)' })\r\n  @IsNotEmpty({ message: 'Ocupação não pode estar vazia' })\r\n  @IsValidName({\r\n    message:\r\n      'Ocupação inválida. Use apenas letras, espaços, hífens e caracteres acentuados',\r\n  })\r\n  occupation!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Data de nascimento (ex: 1990-01-01)',\r\n    example: generateDate(-(365 * 20)),\r\n  })\r\n  @IsDateString({}, { message: 'Data de nascimento inválida' })\r\n  @IsNotEmpty({ message: 'Data de nascimento é obrigatória' })\r\n  birthDate!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Email (ex: <EMAIL>)',\r\n    example: '<EMAIL>',\r\n  })\r\n  @IsEmail({}, { message: 'E-mail inválido' })\r\n  email!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Telefone celular (ex: 5548999999999)',\r\n    example: '48999999999',\r\n  })\r\n  @Matches(/^55(\\d{2})\\d{8,9}$/, {\r\n    message: 'Telefone inválido. (ex: 5548999999999)',\r\n  })\r\n  phone!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Nome da mãe (ex: Maria da Silva)',\r\n    example: 'Maria da Silva',\r\n  })\r\n  @IsValidName({\r\n    message:\r\n      'Nome da mãe inválido. Use apenas letras, espaços, hífens e caracteres acentuados',\r\n  })\r\n  @IsNotEmpty({ message: 'Nome da mãe não pode estar vazio' })\r\n  motherName!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Endereço',\r\n    type: AddressDto,\r\n    example: {\r\n      street: 'Av. Paulista',\r\n      number: '1000',\r\n      city: 'São Paulo',\r\n      state: 'SP',\r\n      postalCode: '01310100',\r\n      neighborhood: 'Bela Vista',\r\n      complement: 'Apt 101',\r\n    },\r\n  })\r\n  @ValidateNested()\r\n  @Type(() => AddressDto)\r\n  address!: AddressDto;\r\n}\r\n\r\nexport class CompanyDto {\r\n  @ApiProperty({\r\n    description: 'Razão social',\r\n    example: 'ACME Ltda',\r\n  })\r\n  @IsString({ message: 'Razão Social é obrigatória' })\r\n  @IsNotEmpty({ message: 'Razão social não pode estar vazia' })\r\n  corporateName!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'CNPJ (ex: 12345678000199)',\r\n    example: '12345678000199',\r\n  })\r\n  @IsNotEmpty({ message: 'CNPJ é obrigatório' })\r\n  @IsCNPJ({\r\n    message:\r\n      'CNPJ inválido. Use o formato XXXXXXXXXXXXXXX (ex: 12345678000199)',\r\n  })\r\n  cnpj!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tipo jurídico da empresa',\r\n    enum: CompanyLegalType,\r\n    example: CompanyLegalType.LTDA,\r\n  })\r\n  @IsEnum(CompanyLegalType, { message: 'Tipo de empresa inválido' })\r\n  type!: CompanyLegalType;\r\n\r\n  @ApiProperty({ description: 'Endereço', type: AddressDto })\r\n  @ValidateNested()\r\n  @Type(() => AddressDto)\r\n  address!: AddressDto;\r\n\r\n  @ApiProperty({ description: 'Representante legal', type: IndividualDto })\r\n  @ValidateNested()\r\n  @Type(() => IndividualDto)\r\n  representative!: IndividualDto;\r\n}\r\n\r\nexport class BankAccountDto {\r\n  @ApiProperty({\r\n    description: 'Nome do banco (ex: ICA Bank)',\r\n    example: 'ICA Bank',\r\n  })\r\n  @IsNotEmpty({ message: 'Nome do banco não pode estar vazio' })\r\n  @MinLength(2, { message: 'Nome do banco muito curto' })\r\n  bank!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Agência bancária (ex: 0001)',\r\n    example: '0001',\r\n  })\r\n  @IsNotEmpty({ message: 'Agência não pode estar vazia' })\r\n  @MinLength(2, { message: 'Agência inválida' })\r\n  agency!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Conta bancária (ex: 1234567)',\r\n    example: '1234567',\r\n  })\r\n  @IsNotEmpty({ message: 'Conta não pode estar vazia' })\r\n  @MinLength(3, { message: 'Conta inválida' })\r\n  account!: string;\r\n\r\n  @ApiPropertyOptional({ description: 'Chave PIX (ex: <EMAIL>)' })\r\n  @IsOptional()\r\n  @IsString()\r\n  @Transform(({ value }) => {\r\n    if (typeof value === 'string') {\r\n      // If it's just DDD + number (e.g., ***********)\r\n      if (value.match(/^\\d{2}9\\d{8}$/)) {\r\n        return '+55' + value;\r\n      }\r\n      // If it has 55 but no + (e.g., *************)\r\n      if (value.match(/^55\\d{2}9\\d{8}$/)) {\r\n        return '+' + value;\r\n      }\r\n    }\r\n    return value as string;\r\n  })\r\n  @Matches(\r\n    /^(\\+55\\d{2}9\\d{8}|[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}|\\d{11}|\\d{14}|[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})$/,\r\n    {\r\n      message:\r\n        'Chave PIX inválida. Use CPF (11 dígitos), CNPJ (14 dígitos), telefone (+55+DDD+Telefone), email ou chave aleatória.',\r\n    },\r\n  )\r\n  pix?: string;\r\n}\r\n\r\nexport class InvestmentDetailsDto {\r\n  @ApiProperty({\r\n    description: 'Valor do investimento',\r\n    type: Number,\r\n  })\r\n  @ToNumber()\r\n  @IsNumber(\r\n    { allowNaN: false },\r\n    { message: 'Valor do investimento é obrigatório' },\r\n  )\r\n  @IsPositive({ message: 'Valor deve ser maior que zero' })\r\n  amount!: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Taxa de remuneração',\r\n    type: Number,\r\n  })\r\n  @ToNumber()\r\n  @IsNumber(\r\n    { allowNaN: false },\r\n    { message: 'Taxa de remuneração é obrigatória' },\r\n  )\r\n  @IsPositive({ message: 'Taxa de remuneração deve ser maior que zero' })\r\n  monthlyRate!: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Prazo em meses',\r\n    type: Number,\r\n  })\r\n  @IsNumber({}, { message: 'Prazo em meses é obrigatório' })\r\n  @IsInt({ message: 'O prazo deve ser um número inteiro' })\r\n  @IsPositive({ message: 'Prazo deve ser maior que zero' })\r\n  @IsNotEmpty({ message: 'Prazo em meses não pode ser vazio' })\r\n  durationInMonths!: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Método de pagamento',\r\n    enum: PaymentMethod,\r\n    example: PaymentMethod.PIX,\r\n  })\r\n  @IsEnum(PaymentMethod, {\r\n    message:\r\n      'Método de pagamento inválido. Tipos válidos: pix, bank_transfer, boleto',\r\n  })\r\n  paymentMethod!: PaymentMethod;\r\n\r\n  @ApiProperty({\r\n    description: 'Data de início (ex: 2025-01-01)',\r\n    example: generateDate(0),\r\n  })\r\n  @IsDateString({}, { message: 'Data de início inválida' })\r\n  @IsNotEmpty({ message: 'Data de início não pode ser vazia' })\r\n  startDate!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Data de fim (ex: 2025-01-01)',\r\n    example: generateDate(365),\r\n  })\r\n  @IsDateString({}, { message: 'Data de fim de contrato inválida' })\r\n  @IsNotEmpty({ message: 'Data de fim do contrato não pode ser vazia' })\r\n  endDate!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Perfil do investidor',\r\n    type: String,\r\n    enum: InvestorProfile,\r\n    example: 'conservative, moderate, aggressive',\r\n  })\r\n  @IsEnum(InvestorProfile, {\r\n    message:\r\n      'Perfil do investidor inválido. Tipos válidos: conservative, moderate, aggressive',\r\n  })\r\n  profile!: InvestorProfile;\r\n\r\n  @ApiPropertyOptional({\r\n    description: 'Quantidade de cotas (ex: 1)',\r\n    example: 2,\r\n  })\r\n  @IsOptional()\r\n  quotaQuantity?: number;\r\n\r\n  @ApiPropertyOptional({\r\n    description: 'É debênture? (ex: false)',\r\n    example: false,\r\n  })\r\n  @ToBoolean()\r\n  @IsBoolean({ message: 'Campo Debênture é obrigatório' })\r\n  isDebenture?: boolean;\r\n}\r\n\r\nexport class AdvisorAssignmentDto {\r\n  @ApiProperty({\r\n    description: 'ID do assessor (ex: advisor-uuid)',\r\n    example: '',\r\n  })\r\n  @IsUUID('all', { message: 'ID do assessor inválido' })\r\n  advisorId!: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Porcentagem do assessor (%) (ex: 10)',\r\n    example: 10,\r\n  })\r\n  @ToNumber()\r\n  @IsNumber({ allowNaN: false }, { message: 'Taxa do assessor é obrigatória' })\r\n  @Min(0.01, { message: 'Porcentagem deve ser maior que zero' })\r\n  @Max(100, { message: 'Porcentagem não pode ser maior que 100' })\r\n  rate!: number;\r\n}\r\n"]}