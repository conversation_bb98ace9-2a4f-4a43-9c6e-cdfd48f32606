(()=>{var e={};e.id=7548,e.ids=[7548],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},86494:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>m,routeModule:()=>g,tree:()=>c});var t=a(73137),s=a(54647),i=a(4183),o=a.n(i),n=a(71775),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(r,l);let d=t.AppPageRouteModule,c=["",{children:["meus-contratos",{children:["contrato",{children:["novo",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,6987)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\novo\\[id]\\page.tsx"],x="/meus-contratos/contrato/novo/[id]/page",u={require:a,loadChunk:()=>Promise.resolve()},g=new d({definition:{kind:s.x.APP_PAGE,page:"/meus-contratos/contrato/novo/[id]/page",pathname:"/meus-contratos/contrato/novo/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},24044:(e,r,a)=>{Promise.resolve().then(a.bind(a,57496))},57496:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>Register});var t=a(60080),s=a(97669),i=a(47956),o=a(9885),n=a(32307),l=a(57086),d=a(66558),c=a(99986),m=a(96413),x=a(50298);let u=x.Ry().shape({isSCP:x.O7(),name:x.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:x.Z_().required("Campo obrigat\xf3rio"),email:x.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),rg:x.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),phoneNumber:x.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:x.Z_().required("Data de nascimento obrigat\xf3ria").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,t,s]=e.split("-");if(!r||!t||!s||4!==r.length)return!1;let i=Number(r);if(isNaN(i)||i<1900||i>new Date().getFullYear())return!1;let o=new Date(e);return!(o>new Date||isNaN(o.getTime())||a(54986).m(e))}),motherName:x.Z_().min(3,"Nome da m\xe3e deve conter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),zipCode:x.Z_().required("Obrigat\xf3rio"),neighborhood:x.Z_().required("Obrigat\xf3rio"),state:x.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:x.Z_().required("Obrigat\xf3rio"),complement:x.Z_().default("").notRequired(),number:x.Z_().required("Obrigat\xf3rio"),street:x.Z_().required("Obrigat\xf3rio"),value:x.Z_().required("Obrigat\xf3rio"),term:x.Z_().required("Obrigat\xf3rio"),yield:x.Z_().required("Obrigat\xf3rio"),purchasedWith:x.Z_().required("Obrigat\xf3rio"),isDebenture:x.Z_().required("Obrigat\xf3rio"),initDate:x.Z_().required("Obrigat\xf3rio"),endDate:x.Z_().required("Obrigat\xf3rio"),profile:x.Z_().required("Obrigat\xf3rio"),bank:x.Z_().required("Obrigat\xf3rio"),agency:x.Z_().required("Obrigat\xf3rio"),accountNumber:x.Z_().required("Obrigat\xf3rio"),pix:x.Z_().notRequired(),issuer:x.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:x.Z_().required("Campo obrigat\xf3rio"),occupation:x.Z_().required("Campo obrigat\xf3rio"),amountQuotes:x.Z_().when("isSCP",(e,r)=>!0===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),observations:x.Z_().notRequired()}).required(),g=x.Ry().shape({isSCP:x.O7(),email:x.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),phoneNumber:x.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:x.Z_().required("Campo obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,t,s]=e.split("-");if(!r||!t||!s||4!==r.length)return!1;let i=Number(r);if(isNaN(i)||i<1900||i>new Date().getFullYear())return!1;let o=new Date(e);return!(o>new Date||isNaN(o.getTime())||a(54986).m(e))}),rg:x.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Obrigat\xf3rio"),ownerName:x.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),ownerDocument:x.Z_().min(3,"O nome da m\xe3e deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),motherName:x.Z_().required("Campo obrigat\xf3rio"),issuer:x.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:x.Z_().required("Campo obrigat\xf3rio"),occupation:x.Z_().required("Campo obrigat\xf3rio"),name:x.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:x.Z_().required("Campo obrigat\xf3rio"),companyType:x.Z_().required("Obrigat\xf3rio"),zipCode:x.Z_().required("Obrigat\xf3rio"),neighborhood:x.Z_().required("Obrigat\xf3rio"),state:x.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:x.Z_().required("Obrigat\xf3rio"),complement:x.Z_().default(""),number:x.Z_().required("Obrigat\xf3rio"),street:x.Z_().required("Obrigat\xf3rio"),value:x.Z_().required("Obrigat\xf3rio"),term:x.Z_().required("Obrigat\xf3rio"),yield:x.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchasedWith:x.Z_().required("Obrigat\xf3rio"),initDate:x.Z_().required("Obrigat\xf3rio"),endDate:x.Z_().required("Obrigat\xf3rio"),profile:x.Z_().required("Obrigat\xf3rio"),isDebenture:x.Z_().required("Obrigat\xf3rio"),amountQuotes:x.Z_().when("isSCP",(e,r)=>!0===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),bank:x.Z_().required("Obrigat\xf3rio"),agency:x.Z_().required("Obrigat\xf3rio"),accountNumber:x.Z_().required("Obrigat\xf3rio"),pix:x.Z_().notRequired(),companyNumber:x.Z_().required("Campo obrigat\xf3rio"),companyComplement:x.Z_().default(""),companyCity:x.Z_().required("Campo obrigat\xf3rio"),companyState:x.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Campo obrigat\xf3rio"),companyStreet:x.Z_().required("Campo obrigat\xf3rio"),companyZipCode:x.Z_().required("Campo obrigat\xf3rio"),companyNeighborhood:x.Z_().required("Campo obrigat\xf3rio"),observations:x.Z_().notRequired()}).required();var p=a(27017),h=a(69145),b=a(24577),f=a(34751),v=a(9993),N=a(64731),j=a.n(N),y=a(17871),Z=a(90682),w=a(40509),C=a(34944),D=a(51778),_=a(28303),q=a(85814);let editContract=async(e,r)=>await q.Z.put(`/contract/${e}/edit`,r);var O=a(57114);let S=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}];function BusinessRegister({modalityContract:e,contractData:r,contractId:a}){let[s,i]=(0,o.useState)(!1),x=(0,Z.e)(),u=(0,O.useRouter)(),N=(0,o.useMemo)(()=>{if(!r)return{};let a=r.investor&&"business"===r.investor.type?r.investor:void 0;return{ownerName:a?.responsibleOwner?.name||"",ownerDocument:a?.responsibleOwner?.document||"",rg:a?.responsibleOwner?.rg||"",issuer:a?.responsibleOwner?.issuingAgency||"",placeOfBirth:a?.responsibleOwner?.nationality||"",occupation:a?.responsibleOwner?.occupation||"",motherName:a?.responsibleOwner?.motherName||"",dtBirth:a?.responsibleOwner?.birthDate||"",phoneNumber:a?.responsibleOwner?.phone?.startsWith("55")?(0,m.gP)(a.responsibleOwner.phone.replace("55","")):a?.responsibleOwner?.phone?(0,m.gP)(a.responsibleOwner.phone):"",email:a?.responsibleOwner?.email||"",zipCode:a?.responsibleOwner?.address?.zipcode||"",neighborhood:a?.responsibleOwner?.address?.neighborhood||"",street:a?.responsibleOwner?.address?.street||"",city:a?.responsibleOwner?.address?.city||"",state:a?.responsibleOwner?.address?.state||"",number:a?.responsibleOwner?.address?.number||"",complement:a?.responsibleOwner?.address?.complement||"",name:a?.name||"",document:a?.document||"",companyType:a?.businessType||"",companyZipCode:a?.address?.zipcode||"",companyNeighborhood:a?.address?.neighborhood||"",companyStreet:a?.address?.street||"",companyCity:a?.address?.city||"",companyState:a?.address?.state||"",companyNumber:a?.address?.number||"",companyComplement:a?.address?.complement||"",value:r.investment?.value?String(r.investment.value):"",yield:r.investment?.yield||0,term:r.investment?.term?String(r.investment.term):"",initDate:r.investment?.start?String(r.investment.start).slice(0,10):"",endDate:r.investment?.end?String(r.investment.end).slice(0,10):"",amountQuotes:r.investment?.quotesAmount?String(r.investment.quotesAmount):"",purchasedWith:r.investment?.purchasedWith||"",isDebenture:r.investment?.isDebenture?"s":"n",isSCP:"SCP"===e,profile:"moderate",bank:r.bankAccount?.bank||"",agency:r.bankAccount?.agency||"",accountNumber:r.bankAccount?.account||"",pix:r.bankAccount?.pix?.replace("+","")||""}},[r,e]),{register:q,handleSubmit:M,watch:A,setValue:P,reset:E,formState:{errors:T,isValid:I}}=(0,d.cI)({defaultValues:N,resolver:(0,l.X)(g),mode:"all"}),R=(0,C.D)({mutationFn:async({contractId:e,data:r})=>await editContract(e,r),onSuccess:()=>{f.Am.success("Contrato atualizado com sucesso!"),u.push("/meus-contratos"),E()},onError:e=>(0,b.Z)(e,"Tivemos um erro ao atualizar o contrato")});(0,D.a)({queryKey:["address",A("zipCode")],queryFn:async()=>{let e=await (0,w.x)((0,m.p4)(A("zipCode")||""));e&&(P("neighborhood",e.neighborhood,{shouldValidate:!0}),P("city",e.city,{shouldValidate:!0}),P("state",e.state,{shouldValidate:!0}),P("street",e.street,{shouldValidate:!0}))},enabled:A("zipCode")?.length===9}),(0,D.a)({queryKey:["companyAddress",A("companyZipCode")],queryFn:async()=>{let e=await (0,w.x)((0,m.p4)(A("companyZipCode")||""));e&&(P("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),P("companyStreet",e.street,{shouldValidate:!0}),P("companyCity",e.city,{shouldValidate:!0}),P("companyState",e.state,{shouldValidate:!0}))},enabled:A("companyZipCode")?.length===9});let onSubmit=async r=>{if(!s)return f.Am.error("Aceite os termos para liberar a atualiza\xe7\xe3o do contrato!");let t={role:x.name,personType:"PJ",contractType:e,investment:{amount:(0,y.Z)(r.value),monthlyRate:Number(r.yield),durationInMonths:Number(r.term),paymentMethod:r.purchasedWith,endDate:r.endDate,startDate:r.initDate,profile:r.profile,quotaQuantity:"SCP"===e?Number(r.amountQuotes):void 0,isDebenture:"s"===r.isDebenture},bankAccount:{bank:r.bank,agency:r.agency,account:r.accountNumber,pix:r.pix||void 0},company:{corporateName:r.name,cnpj:(0,m.p4)(r.document),type:r.companyType,address:{street:r.companyStreet,neighborhood:r.companyNeighborhood,city:r.companyCity,state:r.companyState,postalCode:(0,m.p4)(r.companyZipCode),number:r.companyNumber,complement:r.companyComplement||""},representative:{fullName:(0,_.Z)(r.ownerName),cpf:(0,m.p4)(r.ownerDocument),rg:r.rg,issuingAgency:r.issuer,nationality:r.placeOfBirth,occupation:(0,_.Z)(r.occupation),birthDate:r.dtBirth,email:r.email,phone:`55${(0,m.p4)(r.phoneNumber)}`,motherName:(0,_.Z)(r.motherName),address:{street:r.street,neighborhood:r.neighborhood,city:r.city,state:r.state,postalCode:(0,m.p4)(r.zipCode),number:r.number,complement:r.complement||""}}}};await R.mutateAsync({contractId:a,data:t},{onSuccess:()=>{E(),P("initDate",j()().format("YYYY-MM-DD"))}})};return(0,o.useEffect)(()=>{if(A("initDate")&&A("term")&&Number(A("term"))>0){let e=j()(A("initDate")),r=e.add(Number(A("term")),"months").format("YYYY-MM-DD");P("endDate",r)}},[A("term"),P]),(0,o.useEffect)(()=>{if(A("initDate")&&A("endDate")){let e=j()(A("initDate")),r=j()(A("endDate")),a=r.diff(e,"months");a>0&&P("term",String(a))}},[A("initDate"),A("endDate"),P]),t.jsx("div",{children:(0,t.jsxs)("form",{action:"",onSubmit:M(onSubmit),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[t.jsx(v.Z,{title:"Dados Pessoais - Representante",children:(0,t.jsxs)("div",{className:"flex flex-col gap-5",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"ownerName",width:"100%",error:!!T.ownerName,errorMessage:T?.ownerName?.message,label:"Nome"})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"ownerDocument",width:"100%",error:!!T.ownerDocument,errorMessage:T?.ownerDocument?.message,label:"CPF",setValue:e=>P("ownerDocument",(0,m.VL)(e||""),{shouldValidate:!0})})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"rg",width:"100%",error:!!T.rg,errorMessage:T?.rg?.message,label:"RG"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"issuer",width:"100%",error:!!T.issuer,errorMessage:T?.issuer?.message,label:"Org\xe3o emissor"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"placeOfBirth",width:"100%",error:!!T.placeOfBirth,errorMessage:T?.placeOfBirth?.message,label:"Nacionalidade"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"occupation",width:"100%",error:!!T.occupation,errorMessage:T?.occupation?.message,label:"Ocupa\xe7\xe3o"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"motherName",width:"100%",error:!!T.motherName,errorMessage:T?.motherName?.message,label:"Nome da m\xe3e"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[(0,t.jsxs)("div",{style:{width:"200px"},children:[(0,t.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",t.jsx("b",{className:"text-red-500 font-light text-sm",children:T.dtBirth&&`- ${T.dtBirth.message}`})]}),t.jsx("input",{...q("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",className:`h-12 w-full px-4 text-white rounded-xl ${T.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{width:"100%",register:q,name:"phoneNumber",error:!!T.phoneNumber,errorMessage:T?.phoneNumber?.message,label:"Celular",maxLength:15,setValue:e=>P("phoneNumber",(0,m.gP)(e||""),{shouldValidate:!0})})})]}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"email",width:"100%",error:!!T.email,errorMessage:T?.email?.message,label:"E-mail"})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"zipCode",width:"100%",error:!!T.zipCode,errorMessage:T?.zipCode?.message,label:"CEP",setValue:e=>{P("zipCode",(0,m.Tc)(e),{shouldValidate:!0})}})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,maxLength:2,setValue:e=>P("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"100%",error:!!T.state,errorMessage:T?.state?.message,label:"Estado"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"city",width:"100%",error:!!T.city,errorMessage:T?.city?.message,label:"Cidade"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"neighborhood",width:"100%",error:!!T.neighborhood,errorMessage:T?.neighborhood?.message,label:"Bairro"})})]}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"street",width:"100%",error:!!T.street,errorMessage:T?.street?.message,label:"Rua"})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"number",width:"100%",error:!!T.number,errorMessage:T?.number?.message,label:"N\xfamero"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"complement",width:"100%",error:!!T.complement,errorMessage:T?.complement?.message,label:"Complemento"})})]})]})}),t.jsx(v.Z,{title:"Dados da empresa",children:(0,t.jsxs)("div",{className:"flex flex-col gap-5",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"name",width:"100%",error:!!T.name,errorMessage:T?.name?.message,label:"Raz\xe3o Social"})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"document",width:"100%",error:!!T.document,errorMessage:T?.document?.message,label:"CNPJ",setValue:e=>P("document",(0,m.PK)(e||""),{shouldValidate:!0})})}),t.jsx("div",{className:"flex-1",children:t.jsx(p.Z,{width:"100%",name:"companyType",register:q,options:S,error:!!T.companyType,errorMessage:T?.companyType?.message,label:"Tipo"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"companyZipCode",width:"100%",error:!!T.companyZipCode,errorMessage:T?.companyZipCode?.message,label:"CEP",setValue:e=>{P("companyZipCode",(0,m.Tc)(e),{shouldValidate:!0})}})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,maxLength:2,setValue:e=>P("companyState",String(e).toUpperCase(),{shouldValidate:!0}),name:"companyState",width:"100%",error:!!T.companyState,errorMessage:T?.companyState?.message,label:"Estado"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"companyCity",width:"100%",error:!!T.companyCity,errorMessage:T?.companyCity?.message,label:"Cidade"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"companyNeighborhood",width:"100%",error:!!T.companyNeighborhood,errorMessage:T?.companyNeighborhood?.message,label:"Bairro"})})]}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"companyStreet",width:"100%",error:!!T.companyStreet,errorMessage:T?.companyStreet?.message,label:"Rua"})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"companyNumber",width:"100%",error:!!T.companyNumber,errorMessage:T?.companyNumber?.message,label:"N\xfamero"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"companyComplement",width:"100%",error:!!T.companyComplement,errorMessage:T?.companyComplement?.message,label:"Complemento"})})]})]})}),t.jsx(v.Z,{title:"Dados de Investimento",children:(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"value",width:"100%",error:!!T.value,errorMessage:T?.value?.message,label:"Valor",setValue:e=>P("value",(0,m.Ht)(e||""),{shouldValidate:!0})})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,type:"number",name:"term",width:"100%",error:!!T.term,errorMessage:T?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,type:"number",name:"yield",width:"100%",error:!!T.yield,errorMessage:T?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2,5",setValue:e=>P("yield",Number(e)||0,{shouldValidate:!0})})}),t.jsx("div",{className:"flex-1",children:t.jsx(p.Z,{width:"100%",name:"purchasedWith",register:q,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!T?.purchasedWith,errorMessage:T?.purchasedWith?.message,label:"Comprar com"})}),"SCP"===e&&t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,type:"number",name:"amountQuotes",width:"100%",error:!!T.amountQuotes,errorMessage:T?.amountQuotes?.message,label:"Quantidade de cotas"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{type:"date",register:q,name:"initDate",width:"100%",error:!!T.initDate,errorMessage:T?.initDate?.message,label:"Inicio do contrato"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{type:"date",register:q,value:A("endDate")||"",name:"endDate",width:"100%",disabled:!0,label:"Final do contrato"})}),t.jsx("div",{className:"flex-1",children:t.jsx(p.Z,{width:"100%",name:"profile",register:q,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!T.profile,errorMessage:T?.profile?.message,label:"Perfil"})}),t.jsx("div",{className:"flex-1",children:t.jsx(p.Z,{width:"100%",name:"isDebenture",register:q,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!T.isDebenture,errorMessage:T?.isDebenture?.message,label:"Deb\xeanture"})})]})}),t.jsx(v.Z,{title:"Dados bancarios",children:(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"bank",width:"100%",error:!!T.bank,errorMessage:T?.bank?.message,label:"Banco"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"agency",width:"100%",error:!!T.agency,errorMessage:T?.agency?.message,label:"Ag\xeancia"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"accountNumber",width:"100%",error:!!T.accountNumber,errorMessage:T?.accountNumber?.message,label:"Conta"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"pix",width:"100%",error:!!T.pix,errorMessage:T?.pix?.message,label:"Pix"})})]})}),t.jsx(v.Z,{title:"Observa\xe7\xf5es",children:t.jsx("div",{children:t.jsx(h.Z,{name:"observations",register:q})})}),t.jsx(v.Z,{title:"Dados para Dep\xf3sito",children:(0,t.jsxs)("div",{className:"m-auto border-none",children:[t.jsx("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,t.jsxs)("div",{className:"mb-5",children:[t.jsx("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),t.jsx("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,t.jsxs)("div",{className:"flex mt-5",children:[t.jsx("input",{type:"checkbox",checked:s,className:"cursor-pointer",onChange:()=>i(!s)}),t.jsx("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),t.jsx("div",{className:"md:w-52 mb-10",children:t.jsx(c.Z,{label:"Enviar",size:"lg",loading:R.isPending,disabled:!s||R.isPending||!I||!A("endDate")})})]})})}function PhysicalRegister({modalityContract:e,contractData:r,contractId:a}){let[s,i]=(0,o.useState)(!1),x=(0,Z.e)(),g=(0,O.useRouter)(),N=(0,o.useMemo)(()=>{if(!r)return{};let e=r.investor&&"physical"===r.investor.type?r.investor:void 0;return{name:e?.name||"",document:e?.document||"",rg:e?.rg||"",issuer:e?.issuingAgency||"",placeOfBirth:e?.nationality||"",occupation:e?.occupation||"",phoneNumber:e?.phone?.startsWith("55")?(0,m.gP)(e.phone.replace("55","")):e?.phone?(0,m.gP)(e.phone):"",dtBirth:e?.birthDate||"",email:e?.email||"",motherName:e?.motherName||"",zipCode:e?.address?.zipcode||"",neighborhood:e?.address?.neighborhood||"",street:e?.address?.street||"",city:e?.address?.city||"",state:e?.address?.state||"",number:e?.address?.number||"",complement:e?.address?.complement||"",value:r.investment?.value?String(r.investment.value):"",yield:r.investment?.yield?String(r.investment.yield):"",term:r.investment?.term?String(r.investment.term):"",initDate:r.investment?.start?String(r.investment.start).slice(0,10):"",endDate:r.investment?.end?String(r.investment.end).slice(0,10):"",amountQuotes:r.investment?.quotesAmount?String(r.investment.quotesAmount):"",purchasedWith:r.investment?.purchasedWith||"",isDebenture:r.investment?.isDebenture?"s":"n",isSCP:r.investment?.modality==="SCP",bank:r.bankAccount?.bank||"",agency:r.bankAccount?.agency||"",accountNumber:r.bankAccount?.account||"",pix:r.bankAccount?.pix?.replace("+","")||""}},[r]),{register:q,handleSubmit:S,watch:M,setValue:A,reset:P,formState:{errors:E,isValid:T}}=(0,d.cI)({defaultValues:N,resolver:(0,l.X)(u),mode:"all"}),I=(0,C.D)({mutationFn:async({contractId:e,data:r})=>await editContract(e,r),onSuccess:()=>{f.Am.success("Contrato atualizado com sucesso!"),g.push("/meus-contratos"),P()},onError:e=>(0,b.Z)(e,"Tivemos um erro ao atualizar o contrato")});(0,D.a)({queryKey:["address",M("zipCode")],queryFn:async()=>{let e=await (0,w.x)((0,m.p4)(M("zipCode")||""));e&&(A("neighborhood",e.neighborhood,{shouldValidate:!0}),A("city",e.city,{shouldValidate:!0}),A("state",e.state,{shouldValidate:!0}),A("street",e.street,{shouldValidate:!0}))},enabled:M("zipCode")?.length===9});let onSubmit=async r=>{if(!s)return f.Am.error("Aceite os termos para liberar a atualiza\xe7\xe3o do contrato!");let t={role:x.name,personType:"PF",contractType:e,investment:{amount:(0,y.Z)(r.value),monthlyRate:Number(r.yield),durationInMonths:Number(r.term),paymentMethod:r.purchasedWith,endDate:r.endDate,startDate:r.initDate,profile:r.profile,quotaQuantity:"SCP"===e?Number(r.amountQuotes):void 0,isDebenture:"s"===r.isDebenture},bankAccount:{bank:r.bank,agency:r.agency,account:r.accountNumber,pix:r.pix||void 0},individual:{fullName:(0,_.Z)(r.name),cpf:(0,m.p4)(r.document),rg:r.rg,issuingAgency:r.issuer,nationality:r.placeOfBirth,occupation:(0,_.Z)(r.occupation),birthDate:r.dtBirth,email:r.email,phone:`55${(0,m.p4)(r.phoneNumber)}`,motherName:(0,_.Z)(r.motherName),address:{street:r.street,neighborhood:r.neighborhood,city:r.city,state:r.state,postalCode:(0,m.p4)(r.zipCode),number:r.number,complement:r.complement||""}}};await I.mutateAsync({contractId:a,data:t})};return(0,o.useEffect)(()=>{if(M("initDate")&&M("term")&&Number(M("term"))>0){let e=j()(M("initDate")),r=e.add(Number(M("term")),"months").format("YYYY-MM-DD");A("endDate",r)}},[M("term"),A]),(0,o.useEffect)(()=>{if(M("initDate")&&M("endDate")){let e=j()(M("initDate")),r=j()(M("endDate")),a=r.diff(e,"months");a>0&&A("term",String(a))}},[M("initDate"),M("endDate"),A]),t.jsx("div",{children:(0,t.jsxs)("form",{action:"",onSubmit:S(onSubmit),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[t.jsx(v.Z,{title:"Dados Pessoais",children:(0,t.jsxs)("div",{className:"flex flex-col gap-5",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"name",width:"100%",error:!!E.name,errorMessage:E?.name?.message,label:"Nome completo"})}),(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"rg",width:"100%",error:!!E.rg,errorMessage:E?.rg?.message,label:"Identidade"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{width:"100%",register:q,name:"phoneNumber",maxLength:15,error:!!E.phoneNumber,errorMessage:E?.phoneNumber?.message,label:"Celular",setValue:e=>A("phoneNumber",(0,m.gP)(e||""),{shouldValidate:!0})})})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"document",width:"100%",error:!!E.document,errorMessage:E?.document?.message,label:"CPF",setValue:e=>A("document",(0,m.VL)(e||""),{shouldValidate:!0})})}),(0,t.jsxs)("div",{style:{width:"200px"},children:[(0,t.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",t.jsx("b",{className:"text-red-500 font-light text-sm",children:E.dtBirth&&`- ${E.dtBirth.message}`})]}),t.jsx("input",{...q("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let r=e.target;r.value.length>10&&(r.value=r.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${E.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})]}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"motherName",width:"100%",error:!!E.motherName,errorMessage:E?.motherName?.message,label:"Nome da m\xe3e"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"email",width:"100%",error:!!E.email,errorMessage:E?.email?.message,label:"E-mail"})}),(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"issuer",width:"100%",error:!!E.issuer,errorMessage:E?.issuer?.message,label:"Org\xe3o emissor"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"placeOfBirth",width:"100%",error:!!E.placeOfBirth,errorMessage:E?.placeOfBirth?.message,label:"Nacionalidade"})})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"occupation",width:"100%",error:!!E.occupation,errorMessage:E?.occupation?.message,label:"Ocupa\xe7\xe3o"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"zipCode",width:"100%",error:!!E.zipCode,errorMessage:E?.zipCode?.message,label:"CEP",setValue:e=>{let r=(0,m.Tc)(e);A("zipCode",r,{shouldValidate:!0})}})})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,maxLength:2,setValue:e=>A("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"100%",error:!!E.state,errorMessage:E?.state?.message,label:"Estado"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"city",width:"100%",error:!!E.city,errorMessage:E?.city?.message,label:"Cidade"})})]}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"neighborhood",width:"100%",error:!!E.neighborhood,errorMessage:E?.neighborhood?.message,label:"Bairro"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"street",width:"100%",error:!!E.street,errorMessage:E?.street?.message,label:"Endere\xe7o"})}),(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"number",width:"100%",error:!!E.number,errorMessage:E?.number?.message,label:"N\xfamero"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"complement",width:"100%",error:!!E.complement,errorMessage:E?.complement?.message,label:"Complemento"})})]})]})}),t.jsx(v.Z,{title:"Dados de Investimento",children:(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"value",width:"100%",error:!!E.value,errorMessage:E?.value?.message,label:"Valor",setValue:e=>A("value",(0,m.Ht)(e||""),{shouldValidate:!0})})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,type:"number",name:"term",width:"100%",error:!!E.term,errorMessage:E?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,type:"text",name:"yield",width:"100%",error:!!E.yield,errorMessage:E?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"})}),t.jsx("div",{className:"flex-1",children:t.jsx(p.Z,{width:"100%",name:"purchasedWith",register:q,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!E?.purchasedWith,errorMessage:E?.purchasedWith?.message,label:"Comprar com"})}),"SCP"===e&&t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,type:"number",name:"amountQuotes",width:"100%",error:!!E.amountQuotes,errorMessage:E?.amountQuotes?.message,label:"Quantidade de cotas"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{type:"date",register:q,name:"initDate",width:"100%",error:!!E.initDate,errorMessage:E?.initDate?.message,label:"Inicio do contrato"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{type:"date",register:q,value:M("endDate")||"",name:"endDate",width:"100%",disabled:!0,label:"Final do contrato"})}),t.jsx("div",{className:"flex-1",children:t.jsx(p.Z,{width:"100%",name:"profile",register:q,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!E.profile,errorMessage:E?.profile?.message,label:"Perfil"})}),t.jsx("div",{className:"flex-1",children:t.jsx(p.Z,{width:"100%",name:"isDebenture",register:q,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!E.isDebenture,errorMessage:E?.isDebenture?.message,label:"Deb\xeanture"})})]})}),t.jsx(v.Z,{title:"Dados bancarios",children:(0,t.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-5 lg:gap-x-24",children:[t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"bank",width:"100%",error:!!E.bank,errorMessage:E?.bank?.message,label:"Banco"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"agency",width:"100%",error:!!E.agency,errorMessage:E?.agency?.message,label:"Ag\xeancia"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"accountNumber",width:"100%",error:!!E.accountNumber,errorMessage:E?.accountNumber?.message,label:"Conta"})}),t.jsx("div",{className:"flex-1",children:t.jsx(n.Z,{register:q,name:"pix",width:"100%",error:!!E.pix,errorMessage:E?.pix?.message,label:"Pix"})})]})}),t.jsx(v.Z,{title:"Observa\xe7\xf5es",children:t.jsx("div",{children:t.jsx(h.Z,{name:"observations",register:q})})}),t.jsx(v.Z,{title:"Dados para Dep\xf3sito",children:(0,t.jsxs)("div",{className:"m-auto border-none",children:[t.jsx("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),t.jsx("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,t.jsxs)("div",{className:"mb-5",children:[t.jsx("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),t.jsx("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,t.jsxs)("div",{className:"flex mt-5",children:[t.jsx("input",{type:"checkbox",checked:s,className:"cursor-pointer",onChange:()=>i(!s)}),t.jsx("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),t.jsx("div",{className:"md:w-52 mb-10",children:t.jsx(c.Z,{label:"Enviar",size:"lg",loading:I.isPending,disabled:!s||I.isPending||!T||!M("endDate")})})]})})}var M=a(33806),A=a(84070);let getContractDetail=async e=>{let r=await q.Z.get(`/contract/get-detail/${e}`);return r.data};var P=a(80223);function ReusableSelect({value:e,label:r,children:a}){return(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("p",{className:"text-white mb-1",children:r}),t.jsx(P.Z,{value:e,disabled:!0,onChange:()=>{},children:a})]})}function Register(){let{id:e}=(0,O.useParams)(),r=(0,O.useRouter)(),a=(0,A.NL)();(0,o.useEffect)(()=>()=>a.removeQueries({queryKey:["contract",e]}),[a,e]);let{data:n,isLoading:l,error:d}=(0,D.a)({queryKey:["new-contract",e],queryFn:async()=>{let a=await getContractDetail(e);if(!a)return f.Am.error("Dados do contrato n\xe3o encontrados."),r.push("/meus-contratos"),null;let t=a.investment?.status||"";return M.f.includes(t)?a:(f.Am.error("Este contrato n\xe3o pode ser editado devido ao seu status atual."),r.push("/meus-contratos"),null)},enabled:!!e,staleTime:0,refetchOnMount:"always"});return((0,o.useEffect)(()=>{d&&(console.error("Erro completo:",d),(0,b.Z)(d,"Erro ao buscar dados do contrato."),r.push("/meus-contratos"))},[d,r]),l)?(0,t.jsxs)(t.Fragment,{children:[t.jsx(s.Z,{}),t.jsx(i.Z,{children:t.jsx("div",{className:"m-3 flex justify-center items-center h-[80vh]",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF9900] mx-auto mb-4"}),t.jsx("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})]}):d?null:(0,t.jsxs)("div",{children:[t.jsx(s.Z,{}),t.jsx(i.Z,{children:(0,t.jsxs)("div",{className:"md:px-10",children:[t.jsx(v.Z,{title:"Tipo de contrato",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between lg:gap-24 gap-5",children:[(0,t.jsxs)(ReusableSelect,{value:n?.investor?.type||"physical",label:"Tipo de conta",children:[t.jsx("option",{value:"physical",children:"Pessoa F\xedsica"}),t.jsx("option",{value:"business",children:"Pessoa Jur\xeddica"})]}),(0,t.jsxs)(ReusableSelect,{value:n?.investment?.type==="scp"?"SCP":"MUTUO",label:"Tipo de contrato",children:[t.jsx("option",{value:"MUTUO",children:"M\xfatuo"}),t.jsx("option",{value:"SCP",children:"SCP"})]})]})}),(0,t.jsxs)("div",{children:[n?.investor?.type==="physical"&&t.jsx(PhysicalRegister,{modalityContract:n?.investment?.modality||"MUTUO",contractData:n,contractId:e}),n?.investor?.type==="business"&&t.jsx(BusinessRegister,{modalityContract:n?.investment?.modality||"MUTUO",contractData:n,contractId:e})]})]})})]})}},69145:(e,r,a)=>{"use strict";a.d(r,{Z:()=>InputTextArea});var t=a(60080),s=a(9885);function InputTextArea({setValue:e,value:r,error:a,errorMessage:i,width:o="100%",register:n,name:l,placeholder:d="",className:c=""}){return(0,s.useEffect)(()=>{},[o]),t.jsx("div",{className:"input",style:{width:o},children:t.jsx("textarea",{...n&&{...n(l)},value:r&&r,placeholder:d,id:l,onChange:({target:r})=>{e&&e(r.value)},className:`${c} h-12 w-full min-h-40 p-2 text-white rounded-xl ${a?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})})}},33806:(e,r,a)=>{"use strict";a.d(r,{f:()=>i,z:()=>s});var t=a(32775);let s=[t.rd.REJECTED_BY_AUDIT,t.rd.AWAITING_AUDIT],i=[t.rd.AWAITING_INVESTOR_SIGNATURE]},40509:(e,r,a)=>{"use strict";a.d(r,{x:()=>getZipCode});var t=a(21145);async function getZipCode(e){let r=e.replace(/\D/g,"");if(8!==r.length)return null;try{let e=await t.Z.get(`https://viacep.com.br/ws/${r}/json/`),a=e.data;if(a&&!a.erro)return{neighborhood:a.bairro||"",street:a.logradouro||"",city:a.localidade||"",state:a.uf||""};return null}catch(e){return console.error("Erro ao buscar o CEP:",e),null}}},28303:(e,r,a)=>{"use strict";a.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});let __WEBPACK_DEFAULT_EXPORT__=function(e){return e.trim().replace(/\s+/g," ").toLowerCase().split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}},54986:(e,r,a)=>{"use strict";a.d(r,{m:()=>isUnderage});var t=a(64731),s=a.n(t);function isUnderage(e){let r=s()(e),a=s()().diff(r,"years");return a<18}},6987:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>o,__esModule:()=>i,default:()=>l});var t=a(17536);let s=(0,t.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\meus-contratos\contrato\novo\[id]\page.tsx`),{__esModule:i,$$typeof:o}=s,n=s.default,l=n}};var r=require("../../../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),a=r.X(0,[4103,6426,4731,8813,6558,3356,4944,7207,278,7669,2307,87,9327],()=>__webpack_exec__(86494));module.exports=a})();