"use strict";exports.id=4944,exports.ids=[4944],exports.modules={34944:(t,e,s)=>{s.d(e,{D:()=>useMutation});var i=s(9885),r=s(65226),n=s(18602),u=s(36995),o=s(93224),a=class extends u.l{#t;#e=void 0;#s;#i;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#r()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,o.VS)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,o.Ym)(e.mutation<PERSON>ey)!==(0,o.Ym)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(t){this.#r(),this.#n(t)}getCurrentResult(){return this.#e}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#r(),this.#n()}mutate(t,e){return this.#i=e,this.#s?.removeObserver(this),this.#s=this.#t.getMutationCache().build(this.#t,this.options),this.#s.addObserver(this),this.#s.execute(t)}#r(){let t=this.#s?.state??(0,r.R)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#n(t){n.Vr.batch(()=>{if(this.#i&&this.hasListeners()){let e=this.#e.variables,s=this.#e.context;t?.type==="success"?(this.#i.onSuccess?.(t.data,e,s),this.#i.onSettled?.(t.data,null,e,s)):t?.type==="error"&&(this.#i.onError?.(t.error,e,s),this.#i.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#e)})})}},h=s(84070);function useMutation(t,e){let s=(0,h.NL)(e),[r]=i.useState(()=>new a(s,t));i.useEffect(()=>{r.setOptions(t)},[r,t]);let u=i.useSyncExternalStore(i.useCallback(t=>r.subscribe(n.Vr.batchCalls(t)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),c=i.useCallback((t,e)=>{r.mutate(t,e).catch(o.ZT)},[r]);if(u.error&&(0,o.L3)(r.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:c,mutateAsync:u.mutate}}}};