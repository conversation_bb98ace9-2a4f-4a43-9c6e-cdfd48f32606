(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2864],{4498:function(e,a,t){Promise.resolve().then(t.bind(t,5627))},5627:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return Payment}});var l=t(7437),s=t(3877),n=t(8637),r=t(6691),d=t.n(r),o=t(7120),c=t(2265),i=t(4033),x=t(3862),m=t(2067),u=t.n(m),p=t(5968),h=t(8647),v=t(3014),j=t(4568),b=t(8689),N=t(6654),f=t(5044),y=t(365),g=t(5838),w=t(6121),D=t(4346),F=t(6041),P=t(3256),C=t(8700);function Payment(e){var a;let{params:t}=e,[r,m]=(0,c.useState)(""),[V,L]=(0,c.useState)("e"),[R,Z]=(0,c.useState)(!1),[k,A]=(0,c.useState)(!1),[S,B]=(0,c.useState)(!0),[I,_]=(0,c.useState)(),[E,z]=(0,c.useState)(),U=(0,i.useRouter)(),O=(0,P.e)(),DataValues=e=>{let{label:a,value:t}=e;return(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-bold text-sm",children:a}),(0,l.jsx)(b.Z,{maxWidth:"200px",minWidth:"80px",loading:S,height:"25px",children:(0,l.jsx)("p",{className:" text-md",children:t})})]})},getPaymentData=()=>{v.Am.info("Buscando dados do pagamento!",{toastId:"search",autoClose:!1}),j.Z.get("/income-payment-scheduled/".concat(t.id)).then(e=>{var a;z(e.data),(null===(a=e.data)||void 0===a?void 0:a.status)==="PAID"?L("p"):L("e"),v.Am.dismiss("search")}).catch(e=>{v.Am.error("Tivemos um erro ao buscar os dados do agendamento!",{toastId:"agendamento"})}).finally(()=>B(!1))};(0,c.useEffect)(()=>{getPaymentData()},[]);let W=(0,c.useCallback)(e=>{_(e);let a=URL.createObjectURL(e[0]);m(a)},[]),{getRootProps:q,getInputProps:M}=(0,h.uI)({onDrop:W,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&v.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{m("")}}),T=(null==E?void 0:null===(a=E.addendumData)||void 0===a?void 0:a.reduce((e,a)=>e+(Number(a.investmentValue)||0),0))||0,K=(Number(null==E?void 0:E.investmentValue)||0)+T;return(0,l.jsxs)("div",{className:"".concat(R?"fixed w-full":"relative"),children:[(0,l.jsx)(s.Z,{}),(0,l.jsx)(n.Z,{children:(0,l.jsxs)("div",{className:"w-full text-white",children:[(0,l.jsxs)("div",{className:"w-full text-white flex flex-col md:flex-row flex-wrap gap-6 justify-start",children:[(0,l.jsxs)("div",{className:"w-full sm:w-48 md:w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[(0,l.jsx)("p",{className:"my-4 font-bold text-xl",children:"Valor a ser Pago"}),(0,l.jsx)("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:(0,l.jsx)("div",{className:"flex w-full items-center justify-center py-3",children:(0,l.jsx)(b.Z,{loading:S,height:"25px",width:"100px",children:(0,l.jsx)("p",{className:"font-bold text-xl text-[#FF9900]",children:Number((null==E?void 0:E.paymentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})})})]}),(0,l.jsxs)("div",{className:"w-full sm:w-48 md:w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[(0,l.jsx)("p",{className:"my-4 font-bold text-xl",children:"Pagamento"}),(0,l.jsx)("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:(0,l.jsx)("div",{className:"flex w-full items-center justify-center py-3",children:(0,l.jsx)(b.Z,{loading:S,height:"25px",width:"100px",children:(0,l.jsxs)("p",{className:"font-bold text-xl text-[#FF9900]",children:["Dia ",(null==E?void 0:E.paymentDate)?u()(null==E?void 0:E.paymentDate).format("DD"):"00"]})})})})]}),"p"===V?(0,l.jsxs)("div",{onClick:()=>{(null==E?void 0:E.investorPayment.payment)&&window.open(null==E?void 0:E.investorPayment.payment.file,"_blank")},className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center flex flex-col items-center justify-center cursor-pointer",children:[(0,l.jsx)("p",{className:"mb-5 font-bold text-lg select-none",children:"Ultimo Comprovante"}),(0,l.jsx)(f.Z,{width:40,color:"#fff"})]}):(0,l.jsx)("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl flex items-center justify-center cursor-pointer px-4 py-6 text-center",...q(),children:(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center gap-3 w-full",children:[(0,l.jsx)("p",{className:"font-bold text-lg select-none text-white",children:r?"Comprovante anexado":"Anexar Comprovante"}),(0,l.jsx)("input",{type:"text",...M()}),r?(0,l.jsx)(y.Z,{width:40,color:"#fff"}):(0,l.jsx)(d(),{className:"select-none",src:o.Z,width:30,alt:""})]})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"my-5",children:"p"===V?(0,l.jsx)("div",{className:"bg-[#1ef97d31] w-28 border border-[#1EF97C] rounded-lg",children:(0,l.jsx)("p",{className:"text-center text-[#1EF97C] p-1",children:"Pago"})}):(0,l.jsx)("div",{className:"bg-[#ffb3382a] w-28 border border-[#FFB238] rounded-lg",children:(0,l.jsx)("p",{className:"text-center text-[#FFB238] p-1",children:"Pendente"})})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:"Dados Pessoais"}),(0,l.jsx)("div",{className:"mb-3 mt-2",children:(0,l.jsx)(DataValues,{label:"Nome",value:(null==E?void 0:E.investorPayment.name)||"N\xe3o encontrado"})}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3",children:[(0,l.jsx)(DataValues,{label:"E-mail",value:(null==E?void 0:E.investorPayment.email)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"CPF",value:(0,p.VL)((null==E?void 0:E.investorPayment.document)||"N\xe3o encontrado")}),null==E?void 0:E.advisors.map((e,a)=>(0,l.jsx)(DataValues,{label:"Consultor ".concat(a+1),value:e.name||"N\xe3o encontrado"},a))]}),(0,l.jsx)("div",{children:(0,l.jsx)(DataValues,{label:"Broker",value:(null==E?void 0:E.broker)||"N\xe3o encontrado"})})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es sobre o Investimento"}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2 items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-bold text-sm",children:"Valor Investido"}),(0,l.jsx)(b.Z,{maxWidth:"200px",minWidth:"80px",loading:S,height:"25px",children:(0,l.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,l.jsx)("p",{className:" text-md",children:K.toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsx)("div",{className:"cursor-pointer",children:(0,l.jsx)(d(),{alt:"",src:D.Z,width:15})})]})})]}),(0,l.jsx)(C.z,{className:"ml-[-15px]",onClick:()=>{Z(!0)},children:"Ver mais"}),(null==E?void 0:E.addendsProRata)&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(DataValues,{label:"Pro Rata",value:Number((null==E?void 0:E.addendsProRataValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-bold text-sm",children:"Valor Total"}),(0,l.jsx)(b.Z,{maxWidth:"200px",minWidth:"80px",loading:S,height:"25px",children:(0,l.jsx)("p",{className:"text-md text-[#FF9900]",children:(Number((null==E?void 0:E.investmentValue)||0)+((null==E?void 0:E.addendsProRataValue)||0)).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]}),(0,l.jsx)(DataValues,{label:"Porcentagem",value:"".concat((null==E?void 0:E.investmentYield)||"N\xe3o encontrado","%")}),(0,l.jsx)(DataValues,{label:"Rentabilidade",value:Number((null==E?void 0:E.paymentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es Banc\xe1rias"}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2",children:[(0,l.jsx)(DataValues,{label:"Banco",value:(null==E?void 0:E.investorPayment.account.bank)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"Ag\xeancia",value:(null==E?void 0:E.investorPayment.account.agency)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"Conta",value:(null==E?void 0:E.investorPayment.account.account)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"Chave PIX",value:(null==E?void 0:E.investorPayment.account.pixKey)||"N\xe3o encontrado"})]})]}),(0,l.jsxs)("div",{className:"mb-6 w-[30%]",children:[(0,l.jsx)("div",{children:(0,l.jsx)("p",{className:"text-xl font-bold mb-3",children:"Anexos"})}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0 mb-20",children:[(null==E?void 0:E.attachments.contract)&&(0,l.jsx)("div",{className:"w-full md:w-auto",children:(0,l.jsx)(F.Z,{file:E.attachments.contract,title:"Contrato"})}),(null==E?void 0:E.attachments.residenceProof)&&(0,l.jsx)("div",{className:"w-full md:w-auto",children:(0,l.jsx)(F.Z,{file:E.attachments.residenceProof,title:"Comprovante de resid\xeancia"})}),(null==E?void 0:E.attachments.rg)&&(0,l.jsx)("div",{className:"w-full md:w-auto",children:(0,l.jsx)(F.Z,{file:E.attachments.rg,title:"RG"})})]})]})]}),(0,l.jsxs)("div",{className:"w-full flex flex-wrap gap-4 justify-end mb-4",children:["p"!==V&&(0,l.jsx)("div",{children:(0,l.jsx)(C.z,{loading:k,onClick:()=>{if(A(!0),!r)return v.Am.warning("Para aprovar o pagamento \xe9 preciso anexar o comprovante!");let e=new FormData;I&&e.append("proof",I[0]),e.append("paymentId",(null==E?void 0:E.investorPayment.id)||""),v.Am.info("Aprovando o pagamento...",{toastId:"aprovando-pagamento"}),j.Z.post("/income-payment",e,{headers:{RoleId:O.roleId}}).then(e=>{v.Am.success("Pagamento aprovado!"),v.Am.dismiss("aprovando-pagamento"),U.back()}).catch(e=>{(0,N.Z)(e,"N\xe3o foi poss\xedvel aprovar o pagamento!")}).finally(()=>A(!1))},size:"lg",className:"w-full sm:w-auto",children:"Aprovar pagamento"})}),(0,l.jsx)("div",{children:(0,l.jsx)(C.z,{onClick:()=>{U.back()},size:"lg",variant:"secondary",children:"Voltar"})})]})]})]})}),(0,l.jsx)("div",{children:(0,l.jsx)(x.Z,{width:"7/12",openModal:R,setOpenModal:Z,children:(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"mb-5",children:[(0,l.jsx)("p",{className:"mb-2",children:"Dados do contrato principal."}),(0,l.jsxs)("table",{className:"border border-[#FF9900] w-full",children:[(0,l.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Rendimento"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),(0,l.jsxs)("tr",{className:"text-xs",children:[(0,l.jsx)("td",{className:"text-center px-4 py-2",children:Number((null==E?void 0:E.investmentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsxs)("td",{className:"text-center px-4 py-2",children:[null==E?void 0:E.investmentYield,"%"]}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:(0,w.Z)((null==E?void 0:E.contractStartDate)||"")}),(0,l.jsx)("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>{(null==E?void 0:E.attachments.contract)?window.open(null==E?void 0:E.attachments.contract,"_blanck"):v.Am.warning("N\xe3o conseguimos buscar os dados do contrato.")},children:"ver mais"})]})]})]}),(null==E?void 0:E.addendumData)&&(null==E?void 0:E.addendumData.length)>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)("p",{children:"Contratos aditivos adicionados no c\xe1lculo"})}),(0,l.jsxs)("table",{className:"border border-[#FF9900] w-full min-w-[600px]",children:[(0,l.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Valor rentabilizado"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Dias rentabilizados"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),null==E?void 0:E.addendumData.map(e=>(0,l.jsxs)("tr",{className:"text-xs",children:[(0,l.jsx)("td",{className:"text-center px-4 py-2",children:Number((null==e?void 0:e.investmentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:(0,w.Z)(e.date)}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:e.totalAmount?Number((null==e?void 0:e.totalAmount)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"}):"-"}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:"".concat(e.validDays," dias")}),(null==e?void 0:e.contractUrl)?(0,l.jsx)("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>window.open(e.contractUrl,"_blanck"),children:"ver mais"}):(0,l.jsx)("td",{className:"flex justify-center",children:(0,l.jsx)(g.Z,{className:"mt-1",color:"#FF9900",width:20})})]},e.id))]})]})]})})})]})}}},function(e){e.O(0,[6990,7326,8276,5371,6946,3151,1327,2971,7864,1744],function(){return e(e.s=4498)}),_N_E=e.O()}]);