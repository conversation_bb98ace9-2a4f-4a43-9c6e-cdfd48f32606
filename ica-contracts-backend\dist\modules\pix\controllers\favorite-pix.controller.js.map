{"version": 3, "file": "favorite-pix.controller.js", "sourceRoot": "/", "sources": ["modules/pix/controllers/favorite-pix.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAawB;AACxB,0EAAgE;AAGhE,sEAAgE;AAChE,4EAAsE;AACtE,sFAA+E;AAC/E,kFAA4E;AAC5E,wFAAkF;AAClF,kGAA2F;AAGpF,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEU,kBAAyC,EAEzC,kBAAgD,EAEhD,qBAA+C;QAJ/C,uBAAkB,GAAlB,kBAAkB,CAAuB;QAEzC,uBAAkB,GAAlB,kBAAkB,CAA8B;QAEhD,0BAAqB,GAArB,qBAAqB,CAA0B;IACtD,CAAC;IAKE,AAAN,KAAK,CAAC,WAAW,CACP,IAAuB,EACpB,OAAqB;QAEhC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,cAAc,CACT,KAA2B,EACzB,OAAqB;QAEhC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,kBAAkB,CACb,KAA+B,EAC7B,OAAqB;QAEhC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA5DY,sDAAqB;AAa1B;IAHL,IAAA,aAAI,EAAC,KAAK,CAAC;IACX,IAAA,iBAAQ,EAAC,mBAAU,CAAC,OAAO,CAAC;IAC5B,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADI,wCAAiB;;wDAYhC;AAIK;IAFL,IAAA,eAAM,GAAE;IACR,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADM,8CAAoB;;2DAYrC;AAGK;IAFL,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADM,uDAAwB;;+DAYzC;gCA3DU,qBAAqB;IADjC,IAAA,mBAAU,EAAC,cAAc,CAAC;IAGtB,WAAA,IAAA,eAAM,EAAC,gDAAqB,CAAC,CAAA;IAE7B,WAAA,IAAA,eAAM,EAAC,+DAA4B,CAAC,CAAA;IAEpC,WAAA,IAAA,eAAM,EAAC,sDAAwB,CAAC,CAAA;qCAHL,gDAAqB;QAErB,+DAA4B;QAEzB,sDAAwB;GAP9C,qBAAqB,CA4DjC", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  HttpCode,\r\n  HttpStatus,\r\n  Post,\r\n  UseGuards,\r\n  Request,\r\n  Inject,\r\n  HttpException,\r\n  Get,\r\n  Query,\r\n  Delete,\r\n} from '@nestjs/common';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport { AddFavoritePixDto } from '../dto/add-favorite-pix.dto';\r\nimport { DeleteFavoritePixDto } from '../dto/delete-favorite-pix.dto';\r\nimport { GetFavoritePixAccountDto } from '../dto/get-favorite-pix-account.dto';\r\nimport { AddFavoritePixService } from '../service/add-favorite-pix.service';\r\nimport { DeleteFavoritePixService } from '../service/delete-favorite-pix.service';\r\nimport { GetFavoritePixAccountService } from '../service/get-favorite-pix-account.service';\r\n\r\n@Controller('pix/favorite')\r\nexport class FavoritePixController {\r\n  constructor(\r\n    @Inject(AddFavoritePixService)\r\n    private addFavoriteService: AddFavoritePixService,\r\n    @Inject(GetFavoritePixAccountService)\r\n    private getFavoriteService: GetFavoritePixAccountService,\r\n    @Inject(DeleteFavoritePixService)\r\n    private deleteFavoriteService: DeleteFavoritePixService,\r\n  ) {}\r\n\r\n  @Post('add')\r\n  @HttpCode(HttpStatus.CREATED)\r\n  @UseGuards(JwtAuthGuard)\r\n  async addFavorite(\r\n    @Body() body: AddFavoritePixDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    try {\r\n      const data = await this.addFavoriteService.perform(body, request.user.id);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Delete()\r\n  @UseGuards(JwtAuthGuard)\r\n  async deleteFavorite(\r\n    @Query() query: DeleteFavoritePixDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    try {\r\n      const data = await this.deleteFavoriteService.perform(query, request.user.id);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n  @Get('account')\r\n  @UseGuards(JwtAuthGuard)\r\n  async getFavoriteAccount(\r\n    @Query() query: GetFavoritePixAccountDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    try {\r\n      const data = await this.getFavoriteService.perform(request.user.id);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}