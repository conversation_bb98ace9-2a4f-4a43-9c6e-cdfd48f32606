(()=>{var e={};e.id=9834,e.ids=[9834],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},87847:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>p,routeModule:()=>g,tree:()=>m});var t=a(73137),s=a(54647),o=a(4183),i=a.n(o),n=a(71775),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(r,d);let l=t.AppPageRouteModule,m=["",{children:["meus-contratos",{children:["contrato",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,96218)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],p=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\meus-contratos\\contrato\\[id]\\page.tsx"],c="/meus-contratos/contrato/[id]/page",u={require:a,loadChunk:()=>Promise.resolve()},g=new l({definition:{kind:s.x.APP_PAGE,page:"/meus-contratos/contrato/[id]/page",pathname:"/meus-contratos/contrato/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},44982:(e,r,a)=>{Promise.resolve().then(a.bind(a,68490))},68490:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>EditContract});var t=a(60080),s=a(9885),o=a(84070),i=a(51778),n=a(97669),d=a(47956),l=a(9993),m=a(57114),p=a(85814),c=a(24577),u=a(32307),g=a(57086),x=a(66558),b=a(99986),h=a(96413),v=a(27017),f=a(34751),y=a(64731),Z=a.n(y),j=a(17871),M=a(90682),C=a(15455),E=a(50298);let w=E.Ry().shape({isSCP:E.O7(),name:E.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:E.Z_().required("Campo obrigat\xf3rio"),email:E.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),rg:E.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),phoneNumber:E.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:E.Z_().required("Campo obrigat\xf3rio"),motherName:E.Z_().min(3,"Nome da m\xe3e deve conter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),zipCode:E.Z_().required("Obrigat\xf3rio"),neighborhood:E.Z_().required("Obrigat\xf3rio"),state:E.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:E.Z_().required("Obrigat\xf3rio"),complement:E.Z_().default("").notRequired(),number:E.Z_().required("Obrigat\xf3rio"),street:E.Z_().required("Obrigat\xf3rio"),value:E.Z_().required("Obrigat\xf3rio"),term:E.Z_().required("Obrigat\xf3rio"),yield:E.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchasedWith:E.Z_().required("Obrigat\xf3rio"),isDebenture:E.Z_().required("Obrigat\xf3rio"),initDate:E.Z_().required("Obrigat\xf3rio"),endDate:E.Z_().required("Obrigat\xf3rio"),issuer:E.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:E.Z_().required("Campo obrigat\xf3rio"),occupation:E.Z_().required("Campo obrigat\xf3rio"),amountQuotes:E.Z_().when("isSCP",(e,r)=>!0===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),documentPdf:E.Z_().notRequired(),proofPayment:E.Z_().notRequired(),proofOfResidence:E.Z_().notRequired(),contract:E.Z_().notRequired()}).required(),N=E.Ry().shape({isSCP:E.O7(),email:E.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),phoneNumber:E.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:E.Z_().required("Campo obrigat\xf3rio"),rg:E.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Obrigat\xf3rio"),ownerName:E.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),ownerDocument:E.Z_().min(3,"O nome da m\xe3e deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),motherName:E.Z_().required("Campo obrigat\xf3rio"),issuer:E.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:E.Z_().required("Campo obrigat\xf3rio"),occupation:E.Z_().required("Campo obrigat\xf3rio"),name:E.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:E.Z_().required("Campo obrigat\xf3rio"),companyType:E.Z_().required("Obrigat\xf3rio"),zipCode:E.Z_().required("Obrigat\xf3rio"),neighborhood:E.Z_().required("Obrigat\xf3rio"),state:E.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Obrigat\xf3rio"),city:E.Z_().required("Obrigat\xf3rio"),complement:E.Z_().default("").notRequired(),number:E.Z_().required("Obrigat\xf3rio"),street:E.Z_().required("Obrigat\xf3rio"),value:E.Z_().required("Obrigat\xf3rio"),term:E.Z_().required("Obrigat\xf3rio"),yield:E.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchasedWith:E.Z_().required("Obrigat\xf3rio"),initDate:E.Z_().required("Obrigat\xf3rio"),endDate:E.Z_().required("Obrigat\xf3rio"),isDebenture:E.Z_().required("Obrigat\xf3rio"),amountQuotes:E.Z_().when("isSCP",(e,r)=>!0===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),companyNumber:E.Z_().required("Campo obrigat\xf3rio"),companyComplement:E.Z_().default("").notRequired(),companyCity:E.Z_().required("Campo obrigat\xf3rio"),companyState:E.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").required("Campo obrigat\xf3rio"),companyStreet:E.Z_().required("Campo obrigat\xf3rio"),companyZipCode:E.Z_().required("Campo obrigat\xf3rio"),companyNeighborhood:E.Z_().required("Campo obrigat\xf3rio"),documentPdf:E.Z_().notRequired(),proofPayment:E.Z_().notRequired(),proofOfResidence:E.Z_().notRequired(),contract:E.Z_().notRequired()}).required();var D=a(40509),q=a(33050),_=a(73294),O=a(85334),S=a(34944),R=a(42686);let P=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}];function BusinessEditing({modalityContract:e,contractData:r}){let{id:a}=(0,m.useParams)(),[o,n]=(0,s.useState)(""),[d,y]=(0,s.useState)(),[E,w]=(0,s.useState)(),[F,Y]=(0,s.useState)(),[T,B]=(0,s.useState)(),{navigation:A}=(0,O.H)(),[I,V]=(0,s.useState)([]),[z,k]=(0,s.useState)(),[U,L]=(0,s.useState)(),[Q,W]=(0,s.useState)(),[$,G]=(0,s.useState)(),H=(0,M.e)(),{register:K,handleSubmit:X,watch:J,setError:ee,setValue:er,reset:ea,formState:{errors:et}}=(0,x.cI)({resolver:(0,g.X)(N)}),es=J("term"),eo=(0,S.D)({mutationFn:async e=>{let r=await p.Z.put("/account/resubmit-contract/",e);return r.data},onSuccess:()=>{f.Am.success("Contrato editado com sucesso!"),A("/meus-contratos")},onError:e=>{(0,c.Z)(e,"Erro ao editar o contrato!")}}),ei=(0,s.useCallback)(()=>{if(!r?.investment)return"";let e=Z()(r.investment.start),a=Z()(r.investment.end);return String(a.diff(e,"months"))},[r]),en=(0,s.useCallback)(()=>{r?.investor&&(er("name",r.investor.companyName),er("document",r.investor.document),er("companyType",r.investor.businessType),er("companyZipCode",r.investor.address.zipcode),er("companyNeighborhood",r.investor.address.neighborhood),er("companyStreet",r.investor.address.street),er("companyCity",r.investor.address.city),er("companyState",r.investor.address.state),er("companyNumber",r.investor.address.number),er("companyComplement",r?.investor?.address?.complement??""),er("ownerName",r.investor.responsibleOwner.name),er("ownerDocument",r.investor.responsibleOwner.document),er("rg",r.investor.responsibleOwner.rg),er("issuer",r.investor.responsibleOwner.issuingAgency),er("placeOfBirth",r.investor.responsibleOwner.nationality),er("occupation",r.investor.responsibleOwner.occupation),er("phoneNumber",(0,h.gP)(r.investor.responsibleOwner.phone)),er("dtBirth",r.investor.responsibleOwner.birthDate),er("email",r.investor.responsibleOwner.email),er("motherName",r.investor.responsibleOwner.motherName),er("zipCode",r.investor.responsibleOwner.address.zipcode),er("neighborhood",r.investor.responsibleOwner.address.neighborhood),er("street",r.investor.responsibleOwner.address.street),er("city",r.investor.responsibleOwner.address.city),er("state",r.investor.responsibleOwner.address.state),er("number",r.investor.responsibleOwner.address.number),er("complement",r.investor.responsibleOwner.address.complement),er("value",String((0,_.A)(Number(r.investment.value)))),er("yield",Number(r.investment.yield)),er("term",ei()),er("initDate",r.investment.start),er("endDate",r.investment.end),er("amountQuotes",r?.investment?.quotesAmount||""),er("isDebenture",r?.investment?.isdebenture?"s":"n"),er("purchasedWith",r?.investment?.purchasedWith||""))},[r,ei,er]);(0,s.useEffect)(()=>{en()},[en]),(0,s.useEffect)(()=>{er("isSCP","scp"===e)},[e]);let ed=(0,s.useMemo)(()=>{if(J("initDate")&&es){let e=(0,R.H)({investDate:es,startDate:J("initDate")});return Z()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[J("initDate"),es]);(0,s.useEffect)(()=>{ed&&er("endDate",ed,{shouldValidate:!0})},[ed,er]);let el=(0,s.useCallback)(r=>{let t=new FormData;t.append("personType","PJ"),t.append("contractType","scp"===e?"SCP":"MUTUO"),t.append("role",H.name),t.append("contractId",a),t.append("bankAccount[accountType]","CORRENTE"),t.append("investment[amount]",String((0,j.Z)(r.value))),t.append("investment[monthlyRate]",String(r.yield)),t.append("investment[durationInMonths]",r.term),t.append("investment[startDate]",`${(0,q.l)(r.initDate)}`),t.append("investment[endDate]",`${ed?Z()(ed,"DD/MM/YYYY").format("YYYY-MM-DD"):""}`),t.append("investment[isDebenture]",String("s"===r.isDebenture)),"scp"===e&&t.append("investment[quotaQuantity]",r.amountQuotes||""),t.append("company[corporateName]",r.name),t.append("company[cnpj]",(0,h.p4)(r.document)),t.append("company[type]",r.companyType),t.append("investment[paymentMethod]",r.purchasedWith),t.append("investment[profile]","moderate"),t.append("company[address][street]",r.companyStreet),t.append("company[address][city]",r.companyCity),t.append("company[address][state]",r.companyState),t.append("company[address][neighborhood]",r.companyNeighborhood),t.append("company[address][postalCode]",(0,h.p4)(r.companyZipCode)),t.append("company[address][number]",r.companyNumber),t.append("company[address][complement]",r.companyComplement??""),t.append("company[representative][fullName]",r.ownerName),t.append("company[representative][cpf]",(0,h.p4)(r.ownerDocument)),t.append("company[representative][rg]",r.rg),t.append("company[representative][issuingAgency]",r.issuer),t.append("company[representative][nationality]",r.placeOfBirth),t.append("company[representative][occupation]",r.occupation),t.append("company[representative][birthDate]",r.dtBirth),t.append("company[representative][email]",r.email),t.append("company[representative][phone]",`55${(0,h.p4)(r.phoneNumber)}`),t.append("company[representative][motherName]",r.motherName),t.append("company[representative][address][street]",r.street),t.append("company[representative][address][city]",r.city),t.append("company[representative][address][state]",r.state),t.append("company[representative][address][neighborhood]",r.neighborhood),t.append("company[representative][address][postalCode]",(0,h.p4)(r.zipCode)),t.append("company[representative][address][number]",r.number),t.append("company[representative][address][complement]",r.complement||""),d&&t.append("contract",d[0]),E&&t.append("proofOfPayment",E[0]),F&&t.append("personalDocument",F[0]),T&&t.append("proofOfResidence",T[0]),eo.mutateAsync(t)},[d,E,F,T,er,a,e,H]);(0,i.a)({queryKey:["company-address",J("companyZipCode")],queryFn:async()=>{let e=await (0,D.x)((0,h.p4)(J("companyZipCode")));e&&(er("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),er("companyCity",e.city,{shouldValidate:!0}),er("companyState",e.state,{shouldValidate:!0}),er("companyStreet",e.street,{shouldValidate:!0}))},enabled:J("companyZipCode")?.length===9});let em=(0,s.useCallback)(()=>{f.Am.info("Buscando dados da rejei\xe7\xe3o.",{toastId:"searchReasons"}),p.Z.get(`/audit/contract/${a}`).then(e=>{let r=e.data[0].rejectionReasons.reasons;V(r)}).catch(e=>(0,c.Z)(e,"Erro ao buscar os motivos da rejei\xe7\xe3o")).finally(()=>f.Am.dismiss("searchReasons"))},[a]),ep=(0,s.useCallback)(e=>{e?.length>0&&e.forEach(e=>{ee(e.field,{type:"required",message:e.reason})})},[ee]);(0,s.useEffect)(()=>{em()},[em]),(0,s.useEffect)(()=>{ep(I)},[I,ep]);let ec=(0,s.useCallback)(e=>I.find(r=>r?.field===e),[I]);return t.jsx("div",{children:(0,t.jsxs)("form",{action:"",onSubmit:X(el),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[t.jsx(l.Z,{title:"Dados Pessoais - Representanteeeeee",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"ownerName",width:"300px",error:!!et.ownerName,errorMessage:et?.ownerName?.message,label:"Nome"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"ownerDocument",width:"200px",error:!!et.ownerDocument,errorMessage:et?.ownerDocument?.message,label:"CPF",setValue:e=>er("ownerDocument",(0,h.VL)(e||""))}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"rg",width:"200px",error:!!et.rg,errorMessage:et?.rg?.message,label:"RG"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"issuer",width:"200px",error:!!et.issuer,errorMessage:et?.issuer?.message,label:"Org\xe3o emissor"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"placeOfBirth",width:"200px",error:!!et.placeOfBirth,errorMessage:et?.placeOfBirth?.message,label:"Nacionalidade"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"occupation",width:"200px",error:!!et.occupation,errorMessage:et?.occupation?.message,label:"Ocupa\xe7\xe3o"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"motherName",width:"250px",error:!!et.motherName,errorMessage:et?.motherName?.message,label:"Nome da m\xe3e"}),t.jsx(u.Z,{disableErrorMessage:!0,type:"date",register:K,name:"dtBirth",width:"200px",error:!!et.dtBirth,errorMessage:et?.dtBirth?.message,label:"Data de Nascimento"}),t.jsx(u.Z,{disableErrorMessage:!0,width:"200px",register:K,name:"phoneNumber",error:!!et.phoneNumber,errorMessage:et?.phoneNumber?.message,label:"Celular",maxLength:15,setValue:e=>er("phoneNumber",(0,h.gP)(e||""))}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"email",width:"300px",error:!!et.email,errorMessage:et?.email?.message,label:"E-mail"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"zipCode",width:"200px",error:!!et.zipCode,errorMessage:et?.zipCode?.message,label:"CEP",setValue:e=>{er("zipCode",(0,h.Tc)(e))}}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"neighborhood",width:"300px",error:!!et.neighborhood,errorMessage:et?.neighborhood?.message,label:"Bairro"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"street",width:"300px",error:!!et.street,errorMessage:et?.street?.message,label:"Rua"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"city",width:"200px",error:!!et.city,errorMessage:et?.city?.message,label:"Cidade"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,maxLength:2,setValue:e=>er("state",String(e).toUpperCase()),name:"state",width:"150px",error:!!et.state,errorMessage:et?.state?.message,label:"Estado"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"number",width:"200px",error:!!et.number,errorMessage:et?.number?.message,label:"N\xfamero"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"complement",width:"200px",error:!!et.complement,errorMessage:et?.complement?.message,label:"Complemento"})]})}),t.jsx(l.Z,{title:"Dados da empresa",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"name",width:"400px",error:!!et.name,errorMessage:et?.name?.message,label:"Raz\xe3o Social"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"document",width:"200px",error:!!et.document,errorMessage:et?.document?.message,label:"CNPJ",setValue:e=>er("document",(0,h.PK)(e||""))}),t.jsx(v.Z,{width:"200px",name:"companyType",register:K,options:P,error:!!et.companyType,errorMessage:et?.companyType?.message,label:"Tipo"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"companyZipCode",width:"200px",error:!!et.companyZipCode,errorMessage:et?.companyZipCode?.message,label:"CEP",setValue:e=>{er("companyZipCode",(0,h.Tc)(e))}}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"companyNeighborhood",width:"300px",error:!!et.companyNeighborhood,errorMessage:et?.companyNeighborhood?.message,label:"Bairro"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"companyStreet",width:"300px",error:!!et.companyStreet,errorMessage:et?.companyStreet?.message,label:"Rua"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"companyCity",width:"200px",error:!!et.companyCity,errorMessage:et?.companyCity?.message,label:"Cidade"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,maxLength:2,setValue:e=>er("companyState",String(e).toUpperCase()),name:"companyState",width:"150px",error:!!et.companyState,errorMessage:et?.companyState?.message,label:"Estado"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"companyNumber",width:"200px",error:!!et.companyNumber,errorMessage:et?.companyNumber?.message,label:"N\xfamero"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"companyComplement",width:"200px",error:!!et.companyComplement,errorMessage:et?.companyComplement?.message,label:"Complemento"})]})}),t.jsx(l.Z,{title:"Dados de Investimento",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(u.Z,{disableErrorMessage:!0,register:K,name:"value",width:"200px",error:!!et.value,errorMessage:et?.value?.message,label:"Valor",setValue:e=>er("value",(0,h.Ht)(e||""))}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,type:"number",name:"term",width:"250px",error:!!et.term,errorMessage:et?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),t.jsx(u.Z,{disableErrorMessage:!0,register:K,type:"text",name:"yield",width:"250px",error:!!et.yield,errorMessage:et?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),t.jsx(v.Z,{width:"200px",name:"purchasedWith",register:K,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!et?.purchasedWith,errorMessage:et?.purchasedWith?.message,label:"Comprar com"}),"scp"===e&&t.jsx(t.Fragment,{children:t.jsx(u.Z,{disableErrorMessage:!0,register:K,type:"number",name:"amountQuotes",width:"150px",error:!!et.amountQuotes,errorMessage:et?.amountQuotes?.message,label:"Quantidade de cotas"})}),t.jsx(u.Z,{disableErrorMessage:!0,type:"date",register:K,maxDate:Z()().format("YYYY-MM-DD"),name:"initDate",width:"200px",error:!!et.initDate,errorMessage:et?.initDate?.message,label:"Inicio do contrato"}),t.jsx(u.Z,{disableErrorMessage:!0,type:"date",register:K,name:"endDate",value:ed?Z()(ed,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"200px",label:"Final do contrato",disabled:!0}),t.jsx(v.Z,{width:"100px",name:"isDebenture",register:K,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!et.isDebenture,errorMessage:et?.isDebenture?.message,label:"Deb\xeanture"})," "]})}),t.jsx(l.Z,{title:"Anexo de documentos",children:t.jsx("div",{children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Contrato"}),t.jsx(C.Z,{errorMessage:ec("contract")?.reason,disable:!ec("contract")?.field,onFileUploaded:y,fileName:$,onRemoveFile:()=>{y(void 0),G(void 0)}})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Comprovante"}),t.jsx("div",{children:t.jsx(C.Z,{disable:!ec("proofPayment")?.field,errorMessage:ec("proofPayment")?.reason,onFileUploaded:w,fileName:z,onRemoveFile:()=>{w(void 0),k(void 0)}})})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Documento de identidade"}),t.jsx("div",{children:t.jsx(C.Z,{errorMessage:ec("documentPdf")?.reason,disable:!ec("documentPdf")?.field,onFileUploaded:Y,fileName:U,onRemoveFile:()=>{Y(void 0),L(void 0)}})})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),t.jsx("div",{children:t.jsx(C.Z,{errorMessage:ec("proofOfResidence")?.reason,disable:!ec("proofOfResidence")?.field,onFileUploaded:B,fileName:Q,onRemoveFile:()=>{B(void 0),W(void 0)}})})]})]})})}),t.jsx("div",{className:"md:w-52 mb-10",children:t.jsx(b.Z,{label:"Enviar",size:"lg",loading:eo.isPending,disabled:eo.isPending||!ed})})]})})}let functions_formatName=function(e){return e.toLowerCase().split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")};function PhysicalEditing({modalityContract:e,contractData:r}){let{id:a}=(0,m.useParams)(),[o,i]=(0,s.useState)(!1),[n,d]=(0,s.useState)(""),[y,E]=(0,s.useState)(""),[N,S]=(0,s.useState)(""),[R,P]=(0,s.useState)([]),{navigation:F}=(0,O.H)(),[Y,T]=(0,s.useState)([]),[B,A]=(0,s.useState)([]),[I,V]=(0,s.useState)(""),[z,k]=(0,s.useState)(!1),[U,L]=(0,s.useState)([]),[Q,W]=(0,s.useState)(),[$,G]=(0,s.useState)(),[H,K]=(0,s.useState)(),[X,J]=(0,s.useState)(),[ee,er]=(0,s.useState)(),[ea,et]=(0,s.useState)(),[es,eo]=(0,s.useState)(),[ei,en]=(0,s.useState)(),ed=(0,M.e)(),{register:el,handleSubmit:em,setValue:ep,reset:ec,setError:eu,formState:{errors:eg}}=(0,x.cI)({resolver:(0,g.X)(w)}),ex=(0,s.useCallback)(()=>{if(!r?.investment)return"";let e=Z()(r.investment.start),a=Z()(r.investment.end);return String(a.diff(e,"months"))},[r]);(0,s.useEffect)(()=>{r?.investor&&(ep("name",r.investor.name),ep("document",(0,h.VL)(r.investor.document)),ep("rg",r.investor.rg),ep("issuer",r.investor.issuingAgency),ep("placeOfBirth",r.investor.nationality),ep("occupation",r.investor.occupation),ep("phoneNumber",(0,h.gP)(r.investor.phone)),ep("dtBirth",r.investor.birthDate),ep("email",r.investor.email),ep("motherName",r.investor.motherName),ep("zipCode",r.investor.address.zipcode),ep("neighborhood",r.investor.address.neighborhood),ep("street",r.investor.address.street),ep("city",r.investor.address.city),ep("state",r.investor.address.state),ep("number",r.investor.address.number),ep("complement",r.investor.address.complement),ep("value",(0,_.A)(Number(r.investment.value))),ep("yield",Number(r.investment.yield)),ep("term",ex()),ep("initDate",r.investment.start),ep("endDate",r.investment.end),ep("amountQuotes",r?.investment?.quotesAmount||""),ep("purchasedWith",r.investment.purchasedWith),ep("isDebenture",r.investment.isdebenture?"s":"n"))},[r]),(0,s.useEffect)(()=>{ep("isSCP","scp"===e)},[e]),(0,s.useEffect)(()=>{getReasons()},[]),(0,s.useEffect)(()=>{getCep(n||"")},[n]);let getCep=async e=>{let r=await (0,D.x)((0,h.p4)(e));null!==r&&(ep("neighborhood",r.neighborhood),ep("city",r.city),ep("state",r.state),ep("street",r.street))},getReasons=()=>{f.Am.info("Buscando dados da rejei\xe7\xe3o.",{toastId:"searchReasons"}),p.Z.get(`/audit/contract/${a}`).then(e=>{let r=e.data[0].rejectionReasons.reasons;P(r)}).catch(e=>(0,c.Z)(e,"Erro ao buscar os motivos da rejei\xe7\xe3o")).finally(()=>f.Am.dismiss("searchReasons"))};function getFieldDocument(e){let r=R?.find(r=>r?.field===e)||void 0;return r}return(0,s.useEffect)(()=>{console.log(R),R?.length>0&&R.map(e=>{eu(e.field,{type:"required",message:e.reason})})},[R]),(0,s.useEffect)(()=>{"broker"!==ed.name&&"advisor"!==ed.name&&p.Z.get("superadmin"===ed.name?"/wallets/list-brokers":"/wallets/admin/brokers").then(e=>{A(e.data)}).catch(e=>{(0,c.Z)(e,"Erro ao buscar a lista de brokers")})},[]),t.jsx("div",{children:(0,t.jsxs)("form",{action:"",onSubmit:em(r=>{i(!0);let t=new FormData;t.append("role",ed.name),t.append("personType","PF"),t.append("contractType","scp"===e?"SCP":"MUTUO"),t.append("contractId",a),t.append("individual[fullName]",functions_formatName(String(r.name).trim())),t.append("individual[cpf]",(0,h.p4)(r.document).trim()),t.append("individual[rg]",r.rg),t.append("individual[issuingAgency]",r.issuer),t.append("individual[nationality]",r.placeOfBirth),t.append("individual[occupation]",functions_formatName(r.occupation)),t.append("individual[birthDate]",r.dtBirth),t.append("individual[email]",r.email),t.append("individual[phone]",`55${(0,h.p4)(r.phoneNumber)}`),t.append("individual[motherName]",functions_formatName(r.motherName)),t.append("individual[address][street]",r.street),t.append("individual[address][neighborhood]",r.neighborhood),t.append("individual[address][city]",r.city),t.append("individual[address][state]",r.state),t.append("individual[address][postalCode]",(0,h.p4)(r.zipCode)),t.append("individual[address][number]",r.number),t.append("individual[address][complement]",r.complement),t.append("investment[profile]","moderate"),t.append("investment[amount]",String((0,j.Z)(r.value))),t.append("investment[monthlyRate]",r.yield),t.append("investment[durationInMonths]",r.term),t.append("investment[paymentMethod]",r.purchasedWith),t.append("investment[startDate]",`${(0,q.l)(r.initDate)}`),t.append("investment[endDate]",`${(0,q.l)(r.endDate)}`),t.append("investment[isDebenture]",String("s"===r.isDebenture)),"scp"===e&&t.append("investment[quotaQuantity]",r.amountQuotes),Q&&t.append("contract",Q[0]),$&&t.append("proofOfPayment",$[0]),H&&t.append("personalDocument",H[0]),X&&t.append("proofOfResidence",X[0]),p.Z.put("/account/resubmit-contract/",t).then(e=>{f.Am.success("Contrato editado com sucesso!"),F("/meus-contratos")}).catch(e=>{(0,c.Z)(e,"Erro ao cadastrar o contrato!")}).finally(()=>i(!1))}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[t.jsx(l.Z,{title:"Dados Pessoais",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"name",width:"300px",error:!!eg.name,errorMessage:eg?.name?.message,label:"Nome completo"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"document",width:"200px",error:!!eg.document,errorMessage:eg?.document?.message,label:"CPF",setValue:e=>ep("document",(0,h.VL)(e||""))}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"rg",width:"200px",error:!!eg.rg,errorMessage:eg?.rg?.message,label:"Identidade"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"issuer",width:"200px",error:!!eg.issuer,errorMessage:eg?.issuer?.message,label:"Org\xe3o emissor"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"placeOfBirth",width:"200px",error:!!eg.placeOfBirth,errorMessage:eg?.placeOfBirth?.message,label:"Nacionalidade"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"occupation",width:"200px",error:!!eg.occupation,errorMessage:eg?.occupation?.message,label:"Ocupa\xe7\xe3o"}),t.jsx(u.Z,{disableErrorMessage:!0,width:"200px",register:el,name:"phoneNumber",maxLength:15,error:!!eg.phoneNumber,errorMessage:eg?.phoneNumber?.message,label:"Celular",setValue:e=>ep("phoneNumber",(0,h.gP)(e||""))}),t.jsx(u.Z,{disableErrorMessage:!0,type:"date",register:el,name:"dtBirth",width:"200px",error:!!eg.dtBirth,errorMessage:eg?.dtBirth?.message,label:"Data de Nascimento"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"email",width:"300px",error:!!eg.email,errorMessage:eg?.email?.message,label:"E-mail"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"motherName",width:"300px",error:!!eg.motherName,errorMessage:eg?.motherName?.message,label:"Nome da m\xe3e"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"zipCode",width:"200px",error:!!eg.zipCode,errorMessage:eg?.zipCode?.message,label:"CEP",setValue:e=>{d(e),ep("zipCode",(0,h.Tc)(e))}}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"neighborhood",width:"200px",error:!!eg.neighborhood,errorMessage:eg?.neighborhood?.message,label:"Bairro"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"street",width:"300px",error:!!eg.street,errorMessage:eg?.street?.message,label:"Rua"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"city",width:"200px",error:!!eg.city,errorMessage:eg?.city?.message,label:"Cidade"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,maxLength:2,setValue:e=>ep("state",String(e).toUpperCase()),name:"state",width:"150px",error:!!eg.state,errorMessage:eg?.state?.message,label:"Estado"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"number",width:"200px",error:!!eg.number,errorMessage:eg?.number?.message,label:"N\xfamero"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"complement",width:"200px",error:!!eg.complement,errorMessage:eg?.complement?.message,label:"Complemento"})]})}),t.jsx(l.Z,{title:"Dados de Investimento",children:(0,t.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[t.jsx(u.Z,{disableErrorMessage:!0,register:el,name:"value",width:"200px",error:!!eg.value,errorMessage:eg?.value?.message,label:"Valor",setValue:e=>ep("value",(0,h.Ht)(e))}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,type:"number",name:"term",width:"250px",error:!!eg.term,errorMessage:eg?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),t.jsx(u.Z,{disableErrorMessage:!0,register:el,type:"text",name:"yield",width:"250px",error:!!eg.yield,errorMessage:eg?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),t.jsx(v.Z,{width:"200px",name:"purchasedWith",register:el,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!eg?.purchasedWith,errorMessage:eg?.purchasedWith?.message,label:"Comprar com"}),"scp"===e&&t.jsx(t.Fragment,{children:t.jsx(u.Z,{disableErrorMessage:!0,register:el,type:"number",name:"amountQuotes",width:"150px",error:!!eg.amountQuotes,errorMessage:eg?.amountQuotes?.message,label:"Quantidade de cotas"})}),t.jsx(u.Z,{disableErrorMessage:!0,type:"date",register:el,name:"initDate",width:"200px",error:!!eg.initDate,errorMessage:eg?.initDate?.message,label:"Inicio do contrato"}),t.jsx(u.Z,{disableErrorMessage:!0,type:"date",register:el,name:"endDate",width:"200px",error:!!eg.endDate,errorMessage:eg?.endDate?.message,label:"Final do contrato"}),t.jsx(v.Z,{width:"100px",name:"isDebenture",register:el,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!eg.isDebenture,errorMessage:eg?.isDebenture?.message,label:"Deb\xeanture"})]})}),t.jsx(l.Z,{title:"Anexo de documentos",children:t.jsx("div",{children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Contrato"}),t.jsx(C.Z,{errorMessage:getFieldDocument("contract")?.reason,disable:!getFieldDocument("contract")?.field,onFileUploaded:W,fileName:ei,onRemoveFile:()=>{W(void 0),en(void 0)}})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Comprovante"}),t.jsx("div",{children:t.jsx(C.Z,{disable:!getFieldDocument("proofPayment")?.field,errorMessage:getFieldDocument("proofPayment")?.reason,onFileUploaded:G,fileName:ee,onRemoveFile:()=>{G(void 0),er(void 0)}})})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Documento de identidade"}),t.jsx("div",{children:t.jsx(C.Z,{errorMessage:getFieldDocument("documentPdf")?.reason,disable:!getFieldDocument("documentPdf")?.field,onFileUploaded:K,fileName:ea,onRemoveFile:()=>{K(void 0),et(void 0)}})})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),t.jsx("div",{children:t.jsx(C.Z,{errorMessage:getFieldDocument("proofOfResidence")?.reason,disable:!getFieldDocument("proofOfResidence")?.field,onFileUploaded:J,fileName:es,onRemoveFile:()=>{J(void 0),eo(void 0)}})})]})]})})}),t.jsx("div",{className:"md:w-52 mb-10",children:t.jsx(b.Z,{label:"Enviar",loading:o,size:"lg",disabled:o})})]})})}var F=a(33806);function EditContract(){let{id:e}=(0,m.useParams)(),r=(0,m.useRouter)(),a=(0,o.NL)(),[u,g]=(0,s.useState)("pf"),[x,b]=(0,s.useState)("mutuo"),[h,v]=(0,s.useState)(),[y,Z]=(0,s.useState)(),[j,M]=(0,s.useState)("");(0,s.useEffect)(()=>()=>{g("pf"),b("mutuo"),v(void 0),Z(void 0),M(""),a.removeQueries({queryKey:["contract",e]})},[a,e]);let{isLoading:C,error:E}=(0,i.a)({queryKey:["contract",e],queryFn:async()=>{let a=await p.Z.get(`/contract/get-detail/${e}`);if(!a.data)return f.Am.error("Dados do contrato n\xe3o encontrados."),r.push("/meus-contratos"),null;let t=a.data.status||a.data.statusContrato||a.data.contract?.status||a.data.investment?.status||"";return F.z.includes(t.toUpperCase())?(M(t),a.data.investment&&a.data.investment.type&&b(a.data.investment.type),a.data.investor&&a.data.investor.type&&("business"===a.data.investor.type?(v(a.data),g("pj")):(Z(a.data),g("pf"))),a.data):(f.Am.error("Este contrato n\xe3o pode ser editado devido ao seu status atual."),r.push("/meus-contratos"),null)},enabled:!!e,staleTime:0});return((0,s.useEffect)(()=>{E&&(console.error("Erro completo:",E),(0,c.Z)(E,"Erro ao buscar dados do contrato."),r.push("/meus-contratos"))},[E,r]),C)?(0,t.jsxs)(t.Fragment,{children:[t.jsx(n.Z,{}),t.jsx(d.Z,{children:t.jsx("div",{className:"m-3 flex justify-center items-center h-[80vh]",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF9900] mx-auto mb-4"}),t.jsx("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})]}):E?null:(0,t.jsxs)(t.Fragment,{children:[t.jsx(n.Z,{}),t.jsx(d.Z,{children:(0,t.jsxs)("div",{className:"m-3",children:[t.jsx(l.Z,{title:"Edi\xe7\xe3o de contrato",children:(0,t.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,t.jsxs)("select",{value:u,disabled:!0,onChange:({target:e})=>g(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[t.jsx("option",{value:"pf",selected:!0,children:"Pessoa F\xedsica"}),t.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-white mb-1",children:"Tipo de contrato"}),(0,t.jsxs)("select",{value:x,disabled:!0,onChange:({target:e})=>b("mutuo"===e.value?"mutuo":"scp"),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[t.jsx("option",{selected:!0,value:"mutuo",children:"M\xfatuo"}),t.jsx("option",{value:"scp",children:"SCP"})]})]})]})}),"pj"===u?t.jsx(BusinessEditing,{contractData:h,modalityContract:x}):t.jsx(PhysicalEditing,{contractData:y,modalityContract:x})]})})]})}},33806:(e,r,a)=>{"use strict";a.d(r,{f:()=>o,z:()=>s});var t=a(32775);let s=[t.rd.REJECTED_BY_AUDIT,t.rd.AWAITING_AUDIT],o=[t.rd.AWAITING_INVESTOR_SIGNATURE]},33050:(e,r,a)=>{"use strict";a.d(r,{Z:()=>formatDate,l:()=>formatDateToEnglishType});var t=a(95081),s=a.n(t);function formatDate(e){return s().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?s().utc(e).format("YYYY-MM-DD"):s().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},40509:(e,r,a)=>{"use strict";a.d(r,{x:()=>getZipCode});var t=a(21145);async function getZipCode(e){let r=e.replace(/\D/g,"");if(8!==r.length)return null;try{let e=await t.Z.get(`https://viacep.com.br/ws/${r}/json/`),a=e.data;if(a&&!a.erro)return{neighborhood:a.bairro||"",street:a.logradouro||"",city:a.localidade||"",state:a.uf||""};return null}catch(e){return console.error("Erro ao buscar o CEP:",e),null}}},85334:(e,r,a)=>{"use strict";a.d(r,{H:()=>useNavigation});var t=a(57114);let useNavigation=()=>{let e=(0,t.useRouter)();return{navigation:r=>e.push(r)}}},73294:(e,r,a)=>{"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let r=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return r.replace(/R\$\s?/,"")}a.d(r,{A:()=>cleanValue,Z:()=>formatValue})},96218:(e,r,a)=>{"use strict";a.r(r),a.d(r,{$$typeof:()=>i,__esModule:()=>o,default:()=>d});var t=a(17536);let s=(0,t.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\meus-contratos\contrato\[id]\page.tsx`),{__esModule:o,$$typeof:i}=s,n=s.default,d=n}};var r=require("../../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),a=r.X(0,[4103,6426,4731,8813,5081,6558,3356,4944,7207,278,7669,2307,2686,87,9327],()=>__webpack_exec__(87847));module.exports=a})();