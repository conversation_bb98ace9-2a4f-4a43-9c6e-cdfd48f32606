"use strict";exports.id=6440,exports.ids=[6440],exports.modules={58470:(e,s,t)=>{t.d(s,{Z:()=>ModalBroker});var a=t(60080),l=t(9885),n=t(85814),r=t(34751),d=t(96413);function BrokerData({broker:e,edit:s,setBroker:t}){return(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"<PERSON><PERSON><PERSON> Car<PERSON>ira Respons\xe1vel"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.upper,disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Tipo de Conta"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:"PHYSICAL"===e.accountType?"Pessoa F\xedsica":"Pessoa Juridica",disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Nome Completo"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.name,disabled:!s,onChange:({target:s})=>t({...e,name:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:e.document.length<=11?"CPF":"CNPJ"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.document.length<=11?(0,d.VL)(e.document):(0,d.PK)(e.document),disabled:!s,onChange:({target:s})=>t({...e,document:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Taxa"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.fee?"Sim":"N\xe3o",disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Data de Nascimento"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.birthDate,disabled:!s,onChange:({target:s})=>t({...e,birthDate:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Telefone"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.phone&&(0,d.gP)(e.phone.replace("+55",""))||"",disabled:!s,onChange:({target:s})=>t({...e,phone:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Nome da M\xe3e"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.motherName,disabled:!s,onChange:({target:s})=>t({...e,motherName:s.value})})]})]})}var o=t(21145);function BrokerAddress({broker:e,edit:s,setBroker:t}){let[n,r]=(0,l.useState)("");async function handleGetByCep(s){let a=s.replace(/[^0-9]/g,"");8!==a.length||await o.Z.get(`https://viacep.com.br/ws/${a}/json/`).then(s=>{s&&s.data&&(s.data.erro||t({...e,address:{neighbor:s.data.bairro,street:s.data.logradouro,city:s.data.localidade,uf:s.data.uf,complement:"",number:"",zipcode:a}}))}).catch(()=>{}).finally(()=>{})}return(0,l.useEffect)(()=>{(function(e){let s=e.replace(/[^0-9]/g,"");8===s.length&&handleGetByCep(s)})(n||"")},[n]),(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"ID"}),a.jsx("p",{className:"text-xs",children:e?.id})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"CEP"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address.zipcode||"",disabled:!s,onChange:({target:s})=>{t({...e,address:{...e.address,zipcode:s.value}}),r(s.value)}})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Cidade"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address.city||"",disabled:!s,onChange:({target:s})=>t({...e,address:{...e.address,city:s.value}})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Estado"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address.uf||"",disabled:!s,onChange:({target:s})=>t({...e,address:{...e.address,uf:s.value}})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Endere\xe7o"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address.street||"",disabled:!s,onChange:({target:s})=>t({...e,address:{...e.address,street:s.value}})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"N\xfamero"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address.number||"",disabled:!s,onChange:({target:s})=>t({...e,address:{...e.address,number:s.value}})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Bairro"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address.neighbor||"",disabled:!s,onChange:({target:s})=>t({...e,address:{...e.address,neighbor:s.value}})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Complemento"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address.complement||"",disabled:!s,onChange:({target:s})=>t({...e,address:{...e.address,complement:s.value}})})]})]})}var c=t(15455);let returnTitleDocument=e=>{switch(e){case"CONTRACT":return"Contrato de parceria";case"CARD_CNPJ":return"Cart\xe3o CNPJ";case"RG":return"Documento de identidade";case"PROOF_RESIDENCE":return"Comprovante de resid\xeancia";case"SOCIAL_CONTRACT":return"Contrato social";case"MEI":return"Certificado de mei"}};function BrokerDocuments({broker:e,edit:s,setBroker:t}){return a.jsx("div",{className:"flex gap-y-3 mt-5 flex-wrap gap-4",children:e.uploads.map(e=>(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:returnTitleDocument(e.type)}),a.jsx("div",{className:"w-44",children:a.jsx("div",{onClick:()=>{window.open(e.url,"_blank")},children:a.jsx(c.Z,{disable:!0,onFileUploaded:()=>{}})})})]},e.id))})}var i=t(64731),x=t.n(i),m=t(24577),h=t(90682),p=t(69957);function ModalBroker({setModal:e,broker:s,typeModal:t}){let[d,o]=(0,l.useState)({...s}),[c,i]=(0,l.useState)(0),[u,f]=(0,l.useState)(!1),[N,b]=(0,l.useState)(!1),j=(0,h.e)(),editData=()=>JSON.stringify(d)===JSON.stringify(s)?"old":"new",editBroker=()=>{b(!0);let{document:a,name:l,phone:o,email:c,motherName:i,birthDate:h,address:p,participation:u}=d,{city:N,complement:j,neighbor:v,number:g,street:C,uf:w,zipcode:y}=p,k={cep:y!==s.address.zipcode?y:void 0,street:C!==s.address.street?C:void 0,number:g!==s.address.number?g:void 0,complement:j!==s.address.complement?j:void 0,neighborhood:v!==s.address.neighbor?v:void 0,city:N!==s.address.city?N:void 0,state:w!==s.address.uf?w:void 0},D={owner:{document:a!==s.document?a:void 0,name:l!==s.name?l:void 0,phone:o!==s.phone?o:void 0,email:c!==s.email?c:void 0,motherName:i!==s.motherName?i:void 0,dtBirth:h!==s.birthDate?x()(h).format("YYYY-MM-DD"):void 0,Address:0===Object.keys(k).length?void 0:k},ownerRoleRelationId:s.id,rate:u!==s.participation?u:void 0};"broker"===t?n.Z.patch("/super-admin/broker",D).then(s=>{r.Am.success("Dados alterados com sucesso!"),e(!1),f(!1)}).catch(e=>{(0,m.Z)(e,"Tivemos um problema ao editar os dados do usu\xe1rio")}).finally(()=>b(!1)):n.Z.patch("/super-admin/advisor",D).then(s=>{r.Am.success("Dados alterados com sucesso!"),e(!1),f(!1)}).catch(e=>{(0,m.Z)(e,"Tivemos um problema ao editar os dados do usu\xe1rio")}).finally(()=>b(!1))};return a.jsx("div",{className:"",children:(0,a.jsxs)("div",{className:"w-full text-white h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5"}),(0,a.jsxs)("div",{className:"gap-y-1 flex flex-col",children:[a.jsx("p",{className:"font-bold text-xs",children:d?.name}),a.jsx("p",{className:"text-xs",children:"broker"===t?"Broker":"Assessor"})]})]}),(0,a.jsxs)("div",{className:"w-full flex flex-2 text-center mt-5 mb-4 gap-2 justify-between",children:[a.jsx("div",{className:`cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ${0===c?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>i(0),children:a.jsx("p",{className:"md:text-sm text-xs text-center",children:"Dados Pessoais"})}),a.jsx("div",{className:`cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ${1===c?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>i(1),children:a.jsx("p",{className:"md:text-sm text-xs",children:"Endere\xe7o"})}),a.jsx("div",{className:`cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ${2===c?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>i(2),children:a.jsx("p",{className:"md:text-sm text-xs",children:"Participa\xe7\xe3o"})}),a.jsx("div",{className:`cursor-pointer md:w-auto px-3 py-2 hover:bg-zinc-800 rounded-lg ${3===c?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>i(3),children:a.jsx("p",{className:"md:text-sm text-xs",children:"Anexos"})})]}),a.jsx("div",{className:"w-full flex-1",children:(()=>{switch(c){case 0:return a.jsx(BrokerData,{broker:d,setBroker:o,edit:u});case 1:return a.jsx(BrokerAddress,{broker:d,setBroker:o,edit:u});case 2:return(0,a.jsxs)("div",{className:"flex gap-y-3 mt-5",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Participa\xe7\xe3o"}),(0,a.jsxs)("div",{className:"flex items-center justify-end",children:[a.jsx("input",{type:"number",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:String(Number(d.participation))||"",disabled:!u,onChange:({target:e})=>o({...d,participation:e.value})}),a.jsx("p",{className:"ml-[1px]",children:"%"})]})]});case 3:return a.jsx(BrokerDocuments,{broker:d,setBroker:o,edit:u})}})()}),a.jsx("div",{className:"md:w-9/12 w-full flex flex-col md:flex-row justify-start gap-4 mt-5 select-none",children:"superadmin"===j.name&&a.jsx(p.z,{onClick:()=>{N||(!0===u&&"new"===editData()?editBroker():f(!u))},children:u?"new"!==editData()?"Cancelar edi\xe7\xe3o":"Confirmar edi\xe7\xe3o":"Editar dados"})})]})})}},34692:(e,s,t)=>{t.d(s,{Z:()=>Modal});var a=t(60080),l=t(96413),n=t(21145),r=t(9885);function UserData({investor:e,edit:s,setInvestor:t}){async function handleGetByCep(s){let a=s.replace(/[^0-9]/g,"");8!==a.length||await n.Z.get(`https://viacep.com.br/ws/${a}/json/`).then(s=>{s&&s.data&&(s.data.erro||t({...e,neighborhood:s.data.bairro,address:s.data.logradouro,city:s.data.localidade,state:s.data.uf}))}).catch(()=>{}).finally(()=>{})}return(0,r.useEffect)(()=>{(function(e){let s=e.replace(/[^0-9]/g,"");8===s.length&&handleGetByCep(s)})(e?.zipCode||"")},[e?.zipCode]),(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"ID"}),a.jsx("p",{className:"text-xs",children:e?.id})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Nome"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.name,disabled:!s,onChange:({target:s})=>t({...e,name:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"E-mail"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.email,disabled:!s,onChange:({target:s})=>t({...e,email:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Consultor Respons\xe1vel"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none ",placeholder:"N\xe3o informado",value:e.advisor,disabled:!0})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Telefone"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",value:(0,l.gP)(e?.phone?.replace("+55","")||e?.phone?.replace("55","")||""),disabled:!s,onChange:({target:s})=>t({...e,phone:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Perfil"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.profile||"",disabled:!s,onChange:({target:s})=>t({...e,profile:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"RG"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.rg||"",disabled:!s,onChange:({target:s})=>t({...e,rg:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"CEP"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.zipCode||"",disabled:!s,onChange:({target:s})=>t({...e,zipCode:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Cidade"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.city||"",disabled:!s,onChange:({target:s})=>t({...e,city:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Estado"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.state||"",disabled:!s,onChange:({target:s})=>t({...e,state:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Endere\xe7o"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.address||"",disabled:!s,onChange:({target:s})=>t({...e,address:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"N\xfamero"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.addressNumber||"",disabled:!s,onChange:({target:s})=>t({...e,addressNumber:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Bairro"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.neighborhood||"",disabled:!s,onChange:({target:s})=>t({...e,neighborhood:s.value})})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Complemento"}),a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:e.complement||"",disabled:!s,onChange:({target:s})=>t({...e,complement:s.value})})]})]})}var d=t(957),o=t(32775),c=t(28168),i=t(69888),x=t(1808),m=t(57048),h=t(95081),p=t.n(h);function UserContracts({investor:e}){let[s,t]=(0,r.useState)(),[l,n]=(0,r.useState)([]),returnUrl=(e,s)=>{if(e.files.length>0){let t=Array.isArray(s)?s:[s],l=e.files.filter(e=>t.includes(e.type));if(l.length>0)return a.jsx("p",{className:"w-full flex items-center justify-center",children:a.jsx(c.Z,{className:"cursor-pointer",onClick:()=>{window.open(l[0].url,"_blank")},color:"#fff",width:20})})}return a.jsx("p",{className:"w-full flex items-center justify-center",children:a.jsx(i.Z,{className:"",color:"#FF9900",width:20})})};return(0,a.jsxs)("div",{className:"mt-5",children:[(0,a.jsxs)("table",{className:"w-full relative min-h-20",children:[a.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,a.jsxs)("tr",{className:"w-full py-2",children:[a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Valor"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Rendimento"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Consultor"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Criado em"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Status"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Contrato"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Comprovante"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Aditivos"})})]})}),e.contracts.length>=1?a.jsx("tbody",{className:"w-full",children:e.contracts.map((e,s)=>(0,a.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[a.jsx("td",{children:a.jsx("p",{className:"text-xs text-center",children:Number(e.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),a.jsx("td",{children:(0,a.jsxs)("p",{className:"text-xs text-center",children:[e.investmentYield||"0","%"]})}),a.jsx("td",{className:"text-center",children:a.jsx("p",{className:"text-xs text-center max-w-44 truncate",children:e?.responsibleConsultant})}),a.jsx("td",{children:a.jsx("p",{className:"text-xs text-center",children:p()(e.contractStart||"").format("DD/MM/YYYY")})}),a.jsx("td",{children:a.jsx(d.Z,{description:(0,o.mP)(e.contractStatus).description,text:(0,o.mP)(e.contractStatus).title,textColor:(0,o.mP)(e.contractStatus).textColor})}),a.jsx("td",{children:e?.pdfDocument?a.jsx("p",{className:"w-full flex items-center justify-center",children:a.jsx(c.Z,{className:"cursor-pointer",onClick:()=>{window.open(e?.pdfDocument,"_blank")},color:"#fff",width:20})}):a.jsx("p",{className:"text-xs text-center",children:"N\xe3o possui"})}),a.jsx("td",{children:e?.proofPaymentPdf?a.jsx("p",{className:"w-full flex items-center justify-center",children:a.jsx(c.Z,{className:"cursor-pointer",onClick:()=>{window.open(e?.proofPaymentPdf,"_blank")},color:"#fff",width:20})}):a.jsx("p",{className:"text-xs text-center",children:"N\xe3o possui"})}),a.jsx("td",{children:e?.addendum.length>0?a.jsx("p",{className:"w-full flex items-center justify-center",children:a.jsx(x.Z,{className:"cursor-pointer",onClick:()=>n(e.addendum),color:"#fff",width:20})}):a.jsx("p",{className:"text-xs text-center",children:"N\xe3o possui"})})]},s))}):a.jsx("div",{className:"text-center mt-5 absolute w-full",children:a.jsx("p",{children:"Nenhum dado encontrado"})})]}),l.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-1 mt-7 flex w-full items-center justify-between",children:[a.jsx("p",{children:"Aditivos do contrato selecionado"}),a.jsx(m.Z,{width:20,className:"cursor-pointer",onClick:()=>n([])})]}),(0,a.jsxs)("table",{className:"w-full relative min-h-20 ",children:[a.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,a.jsxs)("tr",{className:"w-full py-2",children:[a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Valor"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Rendimento"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Consultor"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Criado em"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Assinatura"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Contrato"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Comprovante"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm"})})]})}),l?.length>=1?a.jsx("tbody",{className:"w-full",children:l.map((e,s)=>(0,a.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[a.jsx("td",{children:a.jsx("p",{className:"text-xs text-center",children:Number(e.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),a.jsx("td",{children:(0,a.jsxs)("p",{className:"text-xs text-center",children:[e.investmentYield||"0","%"]})}),a.jsx("td",{className:"max-w-28",children:a.jsx("p",{className:"text-xs text-center max-w-48 truncate",children:e?.responsibleConsultant})}),a.jsx("td",{children:a.jsx("p",{className:"text-xs text-center",children:p()(e.contractStart).format("DD/MM/YYYY")})}),a.jsx("td",{children:a.jsx(d.Z,{description:(0,o.XW)(e.contractStatus).description,text:(0,o.XW)(e.contractStatus).title,textColor:(0,o.XW)(e.contractStatus).textColor})}),a.jsx("td",{children:returnUrl(e,["CONTRACT","ADDENDUM"])}),a.jsx("td",{children:returnUrl(e,"PAYMENT")})]},s))}):a.jsx("div",{className:"text-center mt-5 absolute w-full",children:a.jsx("p",{children:"Nenhum dado encontrado"})})]})]})]})}var u=t(66558);function UserDataBank({investor:e,edit:s,setInvestor:t}){let{setValue:l,watch:n}=(0,u.cI)(),[d,o]=(0,r.useState)(e||{});(0,r.useEffect)(()=>{e&&(o(e),l("bank",e.bank||""),l("accountNumber",e.accountNumber||""),l("branch",e.branch||""))},[e,l]);let c=n("bank"),i=n("accountNumber"),x=n("branch"),handleChange=(e,s)=>{l(e,s);let a={...d,[e]:s};o(a),t(a)};return(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Banco"}),s?a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:c,disabled:!s,onChange:({target:e})=>handleChange("bank",e.value)}):a.jsx("p",{className:"text-xs",children:c||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"N\xfamero da conta"}),s?a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:i,disabled:!s,onChange:({target:e})=>handleChange("accountNumber",e.value)}):a.jsx("p",{className:"text-xs",children:i||"N\xe3o informado"})]}),(0,a.jsxs)("div",{className:"flex",children:[a.jsx("p",{className:"flex-1 font-bold text-xs",children:"Ag\xeancia"}),s?a.jsx("input",{type:"text",className:"bg-transparent text-sm w-7/12 text-end focus:border-none",placeholder:"N\xe3o informado",value:x,disabled:!s,onChange:({target:e})=>handleChange("branch",e.value)}):a.jsx("p",{className:"text-xs",children:x||"N\xe3o informado"})]})]})}var f=t(85814),N=t(34751),b=t(90682),j=t(69957),v=t(57114),g=t(75593);let C=(0,g.Z)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function CardUpgradeOpt({title:e,value:s,onClick:t}){return a.jsx("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:t,children:a.jsx("div",{className:"bg-[#1C1C1C] md:w-[100%] p-5 rounded-lg border-[#FF9900] border my-5",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"",children:[a.jsx("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4",children:e}),a.jsx("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px]",children:s})]}),a.jsx("div",{className:"flex-1"}),a.jsx("div",{className:"flex items-center",children:a.jsx("div",{className:"",children:a.jsx("p",{className:"text-white text-lg font-bold",children:a.jsx(C,{color:"#fff",width:20})})})})]})})})}function Modal({setModal:e,investor:s}){let t=(0,v.useRouter)(),[n,d]=(0,r.useState)(s),[o,c]=(0,r.useState)(0),[i,x]=(0,r.useState)(!1),[m,h]=(0,r.useState)(!1),[p,u]=(0,r.useState)(!1),g=(0,b.e)(),editData=()=>JSON.stringify(n)===JSON.stringify(s)?"owner":n.zipCode===s.zipCode&&n.address===s.address&&n.neighborhood===s.neighborhood&&n.addressNumber===s.addressNumber&&n.city===s.city&&n.state===s.state&&n.complement===s.complement?"address":void 0,editBank=()=>{if(n.bank===s.bank&&n.accountNumber===s.accountNumber&&n.branch===s.branch)return"bank"},editUser=()=>{if("owner"===editData())return x(!1);h(!0);let t={owner:"owner"!==editData()?{name:n.name!==s.name?n.name:void 0,document:n.document!==s.document?(0,l.VL)(n.document):void 0,email:n.email!==s.email?n.email:void 0,phone:n.phone!==s.phone?`+55${(0,l.p4)(n.phone)}`:void 0,rg:n.rg!==s.rg?n.rg:void 0}:void 0,bank:"bank"!==editBank()&&"superadmin"===g.name?{bank:n.bank,accountNumber:n.accountNumber,branch:n.branch}:void 0,address:"address"!==editData()?{cep:(0,l.Tc)(n.zipCode),street:n.address,neighborhood:n.neighborhood,number:n.addressNumber,city:n.city,state:n.state,complement:n.complement}:void 0,ownerRoleRelationId:n.id};f.Z.put(`/${g.name}/investor`,t).then(s=>{N.Am.success("Dados alterados com sucesso!"),x(!1),e(!1),window.location.reload()}).catch(e=>{N.Am.error(e.response.data.message||"N\xe3o foi possivel alterar os dados do investidor")}).finally(()=>h(!1))},handleRentabilityClick=()=>{t.push(`/contratos/alterar?tipo=rentabilidade&investorId=${s.id}`)},handleModalityClick=()=>{t.push(`/contratos/alterar?tipo=modalidade&investorId=${s.id}`)},upgradeContractOptions=()=>(0,a.jsxs)(a.Fragment,{children:[a.jsx(CardUpgradeOpt,{title:"Mudan\xe7a de Rentabilidade",value:"Clique aqui para mudar a rentabilidade do contrato do investidor",onClick:handleRentabilityClick}),a.jsx(CardUpgradeOpt,{title:"Mudan\xe7a de Modalidade",value:"Clique aqui para mudar a modalidade do contrato do investidor",onClick:handleModalityClick})]});return a.jsx("div",{className:"z-10 fixed top-0 left-0 w-screen h-screen bg-[#1c1c1c71]",children:(0,a.jsxs)("div",{className:`z-20 w-full ${2!==o||p?"md:w-5/12":"md:w-6/12"} bg-[#1C1C1C] min-h-screen fixed top-0 right-0 border-l border-t border-[#FF9900] p-10 text-white overflow-auto h-full flex flex-col`,children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5"}),(0,a.jsxs)("div",{className:"gap-y-1 flex flex-col",children:[a.jsx("p",{className:"font-bold text-xs",children:n?.name}),a.jsx("p",{className:"text-xs",children:"Investidor"})]})]}),(0,a.jsxs)("div",{className:"flex gap-x-2 mt-5",children:[a.jsx("div",{className:`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${0===o?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>c(0),children:a.jsx("p",{className:"md:text-sm text-xs",children:"Dados Pessoais"})}),a.jsx("div",{className:`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${1===o?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>c(1),children:a.jsx("p",{className:"md:text-sm text-xs",children:"Dados Banc\xe1rios"})}),a.jsx("div",{className:`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${2===o?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>c(2),children:a.jsx("p",{className:"md:text-sm text-xs",children:"Contratos"})})]}),a.jsx("div",{className:"w-full flex-1",children:(()=>{switch(o){case 0:return a.jsx(UserData,{investor:n,setInvestor:d,edit:i});case 1:return a.jsx(UserDataBank,{investor:n,edit:i&&"superadmin"===g.name,setInvestor:d});case 2:return p?upgradeContractOptions():a.jsx(UserContracts,{investor:n})}})()}),(0,a.jsxs)("div",{className:"md:w-9/12 w-full flex flex-col md:flex-row justify-start gap-4 mt-5",children:[(0===o||1===o&&"superadmin"===g.name)&&["superadmin","admin","broker"].includes(g.name)&&a.jsx(j.z,{onClick:()=>{i?editUser():x(!0)},children:i?"owner"===editData()?"Cancelar edi\xe7\xe3o":"Confirmar edi\xe7\xe3o":"Editar dados"}),2===o&&["superadmin","admin","broker","advisor"].includes(g.name)&&n?.contracts&&0!==n.contracts.length&&n.contracts.some(e=>{let s=e.contractStatus?.toUpperCase();return"ACTIVE"===s||"ATIVO"===s})?a.jsx(j.z,{variant:"default",onClick:()=>u(!0),children:"Alterar Contrato"}):null,a.jsx(j.z,{variant:"secondary",onClick:s=>p?u(!1):e(!1),children:"Fechar"})]})]})})}}};