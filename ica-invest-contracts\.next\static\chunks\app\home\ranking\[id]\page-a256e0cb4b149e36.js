(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1389],{4279:function(e,t,n){(e.exports=n(1223)).tz.load(n(6564))},1223:function(e,t,n){var r,o,s;s=function(e){"use strict";void 0===e.version&&e.default&&(e=e.default);var t,n,r={},o={},s={},a={},i={};e&&"string"==typeof e.version||logError("Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/");var l=e.version.split("."),c=+l[0],d=+l[1];function charCodeToInt(e){return e>96?e-87:e>64?e-29:e-48}function unpackBase60(e){var t,n=0,r=e.split("."),o=r[0],s=r[1]||"",a=1,i=0,l=1;for(45===e.charCodeAt(0)&&(n=1,l=-1);n<o.length;n++)i=60*i+(t=charCodeToInt(o.charCodeAt(n)));for(n=0;n<s.length;n++)a/=60,i+=(t=charCodeToInt(s.charCodeAt(n)))*a;return i*l}function arrayToInt(e){for(var t=0;t<e.length;t++)e[t]=unpackBase60(e[t])}function mapIndices(e,t){var n,r=[];for(n=0;n<t.length;n++)r[n]=e[t[n]];return r}function unpack(e){var t=e.split("|"),n=t[2].split(" "),r=t[3].split(""),o=t[4].split(" ");return arrayToInt(n),arrayToInt(r),arrayToInt(o),function(e,t){for(var n=0;n<t;n++)e[n]=Math.round((e[n-1]||0)+6e4*e[n]);e[t-1]=1/0}(o,r.length),{name:t[0],abbrs:mapIndices(t[1].split(" "),r),offsets:mapIndices(n,r),untils:o,population:0|t[5]}}function Zone(e){e&&this._set(unpack(e))}function Country(e,t){this.name=e,this.zones=t}function OffsetAt(e){var t=e.toTimeString(),n=t.match(/\([a-z ]+\)/i);"GMT"===(n=n&&n[0]?(n=n[0].match(/[A-Z]/g))?n.join(""):void 0:(n=t.match(/[A-Z]{3,5}/g))?n[0]:void 0)&&(n=void 0),this.at=+e,this.abbr=n,this.offset=e.getTimezoneOffset()}function ZoneScore(e){this.zone=e,this.offsetScore=0,this.abbrScore=0}function sortZoneScores(e,t){return e.offsetScore!==t.offsetScore?e.offsetScore-t.offsetScore:e.abbrScore!==t.abbrScore?e.abbrScore-t.abbrScore:e.zone.population!==t.zone.population?t.zone.population-e.zone.population:t.zone.name.localeCompare(e.zone.name)}function normalizeName(e){return(e||"").toLowerCase().replace(/\//g,"_")}function addZone(e){var t,n,o,s;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)r[s=normalizeName(n=(o=e[t].split("|"))[0])]=e[t],a[s]=n,function(e,t){var n,r;for(arrayToInt(t),n=0;n<t.length;n++)i[r=t[n]]=i[r]||{},i[r][e]=!0}(s,o[2].split(" "))}function getZone(e,t){var n,s=r[e=normalizeName(e)];return s instanceof Zone?s:"string"==typeof s?(s=new Zone(s),r[e]=s,s):o[e]&&t!==getZone&&(n=getZone(o[e],getZone))?((s=r[e]=new Zone)._set(n),s.name=a[e],s):null}function addLink(e){var t,n,r,s;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)r=normalizeName((n=e[t].split("|"))[0]),s=normalizeName(n[1]),o[r]=s,a[r]=n[0],o[s]=r,a[s]=n[1]}function zoneExists(e){return zoneExists.didShowError||(zoneExists.didShowError=!0,logError("moment.tz.zoneExists('"+e+"') has been deprecated in favor of !moment.tz.zone('"+e+"')")),!!getZone(e)}function needsOffset(e){var t="X"===e._f||"x"===e._f;return!!(e._a&&void 0===e._tzm&&!t)}function logError(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}function tz(t){var n,r=Array.prototype.slice.call(arguments,0,-1),o=arguments[arguments.length-1],s=e.utc.apply(null,r);return!e.isMoment(t)&&needsOffset(s)&&(n=getZone(o))&&s.add(n.parse(s),"minutes"),s.tz(o),s}(c<2||2===c&&d<6)&&logError("Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js "+e.version+". See momentjs.com"),Zone.prototype={_set:function(e){this.name=e.name,this.abbrs=e.abbrs,this.untils=e.untils,this.offsets=e.offsets,this.population=e.population},_index:function(e){var t;if((t=function(e,t){var n,r=t.length;if(e<t[0])return 0;if(r>1&&t[r-1]===1/0&&e>=t[r-2])return r-1;if(e>=t[r-1])return -1;for(var o=0,s=r-1;s-o>1;)t[n=Math.floor((o+s)/2)]<=e?o=n:s=n;return s}(+e,this.untils))>=0)return t},countries:function(){var e=this.name;return Object.keys(s).filter(function(t){return -1!==s[t].zones.indexOf(e)})},parse:function(e){var t,n,r,o,s=+e,a=this.offsets,i=this.untils,l=i.length-1;for(o=0;o<l;o++)if(t=a[o],n=a[o+1],r=a[o?o-1:o],t<n&&tz.moveAmbiguousForward?t=n:t>r&&tz.moveInvalidForward&&(t=r),s<i[o]-6e4*t)return a[o];return a[l]},abbr:function(e){return this.abbrs[this._index(e)]},offset:function(e){return logError("zone.offset has been deprecated in favor of zone.utcOffset"),this.offsets[this._index(e)]},utcOffset:function(e){return this.offsets[this._index(e)]}},ZoneScore.prototype.scoreOffsetAt=function(e){this.offsetScore+=Math.abs(this.zone.utcOffset(e.at)-e.offset),this.zone.abbr(e.at).replace(/[^A-Z]/g,"")!==e.abbr&&this.abbrScore++},tz.version="0.5.48",tz.dataVersion="",tz._zones=r,tz._links=o,tz._names=a,tz._countries=s,tz.add=addZone,tz.link=addLink,tz.load=function(e){addZone(e.zones),addLink(e.links),function(e){var t,n,r,o;if(e&&e.length)for(t=0;t<e.length;t++)n=(o=e[t].split("|"))[0].toUpperCase(),r=o[1].split(" "),s[n]=new Country(n,r)}(e.countries),tz.dataVersion=e.version},tz.zone=getZone,tz.zoneExists=zoneExists,tz.guess=function(e){return(!n||e)&&(n=function(){try{var e=Intl.DateTimeFormat().resolvedOptions().timeZone;if(e&&e.length>3){var t=a[normalizeName(e)];if(t)return t;logError("Moment Timezone found "+e+" from the Intl api, but did not have that data loaded.")}}catch(e){}var n,r,o,s=function(){var e,t,n,r,o=new Date().getFullYear()-2,s=new OffsetAt(new Date(o,0,1)),a=s.offset,i=[s];for(r=1;r<48;r++)(n=new Date(o,r,1).getTimezoneOffset())!==a&&(i.push(e=function(e,t){for(var n,r;r=((t.at-e.at)/12e4|0)*6e4;)(n=new OffsetAt(new Date(e.at+r))).offset===e.offset?e=n:t=n;return e}(s,t=new OffsetAt(new Date(o,r,1)))),i.push(new OffsetAt(new Date(e.at+6e4))),s=t,a=n);for(r=0;r<4;r++)i.push(new OffsetAt(new Date(o+r,0,1))),i.push(new OffsetAt(new Date(o+r,6,1)));return i}(),l=s.length,c=function(e){var t,n,r,o,s=e.length,l={},c=[],d={};for(t=0;t<s;t++)if(r=e[t].offset,!d.hasOwnProperty(r)){for(n in o=i[r]||{})o.hasOwnProperty(n)&&(l[n]=!0);d[r]=!0}for(t in l)l.hasOwnProperty(t)&&c.push(a[t]);return c}(s),d=[];for(r=0;r<c.length;r++){for(o=0,n=new ZoneScore(getZone(c[r]),l);o<l;o++)n.scoreOffsetAt(s[o]);d.push(n)}return d.sort(sortZoneScores),d.length>0?d[0].zone.name:void 0}()),n},tz.names=function(){var e,t=[];for(e in a)a.hasOwnProperty(e)&&(r[e]||r[o[e]])&&a[e]&&t.push(a[e]);return t.sort()},tz.Zone=Zone,tz.unpack=unpack,tz.unpackBase60=unpackBase60,tz.needsOffset=needsOffset,tz.moveInvalidForward=!0,tz.moveAmbiguousForward=!1,tz.countries=function(){return Object.keys(s)},tz.zonesForCountry=function(e,t){if(!(e=s[e.toUpperCase()]||null))return null;var n=e.zones.sort();return t?n.map(function(e){var t=getZone(e);return{name:e,offset:t.utcOffset(new Date)}}):n};var u=e.fn;function abbrWrap(e){return function(){return this._z?this._z.abbr(this):e.call(this)}}function resetZoneWrap(e){return function(){return this._z=null,e.apply(this,arguments)}}e.tz=tz,e.defaultZone=null,e.updateOffset=function(t,n){var r,o=e.defaultZone;if(void 0===t._z&&(o&&needsOffset(t)&&!t._isUTC&&t.isValid()&&(t._d=e.utc(t._a)._d,t.utc().add(o.parse(t),"minutes")),t._z=o),t._z){if(16>Math.abs(r=t._z.utcOffset(t))&&(r/=60),void 0!==t.utcOffset){var s=t._z;t.utcOffset(-r,n),t._z=s}else t.zone(r,n)}},u.tz=function(t,n){if(t){if("string"!=typeof t)throw Error("Time zone name must be a string, got "+t+" ["+typeof t+"]");return this._z=getZone(t),this._z?e.updateOffset(this,n):logError("Moment Timezone has no data for "+t+". See http://momentjs.com/timezone/docs/#/data-loading/."),this}if(this._z)return this._z.name},u.zoneName=abbrWrap(u.zoneName),u.zoneAbbr=abbrWrap(u.zoneAbbr),u.utc=resetZoneWrap(u.utc),u.local=resetZoneWrap(u.local),u.utcOffset=(t=u.utcOffset,function(){return arguments.length>0&&(this._z=null),t.apply(this,arguments)}),e.tz.setDefault=function(t){return(c<2||2===c&&d<9)&&logError("Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js "+e.version+"."),e.defaultZone=t?getZone(t):null,e};var f=e.momentProperties;return"[object Array]"===Object.prototype.toString.call(f)?(f.push("_z"),f.push("_a")):f&&(f._z=null),e},e.exports?e.exports=s(n(2067)):(r=[n(2067)],void 0===(o=s.apply(t,r))||(e.exports=o))},3844:function(e,t,n){Promise.resolve().then(n.bind(n,5312))},5312:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return RankingSearch}});var r=n(7437),o=n(7059),s=n(3877),a=n(9715),i=n(8637),l=n(8689),c=n(4568),d=n(6654),u=n(4734),f=n(1458),h=n(5968),m=n(4279),p=n.n(m),x=n(2265);function RankingSearch(e){let{params:t}=e,n=localStorage.getItem("searchRanking"),{notificationModal:m}=(0,x.useContext)(u.Z),[v,g]=(0,x.useState)(!1),[j,b]=(0,x.useState)(1),[z,w]=(0,x.useState)(!1),[y,N]=(0,x.useState)({startData:"",endData:"",input:"",type:"",filterOptions:[{label:"Todos",value:""},{label:"Contratos SCP",value:"SCP"},{label:"Contratos M\xfatuo",value:"P2P"}]}),[Z,k]=(0,x.useState)({total:0,lastPage:1,perPage:0}),[_,O]=(0,x.useState)(),getBroker=()=>{g(!0),c.Z.get("/super-admin/active-brokers/".concat(t.id),{params:{page:j,limit:10,dateFrom:""!==y.startData?y.startData:void 0,dateTo:""!==y.endData?y.endData:void 0,contractType:""!==y.type?y.type:void 0}}).then(e=>{var t,n;(null==e?void 0:null===(n=e.data)||void 0===n?void 0:null===(t=n.data)||void 0===t?void 0:t.length)>0&&(k(e.data.meta),O(e.data.data))}).catch(e=>{(0,d.Z)(e,"N\xe3o foi possivel buscar os investidores do broker")}).finally(()=>{g(!1)})},getAdvisor=()=>{g(!0),c.Z.get("/super-admin/active-advisors/".concat(t.id),{params:{page:j,limit:10,dateFrom:""!==y.startData?y.startData:void 0,dateTo:""!==y.endData?y.endData:void 0,contractType:""!==y.type?y.type:void 0}}).then(e=>{var t,n;(null==e?void 0:null===(n=e.data)||void 0===n?void 0:null===(t=n.data)||void 0===t?void 0:t.length)>0&&(k(e.data.meta),O(e.data.data))}).catch(e=>{(0,d.Z)(e,"N\xe3o foi possivel buscar os investidores do assessor")}).finally(()=>{g(!1)})};return(0,x.useEffect)(()=>{"broker"===n?getBroker():getAdvisor()},[j]),(0,r.jsxs)("div",{className:"".concat(m?"fixed w-full":"relative"),children:[(0,r.jsx)(s.Z,{}),(0,r.jsx)(i.Z,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"capitalize text-white text-center text-xl",children:["broker"===n?"Broker":"Assessor"," - Contratos Captados"]})}),(0,r.jsxs)("div",{className:"min-h-[400px] rounded-t-md bg-[#1C1C1C] w-full text-white mt-10 overflow-x-auto rounded-b-md border border-[#FF9900] flex flex-col",children:[(0,r.jsx)("div",{className:"flex w-full justify-end p-2",children:(0,r.jsx)(o.Z,{inputPlaceholder:"Pesquisar por nome",handleSearch:"broker"===n?getBroker:getAdvisor,filterData:y,setFilterData:N,activeModal:z,setActiveModal:w})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("table",{className:"w-full relative min-h-20",children:[(0,r.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,r.jsxs)("tr",{className:"w-full",children:[(0,r.jsx)("th",{className:"py-2 max-w-[350px] text-center",children:(0,r.jsx)("p",{className:"font-bold text-sm ",children:"Nome"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"Valor"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"Data e hor\xe1rio"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"Valor Captado"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"Perfil"})}),(0,r.jsx)("th",{className:""})]})}),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("tbody",{className:"w-full",children:v?(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(l.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(l.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(l.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(l.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(l.j,{height:"25px"})})]}):null==_?void 0:_.map((e,t)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"max-w-[350px]",children:(0,r.jsx)("p",{className:"text-sm text-center py-1",children:e.name})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:(0,f.Z)(e.totalContractAmount||0)})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:(0,h.p4)(e.document).length<=11?(0,h.VL)(e.document):(0,h.PK)(e.document)})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:p().utc(e.createdAt).format("DD/MM/YYYY [\xe0s] HH:mm")})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:(0,f.Z)(e.totalCaptured||0)})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:"Investidor"})})]},t))})})]})}),(0,r.jsx)(a.Z,{lastPage:Z.lastPage,page:j,perPage:Z.perPage,setPage:b,totalItems:String(Z.total)})]})]})})]})}},9715:function(e,t,n){"use strict";n.d(t,{Z:function(){return Pagination}});var r=n(7437),o=n(13),s=n(3217);function Pagination(e){let{lastPage:t,page:n,setPage:a,totalItems:i,perPage:l,loading:c=!1}=e;return c?(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:"Carregando..."})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(o.Z,{color:"#555",width:15})}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818]",children:"..."}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(s.Z,{color:"#555",width:15})})]})]}):(isNaN(t),(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:(()=>{let e=parseInt(i,10);if(0===e)return"Nenhum resultado";let t=(n-1)*l+1,r=Math.min(n*l,e);return"Exibindo ".concat(t," a ").concat(r," de ").concat(e," resultados")})()})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(n>1?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>n>1&&a(n-1),children:(0,r.jsx)(o.Z,{color:"#fff",width:15})}),(0,r.jsx)("div",{className:"flex gap-2 select-none",children:(()=>{let e=new Set;e.add(1),e.add(t),n>1&&e.add(n-1),e.add(n),n<t&&e.add(n+1);let o=Array.from(e).sort((e,t)=>e-t);return o.map((e,t,o)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[t>0&&o[t-1]!==e-1&&(0,r.jsx)("div",{className:"flex items-center justify-center",children:"..."}),Number.isFinite(e)&&(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer bg-[#262626] ".concat(n===e?"text-[#FF9900]":"text-white"),onClick:()=>a(e),children:e})]},e))})()}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(n<t?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>n<t&&a(n+1),children:(0,r.jsx)(s.Z,{color:"#fff",width:15})})]})]}))}},6654:function(e,t,n){"use strict";n.d(t,{Z:function(){return returnError}});var r=n(3014);function returnError(e,t){var n,o,s,a;let i=(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(n=o.data)||void 0===n?void 0:n.message)||(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.error);if(Array.isArray(i))return i.forEach(e=>{r.Am.error(e,{toastId:e})}),i.join("\n");if("string"==typeof i)return r.Am.error(i,{toastId:i}),i;if("object"==typeof i&&null!==i){let e=Object.values(i).flat().join("\n");return r.Am.error(e,{toastId:e}),e}return r.Am.error(t,{toastId:t}),t}},1458:function(e,t,n){"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}n.d(t,{A:function(){return cleanValue},Z:function(){return formatValue}})},13:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))});t.Z=o},3217:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))});t.Z=o}},function(e){e.O(0,[6990,7326,8276,5371,6946,3151,2971,7864,1744],function(){return e(e.s=3844)}),_N_E=e.O()}]);