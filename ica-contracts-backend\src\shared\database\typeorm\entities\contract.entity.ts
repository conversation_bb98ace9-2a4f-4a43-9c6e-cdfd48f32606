import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm';

import { AddendumEntity } from './addendum.entity';
import { ContractAdvisorEntity } from './contract-advisor.entity';
import { ContractAuditEntity } from './contract-audit.entity';
import { ContractDeletionEntity } from './contract-deletion.entity';
import { ContractEventEntity } from './contract-event.entity';
import { IncomePaymentScheduledEntity } from './income-payment-scheduled.entity';
import { IncomeReportsContractsEntity } from './income-reports-contracts.entity';
import { NotificationEntity } from './notification.entity';
import { OwnerRoleRelationEntity } from './owner-role-relation.entity';
import { PreRegisterEntity } from './pre-register.entity';

@Entity('contract')
export class ContractEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'external_id', nullable: true })
  externalId: number;

  @Column({ name: 'owner_role_relation', nullable: true })
  brokerId: string;

  @Column({ name: 'investor_id', nullable: true })
  investorId: string;

  @Column({ name: 'start_contract', type: 'date' })
  startContract: Date;

  @Column({ name: 'end_contract', type: 'date' })
  endContract: Date;

  @Column({ name: 'contract_pdf', nullable: true })
  contractPdf: string;

  @Column({ name: 'old_contract_pdf', nullable: true })
  oldContractPdf: string;

  @Column({ name: 'proof_payment', nullable: true })
  proofPayment: string;

  @Column({ name: 'sign_investor', nullable: true })
  signInvestor: string;

  @Column({ name: 'sign_ica', nullable: true })
  signIca: string;

  @Column({ name: 'status', nullable: true, default: 'aberto' })
  status: string;

  @Column({ name: 'type', default: 'p2p' })
  type: string;

  @Column({ name: 'is_debenture', default: false })
  isDebenture: boolean;

  @Column({ name: 'contract_number', default: 1 })
  contractNumber: number;

  @Column({ name: 'duration_in_months', nullable: true })
  durationInMonths: number;
  
  @Column({
    name: 'broker_participation_percentage',
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
  })
  brokerParticipationPercentage: string;

  @Column({
    name: 'advisor_participation_percentage',
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
  })
  advisorParticipationPercentage: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => OwnerRoleRelationEntity, {
    nullable: true,
  })
  @JoinColumn({
    name: 'owner_role_relation',
    referencedColumnName: 'id',
  })
  ownerRoleRelation: Relation<OwnerRoleRelationEntity>;

  @OneToMany(() => PreRegisterEntity, (signed) => signed.contract, {
    nullable: true,
    cascade: true,
  })
  signataries: PreRegisterEntity[];

  @ManyToOne(
    () => OwnerRoleRelationEntity,
    (investor) => investor.contractInvestor,
  )
  @JoinColumn([{ name: 'investor_id', referencedColumnName: 'id' }])
  investor: Relation<OwnerRoleRelationEntity>;

  @OneToMany(() => ContractEventEntity, (event) => event.contract)
  events: Relation<ContractEventEntity[]>;

  @OneToMany(
    () => IncomePaymentScheduledEntity,
    (scheduledPayments) => scheduledPayments.contract,
  )
  scheduledPayments: Relation<IncomePaymentScheduledEntity[]>;

  @OneToMany(() => AddendumEntity, (addendum) => addendum.contract)
  addendum: Relation<AddendumEntity[]>;

  @OneToMany(
    () => ContractAdvisorEntity,
    (contractAdvisor) => contractAdvisor.contract,
  )
  contractAdvisors: Relation<ContractAdvisorEntity[]>;

  latestEvent?: ContractEventEntity;

  @OneToMany(() => NotificationEntity, (item) => item.contract)
  notifications: NotificationEntity[];

  @OneToMany(
    () => IncomeReportsContractsEntity,
    (incomeReports) => incomeReports.contracts,
  )
  incomeReportsContracts: IncomeReportsContractsEntity[];

  @OneToMany(() => ContractAuditEntity, (audit) => audit.contract)
  audits: Relation<ContractAuditEntity[]>;
  @OneToMany(() => ContractDeletionEntity, (deletion) => deletion.contract)
  deletions: Relation<ContractDeletionEntity[]>;
}
