{"version": 3, "file": "contract-status.enum.js", "sourceRoot": "/", "sources": ["shared/enums/contract-status.enum.ts"], "names": [], "mappings": ";;;AAAA,IAAY,kBAmBX;AAnBD,WAAY,kBAAkB;IAC5B,uCAAiB,CAAA;IACjB,qCAAe,CAAA;IACf,6CAAuB,CAAA;IACvB,uDAAiC,CAAA;IACjC,iFAA2D,CAAA;IAC3D,2DAAqC,CAAA;IACrC,uDAAiC,CAAA;IACjC,2EAAqD,CAAA;IACrD,uCAAiB,CAAA;IACjB,2DAAqC,CAAA;IACrC,iEAA2C,CAAA;IAC3C,2DAAqC,CAAA;IACrC,qFAA+D,CAAA;IAC/D,2CAAqB,CAAA;IACrB,6DAAuC,CAAA;IACvC,2EAAqD,CAAA;IACrD,yCAAmB,CAAA;IACnB,yCAAmB,CAAA;AACrB,CAAC,EAnBW,kBAAkB,kCAAlB,kBAAkB,QAmB7B", "sourcesContent": ["export enum ContractStatusEnum {\r\n  ABERTO = 'aberto',\r\n  DRAFT = 'DRAFT',\r\n  GENERATED = 'GENERATED',\r\n  SIGNATURE_SENT = 'SIGNATURE_SENT',\r\n  AWAITING_INVESTOR_SIGNATURE = 'AWAITING_INVESTOR_SIGNATURE',\r\n  AWAITING_DEPOSIT = 'AWAITING_DEPOSIT',\r\n  AWAITING_AUDIT = 'AWAITING_AUDIT',\r\n  AWAITING_AUDIT_SIGNATURE = 'AWAITING_AUDIT_SIGNATURE',\r\n  ACTIVE = 'ACTIVE',\r\n  SIGNATURE_FAILED = 'SIGNATURE_FAILED',\r\n  EXPIRED_BY_INVESTOR = 'EXPIRED_BY_INVESTOR',\r\n  EXPIRED_BY_AUDIT = 'EXPIRED_BY_AUDIT',\r\n  EXPIRED_FAILURE_PROOF_PAYMENT = 'EXPIRED_FAILURE_PROOF_PAYMENT',\r\n  REJECTED = 'REJECTED',\r\n  REJECTED_BY_AUDIT = 'REJECTED_BY_AUDIT', // REJEITADO PELA AUDITORIA\r\n  GENERATE_CONTRACT_FAILED = 'GENERATE_CONTRACT_FAILED',\r\n  EXPIRED = 'EXPIRED',\r\n  DELETED = 'DELETED',\r\n}\r\n"]}