(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6930],{4682:function(e,s,t){Promise.resolve().then(t.bind(t,1089))},1089:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return CadastroManual}});var a=t(7437),i=t(2265),n=t(3877),r=t(8637),l=t(4207),o=t(9701),d=t(1865),c=t(2875),m=t(5968),x=t(977),u=t(4568),h=t(6654),p=t(3014),g=t(3336),b=t(3220),f=t(7227),j=t(5255),v=t(5233),N=t(919),w=t(2067),y=t.n(w),C=t(3277),S=t(3256),F=t(4984),Z=t(7412),k=t(4765),D=t(3588),E=t(9891),P=t(7014);function PhysicalRegister(e){var s,t,n,r,w,q,O,T,M,_,I,A,R,V,L,Y,U,B,z,K,W,G,Q,X,J,$;let{modalityContract:H}=e,[ee,es]=(0,i.useState)(""),[et,ea]=(0,i.useState)(""),[ei,en]=(0,i.useState)(!1),[er,el]=(0,i.useState)([]),[eo,ed]=(0,i.useState)(),[ec,em]=(0,i.useState)(),[ex,eu]=(0,i.useState)(),[eh,ep]=(0,i.useState)(),eg=(0,S.e)(),{register:eb,handleSubmit:ef,watch:ej,setValue:ev,reset:eN,formState:{errors:ew,isValid:ey}}=(0,d.cI)({resolver:(0,o.X)(x.$r),mode:"all",defaultValues:{initDate:y()().format("DD/MM/YYYY"),isSCP:"SCP"===H,purchaseWith:"pix",profile:"moderate",isDebenture:"s"}}),eC=ej("term"),eS=(0,D.D)({mutationFn:async e=>u.Z.post("/account/create/existing-contract",e),onSuccess:()=>{p.Am.success("Investidor cadastrado com sucesso!"),eN(),el([]),ed(void 0),em(void 0),eu(void 0),ep(void 0)},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o contrato")}}),{data:eF=[]}=(0,E.a)({queryKey:["brokers"],queryFn:async()=>{let e=await u.Z.get("superadmin"===eg.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data}}),{data:eZ=[],isLoading:ek}=(0,E.a)({queryKey:["acessors",et],queryFn:async()=>{let e=await u.Z.get(function(){switch(eg.name){case"superadmin":return"/wallets/list-advisors-broker";case"admin":return"/wallets/admin/advisors-broker";case"broker":return"/wallets/broker/advisors";default:return""}}(),{params:{brokerId:"broker"!==eg.name?et:void 0}});return e.data},enabled:"broker"===eg.name||!et}),eD=(0,i.useMemo)(()=>{if(ej("initDate")&&eC){let e=(0,N.H)({investDate:eC,startDate:ej("initDate")});return y()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[ej("initDate"),eC]);(0,i.useEffect)(()=>{eD&&ev("endDate",eD,{shouldValidate:!0})},[eD,ev]),(0,i.useEffect)(()=>{"broker"===eg.name&&eg.roleId&&ea(eg.roleId)},[eg]),(0,E.a)({queryKey:["address",ej("zipCode")],queryFn:async()=>{let e=await (0,Z.x)(ej("zipCode"));e&&(ev("neighborhood",e.neighborhood,{shouldValidate:!0}),ev("city",e.city,{shouldValidate:!0}),ev("state",e.state,{shouldValidate:!0}),ev("street",e.street,{shouldValidate:!0}))},enabled:(null===(s=ej("zipCode"))||void 0===s?void 0:s.length)===9});let eE=(0,i.useCallback)(()=>{el(e=>[...e,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},[]),eP=(0,i.useCallback)(async e=>{let s=Number(e.yield.replace(",",".")),t=new FormData;t.append("role",eg.name),"broker"!==eg.name&&t.append("brokerId",et),t.append("personType","PF"),t.append("contractType",H),t.append("individual[fullName]",(0,P.Z)(String(e.name).trim())),t.append("individual[cpf]",(0,m.p4)(e.document).trim()),t.append("individual[rg]",e.rg),t.append("individual[issuingAgency]",e.issuer),t.append("individual[nationality]",e.placeOfBirth),t.append("individual[occupation]",(0,P.Z)(e.occupation)),t.append("individual[birthDate]",e.dtBirth),t.append("individual[email]",e.email),t.append("individual[phone]","55".concat((0,m.p4)(e.phoneNumber))),t.append("individual[motherName]",(0,P.Z)(e.motherName)),t.append("individual[address][street]",e.street),t.append("individual[address][neighborhood]",e.neighborhood),t.append("individual[address][city]",e.city),t.append("individual[address][state]",e.state),t.append("individual[address][postalCode]",(0,m.p4)(e.zipCode)),t.append("individual[address][number]",e.number),t.append("individual[address][complement]",e.complement||""),t.append("bankAccount[bank]",e.bank),t.append("bankAccount[agency]",e.agency),t.append("bankAccount[account]",e.accountNumber),t.append("bankAccount[pix]",e.pix),t.append("bankAccount[accountType]","CORRENTE"),t.append("investment[amount]",String((0,C.Z)(e.value))),t.append("investment[monthlyRate]",String(s)),t.append("investment[durationInMonths]",e.term),t.append("investment[paymentMethod]",e.purchaseWith),t.append("investment[startDate]","".concat(e.initDate,"T00:00:00.000Z")),t.append("investment[endDate]","".concat(eD?y()(eD,"DD/MM/YYYY").format("YYYY-MM-DD"):"","T00:00:00.000Z")),t.append("investment[profile]",e.profile),t.append("investment[isDebenture]","s"===e.isDebenture?"true":"false"),"SCP"===H&&t.append("investment[quotaQuantity]",e.amountQuotes||""),1===er.length?er.map((e,s)=>{t.append("advisors[".concat(s,"][advisorId]"),e.id),t.append("advisors[".concat(s,"][rate]"),String(e.taxValue))}):er.length>1&&""!==er[0].id&&er.map((e,s)=>{t.append("advisors[".concat(s,"][advisorId]"),e.id),t.append("advisors[".concat(s,"][rate]"),String(e.taxValue))}),eo&&t.append("contract",eo[0]),ec&&t.append("proofOfPayment",ec[0]),ex&&t.append("personalDocument",ex[0]),eh&&t.append("proofOfResidence",eh[0]),await eS.mutateAsync(t,{onSuccess:()=>{el([]),eN()}})},[er,et,eo,ex,eh,H,ec,eg.name,eS]),eq=(0,i.useCallback)((e,s)=>{el(t=>t.map(t=>t.generatedId===e?{...t,taxValue:s}:t))},[]);return(0,a.jsx)("div",{children:(0,a.jsxs)("form",{action:"",onSubmit:ef(eP),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,a.jsx)(v.Z,{color:"black",title:"Dados Pessoais",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(l.Z,{register:eb,name:"name",width:"300px",error:!!ew.name,errorMessage:null==ew?void 0:null===(t=ew.name)||void 0===t?void 0:t.message,label:"Nome completo"}),(0,a.jsx)(l.Z,{register:eb,name:"document",width:"200px",error:!!ew.document,errorMessage:null==ew?void 0:null===(n=ew.document)||void 0===n?void 0:n.message,label:"CPF",setValue:e=>ev("document",(0,m.VL)(e||""),{shouldValidate:!0})}),(0,a.jsx)(l.Z,{register:eb,name:"rg",width:"200px",error:!!ew.rg,errorMessage:null==ew?void 0:null===(r=ew.rg)||void 0===r?void 0:r.message,label:"Identidade"}),(0,a.jsx)(l.Z,{register:eb,name:"issuer",width:"200px",error:!!ew.issuer,errorMessage:null==ew?void 0:null===(w=ew.issuer)||void 0===w?void 0:w.message,label:"Org\xe3o emissor"}),(0,a.jsx)(l.Z,{register:eb,name:"placeOfBirth",width:"200px",error:!!ew.placeOfBirth,errorMessage:null==ew?void 0:null===(q=ew.placeOfBirth)||void 0===q?void 0:q.message,label:"Nacionalidade"}),(0,a.jsx)(l.Z,{register:eb,name:"occupation",width:"200px",error:!!ew.occupation,errorMessage:null==ew?void 0:null===(O=ew.occupation)||void 0===O?void 0:O.message,label:"Ocupa\xe7\xe3o"}),(0,a.jsx)(l.Z,{width:"200px",register:eb,name:"phoneNumber",maxLength:15,error:!!ew.phoneNumber,errorMessage:null==ew?void 0:null===(T=ew.phoneNumber)||void 0===T?void 0:T.message,label:"Celular",setValue:e=>ev("phoneNumber",(0,m.gP)(e||""),{shouldValidate:!0})}),(0,a.jsxs)("div",{style:{width:"200px"},children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ew.dtBirth&&"- ".concat(ew.dtBirth.message)})]}),(0,a.jsx)("input",{...eb("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value=s.value.replace(/[^0-9\-]/g,""),s.value.length>10&&(s.value=s.value.slice(0,10));let[t,a,i]=s.value.split("-");t&&t.length>4&&(s.value=t.slice(0,4)+(a?"-"+a:"")+(i?"-"+i:""))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ew.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)(l.Z,{register:eb,name:"email",width:"300px",error:!!ew.email,errorMessage:null==ew?void 0:null===(M=ew.email)||void 0===M?void 0:M.message,label:"E-mail"}),(0,a.jsx)(l.Z,{register:eb,name:"motherName",width:"300px",error:!!ew.motherName,errorMessage:null==ew?void 0:null===(_=ew.motherName)||void 0===_?void 0:_.message,label:"Nome da m\xe3e"}),(0,a.jsx)(l.Z,{register:eb,name:"zipCode",width:"200px",error:!!ew.zipCode,errorMessage:null==ew?void 0:null===(I=ew.zipCode)||void 0===I?void 0:I.message,label:"CEP",setValue:e=>{ev("zipCode",(0,m.Tc)(e),{shouldValidate:!0})}}),(0,a.jsx)(l.Z,{register:eb,name:"neighborhood",width:"200px",error:!!ew.neighborhood,errorMessage:null==ew?void 0:null===(A=ew.neighborhood)||void 0===A?void 0:A.message,label:"Bairro"}),(0,a.jsx)(l.Z,{register:eb,name:"street",width:"300px",error:!!ew.street,errorMessage:null==ew?void 0:null===(R=ew.street)||void 0===R?void 0:R.message,label:"Rua"}),(0,a.jsx)(l.Z,{register:eb,name:"city",width:"200px",error:!!ew.city,errorMessage:null==ew?void 0:null===(V=ew.city)||void 0===V?void 0:V.message,label:"Cidade"}),(0,a.jsx)(l.Z,{register:eb,maxLength:2,setValue:e=>ev("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!ew.state,errorMessage:null==ew?void 0:null===(L=ew.state)||void 0===L?void 0:L.message,label:"Estado"}),(0,a.jsx)(l.Z,{register:eb,name:"number",width:"200px",error:!!ew.number,errorMessage:null==ew?void 0:null===(Y=ew.number)||void 0===Y?void 0:Y.message,label:"N\xfamero"}),(0,a.jsx)(l.Z,{register:eb,name:"complement",width:"200px",error:!!ew.complement,errorMessage:null==ew?void 0:null===(U=ew.complement)||void 0===U?void 0:U.message,label:"Complemento"})]})}),(0,a.jsx)(v.Z,{color:"black",title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(l.Z,{register:eb,name:"value",width:"200px",error:!!ew.value,errorMessage:null==ew?void 0:null===(B=ew.value)||void 0===B?void 0:B.message,label:"Valor",setValue:e=>ev("value",(0,m.Ht)(e||""),{shouldValidate:!0})}),(0,a.jsx)(l.Z,{register:eb,type:"number",name:"term",width:"250px",error:!!ew.term,errorMessage:null==ew?void 0:null===(z=ew.term)||void 0===z?void 0:z.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),(0,a.jsx)(l.Z,{register:eb,type:"text",name:"yield",width:"250px",error:!!ew.yield,errorMessage:null==ew?void 0:null===(K=ew.yield)||void 0===K?void 0:K.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),(0,a.jsxs)("div",{className:"w-[200px]",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Comprar com"}),(0,a.jsxs)(k.Z,{value:ej("purchaseWith"),onChange:e=>ev("purchaseWith",e.target.value,{shouldValidate:!0}),children:[(0,a.jsx)("option",{value:"pix",children:"PIX"}),(0,a.jsx)("option",{value:"boleto",children:"Boleto"}),(0,a.jsx)("option",{value:"bank_transfer",children:"Transfer\xeancia"})]})]}),"SCP"===H&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(l.Z,{register:eb,type:"number",name:"amountQuotes",width:"150px",error:!!ew.amountQuotes,errorMessage:null==ew?void 0:null===(W=ew.amountQuotes)||void 0===W?void 0:W.message,label:"Quantidade de cotas"})}),(0,a.jsx)(l.Z,{type:"date",register:eb,maxDate:y()().format("YYYY-MM-DD"),name:"initDate",width:"200px",setValue:e=>ev("initDate",e,{shouldValidate:!0}),error:!!ew.initDate,errorMessage:null==ew?void 0:null===(G=ew.initDate)||void 0===G?void 0:G.message,label:"Inicio do contrato"}),(0,a.jsx)(l.Z,{type:"date",register:eb,name:"endDate",value:eD?y()(eD,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"200px",disabled:!0,label:"Final do contrato"}),(0,a.jsxs)("div",{className:"w-[200px]",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Perfil"}),(0,a.jsxs)(k.Z,{value:ej("profile"),onChange:e=>ev("profile",e.target.value,{shouldValidate:!0}),children:[(0,a.jsx)("option",{value:"conservative",children:"Conservador"}),(0,a.jsx)("option",{value:"moderate",children:"Moderado"}),(0,a.jsx)("option",{value:"aggressive",children:"Agressivo"})]})]}),(0,a.jsxs)("div",{className:"w-[100px]",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Deb\xeanture"}),(0,a.jsxs)(k.Z,{value:ej("isDebenture"),onChange:e=>ev("isDebenture",e.target.value,{shouldValidate:!0}),children:[(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})]})}),"broker"!==eg.name&&(0,a.jsx)(v.Z,{color:"black",title:"Selecione o broker",children:(0,a.jsx)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-start",children:(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsx)(g.Z,{label:"Broker",items:eF,value:et,setValue:ea})})})}),("broker"!==eg.name&&""!==et||"broker"===eg.name)&&(0,a.jsx)(v.Z,{color:"black",title:"Adicionar Assessor",children:(0,a.jsxs)("div",{children:[er.length>0?null==er?void 0:er.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-end gap-4 mb-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(g.Z,{label:"Selecione um Assessor",items:eZ,value:"",setValue:()=>{},loading:ei,handleChange:t=>{let a=er.filter(s=>s.generatedId===e.generatedId)[0],i={generatedId:a.generatedId,id:t.id,taxValue:Number(t.rate)};er[s]=i,el([...er])}})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(b.Z,{label:"Adicione a Taxa - em %",id:"",name:"",value:String(e.taxValue),type:"text",onChange:s=>eq(e.generatedId,s.target.value)})}),(0,a.jsx)("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=er.filter((e,t)=>t!==s);el(e)},children:(0,a.jsx)(f.Z,{width:20})})]},s)):(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),(0,a.jsx)("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:eE,children:(0,a.jsx)(j.Z,{width:25,color:"#fff"})})]})}),(0,a.jsx)(v.Z,{color:"black",title:"Dados bancarios",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(l.Z,{register:eb,name:"bank",width:"300px",error:!!ew.bank,errorMessage:null==ew?void 0:null===(Q=ew.bank)||void 0===Q?void 0:Q.message,label:"Banco"}),(0,a.jsx)(l.Z,{register:eb,name:"agency",width:"200px",error:!!ew.agency,errorMessage:null==ew?void 0:null===(X=ew.agency)||void 0===X?void 0:X.message,label:"Ag\xeancia"}),(0,a.jsx)(l.Z,{register:eb,name:"accountNumber",width:"200px",error:!!ew.accountNumber,errorMessage:null==ew?void 0:null===(J=ew.accountNumber)||void 0===J?void 0:J.message,label:"Conta"}),(0,a.jsx)(l.Z,{register:eb,name:"pix",width:"250px",error:!!ew.pix,errorMessage:null==ew?void 0:null===($=ew.pix)||void 0===$?void 0:$.message,label:"Pix"})]})}),(0,a.jsx)(v.Z,{color:"black",title:"Anexo de documentos",children:(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato"}),(0,a.jsx)(F.Z,{onFileUploaded:ed})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante"}),(0,a.jsx)("div",{children:(0,a.jsx)(F.Z,{onFileUploaded:em})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)("div",{children:(0,a.jsx)(F.Z,{onFileUploaded:eu})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)("div",{children:(0,a.jsx)(F.Z,{onFileUploaded:ep})})]})]})})}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",size:"lg",loading:eS.isPending,disabled:eS.isPending||!eD||!ey||!eo||!ec||!ex||!eh})})]})})}var q=t(5554);let O=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}];function BusinessRegister(e){var s,t,n,r,w,k,T,M,_,I,A,R,V,L,Y,U,B,z,K,W,G,Q,X,J,$,H,ee,es,et,ea,ei,en,er,el,eo,ed,ec,em,ex,eu;let{modalityContract:eh}=e,[ep,eg]=(0,i.useState)(""),[eb,ef]=(0,i.useState)(""),[ej,ev]=(0,i.useState)(),[eN,ew]=(0,i.useState)(),[ey,eC]=(0,i.useState)(),[eS,eF]=(0,i.useState)(),[eZ,ek]=(0,i.useState)(""),[eD,eE]=(0,i.useState)([]),eP=(0,S.e)(),{register:eq,handleSubmit:eO,watch:eT,setValue:eM,reset:e_,formState:{errors:eI,isValid:eA}}=(0,d.cI)({resolver:(0,o.X)(x._R),mode:"all",defaultValues:{initDate:y()().format("DD/MM/YYYY"),isSCP:"SCP"===eh,purchaseWith:"pix",profile:"moderate",isDebenture:"s"}}),eR=eT("term"),{data:eV=[]}=(0,E.a)({queryKey:["brokers"],queryFn:async()=>{let e=await u.Z.get("superadmin"===eP.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data},enabled:"broker"!==eP.name&&"advisor"!==eP.name}),{data:eL=[],isLoading:eY}=(0,E.a)({queryKey:["acessors",eZ],queryFn:async()=>{let e=await u.Z.get(eB(),{params:{brokerId:"broker"!==eP.name?eZ:void 0}});return e.data},enabled:"broker"!==eP.name&&""!==eZ||"broker"===eP.name}),eU=(0,i.useMemo)(()=>{if(eT("initDate")&&eR){let e=(0,N.H)({investDate:eR,startDate:eT("initDate")});return y()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[eT("initDate"),eR]);(0,i.useEffect)(()=>{eU&&eM("endDate",eU,{shouldValidate:!0})},[eU,eM]);let eB=(0,i.useCallback)(()=>{switch(eP.name){case"superadmin":return"/wallets/list-advisors-broker";case"admin":return"/wallets/admin/advisors-broker";case"broker":return"/wallets/broker/advisors";default:return""}},[eP.name]);(0,i.useEffect)(()=>{eM("isSCP","SCP"===eh,{shouldValidate:!0})},[eh]),(0,i.useEffect)(()=>{"broker"===eP.name&&eP.roleId&&ek(eP.roleId)},[eP]);let ez=(0,D.D)({mutationFn:async e=>{await u.Z.post("/account/create/existing-contract",e)},onSuccess:()=>{p.Am.success("Investidor cadastrado com sucesso!"),e_(),eE([]),eC(void 0),eF(void 0),ev(void 0),ew(void 0)},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o contrato!")}}),eK=(0,i.useCallback)(async e=>{let s=Number(e.yield.replace(",",".")),t=new FormData;t.append("personType","PJ"),t.append("contractType",eh),t.append("role",eP.name),"broker"!==eP.name&&t.append("brokerId",eZ),t.append("bankAccount[bank]",e.bank),t.append("bankAccount[agency]",e.agency),t.append("bankAccount[account]",e.accountNumber),t.append("bankAccount[pix]",e.pix),t.append("bankAccount[accountType]","CORRENTE"),t.append("investment[amount]",String((0,C.Z)(e.value))),t.append("investment[monthlyRate]",String(s)),t.append("investment[durationInMonths]",e.term),t.append("investment[paymentMethod]",e.purchaseWith),t.append("investment[startDate]","".concat(e.initDate,"T00:00:00.000Z")),t.append("investment[endDate]","".concat(eU?y()(eU,"DD/MM/YYYY").format("YYYY-MM-DD"):"","T00:00:00.000Z")),t.append("investment[profile]",e.profile),t.append("investment[isDebenture]","s"===e.isDebenture?"true":"false"),"SCP"===eh&&t.append("investment[quotaQuantity]",e.amountQuotes||""),t.append("company[corporateName]",e.name),t.append("company[cnpj]",(0,m.p4)(e.document)),t.append("company[type]",e.companyType),t.append("company[address][street]",e.companyStreet),t.append("company[address][city]",e.companyCity),t.append("company[address][state]",e.companyState),t.append("company[address][neighborhood]",e.companyNeighborhood),t.append("company[address][postalCode]",(0,m.p4)(e.companyZipCode)),t.append("company[address][number]",e.companyNumber),t.append("company[address][complement]",e.companyComplement||""),t.append("company[representative][fullName]",(0,P.Z)(e.ownerName)),t.append("company[representative][cpf]",(0,m.p4)(e.ownerDocument)),t.append("company[representative][rg]",e.rg),t.append("company[representative][issuingAgency]",e.issuer||""),t.append("company[representative][nationality]",e.placeOfBirth),t.append("company[representative][occupation]",(0,P.Z)(e.occupation)),t.append("company[representative][birthDate]",e.dtBirth),t.append("company[representative][email]",e.email),t.append("company[representative][phone]","55".concat((0,m.p4)(e.phoneNumber))),t.append("company[representative][motherName]",(0,P.Z)(e.motherName)),t.append("company[representative][address][street]",e.street),t.append("company[representative][address][city]",e.city),t.append("company[representative][address][state]",e.state),t.append("company[representative][address][neighborhood]",e.neighborhood),t.append("company[representative][address][postalCode]",(0,m.p4)(e.zipCode)),t.append("company[representative][address][number]",e.number),t.append("company[representative][address][complement]",e.complement||""),(1===eD.length||eD.length>1&&""!==eD[0].id)&&eD.forEach((e,s)=>{t.append("advisors[".concat(s,"][advisorId]"),e.id),t.append("advisors[".concat(s,"][rate]"),String(e.taxValue))}),ej&&t.append("contract",ej[0]),eN&&t.append("proofOfPayment",eN[0]),ey&&t.append("personalDocument",ey[0]),eS&&t.append("proofOfResidence",eS[0]),ez.mutate(t)},[ez,eD,ej,eN,ey,eS,eh,eZ,eP.name]);(0,E.a)({queryKey:["address",eT("zipCode")],queryFn:async()=>{let e=await (0,Z.x)(eT("zipCode"));e&&(eM("neighborhood",e.neighborhood,{shouldValidate:!0}),eM("city",e.city,{shouldValidate:!0}),eM("state",e.state,{shouldValidate:!0}),eM("street",e.street,{shouldValidate:!0}))},enabled:(null===(s=eT("zipCode"))||void 0===s?void 0:s.length)===9}),(0,E.a)({queryKey:["address",eT("companyZipCode")],queryFn:async()=>{let e=await (0,Z.x)(eT("companyZipCode"));e&&(eM("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),eM("companyCity",e.city,{shouldValidate:!0}),eM("companyState",e.state,{shouldValidate:!0}),eM("companyStreet",e.street,{shouldValidate:!0}))},enabled:(null===(t=eT("companyZipCode"))||void 0===t?void 0:t.length)===9});let eW=(0,i.useCallback)((e,s)=>{eE(t=>t.map(t=>t.generatedId===e?{...t,taxValue:s}:t))},[]),eG=(0,i.useCallback)(()=>{eE(e=>[...e,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},[]);return(0,a.jsx)("div",{children:(0,a.jsxs)("form",{action:"",onSubmit:eO(eK),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,a.jsx)(v.Z,{color:"black",title:"Dados Pessoais - Representante",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(l.Z,{register:eq,name:"ownerName",width:"300px",error:!!eI.ownerName,errorMessage:null==eI?void 0:null===(n=eI.ownerName)||void 0===n?void 0:n.message,label:"Nome"}),(0,a.jsx)(l.Z,{register:eq,name:"ownerDocument",width:"200px",error:!!eI.ownerDocument,errorMessage:null==eI?void 0:null===(r=eI.ownerDocument)||void 0===r?void 0:r.message,label:"CPF",setValue:e=>eM("ownerDocument",(0,m.VL)(e||""),{shouldValidate:!0})}),(0,a.jsx)(l.Z,{register:eq,name:"rg",width:"200px",error:!!eI.rg,errorMessage:null==eI?void 0:null===(w=eI.rg)||void 0===w?void 0:w.message,label:"RG"}),(0,a.jsx)(l.Z,{register:eq,name:"issuer",width:"200px",error:!!eI.issuer,errorMessage:null==eI?void 0:null===(k=eI.issuer)||void 0===k?void 0:k.message,label:"Org\xe3o emissor"}),(0,a.jsx)(l.Z,{register:eq,name:"placeOfBirth",width:"200px",error:!!eI.placeOfBirth,errorMessage:null==eI?void 0:null===(T=eI.placeOfBirth)||void 0===T?void 0:T.message,label:"Nacionalidade"}),(0,a.jsx)(l.Z,{register:eq,name:"occupation",width:"200px",error:!!eI.occupation,errorMessage:null==eI?void 0:null===(M=eI.occupation)||void 0===M?void 0:M.message,label:"Ocupa\xe7\xe3o"}),(0,a.jsx)(l.Z,{register:eq,name:"motherName",width:"250px",error:!!eI.motherName,errorMessage:null==eI?void 0:null===(_=eI.motherName)||void 0===_?void 0:_.message,label:"Nome da m\xe3e"}),(0,a.jsxs)("div",{style:{width:"200px"},children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eI.dtBirth&&"- ".concat(eI.dtBirth.message)})]}),(0,a.jsx)("input",{...eq("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eI.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)(l.Z,{width:"200px",register:eq,name:"phoneNumber",error:!!eI.phoneNumber,errorMessage:null==eI?void 0:null===(I=eI.phoneNumber)||void 0===I?void 0:I.message,label:"Celular",maxLength:15,setValue:e=>eM("phoneNumber",(0,m.gP)(e||""),{shouldValidate:!0})}),(0,a.jsx)(l.Z,{register:eq,name:"email",width:"300px",error:!!eI.email,errorMessage:null==eI?void 0:null===(A=eI.email)||void 0===A?void 0:A.message,label:"E-mail"}),(0,a.jsx)(l.Z,{register:eq,name:"zipCode",width:"200px",error:!!eI.zipCode,errorMessage:null==eI?void 0:null===(R=eI.zipCode)||void 0===R?void 0:R.message,label:"CEP",setValue:e=>{eg(e),eM("zipCode",(0,m.Tc)(e),{shouldValidate:!0})}}),(0,a.jsx)(l.Z,{register:eq,name:"neighborhood",width:"300px",error:!!eI.neighborhood,errorMessage:null==eI?void 0:null===(V=eI.neighborhood)||void 0===V?void 0:V.message,label:"Bairro"}),(0,a.jsx)(l.Z,{register:eq,name:"street",width:"300px",error:!!eI.street,errorMessage:null==eI?void 0:null===(L=eI.street)||void 0===L?void 0:L.message,label:"Rua"}),(0,a.jsx)(l.Z,{register:eq,name:"city",width:"200px",error:!!eI.city,errorMessage:null==eI?void 0:null===(Y=eI.city)||void 0===Y?void 0:Y.message,label:"Cidade"}),(0,a.jsx)(l.Z,{register:eq,maxLength:2,setValue:e=>eM("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!eI.state,errorMessage:null==eI?void 0:null===(U=eI.state)||void 0===U?void 0:U.message,label:"Estado"}),(0,a.jsx)(l.Z,{register:eq,name:"number",width:"200px",error:!!eI.number,errorMessage:null==eI?void 0:null===(B=eI.number)||void 0===B?void 0:B.message,label:"N\xfamero"}),(0,a.jsx)(l.Z,{register:eq,name:"complement",width:"200px",error:!!eI.complement,errorMessage:null==eI?void 0:null===(z=eI.complement)||void 0===z?void 0:z.message,label:"Complemento"})]})}),(0,a.jsx)(v.Z,{color:"black",title:"Dados da empresa",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(l.Z,{register:eq,name:"name",width:"400px",error:!!eI.name,errorMessage:null==eI?void 0:null===(K=eI.name)||void 0===K?void 0:K.message,label:"Raz\xe3o Social"}),(0,a.jsx)(l.Z,{register:eq,name:"document",width:"200px",error:!!eI.document,errorMessage:null==eI?void 0:null===(W=eI.document)||void 0===W?void 0:W.message,label:"CNPJ",setValue:e=>eM("document",(0,m.PK)(e||""),{shouldValidate:!0})}),(0,a.jsx)(q.Z,{width:"200px",name:"companyType",register:eq,options:O,error:!!eI.companyType,errorMessage:null==eI?void 0:null===(G=eI.companyType)||void 0===G?void 0:G.message,label:"Tipo"}),(0,a.jsx)(l.Z,{register:eq,name:"companyZipCode",width:"200px",error:!!eI.companyZipCode,errorMessage:null==eI?void 0:null===(Q=eI.companyZipCode)||void 0===Q?void 0:Q.message,label:"CEP",setValue:e=>{ef(e),eM("companyZipCode",(0,m.Tc)(e),{shouldValidate:!0})}}),(0,a.jsx)(l.Z,{register:eq,name:"companyNeighborhood",width:"300px",error:!!eI.companyNeighborhood,errorMessage:null==eI?void 0:null===(X=eI.companyNeighborhood)||void 0===X?void 0:X.message,label:"Bairro"}),(0,a.jsx)(l.Z,{register:eq,name:"companyStreet",width:"300px",error:!!eI.companyStreet,errorMessage:null==eI?void 0:null===(J=eI.companyStreet)||void 0===J?void 0:J.message,label:"Rua"}),(0,a.jsx)(l.Z,{register:eq,name:"companyCity",width:"200px",error:!!eI.companyCity,errorMessage:null==eI?void 0:null===($=eI.companyCity)||void 0===$?void 0:$.message,label:"Cidade"}),(0,a.jsx)(l.Z,{register:eq,maxLength:2,setValue:e=>eM("companyState",String(e).toUpperCase(),{shouldValidate:!0}),name:"companyState",width:"150px",error:!!eI.companyState,errorMessage:null==eI?void 0:null===(H=eI.companyState)||void 0===H?void 0:H.message,label:"Estado"}),(0,a.jsx)(l.Z,{register:eq,name:"companyNumber",width:"200px",error:!!eI.companyNumber,errorMessage:null==eI?void 0:null===(ee=eI.companyNumber)||void 0===ee?void 0:ee.message,label:"N\xfamero"}),(0,a.jsx)(l.Z,{register:eq,name:"companyComplement",width:"200px",error:!!eI.companyComplement,errorMessage:null==eI?void 0:null===(es=eI.companyComplement)||void 0===es?void 0:es.message,label:"Complemento"})]})}),(0,a.jsx)(v.Z,{color:"black",title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(l.Z,{register:eq,name:"value",width:"200px",error:!!eI.value,errorMessage:null==eI?void 0:null===(et=eI.value)||void 0===et?void 0:et.message,label:"Valor",setValue:e=>eM("value",(0,m.Ht)(e||""),{shouldValidate:!0})}),(0,a.jsx)(l.Z,{register:eq,type:"number",name:"term",width:"250px",error:!!eI.term,errorMessage:null==eI?void 0:null===(ea=eI.term)||void 0===ea?void 0:ea.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),(0,a.jsx)(l.Z,{register:eq,type:"text",name:"yield",width:"250px",error:!!eI.yield,errorMessage:null==eI?void 0:null===(ei=eI.yield)||void 0===ei?void 0:ei.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),(0,a.jsx)(q.Z,{width:"200px",name:"purchaseWith",register:eq,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!(null==eI?void 0:eI.purchaseWith),errorMessage:null==eI?void 0:null===(en=eI.purchaseWith)||void 0===en?void 0:en.message,label:"Comprar com"}),"SCP"===eh&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(l.Z,{register:eq,type:"number",name:"amountQuotes",width:"150px",error:!!eI.amountQuotes,errorMessage:null==eI?void 0:null===(er=eI.amountQuotes)||void 0===er?void 0:er.message,label:"Quantidade de cotas"})}),(0,a.jsx)(l.Z,{type:"date",register:eq,maxDate:y()().format("YYYY-MM-DD"),name:"initDate",width:"200px",setValue:e=>eM("initDate",e,{shouldValidate:!0}),error:!!eI.initDate,errorMessage:null==eI?void 0:null===(el=eI.initDate)||void 0===el?void 0:el.message,label:"Inicio do contrato"}),(0,a.jsx)(l.Z,{type:"date",register:eq,name:"endDate",value:eU?y()(eU,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"200px",disabled:!0,label:"Final do contrato"}),(0,a.jsx)(q.Z,{width:"200px",name:"profile",register:eq,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!eI.profile,errorMessage:null==eI?void 0:null===(eo=eI.profile)||void 0===eo?void 0:eo.message,label:"Perfil"}),(0,a.jsx)(q.Z,{width:"100px",name:"isDebenture",register:eq,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!eI.isDebenture,errorMessage:null==eI?void 0:null===(ed=eI.isDebenture)||void 0===ed?void 0:ed.message,label:"Deb\xeanture"})]})}),"broker"!==eP.name&&(0,a.jsx)(v.Z,{color:"black",title:"Selecione o broker",children:(0,a.jsx)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-start",children:(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsx)(g.Z,{label:"Broker",items:eV,value:eZ,setValue:ek})})})}),("broker"!==eP.name&&""!==eZ||"broker"===eP.name)&&(0,a.jsx)(v.Z,{color:"black",title:"Adicionar Assessor",children:(0,a.jsxs)("div",{children:[eD.length>0?null==eD?void 0:eD.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-end gap-4 mb-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(g.Z,{label:"Selecione um Assessor",items:eL,value:"",setValue:()=>{},loading:eY,handleChange:t=>{let a=eD.filter(s=>s.generatedId===e.generatedId)[0],i={generatedId:a.generatedId,id:t.id,taxValue:Number(t.rate)};eD[s]=i,eE([...eD])}})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(b.Z,{label:"Adicione a Taxa - em %",id:"",name:"",value:String(e.taxValue),type:"text",onChange:s=>eW(e.generatedId,s.target.value)})}),(0,a.jsx)("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=eD.filter((e,t)=>t!==s);eE(e)},children:(0,a.jsx)(f.Z,{width:20})})]},s)):(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),(0,a.jsx)("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:eG,children:(0,a.jsx)(j.Z,{width:25,color:"#fff"})})]})}),(0,a.jsx)(v.Z,{color:"black",title:"Dados bancarios",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(l.Z,{register:eq,name:"bank",width:"300px",error:!!eI.bank,errorMessage:null==eI?void 0:null===(ec=eI.bank)||void 0===ec?void 0:ec.message,label:"Banco"}),(0,a.jsx)(l.Z,{register:eq,name:"agency",width:"200px",error:!!eI.agency,errorMessage:null==eI?void 0:null===(em=eI.agency)||void 0===em?void 0:em.message,label:"Ag\xeancia"}),(0,a.jsx)(l.Z,{register:eq,name:"accountNumber",width:"200px",error:!!eI.accountNumber,errorMessage:null==eI?void 0:null===(ex=eI.accountNumber)||void 0===ex?void 0:ex.message,label:"Conta"}),(0,a.jsx)(l.Z,{register:eq,name:"pix",width:"250px",error:!!eI.pix,errorMessage:null==eI?void 0:null===(eu=eI.pix)||void 0===eu?void 0:eu.message,label:"Pix"})]})}),(0,a.jsx)(v.Z,{color:"black",title:"Anexo de documentos",children:(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato"}),(0,a.jsx)(F.Z,{onFileUploaded:ev})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante"}),(0,a.jsx)("div",{children:(0,a.jsx)(F.Z,{onFileUploaded:ew})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)("div",{children:(0,a.jsx)(F.Z,{onFileUploaded:eC})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)("div",{children:(0,a.jsx)(F.Z,{onFileUploaded:eF})})]})]})})}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",size:"lg",loading:ez.isPending,disabled:ez.isPending||!eA||!ej||!eN||!ey||!eS||!eU})})]})})}function CreateInvestor(){let[e,s]=(0,i.useState)("pf"),[t,n]=(0,i.useState)("MUTUO");return(0,a.jsxs)("div",{className:"m-3",children:[(0,a.jsx)("p",{className:"text-xl text-white mb-3",children:"Cadastro de contrato"}),(0,a.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:e=>{let{target:t}=e;return s(t.value)},children:[(0,a.jsx)("option",{value:"pf",children:"Pessoa F\xedsica"}),(0,a.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Tipo de contrato"}),(0,a.jsxs)(k.Z,{value:t,onChange:e=>{let{target:s}=e;return n("MUTUO"===s.value?"MUTUO":"SCP")},children:[(0,a.jsx)("option",{value:"MUTUO",children:"M\xfatuo"}),(0,a.jsx)("option",{value:"SCP",children:"SCP"})]})]})]}),"pj"===e?(0,a.jsx)(BusinessRegister,{modalityContract:t}):(0,a.jsx)(PhysicalRegister,{modalityContract:t})]})}var T=t(7934),M=t(601),_=t(7848),I=t(4425);function BrokerRegisterPf(e){var s;let{typeCreate:t,hide:n,adminId:r}=e,[l,x]=(0,i.useState)("");(0,S.e)();let[g,f]=(0,i.useState)(!1),[j,v]=(0,i.useState)(""),[N,w]=(0,i.useState)(""),[y,C]=(0,i.useState)(),[q,O]=(0,i.useState)(""),[A,R]=(0,i.useState)(),[V,L]=(0,i.useState)(),[Y,U]=(0,i.useState)(""),[B,z]=(0,i.useState)(""),[K,W]=(0,i.useState)(""),[G,Q]=(0,i.useState)({document:!1,residence:!1,parcer:!1}),{register:X,handleSubmit:J,watch:$,setValue:H,reset:ee,formState:{errors:es,isValid:et},getValues:ea}=(0,d.cI)({resolver:(0,o.X)(T.WF),mode:"all"}),ei=(0,D.D)({mutationFn:async e=>{let s=await u.Z.post("/create-wallets/".concat(t),e);return s.data},onSuccess:e=>{p.Am.success("Acesso cadastrado com sucesso!"),f(!0),v(e.id),eo(e.id),w(""),ee(),C(void 0),R(void 0),L(void 0)},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o Broker")}}),en=(0,D.D)({mutationFn:async e=>{let{id:s,type:t,file:a}=e,i=new FormData;return i.append("id",s),i.append("type",t),i.append("file",a),await u.Z.post("/uploads/documents",i)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer"}[s.type.toLowerCase()];t&&er(t)},onError:e=>{(0,h.Z)(e,"Erro ao enviar o documento")}}),er=(0,i.useCallback)(e=>{Q(s=>({...s,[e]:!0}))},[]),el=(0,i.useMemo)(()=>{if("broker"===t&&"pf"===q)return{document:!0,card:!1,mei:!1,residence:!0,social:!1,parcer:!n}},[t,q,n]),eo=(0,i.useCallback)(e=>{let s=[{file:null==y?void 0:y[0],type:"RG",title:"Documento de identidade",required:null==el?void 0:el.document},{file:null==A?void 0:A[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:null==el?void 0:el.residence},{file:null==V?void 0:V[0],type:"CONTRACT",title:"Contrato de parceria",required:null==el?void 0:el.parcer}].filter(e=>e.required&&e.file);p.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"}),s.forEach((t,a)=>{en.mutateAsync({id:e,type:t.type,file:t.file}).then(()=>{a===s.length-1&&p.Am.dismiss("sendDocuments")}).catch(()=>{p.Am.dismiss("sendDocuments")})})},[y,A,V,el,en]);(0,E.a)({queryKey:["address",$("ownerCep")],queryFn:async()=>{let e=await (0,Z.x)((0,m.p4)($("ownerCep")));e&&(H("ownerCity",e.city,{shouldValidate:!0}),H("ownerState",e.state,{shouldValidate:!0}),H("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),H("ownerStreet",e.street,{shouldValidate:!0}))},enabled:(null===(s=$("ownerCep"))||void 0===s?void 0:s.length)===9});let ed=(0,i.useCallback)(async e=>{if(g)return eo(j);if(!(0,_.p)((0,m.p4)(String(e.cpf||""))))return p.Am.warn("CPF do investidor inv\xe1lido!");if(!(0,I.Z)(e.phoneNumber))return p.Am.warn("N\xfamero de telefone inv\xe1lido");if(!(0,M.Z)(e.ownerState))return p.Am.warn("Estado inv\xe1lido");let s={birthDate:e.birthDate,socialName:(0,P.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,m.p4)(String(e.cpf||"")),email:e.email,phoneNumber:"55".concat((0,m.p4)(e.phoneNumber||"")),motherName:(0,P.Z)(e.motherName),pep:"s"===e.pep,password:""!==N?N:void 0,address:{cep:(0,m.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},t={adminId:r,create:{accountType:"PHYSICAL",owner:s},partPercent:e.participationPercentage};await ei.mutateAsync(t)},[g,j,N,eo,ei]);return(0,i.useEffect)(()=>{H("isPf",!0)},[H]),(0,i.useEffect)(()=>{console.log("document",y),console.log("residence",A),console.log("parcer",V),console.log("isValid",et),console.log("errors",es),console.log("form values",$())},[y,A,V,et,es,$]),(0,a.jsx)("div",{className:"md:px-5",children:(0,a.jsx)("form",{onSubmit:J(ed),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.fullName&&"- ".concat(es.fullName.message)})]}),(0,a.jsx)("input",{...X("fullName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.fullName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.cpf&&"- ".concat(es.cpf.message)})]}),(0,a.jsx)("input",{...X("cpf"),onChange:e=>{let{target:s}=e,t=s.value;H("cpf",(0,m.VL)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.cpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.isTaxable&&"- ".concat(es.isTaxable.message)})]}),(0,a.jsxs)(k.Z,{value:$("isTaxable"),onChange:e=>H("isTaxable",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.birthDate&&"- ".concat(es.birthDate.message)})]}),(0,a.jsx)("input",{...X("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.birthDate?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.phoneNumber&&"- ".concat(es.phoneNumber.message)})]}),(0,a.jsx)("input",{...X("phoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return H("phoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.email&&"- ".concat(es.email.message)})]}),(0,a.jsx)("input",{...X("email"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.participationPercentage&&"- ".concat(es.participationPercentage.message)})]}),(0,a.jsx)("input",{...X("participationPercentage"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.motherName&&"- ".concat(es.motherName.message)})]}),(0,a.jsx)("input",{...X("motherName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.motherName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsx)("div",{children:(0,a.jsx)(b.Z,{id:"",label:"Senha",type:"password",value:N,onChange:e=>w(e.target.value),name:"password"})})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.pep&&"- ".concat(es.pep.message)})]}),(0,a.jsxs)(k.Z,{value:$("pep"),onChange:e=>H("pep",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.ownerCep&&"- ".concat(es.ownerCep.message)})]}),(0,a.jsx)("input",{...X("ownerCep"),onChange:e=>{let{target:s}=e;return H("ownerCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.ownerCity&&"- ".concat(es.ownerCity.message)})]}),(0,a.jsx)("input",{...X("ownerCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.ownerState&&"- ".concat(es.ownerState.message)})]}),(0,a.jsx)("input",{...X("ownerState"),maxLength:2,onChange:e=>{let{target:s}=e;return H("ownerState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.ownerState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.ownerNeighborhood&&"- ".concat(es.ownerNeighborhood.message)})]}),(0,a.jsx)("input",{...X("ownerNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.ownerNumber&&"- ".concat(es.ownerNumber.message)})]}),(0,a.jsx)("input",{...X("ownerNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.ownerStreet&&"- ".concat(es.ownerStreet.message)})]}),(0,a.jsx)("input",{...X("ownerStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:es.ownerComplement&&"- ".concat(es.ownerComplement.message)})]}),(0,a.jsx)("input",{...X("ownerComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(es.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)(F.Z,{onFileUploaded:C,fileName:Y,onRemoveFile:()=>{C(void 0),U("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)(F.Z,{onFileUploaded:R,fileName:B,onRemoveFile:()=>{R(void 0),z("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato de parceria"}),(0,a.jsx)(F.Z,{onFileUploaded:L,fileName:K,onRemoveFile:()=>{L(void 0),W("")}})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",loading:ei.isPending,disabled:ei.isPending||!et||!y||!A||!V})})]})})})}function BrokerRegisterPj(e){var s;let{typeCreate:t,hide:n,adminId:r}=e,[l,x]=(0,i.useState)(""),[g,f]=(0,i.useState)(),[j,v]=(0,i.useState)(),[N,w]=(0,i.useState)(),[C,q]=(0,i.useState)(),[O,M]=(0,i.useState)(),[_,I]=(0,i.useState)(),[A,R]=(0,i.useState)(!1),[V,L]=(0,i.useState)(""),[Y,U]=(0,i.useState)(""),[B,z]=(0,i.useState)(""),[K,W]=(0,i.useState)(""),[G,Q]=(0,i.useState)(""),[X,J]=(0,i.useState)(""),[$,H]=(0,i.useState)(""),[ee,es]=(0,i.useState)({document:!1,card:!1,mei:!1,residence:!1,social:!1,parcer:!1}),[et,ea]=(0,i.useState)("");(0,S.e)();let{register:ei,handleSubmit:en,watch:er,setValue:el,reset:eo,formState:{errors:ed,isValid:ec}}=(0,d.cI)({resolver:(0,o.X)(T.WF),mode:"all"}),em=er("type"),ex=(0,D.D)({mutationFn:async e=>{let s=await u.Z.post("/create-wallets/".concat(t),e);return s.data},onSuccess:e=>{p.Am.success("Acesso cadastrado com sucesso!"),R(!0),L(e.id),eg(e.id),x(""),eo()},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o Broker")}}),eu=(0,D.D)({mutationFn:async e=>{let{id:s,type:t,file:a}=e,i=new FormData;return i.append("id",s),i.append("type",t),i.append("file",a),await u.Z.post("/uploads/documents",i)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer",card_cnpj:"card",social_contract:"social",mei:"mei"}[s.type.toLowerCase()];t&&eh(t)},onError:e=>{(0,h.Z)(e,"Erro ao enviar o documento")}}),eh=(0,i.useCallback)(e=>{es(s=>({...s,[e]:!0}))},[]),ep=(0,i.useMemo)(()=>{if("pj"===et)return{document:!0,card:!0,mei:"MEI"===em,residence:!0,social:!0,parcer:!n}},[t,et,em,n]),eg=(0,i.useCallback)(e=>{let s=[{file:null==g?void 0:g[0],type:"RG",title:"Documento de identidade",required:null==ep?void 0:ep.document},{file:null==C?void 0:C[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:null==ep?void 0:ep.residence},{file:null==_?void 0:_[0],type:"CONTRACT",title:"Contrato de parceria",required:null==ep?void 0:ep.parcer}].filter(e=>e.required&&e.file);p.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{p.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{eu.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),p.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),p.Am.dismiss("sendDocuments")})})},[g,C,_,ep,eu]),eb=(0,i.useCallback)(async e=>{if(A)return eg(V);if(!r)return p.Am.warn("Selecione o gestor de carteira para vincular o broker.");let s={birthDate:e.birthDate,socialName:(0,P.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,m.p4)(String(e.cpf||"")),email:e.email,phoneNumber:"55".concat((0,m.p4)(e.phoneNumber||"")),motherName:(0,P.Z)(e.motherName),pep:"s"===e.pep,password:""!==l?l:void 0,address:{cep:(0,m.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},t={fantasyName:(0,P.Z)(e.fantasyName||""),cnpj:(0,m.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:"55".concat((0,m.p4)(e.businessPhoneNumber||"")),isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,m.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}},a={adminId:r,create:{accountType:"BUSINESS",owner:s,business:t},partPercent:e.participationPercentage};await ex.mutateAsync(a)},[A,V,r,l,eg,ex]);return(0,E.a)({queryKey:["address",er("ownerCep")],queryFn:async()=>{let e=await (0,Z.x)((0,m.p4)(er("ownerCep")));e&&(el("ownerCity",e.city,{shouldValidate:!0}),el("ownerState",e.state,{shouldValidate:!0}),el("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),el("ownerStreet",e.street,{shouldValidate:!0}))},enabled:(null===(s=er("ownerCep"))||void 0===s?void 0:s.length)===9}),(0,E.a)({queryKey:["address",er("businessCep")],queryFn:async()=>{var e;let s=await (0,Z.x)((0,m.p4)(null!==(e=er("businessCep"))&&void 0!==e?e:""));s&&(el("businessCity",s.city,{shouldValidate:!0}),el("businessState",s.state,{shouldValidate:!0}),el("businessNeighborhood",s.neighborhood,{shouldValidate:!0}),el("businessStreet",s.street,{shouldValidate:!0}))}}),(0,a.jsx)("div",{className:"md:px-5",children:(0,a.jsx)("form",{onSubmit:en(eb),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.fullName&&"- ".concat(ed.fullName.message)})]}),(0,a.jsx)("input",{...ei("fullName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.fullName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.cpf&&"- ".concat(ed.cpf.message)})]}),(0,a.jsx)("input",{...ei("cpf"),onChange:e=>{let{target:s}=e,t=s.value;el("cpf",(0,m.VL)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.cpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.isTaxable&&"- ".concat(ed.isTaxable.message)})]}),(0,a.jsxs)(k.Z,{value:er("isTaxable"),onChange:e=>el("isTaxable",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.birthDate&&"- ".concat(ed.birthDate.message)})]}),(0,a.jsx)("input",{...ei("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.birthDate?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.phoneNumber&&"- ".concat(ed.phoneNumber.message)})]}),(0,a.jsx)("input",{...ei("phoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return el("phoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.email&&"- ".concat(ed.email.message)})]}),(0,a.jsx)("input",{...ei("email"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),!n&&(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.participationPercentage&&"- ".concat(ed.participationPercentage.message)})]}),(0,a.jsx)("input",{...ei("participationPercentage"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.motherName&&"- ".concat(ed.motherName.message)})]}),(0,a.jsx)("input",{...ei("motherName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.motherName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsx)("div",{children:(0,a.jsx)(b.Z,{id:"",label:"Senha",type:"password",value:l,onChange:e=>x(e.target.value),name:"password"})})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.pep&&"- ".concat(ed.pep.message)})]}),(0,a.jsxs)(k.Z,{value:er("pep"),onChange:e=>el("pep",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.ownerCep&&"- ".concat(ed.ownerCep.message)})]}),(0,a.jsx)("input",{...ei("ownerCep"),onChange:e=>{let{target:s}=e;return el("ownerCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.ownerCity&&"- ".concat(ed.ownerCity.message)})]}),(0,a.jsx)("input",{...ei("ownerCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.ownerState&&"- ".concat(ed.ownerState.message)})]}),(0,a.jsx)("input",{...ei("ownerState"),maxLength:2,onChange:e=>{let{target:s}=e;return el("ownerState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.ownerState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.ownerNeighborhood&&"- ".concat(ed.ownerNeighborhood.message)})]}),(0,a.jsx)("input",{...ei("ownerNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.ownerNumber&&"- ".concat(ed.ownerNumber.message)})]}),(0,a.jsx)("input",{...ei("ownerNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.ownerStreet&&"- ".concat(ed.ownerStreet.message)})]}),(0,a.jsx)("input",{...ei("ownerStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.ownerComplement&&"- ".concat(ed.ownerComplement.message)})]}),(0,a.jsx)("input",{...ei("ownerComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados da Empresa"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Fantasia"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.fantasyName&&"- ".concat(ed.fantasyName.message)})]}),(0,a.jsx)("input",{...ei("fantasyName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.fantasyName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CNPJ"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.cnpj&&"- ".concat(ed.cnpj.message)})]}),(0,a.jsx)("input",{...ei("cnpj"),onChange:e=>{let{target:s}=e,t=s.value;el("cnpj",(0,m.PK)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.cnpj?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Abertura"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.dtOpening&&"- ".concat(ed.dtOpening.message)})]}),(0,a.jsx)("input",{...ei("dtOpening"),type:"date",max:y().utc().format("YYYY-MM-DD"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.dtOpening?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessPhoneNumber&&"- ".concat(ed.businessPhoneNumber.message)})]}),(0,a.jsx)("input",{...ei("businessPhoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return el("businessPhoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessPhoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessEmail&&"- ".concat(ed.businessEmail.message)})]}),(0,a.jsx)("input",{...ei("businessEmail"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessEmail?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da Companhia"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.companyName&&"- ".concat(ed.companyName.message)})]}),(0,a.jsx)("input",{...ei("companyName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.companyName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tipo",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.type&&"- ".concat(ed.type.message)})]}),(0,a.jsxs)(k.Z,{value:er("type"),onChange:e=>el("type",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Selecione o tipo"}),(0,a.jsx)("option",{value:"MEI",children:"MEI"}),(0,a.jsx)("option",{value:"EI",children:"EI"}),(0,a.jsx)("option",{value:"EIRELI",children:"EIRELI"}),(0,a.jsx)("option",{value:"SLU",children:"SLU"}),(0,a.jsx)("option",{value:"LTDA",children:"LTDA"}),(0,a.jsx)("option",{value:"SA",children:"SA"}),(0,a.jsx)("option",{value:"TS",children:"TS"})]})]}),(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tamanho",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.size&&"- ".concat(ed.size.message)})]}),(0,a.jsxs)(k.Z,{value:er("size"),onChange:e=>el("size",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Selecione o tamanho"}),(0,a.jsx)("option",{value:"MEI",children:"MEI"}),(0,a.jsx)("option",{value:"ME",children:"ME"}),(0,a.jsx)("option",{value:"EPP",children:"EPP"}),(0,a.jsx)("option",{value:"SMALL",children:"SMALL"}),(0,a.jsx)("option",{value:"MEDIUM",children:"MEDIUM"}),(0,a.jsx)("option",{value:"LARGE",children:"LARGE"})]})]})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessCep&&"- ".concat(ed.businessCep.message)})]}),(0,a.jsx)("input",{...ei("businessCep"),onChange:e=>{let{target:s}=e;return el("businessCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessState&&"- ".concat(ed.businessState.message)})]}),(0,a.jsx)("input",{...ei("businessState"),maxLength:2,onChange:e=>{let{target:s}=e;return el("businessState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessCity&&"- ".concat(ed.businessCity.message)})]}),(0,a.jsx)("input",{...ei("businessCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessNeighborhood&&"- ".concat(ed.businessNeighborhood.message)})]}),(0,a.jsx)("input",{...ei("businessNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessNumber&&"- ".concat(ed.businessNumber.message)})]}),(0,a.jsx)("input",{...ei("businessNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessStreet&&"- ".concat(ed.businessStreet.message)})]}),(0,a.jsx)("input",{...ei("businessStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ed.businessComplement&&"- ".concat(ed.businessComplement.message)})]}),(0,a.jsx)("input",{...ei("businessComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ed.businessComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)(F.Z,{onFileUploaded:f,fileName:Y,onRemoveFile:()=>{f(void 0),U("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)(F.Z,{onFileUploaded:q,fileName:B,onRemoveFile:()=>{q(void 0),z("")}})]}),!n&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato de parceria"}),(0,a.jsx)(F.Z,{onFileUploaded:I,fileName:K,onRemoveFile:()=>{I(void 0),W("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Cart\xe3o cnpj"}),(0,a.jsx)(F.Z,{onFileUploaded:v,fileName:G,onRemoveFile:()=>{v(void 0),Q("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato social"}),(0,a.jsx)(F.Z,{onFileUploaded:M,fileName:X,onRemoveFile:()=>{M(void 0),J("")}})]}),"MEI"===em&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Certificado de mei"}),(0,a.jsx)(F.Z,{onFileUploaded:w,fileName:$,onRemoveFile:()=>{w(void 0),H("")}})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",loading:ex.isPending,disabled:ex.isPending||!ec||!g||!C||!_||!j||!O||"MEI"===em&&!N})})]})})})}function BrokerCreate(){let[e,s]=(0,i.useState)("pf"),[t,n]=(0,i.useState)(""),r=(0,S.e)(),{data:l=[]}=(0,E.a)({queryKey:["admins"],queryFn:async()=>{let e=await u.Z.get("/wallets/list-admin");return e.data.map(e=>({...e,document:(0,m.PK)((null==e?void 0:e.document)||""),type:"admin"}))},enabled:"superadmin"===r.name});return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"m-3",children:(0,a.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:e=>{let{target:t}=e;return s(t.value)},children:[(0,a.jsx)("option",{value:"pf",children:"Pessoa F\xedsica"}),(0,a.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,a.jsx)("div",{className:"md:w-3/4 mb-5",children:(0,a.jsx)(g.Z,{label:"Vincular ao gestor de carteiras",items:l,value:t,setValue:n})})]})}),"pj"===e?(0,a.jsx)(BrokerRegisterPj,{typeCreate:"broker",adminId:t}):(0,a.jsx)(BrokerRegisterPf,{typeCreate:"broker",adminId:t})]})}function AssessorRegisterPf(e){var s;let{typeCreate:t,hide:n,brokerId:r}=e,l=(0,S.e)(),[x,g]=(0,i.useState)(!1),[f,j]=(0,i.useState)(""),[v,N]=(0,i.useState)(""),[w,y]=(0,i.useState)(),[C,q]=(0,i.useState)(""),[O,A]=(0,i.useState)(),[R,V]=(0,i.useState)(),[L,Y]=(0,i.useState)(""),[U,B]=(0,i.useState)(""),[z,K]=(0,i.useState)(""),[W,G]=(0,i.useState)(""),[Q,X]=(0,i.useState)({document:!1,residence:!1,parcer:!1}),{register:J,handleSubmit:$,watch:H,setValue:ee,reset:es,formState:{errors:et,isValid:ea},getValues:ei}=(0,d.cI)({resolver:(0,o.X)(T.WF),mode:"all"}),{data:en=[]}=(0,E.a)({queryKey:["brokers",l.name],queryFn:async()=>{let e=await u.Z.get("superadmin"===l.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data},enabled:"advisor"!==l.name&&"broker"!==l.name}),er=(0,D.D)({mutationFn:async e=>{let s=await u.Z.post("/create-wallets/advisor",e);return s.data},onSuccess:e=>{p.Am.success("Acesso cadastrado com sucesso!"),g(!0),j(e.id),ec(e.id),N(""),es(),y(void 0),A(void 0),V(void 0)},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o Broker")}}),el=(0,D.D)({mutationFn:async e=>{let{id:s,type:t,file:a}=e,i=new FormData;return i.append("id",s),i.append("type",t),i.append("file",a),await u.Z.post("/uploads/documents",i)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer"}[s.type.toLowerCase()];t&&eo(t)},onError:e=>{(0,h.Z)(e,"Erro ao enviar o documento")}}),eo=(0,i.useCallback)(e=>{X(s=>({...s,[e]:!0}))},[]),ed=(0,i.useMemo)(()=>{if("advisor"===t&&"pf"===C)return{document:!0,card:!1,mei:!1,residence:!0,social:!1,parcer:!n}},[t,C,n]),ec=(0,i.useCallback)(e=>{let s=[{file:null==w?void 0:w[0],type:"RG",title:"Documento de identidade",required:null==ed?void 0:ed.document},{file:null==O?void 0:O[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:null==ed?void 0:ed.residence},{file:null==R?void 0:R[0],type:"CONTRACT",title:"Contrato de parceria",required:null==ed?void 0:ed.parcer}].filter(e=>e.required&&e.file);p.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{p.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{el.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),p.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),p.Am.dismiss("sendDocuments")})})},[w,O,R,ed,el]);(0,E.a)({queryKey:["address",H("ownerCep")],queryFn:async()=>{let e=await (0,Z.x)((0,m.p4)(H("ownerCep")));e&&(ee("ownerCity",e.city,{shouldValidate:!0}),ee("ownerState",e.state,{shouldValidate:!0}),ee("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),ee("ownerStreet",e.street,{shouldValidate:!0}))},enabled:(null===(s=H("ownerCep"))||void 0===s?void 0:s.length)===9});let em=(0,i.useCallback)(async e=>{if(x)return ec(f);if(!(0,_.p)((0,m.p4)(String(e.cpf||""))))return p.Am.warn("CPF do investidor inv\xe1lido!");if(!(0,I.Z)(e.phoneNumber))return p.Am.warn("N\xfamero de telefone inv\xe1lido");if(!(0,M.Z)(e.ownerState))return p.Am.warn("Estado inv\xe1lido");let s={birthDate:e.birthDate,socialName:(0,P.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,m.p4)(String(e.cpf||"")),email:e.email,phoneNumber:"55".concat((0,m.p4)(e.phoneNumber||"")),motherName:(0,P.Z)(e.motherName),pep:"s"===e.pep,password:""!==v?v:void 0,address:{cep:(0,m.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},t={brokerId:r,create:{accountType:"PHYSICAL",owner:s},partPercent:e.participationPercentage};await er.mutateAsync(t)},[x,f,v,ec,er]);return(0,i.useEffect)(()=>{ee("isPf",!0)},[ee]),(0,a.jsx)("div",{className:"md:px-5",children:(0,a.jsx)("form",{onSubmit:$(em),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.fullName&&"- ".concat(et.fullName.message)})]}),(0,a.jsx)("input",{...J("fullName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.fullName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.cpf&&"- ".concat(et.cpf.message)})]}),(0,a.jsx)("input",{...J("cpf"),onChange:e=>{let{target:s}=e,t=s.value;ee("cpf",(0,m.VL)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.cpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.isTaxable&&"- ".concat(et.isTaxable.message)})]}),(0,a.jsxs)(k.Z,{value:H("isTaxable"),onChange:e=>ee("isTaxable",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.birthDate&&"- ".concat(et.birthDate.message)})]}),(0,a.jsx)("input",{...J("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.birthDate?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.phoneNumber&&"- ".concat(et.phoneNumber.message)})]}),(0,a.jsx)("input",{...J("phoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return ee("phoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.email&&"- ".concat(et.email.message)})]}),(0,a.jsx)("input",{...J("email"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.participationPercentage&&"- ".concat(et.participationPercentage.message)})]}),(0,a.jsx)("input",{...J("participationPercentage"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.motherName&&"- ".concat(et.motherName.message)})]}),(0,a.jsx)("input",{...J("motherName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.motherName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsx)("div",{children:(0,a.jsx)(b.Z,{id:"",label:"Senha",type:"password",value:v,onChange:e=>N(e.target.value),name:"password"})})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.pep&&"- ".concat(et.pep.message)})]}),(0,a.jsxs)(k.Z,{value:H("pep"),onChange:e=>ee("pep",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerCep&&"- ".concat(et.ownerCep.message)})]}),(0,a.jsx)("input",{...J("ownerCep"),onChange:e=>{let{target:s}=e;return ee("ownerCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerCity&&"- ".concat(et.ownerCity.message)})]}),(0,a.jsx)("input",{...J("ownerCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerState&&"- ".concat(et.ownerState.message)})]}),(0,a.jsx)("input",{...J("ownerState"),maxLength:2,onChange:e=>{let{target:s}=e;return ee("ownerState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerNeighborhood&&"- ".concat(et.ownerNeighborhood.message)})]}),(0,a.jsx)("input",{...J("ownerNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerNumber&&"- ".concat(et.ownerNumber.message)})]}),(0,a.jsx)("input",{...J("ownerNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerStreet&&"- ".concat(et.ownerStreet.message)})]}),(0,a.jsx)("input",{...J("ownerStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerComplement&&"- ".concat(et.ownerComplement.message)})]}),(0,a.jsx)("input",{...J("ownerComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)(F.Z,{onFileUploaded:y,fileName:L,onRemoveFile:()=>{y(void 0),Y("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)(F.Z,{onFileUploaded:A,fileName:U,onRemoveFile:()=>{A(void 0),B("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato de parceria"}),(0,a.jsx)(F.Z,{onFileUploaded:V,fileName:z,onRemoveFile:()=>{V(void 0),K("")}})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",loading:er.isPending,disabled:er.isPending||!ea||!w||!O||!R})})]})})})}function AssessorRegisterPj(e){var s;let{typeCreate:t,hide:n,brokerId:r}=e,[l,x]=(0,i.useState)(""),[g,f]=(0,i.useState)(),[j,v]=(0,i.useState)(),[N,w]=(0,i.useState)(),[C,q]=(0,i.useState)(),[O,M]=(0,i.useState)(),[_,I]=(0,i.useState)(),[A,R]=(0,i.useState)(!1),[V,L]=(0,i.useState)(""),[Y,U]=(0,i.useState)(""),[B,z]=(0,i.useState)(""),[K,W]=(0,i.useState)(""),[G,Q]=(0,i.useState)(""),[X,J]=(0,i.useState)(""),[$,H]=(0,i.useState)(""),[ee,es]=(0,i.useState)({document:!1,card:!1,mei:!1,residence:!1,social:!1,parcer:!1}),[et,ea]=(0,i.useState)(""),[ei,en]=(0,i.useState)(""),[er,el]=(0,i.useState)("");(0,S.e)();let{register:eo,handleSubmit:ed,watch:ec,setValue:em,reset:ex,formState:{errors:eu,isValid:eh}}=(0,d.cI)({resolver:(0,o.X)(T.WF),mode:"all"}),ep=ec("type"),eg=(0,D.D)({mutationFn:async e=>{let s=await u.Z.post("/create-wallets/".concat(t),e);return s.data},onSuccess:e=>{p.Am.success("Acesso cadastrado com sucesso!"),R(!0),L(e.id),ev(e.id),x(""),ex()},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o Broker")}}),eb=(0,D.D)({mutationFn:async e=>{let{id:s,type:t,file:a}=e,i=new FormData;return i.append("id",s),i.append("type",t),i.append("file",a),await u.Z.post("/uploads/documents",i)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer",card_cnpj:"card",social_contract:"social",mei:"mei"}[s.type.toLowerCase()];t&&ef(t)},onError:e=>{(0,h.Z)(e,"Erro ao enviar o documento")}}),ef=(0,i.useCallback)(e=>{es(s=>({...s,[e]:!0}))},[]),ej=(0,i.useMemo)(()=>{if("pj"===ei)return{document:!0,card:!0,mei:"MEI"===ep,residence:!0,social:!0,parcer:!n}},[t,ei,ep,n]),ev=(0,i.useCallback)(e=>{let s=[{file:null==g?void 0:g[0],type:"RG",title:"Documento de identidade",required:null==ej?void 0:ej.document},{file:null==C?void 0:C[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:null==ej?void 0:ej.residence},{file:null==_?void 0:_[0],type:"CONTRACT",title:"Contrato de parceria",required:null==ej?void 0:ej.parcer}].filter(e=>e.required&&e.file);p.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{p.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{eb.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),p.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),p.Am.dismiss("sendDocuments")})})},[g,C,_,ej,eb]),eN=(0,i.useCallback)(async e=>{if(A)return ev(V);if(!r)return p.Am.warn("Selecione o broker para vincular o assessor.");let s={birthDate:e.birthDate,socialName:(0,P.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,m.p4)(String(e.cpf||"")),email:e.email,phoneNumber:"55".concat((0,m.p4)(e.phoneNumber||"")),motherName:(0,P.Z)(e.motherName),pep:"s"===e.pep,password:""!==l?l:void 0,address:{cep:(0,m.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},t={fantasyName:(0,P.Z)(e.fantasyName||""),cnpj:(0,m.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:"55".concat((0,m.p4)(e.businessPhoneNumber||"")),isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,m.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}},a={brokerId:r,create:{accountType:"BUSINESS",owner:s,business:t},partPercent:e.participationPercentage};await eg.mutateAsync(a)},[A,V,r,l,ev,eg]);return(0,E.a)({queryKey:["address",ec("ownerCep")],queryFn:async()=>{let e=await (0,Z.x)((0,m.p4)(ec("ownerCep")));e&&(em("ownerCity",e.city,{shouldValidate:!0}),em("ownerState",e.state,{shouldValidate:!0}),em("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),em("ownerStreet",e.street,{shouldValidate:!0}))},enabled:(null===(s=ec("ownerCep"))||void 0===s?void 0:s.length)===9}),(0,E.a)({queryKey:["address",ec("businessCep")],queryFn:async()=>{var e;let s=await (0,Z.x)((0,m.p4)(null!==(e=ec("businessCep"))&&void 0!==e?e:""));s&&(em("businessCity",s.city,{shouldValidate:!0}),em("businessState",s.state,{shouldValidate:!0}),em("businessNeighborhood",s.neighborhood,{shouldValidate:!0}),em("businessStreet",s.street,{shouldValidate:!0}))}}),(0,a.jsx)("div",{className:"md:px-5",children:(0,a.jsx)("form",{onSubmit:ed(eN),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.fullName&&"- ".concat(eu.fullName.message)})]}),(0,a.jsx)("input",{...eo("fullName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.fullName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.cpf&&"- ".concat(eu.cpf.message)})]}),(0,a.jsx)("input",{...eo("cpf"),onChange:e=>{let{target:s}=e,t=s.value;em("cpf",(0,m.VL)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.cpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.isTaxable&&"- ".concat(eu.isTaxable.message)})]}),(0,a.jsxs)(k.Z,{value:ec("isTaxable"),onChange:e=>em("isTaxable",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.birthDate&&"- ".concat(eu.birthDate.message)})]}),(0,a.jsx)("input",{...eo("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.birthDate?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.phoneNumber&&"- ".concat(eu.phoneNumber.message)})]}),(0,a.jsx)("input",{...eo("phoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return em("phoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.email&&"- ".concat(eu.email.message)})]}),(0,a.jsx)("input",{...eo("email"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),!n&&(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.participationPercentage&&"- ".concat(eu.participationPercentage.message)})]}),(0,a.jsx)("input",{...eo("participationPercentage"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.motherName&&"- ".concat(eu.motherName.message)})]}),(0,a.jsx)("input",{...eo("motherName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.motherName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsx)("div",{children:(0,a.jsx)(b.Z,{id:"",label:"Senha",type:"password",value:l,onChange:e=>x(e.target.value),name:"password"})})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.pep&&"- ".concat(eu.pep.message)})]}),(0,a.jsxs)(k.Z,{value:ec("pep"),onChange:e=>em("pep",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.ownerCep&&"- ".concat(eu.ownerCep.message)})]}),(0,a.jsx)("input",{...eo("ownerCep"),onChange:e=>{let{target:s}=e;return em("ownerCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.ownerCity&&"- ".concat(eu.ownerCity.message)})]}),(0,a.jsx)("input",{...eo("ownerCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.ownerState&&"- ".concat(eu.ownerState.message)})]}),(0,a.jsx)("input",{...eo("ownerState"),maxLength:2,onChange:e=>{let{target:s}=e;return em("ownerState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.ownerState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.ownerNeighborhood&&"- ".concat(eu.ownerNeighborhood.message)})]}),(0,a.jsx)("input",{...eo("ownerNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.ownerNumber&&"- ".concat(eu.ownerNumber.message)})]}),(0,a.jsx)("input",{...eo("ownerNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.ownerStreet&&"- ".concat(eu.ownerStreet.message)})]}),(0,a.jsx)("input",{...eo("ownerStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.ownerComplement&&"- ".concat(eu.ownerComplement.message)})]}),(0,a.jsx)("input",{...eo("ownerComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados da Empresa"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Fantasia"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.fantasyName&&"- ".concat(eu.fantasyName.message)})]}),(0,a.jsx)("input",{...eo("fantasyName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.fantasyName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CNPJ"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.cnpj&&"- ".concat(eu.cnpj.message)})]}),(0,a.jsx)("input",{...eo("cnpj"),onChange:e=>{let{target:s}=e,t=s.value;em("cnpj",(0,m.PK)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.cnpj?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Abertura"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.dtOpening&&"- ".concat(eu.dtOpening.message)})]}),(0,a.jsx)("input",{...eo("dtOpening"),type:"date",max:y().utc().format("YYYY-MM-DD"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.dtOpening?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessPhoneNumber&&"- ".concat(eu.businessPhoneNumber.message)})]}),(0,a.jsx)("input",{...eo("businessPhoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return em("businessPhoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessPhoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessEmail&&"- ".concat(eu.businessEmail.message)})]}),(0,a.jsx)("input",{...eo("businessEmail"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessEmail?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da Companhia"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.companyName&&"- ".concat(eu.companyName.message)})]}),(0,a.jsx)("input",{...eo("companyName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.companyName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tipo",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.type&&"- ".concat(eu.type.message)})]}),(0,a.jsxs)(k.Z,{value:ec("type"),onChange:e=>em("type",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Selecione o tipo"}),(0,a.jsx)("option",{value:"MEI",children:"MEI"}),(0,a.jsx)("option",{value:"EI",children:"EI"}),(0,a.jsx)("option",{value:"EIRELI",children:"EIRELI"}),(0,a.jsx)("option",{value:"SLU",children:"SLU"}),(0,a.jsx)("option",{value:"LTDA",children:"LTDA"}),(0,a.jsx)("option",{value:"SA",children:"SA"}),(0,a.jsx)("option",{value:"TS",children:"TS"})]})]}),(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tamanho",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.size&&"- ".concat(eu.size.message)})]}),(0,a.jsxs)(k.Z,{value:ec("size"),onChange:e=>em("size",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Selecione o tamanho"}),(0,a.jsx)("option",{value:"MEI",children:"MEI"}),(0,a.jsx)("option",{value:"ME",children:"ME"}),(0,a.jsx)("option",{value:"EPP",children:"EPP"}),(0,a.jsx)("option",{value:"SMALL",children:"SMALL"}),(0,a.jsx)("option",{value:"MEDIUM",children:"MEDIUM"}),(0,a.jsx)("option",{value:"LARGE",children:"LARGE"})]})]})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessCep&&"- ".concat(eu.businessCep.message)})]}),(0,a.jsx)("input",{...eo("businessCep"),onChange:e=>{let{target:s}=e;return em("businessCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessState&&"- ".concat(eu.businessState.message)})]}),(0,a.jsx)("input",{...eo("businessState"),maxLength:2,onChange:e=>{let{target:s}=e;return em("businessState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessCity&&"- ".concat(eu.businessCity.message)})]}),(0,a.jsx)("input",{...eo("businessCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessNeighborhood&&"- ".concat(eu.businessNeighborhood.message)})]}),(0,a.jsx)("input",{...eo("businessNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessNumber&&"- ".concat(eu.businessNumber.message)})]}),(0,a.jsx)("input",{...eo("businessNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessStreet&&"- ".concat(eu.businessStreet.message)})]}),(0,a.jsx)("input",{...eo("businessStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eu.businessComplement&&"- ".concat(eu.businessComplement.message)})]}),(0,a.jsx)("input",{...eo("businessComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(eu.businessComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)(F.Z,{onFileUploaded:f,fileName:Y,onRemoveFile:()=>{f(void 0),U("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)(F.Z,{onFileUploaded:q,fileName:B,onRemoveFile:()=>{q(void 0),z("")}})]}),!n&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato de parceria"}),(0,a.jsx)(F.Z,{onFileUploaded:I,fileName:K,onRemoveFile:()=>{I(void 0),W("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Cart\xe3o cnpj"}),(0,a.jsx)(F.Z,{onFileUploaded:v,fileName:G,onRemoveFile:()=>{v(void 0),Q("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato social"}),(0,a.jsx)(F.Z,{onFileUploaded:M,fileName:X,onRemoveFile:()=>{M(void 0),J("")}})]}),"MEI"===ep&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Certificado de mei"}),(0,a.jsx)(F.Z,{onFileUploaded:w,fileName:$,onRemoveFile:()=>{w(void 0),H("")}})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",loading:eg.isPending,disabled:eg.isPending||!eh||!g||!C||!_||!j||!O||"MEI"===ep&&!N})})]})})})}let AssessorCreate=()=>{let[e,s]=(0,i.useState)("pf"),t=(0,S.e)();(0,S.P)();let[n,r]=(0,i.useState)("broker"===t.name?t.roleId:""),{data:l=[]}=(0,E.a)({queryKey:["brokers",t.name],queryFn:async()=>{let e=await u.Z.get("superadmin"===t.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data},enabled:"advisor"!==t.name&&"broker"!==t.name});return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"m-3",children:(0,a.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:e=>{let{target:t}=e;return s(t.value)},children:[(0,a.jsx)("option",{value:"pf",children:"Pessoa F\xedsica"}),(0,a.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),"superadmin"===t.name&&(0,a.jsx)("div",{className:"md:w-3/4 mb-5",children:(0,a.jsx)(g.Z,{label:"Vincular ao Broker",items:l,value:n,setValue:r})})]})}),"pj"===e?(0,a.jsx)(AssessorRegisterPj,{typeCreate:"advisor",brokerId:n}):(0,a.jsx)(AssessorRegisterPf,{typeCreate:"advisor",brokerId:n})]})};function AdminRegisterPj(e){var s;let{typeCreate:t,hide:n}=e,[r,l]=(0,i.useState)(""),[x,g]=(0,i.useState)(),[f,j]=(0,i.useState)(),[v,N]=(0,i.useState)(),[w,C]=(0,i.useState)(),[q,O]=(0,i.useState)(),[M,_]=(0,i.useState)(),[I,A]=(0,i.useState)(!1),[R,V]=(0,i.useState)(""),[L,Y]=(0,i.useState)(""),[U,B]=(0,i.useState)(""),[z,K]=(0,i.useState)(""),[W,G]=(0,i.useState)(""),[Q,X]=(0,i.useState)(""),[J,$]=(0,i.useState)(""),[H,ee]=(0,i.useState)({document:!1,card:!1,mei:!1,residence:!1,social:!1,parcer:!1}),[es,et]=(0,i.useState)(""),[ea,ei]=(0,i.useState)("");(0,S.e)();let{register:en,handleSubmit:er,watch:el,setValue:eo,reset:ed,formState:{errors:ec,isValid:em}}=(0,d.cI)({resolver:(0,o.X)(T.WF),mode:"all"}),ex=el("type"),eu=(0,D.D)({mutationFn:async e=>{let s=await u.Z.post("/create-wallets/".concat(t),e);return s.data},onSuccess:e=>{p.Am.success("Acesso cadastrado com sucesso!"),A(!0),V(e.id),eb(e.id),l(""),ed()},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o Broker")}}),eh=(0,D.D)({mutationFn:async e=>{let{id:s,type:t,file:a}=e,i=new FormData;return i.append("id",s),i.append("type",t),i.append("file",a),await u.Z.post("/uploads/documents",i)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer",card_cnpj:"card",social_contract:"social",mei:"mei"}[s.type.toLowerCase()];t&&ep(t)},onError:e=>{(0,h.Z)(e,"Erro ao enviar o documento")}}),ep=(0,i.useCallback)(e=>{ee(s=>({...s,[e]:!0}))},[]),eg=(0,i.useMemo)(()=>{if("pj"===es)return{document:!0,card:!0,mei:"MEI"===ex,residence:!0,social:!0,parcer:!n}},[t,es,ex,n]),eb=(0,i.useCallback)(e=>{let s=[{file:null==x?void 0:x[0],type:"RG",title:"Documento de identidade",required:null==eg?void 0:eg.document},{file:null==w?void 0:w[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:null==eg?void 0:eg.residence},{file:null==M?void 0:M[0],type:"CONTRACT",title:"Contrato de parceria",required:null==eg?void 0:eg.parcer}].filter(e=>e.required&&e.file);p.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{p.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{eh.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),p.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),p.Am.dismiss("sendDocuments")})})},[x,w,M,eg,eh]),ef=(0,i.useCallback)(async e=>{if(I)return eb(R);let s={birthDate:e.birthDate,socialName:(0,P.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,m.p4)(String(e.cpf||"")),email:e.email,phoneNumber:"55".concat((0,m.p4)(e.phoneNumber||"")),motherName:(0,P.Z)(e.motherName),pep:"s"===e.pep,password:""!==r?r:void 0,address:{cep:(0,m.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},t={fantasyName:(0,P.Z)(e.fantasyName||""),cnpj:(0,m.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:"55".concat((0,m.p4)(e.businessPhoneNumber||"")),isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,m.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}};await eu.mutateAsync({create:{accountType:"BUSINESS",owner:s,business:t}})},[I,R,r,eb,eu]);return(0,E.a)({queryKey:["address",el("ownerCep")],queryFn:async()=>{let e=await (0,Z.x)((0,m.p4)(el("ownerCep")));e&&(eo("ownerCity",e.city,{shouldValidate:!0}),eo("ownerState",e.state,{shouldValidate:!0}),eo("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),eo("ownerStreet",e.street,{shouldValidate:!0}))},enabled:(null===(s=el("ownerCep"))||void 0===s?void 0:s.length)===9}),(0,E.a)({queryKey:["address",el("businessCep")],queryFn:async()=>{var e;let s=await (0,Z.x)((0,m.p4)(null!==(e=el("businessCep"))&&void 0!==e?e:""));s&&(eo("businessCity",s.city,{shouldValidate:!0}),eo("businessState",s.state,{shouldValidate:!0}),eo("businessNeighborhood",s.neighborhood,{shouldValidate:!0}),eo("businessStreet",s.street,{shouldValidate:!0}))}}),(0,a.jsx)("div",{className:"md:px-5",children:(0,a.jsx)("form",{onSubmit:er(ef),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.fullName&&"- ".concat(ec.fullName.message)})]}),(0,a.jsx)("input",{...en("fullName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.fullName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.cpf&&"- ".concat(ec.cpf.message)})]}),(0,a.jsx)("input",{...en("cpf"),onChange:e=>{let{target:s}=e,t=s.value;eo("cpf",(0,m.VL)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.cpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.isTaxable&&"- ".concat(ec.isTaxable.message)})]}),(0,a.jsxs)(k.Z,{value:el("isTaxable"),onChange:e=>eo("isTaxable",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.birthDate&&"- ".concat(ec.birthDate.message)})]}),(0,a.jsx)("input",{...en("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.birthDate?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.phoneNumber&&"- ".concat(ec.phoneNumber.message)})]}),(0,a.jsx)("input",{...en("phoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return eo("phoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.email&&"- ".concat(ec.email.message)})]}),(0,a.jsx)("input",{...en("email"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),!n&&(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.participationPercentage&&"- ".concat(ec.participationPercentage.message)})]}),(0,a.jsx)("input",{...en("participationPercentage"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.motherName&&"- ".concat(ec.motherName.message)})]}),(0,a.jsx)("input",{...en("motherName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.motherName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsx)("div",{children:(0,a.jsx)(b.Z,{id:"",label:"Senha",type:"password",value:r,onChange:e=>l(e.target.value),name:"password"})})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.pep&&"- ".concat(ec.pep.message)})]}),(0,a.jsxs)(k.Z,{value:el("pep"),onChange:e=>eo("pep",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.ownerCep&&"- ".concat(ec.ownerCep.message)})]}),(0,a.jsx)("input",{...en("ownerCep"),onChange:e=>{let{target:s}=e;return eo("ownerCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.ownerCity&&"- ".concat(ec.ownerCity.message)})]}),(0,a.jsx)("input",{...en("ownerCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.ownerState&&"- ".concat(ec.ownerState.message)})]}),(0,a.jsx)("input",{...en("ownerState"),maxLength:2,onChange:e=>{let{target:s}=e;return eo("ownerState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.ownerState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.ownerNeighborhood&&"- ".concat(ec.ownerNeighborhood.message)})]}),(0,a.jsx)("input",{...en("ownerNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.ownerNumber&&"- ".concat(ec.ownerNumber.message)})]}),(0,a.jsx)("input",{...en("ownerNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.ownerStreet&&"- ".concat(ec.ownerStreet.message)})]}),(0,a.jsx)("input",{...en("ownerStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.ownerComplement&&"- ".concat(ec.ownerComplement.message)})]}),(0,a.jsx)("input",{...en("ownerComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados da Empresa"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Fantasia"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.fantasyName&&"- ".concat(ec.fantasyName.message)})]}),(0,a.jsx)("input",{...en("fantasyName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.fantasyName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CNPJ"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.cnpj&&"- ".concat(ec.cnpj.message)})]}),(0,a.jsx)("input",{...en("cnpj"),onChange:e=>{let{target:s}=e,t=s.value;eo("cnpj",(0,m.PK)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.cnpj?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Abertura"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.dtOpening&&"- ".concat(ec.dtOpening.message)})]}),(0,a.jsx)("input",{...en("dtOpening"),type:"date",max:y().utc().format("YYYY-MM-DD"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.dtOpening?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessPhoneNumber&&"- ".concat(ec.businessPhoneNumber.message)})]}),(0,a.jsx)("input",{...en("businessPhoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return eo("businessPhoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessPhoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessEmail&&"- ".concat(ec.businessEmail.message)})]}),(0,a.jsx)("input",{...en("businessEmail"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessEmail?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da Companhia"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.companyName&&"- ".concat(ec.companyName.message)})]}),(0,a.jsx)("input",{...en("companyName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.companyName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tipo",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.type&&"- ".concat(ec.type.message)})]}),(0,a.jsxs)(k.Z,{value:el("type"),onChange:e=>eo("type",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Selecione o tipo"}),(0,a.jsx)("option",{value:"MEI",children:"MEI"}),(0,a.jsx)("option",{value:"EI",children:"EI"}),(0,a.jsx)("option",{value:"EIRELI",children:"EIRELI"}),(0,a.jsx)("option",{value:"SLU",children:"SLU"}),(0,a.jsx)("option",{value:"LTDA",children:"LTDA"}),(0,a.jsx)("option",{value:"SA",children:"SA"}),(0,a.jsx)("option",{value:"TS",children:"TS"})]})]}),(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tamanho",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.size&&"- ".concat(ec.size.message)})]}),(0,a.jsxs)(k.Z,{value:el("size"),onChange:e=>eo("size",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"Selecione o tamanho"}),(0,a.jsx)("option",{value:"MEI",children:"MEI"}),(0,a.jsx)("option",{value:"ME",children:"ME"}),(0,a.jsx)("option",{value:"EPP",children:"EPP"}),(0,a.jsx)("option",{value:"SMALL",children:"SMALL"}),(0,a.jsx)("option",{value:"MEDIUM",children:"MEDIUM"}),(0,a.jsx)("option",{value:"LARGE",children:"LARGE"})]})]})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessCep&&"- ".concat(ec.businessCep.message)})]}),(0,a.jsx)("input",{...en("businessCep"),onChange:e=>{let{target:s}=e;return eo("businessCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessState&&"- ".concat(ec.businessState.message)})]}),(0,a.jsx)("input",{...en("businessState"),maxLength:2,onChange:e=>{let{target:s}=e;return eo("businessState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessCity&&"- ".concat(ec.businessCity.message)})]}),(0,a.jsx)("input",{...en("businessCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessNeighborhood&&"- ".concat(ec.businessNeighborhood.message)})]}),(0,a.jsx)("input",{...en("businessNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessNumber&&"- ".concat(ec.businessNumber.message)})]}),(0,a.jsx)("input",{...en("businessNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessStreet&&"- ".concat(ec.businessStreet.message)})]}),(0,a.jsx)("input",{...en("businessStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ec.businessComplement&&"- ".concat(ec.businessComplement.message)})]}),(0,a.jsx)("input",{...en("businessComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(ec.businessComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)(F.Z,{onFileUploaded:g,fileName:L,onRemoveFile:()=>{g(void 0),Y("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)(F.Z,{onFileUploaded:C,fileName:U,onRemoveFile:()=>{C(void 0),B("")}})]}),!n&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato de parceria"}),(0,a.jsx)(F.Z,{onFileUploaded:_,fileName:z,onRemoveFile:()=>{_(void 0),K("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Cart\xe3o cnpj"}),(0,a.jsx)(F.Z,{onFileUploaded:j,fileName:W,onRemoveFile:()=>{j(void 0),G("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato social"}),(0,a.jsx)(F.Z,{onFileUploaded:O,fileName:Q,onRemoveFile:()=>{O(void 0),X("")}})]}),"MEI"===ex&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Certificado de mei"}),(0,a.jsx)(F.Z,{onFileUploaded:N,fileName:J,onRemoveFile:()=>{N(void 0),$("")}})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",loading:eu.isPending,disabled:eu.isPending||!em||!x||!w||!M||!f||!q||"MEI"===ex&&!v})})]})})})}function AdminRegisterPf(e){var s;let{typeCreate:t,hide:n}=e,[r,l]=(0,i.useState)("");(0,S.e)();let[x,g]=(0,i.useState)(!1),[f,j]=(0,i.useState)(""),[v,N]=(0,i.useState)(""),[w,y]=(0,i.useState)(),[C,q]=(0,i.useState)(""),[O,A]=(0,i.useState)(),[R,V]=(0,i.useState)(),[L,Y]=(0,i.useState)(""),[U,B]=(0,i.useState)(""),[z,K]=(0,i.useState)(""),[W,G]=(0,i.useState)(""),[Q,X]=(0,i.useState)({document:!1,residence:!1,parcer:!1}),{register:J,handleSubmit:$,watch:H,setValue:ee,reset:es,formState:{errors:et,isValid:ea},getValues:ei}=(0,d.cI)({resolver:(0,o.X)(T.WF),mode:"all"}),en=(0,D.D)({mutationFn:async e=>{let s=await u.Z.post("/create-wallets/".concat(t),e);return s.data},onSuccess:e=>{p.Am.success("Broker cadastrado com sucesso!"),g(!0),j(e.id),ed(e.id),N(""),es(),y(void 0),A(void 0),V(void 0)},onError:e=>{(0,h.Z)(e,"Erro ao cadastrar o Broker")}}),er=(0,D.D)({mutationFn:async e=>{let{id:s,type:t,file:a}=e,i=new FormData;return i.append("id",s),i.append("type",t),i.append("file",a),await u.Z.post("/uploads/documents",i)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer"}[s.type.toLowerCase()];t&&el(t)},onError:e=>{(0,h.Z)(e,"Erro ao enviar o documento")}}),el=(0,i.useCallback)(e=>{X(s=>({...s,[e]:!0}))},[]),eo=(0,i.useMemo)(()=>{if("broker"===t&&"pf"===C)return{document:!0,card:!1,mei:!1,residence:!0,social:!1,parcer:!n}},[t,C,n]),ed=(0,i.useCallback)(e=>{let s=[{file:null==w?void 0:w[0],type:"RG",title:"Documento de identidade",required:null==eo?void 0:eo.document},{file:null==O?void 0:O[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:null==eo?void 0:eo.residence},{file:null==R?void 0:R[0],type:"CONTRACT",title:"Contrato de parceria",required:null==eo?void 0:eo.parcer}].filter(e=>e.required&&e.file);p.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{p.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{er.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),p.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),p.Am.dismiss("sendDocuments")})})},[w,O,R,eo,er]);(0,E.a)({queryKey:["address",H("ownerCep")],queryFn:async()=>{let e=await (0,Z.x)((0,m.p4)(H("ownerCep")));e&&(ee("ownerCity",e.city,{shouldValidate:!0}),ee("ownerState",e.state,{shouldValidate:!0}),ee("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),ee("ownerStreet",e.street,{shouldValidate:!0}))},enabled:(null===(s=H("ownerCep"))||void 0===s?void 0:s.length)===9});let ec=(0,i.useCallback)(async e=>{if(x)return ed(f);if(!(0,_.p)((0,m.p4)(String(e.cpf||""))))return p.Am.warn("CPF do investidor inv\xe1lido!");if(!(0,I.Z)(e.phoneNumber))return p.Am.warn("N\xfamero de telefone inv\xe1lido");if(!(0,M.Z)(e.ownerState))return p.Am.warn("Estado inv\xe1lido");let s={birthDate:e.birthDate,socialName:(0,P.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,m.p4)(String(e.cpf||"")),email:e.email,phoneNumber:"55".concat((0,m.p4)(e.phoneNumber||"")),motherName:(0,P.Z)(e.motherName),pep:"s"===e.pep,password:""!==v?v:void 0,address:{cep:(0,m.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},t={fantasyName:(0,P.Z)(e.fantasyName||""),cnpj:(0,m.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:"55".concat((0,m.p4)(e.businessPhoneNumber||"")),isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,m.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}};await en.mutateAsync({create:{accountType:"BUSINESS",owner:s,business:t}})},[x,f,v,ed,en]);return(0,i.useEffect)(()=>{ee("isPf",!0)},[ee]),(0,a.jsx)("div",{className:"md:px-5",children:(0,a.jsx)("form",{onSubmit:$(ec),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.fullName&&"- ".concat(et.fullName.message)})]}),(0,a.jsx)("input",{...J("fullName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.fullName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.cpf&&"- ".concat(et.cpf.message)})]}),(0,a.jsx)("input",{...J("cpf"),onChange:e=>{let{target:s}=e,t=s.value;ee("cpf",(0,m.VL)(t))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.cpf?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.isTaxable&&"- ".concat(et.isTaxable.message)})]}),(0,a.jsxs)(k.Z,{value:H("isTaxable"),onChange:e=>ee("isTaxable",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.birthDate&&"- ".concat(et.birthDate.message)})]}),(0,a.jsx)("input",{...J("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.birthDate?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.phoneNumber&&"- ".concat(et.phoneNumber.message)})]}),(0,a.jsx)("input",{...J("phoneNumber"),maxLength:15,onChange:e=>{let{target:s}=e;return ee("phoneNumber",String((0,m.gP)(s.value)))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.email&&"- ".concat(et.email.message)})]}),(0,a.jsx)("input",{...J("email"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.participationPercentage&&"- ".concat(et.participationPercentage.message)})]}),(0,a.jsx)("input",{...J("participationPercentage"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.motherName&&"- ".concat(et.motherName.message)})]}),(0,a.jsx)("input",{...J("motherName"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.motherName?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsx)("div",{children:(0,a.jsx)(b.Z,{id:"",label:"Senha",type:"password",value:v,onChange:e=>N(e.target.value),name:"password"})})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.pep&&"- ".concat(et.pep.message)})]}),(0,a.jsxs)(k.Z,{value:H("pep"),onChange:e=>ee("pep",e.target.value),children:[(0,a.jsx)("option",{disabled:!0,selected:!0,children:"Selecione"}),(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerCep&&"- ".concat(et.ownerCep.message)})]}),(0,a.jsx)("input",{...J("ownerCep"),onChange:e=>{let{target:s}=e;return ee("ownerCep",(0,m.Tc)(s.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerCity&&"- ".concat(et.ownerCity.message)})]}),(0,a.jsx)("input",{...J("ownerCity"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerState&&"- ".concat(et.ownerState.message)})]}),(0,a.jsx)("input",{...J("ownerState"),maxLength:2,onChange:e=>{let{target:s}=e;return ee("ownerState",s.value.toUpperCase())},className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerState?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerNeighborhood&&"- ".concat(et.ownerNeighborhood.message)})]}),(0,a.jsx)("input",{...J("ownerNeighborhood"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerNumber&&"- ".concat(et.ownerNumber.message)})]}),(0,a.jsx)("input",{...J("ownerNumber"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerStreet&&"- ".concat(et.ownerStreet.message)})]}),(0,a.jsx)("input",{...J("ownerStreet"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,a.jsx)("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:et.ownerComplement&&"- ".concat(et.ownerComplement.message)})]}),(0,a.jsx)("input",{...J("ownerComplement"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(et.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Documento de identidade"}),(0,a.jsx)(F.Z,{onFileUploaded:y,fileName:L,onRemoveFile:()=>{y(void 0),Y("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),(0,a.jsx)(F.Z,{onFileUploaded:A,fileName:U,onRemoveFile:()=>{A(void 0),B("")}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-1",children:"Contrato de parceria"}),(0,a.jsx)(F.Z,{onFileUploaded:V,fileName:z,onRemoveFile:()=>{V(void 0),K("")}})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(c.Z,{label:"Enviar",loading:en.isPending,disabled:en.isPending||!ea||!w||!O||!R})})]})})})}let AdminCreate=()=>{let[e,s]=(0,i.useState)("pf");return(0,S.e)(),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"m-3",children:[(0,a.jsx)("p",{className:"text-xl text-white mb-5",children:"Cadastro de Admin / Gestor de carteira"}),(0,a.jsx)("div",{className:" mb-5 flex items-center gap-4",children:(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:e=>{let{target:t}=e;return s(t.value)},children:[(0,a.jsx)("option",{value:"pf",children:"Pessoa F\xedsica"}),(0,a.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]})})]}),"pj"===e?(0,a.jsx)(AdminRegisterPj,{typeCreate:"admin"}):(0,a.jsx)(AdminRegisterPf,{typeCreate:"admin"})]})};function CadastroManual(){let[e,s]=(0,i.useState)(),t=(0,S.e)();return(0,i.useEffect)(()=>{"admin"===t.name?s("broker"):s("investor")},[]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.Z,{}),(0,a.jsx)(r.Z,{children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"text-white flex",children:["admin"!==t.name&&(0,a.jsx)("div",{onClick:()=>s("investor"),className:"px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ".concat("investor"===e?"bg-zinc-700":""),children:(0,a.jsx)("p",{children:"Investidor"})}),"broker"!==t.name&&(0,a.jsx)("div",{onClick:()=>s("broker"),className:"px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ".concat("broker"===e?"bg-zinc-700":""),children:(0,a.jsx)("p",{children:"Broker"})}),(0,a.jsx)("div",{onClick:()=>s("acessor"),className:"px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ".concat("acessor"===e?"bg-zinc-700":""),children:(0,a.jsx)("p",{children:"Assessor"})}),"superadmin"===t.name&&(0,a.jsx)("div",{onClick:()=>s("admin"),className:"px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ".concat("admin"===e?"bg-zinc-700":""),children:(0,a.jsx)("p",{children:"Admin / Gestor de carteiras"})})]}),(0,a.jsx)("div",{className:"border rounded-tr-2xl rounded-b-2xl p-2 border-zinc-700",children:(()=>{switch(e){case"investor":return(0,a.jsx)(CreateInvestor,{});case"broker":return(0,a.jsx)(BrokerCreate,{});case"acessor":return(0,a.jsx)(AssessorCreate,{});case"admin":return(0,a.jsx)(AdminCreate,{});default:return(0,a.jsx)("div",{})}})()})]})})]})}},7934:function(e,s,t){"use strict";t.d(s,{WF:function(){return r},_n:function(){return l},bs:function(){return n}});var a=t(5691),i=t(5968);let n=a.Ry().shape({document:a.Z_().required("Obrigat\xf3rio"),email:a.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),value:a.Z_().required("Obrigat\xf3rio"),term:a.Z_().required("Obrigat\xf3rio"),modality:a.Z_().required("Obrigat\xf3rio"),yield:a.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchaseWith:a.Z_().required("Obrigat\xf3rio"),amountQuotes:a.Z_().default(""),startContract:a.Z_().required("Obrigat\xf3rio"),endContract:a.Z_().required("Obrigat\xf3rio"),profile:a.Z_().required("Obrigat\xf3rio"),details:a.Z_().notRequired(),debenture:a.Z_().required("Obrigat\xf3rio")}).required();a.Ry().shape({name:a.Z_().required("Obrigat\xf3rio"),rg:a.Z_().required("Obrigat\xf3rio"),document:a.Z_().required("Obrigat\xf3rio"),phoneNumber:a.Z_().min(15,"N\xfamero de telefone inv\xe1lido!").max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:a.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[s,a,i]=e.split("-");if(!s||!a||!i||4!==s.length)return!1;let n=Number(s);if(isNaN(n)||n<1900||n>new Date().getFullYear())return!1;let r=new Date(e);return!(isNaN(r.getTime())||t(7157).m(e))}),email:a.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:a.Z_().required("Obrigat\xf3rio"),neighborhood:a.Z_().required("Obrigat\xf3rio"),state:a.Z_().required("Obrigat\xf3rio"),city:a.Z_().required("Obrigat\xf3rio"),complement:a.Z_().default(""),number:a.Z_().required("Obrigat\xf3rio"),value:a.Z_().required("Obrigat\xf3rio"),term:a.Z_().required("Obrigat\xf3rio"),modality:a.Z_().required("Obrigat\xf3rio"),yield:a.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),observations:a.Z_().default(""),purchaseWith:a.Z_().required("Obrigat\xf3rio"),amountQuotes:a.Z_(),initDate:a.Z_().required("Obrigat\xf3rio"),endDate:a.Z_().required("Obrigat\xf3rio"),gracePeriod:a.Z_().required("Obrigat\xf3rio"),profile:a.Z_().required("Obrigat\xf3rio"),bank:a.Z_().required("Obrigat\xf3rio"),accountNumber:a.Z_().required("Obrigat\xf3rio"),agency:a.Z_().required("Obrigat\xf3rio"),pix:a.Z_().required("Obrigat\xf3rio"),debenture:a.Z_().required("Obrigat\xf3rio"),motherName:a.Z_(),placeOfBirth:a.Z_(),occupation:a.Z_(),issuer:a.Z_(),testifyPrimaryName:a.Z_(),testifyPrimaryCpf:a.Z_(),testifySecondaryName:a.Z_(),testifySecondaryCpf:a.Z_(),companyAddress:a.Z_(),companyCity:a.Z_(),companyUF:a.Z_(),companyType:a.Z_()}).required(),a.Ry().shape({value:a.Z_().required("Obrigat\xf3rio"),profile:a.Z_().required("Obrigat\xf3rio"),yield:a.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),date:a.Z_().required("Obrigat\xf3rio"),bank:a.Z_().required("Obrigat\xf3rio"),accountNumber:a.Z_().required("Obrigat\xf3rio"),agency:a.Z_().required("Obrigat\xf3rio"),pix:a.Z_().required("Obrigat\xf3rio")}),a.Ry().shape({name:a.Z_().required("Obrigat\xf3rio"),document:a.Z_().required("Obrigat\xf3rio"),phoneNumber:a.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:a.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[s,a,i]=e.split("-");if(!s||!a||!i||4!==s.length)return!1;let n=Number(s);if(isNaN(n)||n<1900||n>new Date().getFullYear())return!1;let r=new Date(e);return!(isNaN(r.getTime())||t(7157).m(e))}),email:a.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:a.Z_().required("Obrigat\xf3rio"),neighborhood:a.Z_().required("Obrigat\xf3rio"),state:a.Z_().required("Obrigat\xf3rio"),city:a.Z_().required("Obrigat\xf3rio"),complement:a.Z_().default(""),number:a.Z_().required("Obrigat\xf3rio"),profile:a.Z_().required("Obrigat\xf3rio"),term:a.Z_().required("Obrigat\xf3rio"),yield:a.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),value:a.Z_().required("Obrigat\xf3rio"),bank:a.Z_().required("Obrigat\xf3rio"),agency:a.Z_().required("Obrigat\xf3rio"),accountNumber:a.Z_().required("Obrigat\xf3rio"),pix:a.Z_().required("Obrigat\xf3rio"),debenture:a.Z_().required("Obrigat\xf3rio"),observations:a.Z_().default(""),details:a.Z_().default(""),initDate:a.Z_().required("Obrigat\xf3rio"),endDate:a.Z_().required("Obrigat\xf3rio"),amountQuotes:a.Z_().default(""),modality:a.Z_().required("Obrigat\xf3rio"),purchaseWith:a.Z_().required("Obrigat\xf3rio"),motherName:a.Z_().when("document",(e,s)=>e[0]&&(0,i.p4)(e[0]).length<=11?s.required("Campo obrigat\xf3rio"):s.notRequired())}).required();let r=a.Ry().shape({isPf:a.O7().default(!1),birthDate:a.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return!1;let s=new Date(e),a=s.getFullYear();return!(isNaN(a)||a<1900||a>new Date().getFullYear()||s>new Date||t(7157).m(e))}),socialName:a.Z_(),isTaxable:a.Z_().required("Obrigat\xf3rio"),fullName:a.Z_().required("Obrigat\xf3rio"),cpf:a.Z_().required("Obrigat\xf3rio"),email:a.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),phoneNumber:a.Z_().required("Obrigat\xf3rio"),motherName:a.Z_().required("Obrigat\xf3rio"),pep:a.Z_().required("Obrigat\xf3rio"),ownerCep:a.Z_().required("Obrigat\xf3rio"),ownerCity:a.Z_().required("Obrigat\xf3rio"),ownerState:a.Z_().required("Obrigat\xf3rio"),ownerNeighborhood:a.Z_().required("Obrigat\xf3rio"),ownerStreet:a.Z_().required("Obrigat\xf3rio"),ownerComplement:a.Z_().notRequired(),ownerNumber:a.Z_().required("Obrigat\xf3rio"),fantasyName:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),cnpj:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),companyName:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessPhoneNumber:a.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),dtOpening:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessEmail:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"):s.notRequired()),type:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),size:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessCep:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessCity:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessState:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessNeighborhood:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessStreet:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),businessComplement:a.Z_().when("isPf",(e,s)=>(e[0],s.notRequired())),businessNumber:a.Z_().when("isPf",(e,s)=>!1===e[0]?s.required("Campo obrigat\xf3rio"):s.notRequired()),participationPercentage:a.Rx().min(0,"O valor m\xednimo \xe9 0").max(15,"O valor m\xe1ximo \xe9 15").typeError("O valor deve ser um n\xfamero v\xe1lido").when("$hide",{is:!0,then:e=>e.notRequired(),otherwise:e=>e.required("Obrigat\xf3rio")})}).required(),l=a.Ry().shape({name:a.Z_().required("Obrigat\xf3rio"),rg:a.Z_().required("Obrigat\xf3rio"),document:a.Z_().required("Obrigat\xf3rio"),phoneNumber:a.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:a.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[s,a,i]=e.split("-");if(!s||!a||!i||4!==s.length)return!1;let n=Number(s);if(isNaN(n)||n<1900||n>new Date().getFullYear())return!1;let r=new Date(e);return!(isNaN(r.getTime())||t(7157).m(e))}),email:a.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:a.Z_().required("Obrigat\xf3rio"),neighborhood:a.Z_().required("Obrigat\xf3rio"),state:a.Z_().required("Obrigat\xf3rio"),city:a.Z_().required("Obrigat\xf3rio"),complement:a.Z_().default(""),number:a.Z_().required("Obrigat\xf3rio"),bank:a.Z_().required("Obrigat\xf3rio"),accountNumber:a.Z_().required("Obrigat\xf3rio"),agency:a.Z_().required("Obrigat\xf3rio"),pix:a.Z_().required("Obrigat\xf3rio"),motherName:a.Z_().required("Obrigat\xf3rio"),placeOfBirth:a.Z_(),occupation:a.Z_(),issuer:a.Z_(),testifyPrimaryName:a.Z_(),testifyPrimaryCpf:a.Z_(),testifySecondaryName:a.Z_(),testifySecondaryCpf:a.Z_()}).required()}},function(e){e.O(0,[6990,8276,5371,6946,1865,3964,9891,3151,396,2971,7864,1744],function(){return e(e.s=4682)}),_N_E=e.O()}]);