(()=>{var e={};e.id=6432,e.ids=[6432],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},89497:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>m,routeModule:()=>p,tree:()=>c});var t=r(73137),o=r(54647),s=r(4183),l=r.n(s),i=r(71775),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);r.d(a,n);let d=t.AppPageRouteModule,c=["",{children:["contratos",{children:["alterar",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,78618)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\alterar\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,51918,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\alterar\\page.tsx"],x="/contratos/alterar/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new d({definition:{kind:o.x.APP_PAGE,page:"/contratos/alterar/page",pathname:"/contratos/alterar",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},95319:(e,a,r)=>{Promise.resolve().then(r.bind(r,24597))},24597:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>AlterarContrato});var t=r(60080),o=r(69957),s=r(34751),l=r(57114),i=r(97669),n=r(47956),d=r(96413),c=r(64731),m=r.n(c),x=r(32307),u=r(80223),p=r(9885),h=r(66558),g=r(57086),b=r(50298),f=r(42686),v=r(51778),j=r(34944),N=r(24577),C=r(85814),w=r(90682);let y=b.Ry().shape({tipoContrato:b.Z_().required("Selecione o tipo de contrato"),nomeCompleto:b.Z_().required("Nome completo obrigat\xf3rio"),identidade:b.Z_().required("Identidade obrigat\xf3ria"),celular:b.Z_().required("Celular obrigat\xf3rio"),cpf:b.Z_().required("CPF/CNPJ obrigat\xf3rio"),dataNascimento:b.Z_().optional(),nomeMae:b.Z_().optional(),email:b.Z_().email("E-mail inv\xe1lido").required("E-mail obrigat\xf3rio"),cep:b.Z_().required("CEP obrigat\xf3rio"),cidade:b.Z_().required("Cidade obrigat\xf3ria"),endereco:b.Z_().required("Endere\xe7o obrigat\xf3rio"),numero:b.Z_().required("N\xfamero obrigat\xf3rio"),complemento:b.Z_().optional(),estado:b.Z_().required("Estado obrigat\xf3rio"),banco:b.Z_().required("Banco obrigat\xf3rio"),conta:b.Z_().required("Conta obrigat\xf3ria"),agencia:b.Z_().required("Ag\xeancia obrigat\xf3ria"),chavePix:b.Z_().required("Chave PIX obrigat\xf3ria"),modalidade:b.Z_().required("Modalidade obrigat\xf3ria"),valorInvestimento:b.Z_().required("Valor do investimento obrigat\xf3rio").test("scp-multiple","Para contratos SCP, o valor deve ser m\xfaltiplo de R$ 5.000",function(e){let a=this.parent.modalidade;if("SCP"===a&&e){let a=parseInt(e.replace(/\D/g,""));return a>0&&a%5e3==0}return!0}),prazoInvestimento:b.Z_().required("Prazo do investimento obrigat\xf3rio"),taxaRemuneracao:b.Z_().required("Taxa de remunera\xe7\xe3o obrigat\xf3ria"),comprarCom:b.Z_().required("Forma de compra obrigat\xf3ria"),inicioContrato:b.Z_().required("In\xedcio do contrato obrigat\xf3rio"),fimContrato:b.Z_(),perfil:b.Z_().required("Perfil obrigat\xf3rio"),debenture:b.Z_().required("Deb\xeanture obrigat\xf3ria"),observacoes:b.Z_().optional(),quotaQuantity:b.Z_().when("modalidade",{is:e=>"SCP"===e,then:e=>e.required("Quantidade de cotas obrigat\xf3ria"),otherwise:e=>e.notRequired()}),irDeposito:b.O7().optional(),irDesconto:b.O7().optional()});function AlterarContrato(){let e=(0,l.useRouter)(),a=(0,l.useSearchParams)(),r=a.get("tipo"),c=a.get("investorId"),b=(0,w.e)(),{data:F,isLoading:I}=(0,v.a)({queryKey:["contract",c],queryFn:async()=>{if(!c)return null;let e=await C.Z.get(`/contract/${c}`);return e.data},enabled:!!c}),{register:M,handleSubmit:R,watch:P,setValue:q,trigger:Z,formState:{errors:D,isValid:_,isSubmitting:S}}=(0,h.cI)({resolver:(0,g.X)(y),mode:"all",defaultValues:{tipoContrato:"pf",nomeCompleto:"",identidade:"",celular:"",cpf:"",dataNascimento:"",nomeMae:"",email:"",cep:"",cidade:"",endereco:"",numero:"",complemento:"",estado:"",banco:"",conta:"",agencia:"",chavePix:"",observacoes:"",modalidade:"MUTUO",valorInvestimento:"",prazoInvestimento:"",taxaRemuneracao:"",comprarCom:"pix",inicioContrato:"",fimContrato:"",perfil:"conservative",debenture:"n",quotaQuantity:"",irDeposito:!1,irDesconto:!1}}),[A,V]=(0,p.useState)(1),[E,T]=(0,p.useState)(""),[Y,z]=(0,p.useState)(!1);(0,p.useMemo)(()=>{if(F&&!I){let e=F.contracts?.[0];if(e){q("nomeCompleto",e.investorName||""),q("cpf",F.document||""),q("identidade",F.rg||""),q("email",F.email||""),q("celular",F.phone||""),q("nomeMae",F.motherName||""),q("dataNascimento",F.birthDate||""),q("cep",F.zipCode||""),q("cidade",F.city||""),q("endereco",F.address||""),q("numero",F.addressNumber||""),q("complemento",F.complement||""),q("estado",F.state||""),q("banco",F.bank||""),q("agencia",F.branch||""),q("conta",F.accountNumber||""),q("chavePix",F.phone||F.email||""),q("valorInvestimento",e.investmentValue||""),q("taxaRemuneracao",e.investmentYield||""),q("prazoInvestimento",e.investmentTerm||"");let a="pix";e.purchasedWith?.includes("TRANSFER\xcaNCIA")||e.purchasedWith?.includes("BANC\xc1RIA")?a="bank_transfer":e.purchasedWith?.includes("PIX")?a="pix":e.purchasedWith?.includes("BOLETO")&&(a="boleto"),q("comprarCom",a),q("inicioContrato",e.contractStart?m()(e.contractStart).format("YYYY-MM-DD"):""),q("modalidade","P2P"===e.tags?"MUTUO":"SCP"),console.log("Formul\xe1rio pr\xe9-preenchido com dados do contrato:",F)}}},[F,I,q,P]),(0,p.useEffect)(()=>{let e=P("modalidade"),a=P("quotaQuantity");if("SCP"===e&&a){let e=parseInt(a)||0;q("valorInvestimento",(5e3*e).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}))}},[P("modalidade"),P("quotaQuantity"),q]);let k=(0,p.useMemo)(()=>{if(!F||!F.contracts?.[0])return!1;let e=F.contracts[0],a=e.contractStatus?.toUpperCase(),r=!1;return e.addendum&&e.addendum.length>0&&(r=e.addendum.some(e=>{let a=e.contractStatus?.toUpperCase();return"ACTIVE"===a||"ATIVO"===a})),"ACTIVE"===a||"ATIVO"===a||r},[F]),onSubmit=async e=>{console.log("Iniciando submiss\xe3o do formul\xe1rio...",e);let a=F?.contracts?.[0]?.id;if(console.log("Contract ID:",a),console.log("Investor ID:",c),!a){s.Am.error("ID do contrato n\xe3o encontrado nos dados carregados");return}try{let r="pj"===e.tipoContrato,t=e.cpf.replace(/\D/g,"");console.log("Dados processados:",{isPJ:r,documento:t,userProfile:b});let o={role:b?.name||"",personType:r?"PJ":"PF",contractType:e.modalidade,bankAccount:{bank:e.banco,agency:e.agencia,account:e.conta,pix:e.chavePix,type:"CORRENTE"},investment:{amount:parseInt(e.valorInvestimento.replace(/\D/g,"")),monthlyRate:parseFloat(e.taxaRemuneracao),durationInMonths:parseInt(e.prazoInvestimento),paymentMethod:e.comprarCom,startDate:e.inicioContrato,endDate:e.fimContrato,profile:e.perfil,isDebenture:"s"===e.debenture,..."SCP"===e.modalidade&&{quotaQuantity:parseInt(e.quotaQuantity||"0")}}};r?o.company={corporateName:e.nomeCompleto,cnpj:t}:o.individual={fullName:e.nomeCompleto,cpf:t,rg:e.identidade,birthDate:e.dataNascimento,email:e.email,phone:e.celular.replace(/\D/g,""),motherName:e.nomeMae,nationality:"brasileira",occupation:"Investidor",issuingAgency:"SSP",address:{street:e.endereco,city:e.cidade,state:e.estado||"",postalCode:e.cep.replace(/\D/g,""),number:e.numero,neighborhood:"Centro",complement:e.complemento||""}},console.log("Enviando dados para API...",o),console.log("Usando Contract ID para upgrade:",a),O.mutate(o)}catch(e){console.error("Erro ao processar dados:",e),s.Am.error("Erro ao processar dados do contrato")}},U=["tipoContrato","nomeCompleto","identidade","celular","cpf","email","cep","cidade","endereco","numero","estado","banco","conta","agencia","chavePix"],handleNext=async()=>{console.log("Validando campos da p\xe1gina 1:",U);let e=await Z(U);if(console.log("Resultado da valida\xe7\xe3o:",e),console.log("Erros atuais:",D),e)V(2),s.Am.success("Dados da p\xe1gina 1 validados com sucesso!");else{let e=U.filter(e=>D[e]),a=e.length;a>0?s.Am.error(`Por favor, preencha ${a} campo(s) obrigat\xf3rio(s) antes de continuar.`):s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios antes de continuar.")}},O=(0,j.D)({mutationFn:async e=>{let a=F?.contracts?.[0]?.id;if(console.log("Mutation - Contract Data:",F),console.log("Mutation - Contract ID:",a),!a)throw Error("ID do contrato n\xe3o encontrado nos dados carregados");return console.log("Fazendo PUT para:",`/contract/${a}/upgrade`),C.Z.put("/contract/"+a+"/upgrade",e)},onSuccess:a=>{console.log("Upgrade realizado com sucesso:",a.data);let r=a.data?.id;r?s.Am.success(`Upgrade realizado com sucesso! Novo contrato: ${r.substring(0,8)}... Redirecionando para a home...`):s.Am.success("Upgrade realizado com sucesso! Redirecionando para a home..."),z(!0),setTimeout(()=>{e.push("/")},3e3)},onError:e=>{(0,N.Z)(e,"Erro ao atualizar o contrato")}}),B=P("prazoInvestimento"),L=P("inicioContrato"),$=(0,p.useMemo)(()=>{if(L&&B){let e=(0,f.H)({investDate:String(B),startDate:L}),a=m()(e,"DD-MM-YYYY").format("DD/MM/YYYY");return q("fimContrato",m()(e,"DD-MM-YYYY").format("YYYY-MM-DD"),{shouldValidate:!0}),a}return""},[L,B,q]),Q=(0,p.useMemo)(()=>{if(!F||!F.contracts?.[0])return null;let e=F.contracts[0],a=[],calculateIR=(e,a,r)=>{let t=e*a*(r/30)/100,o=0;o=r<=180?22.5:r<=360?20:r<=720?17.5:15;let s=t*o/100;return{totalReturn:t,irRate:o,irValue:s}},r=e.contractStatus?.toUpperCase();if("ACTIVE"===r||"ATIVO"===r){let r=parseInt(e.investmentValue)||0,t=parseFloat(e.investmentYield)||0,o=e.contractStart?m()(e.contractStart):m()(),s=e.contractEnd?m()(e.contractEnd):m()(),l=s.diff(o,"days"),i=calculateIR(r,t,l);a.push({type:"Contrato Inicial",amount:r,daysRentabilized:l,monthlyRate:t,irRate:i.irRate,irValue:i.irValue,totalReturn:i.totalReturn,status:e.contractStatus})}if(e.addendum&&e.addendum.length>0){let r=1;e.addendum.forEach(e=>{let t=e.contractStatus?.toUpperCase();if("ACTIVE"===t||"ATIVO"===t){let t=parseFloat(e.investmentValue)||0,o=parseFloat(e.investmentYield)||0,s=e.contractStart?m()(e.contractStart):m()(),l=e.contractEnd?m()(e.contractEnd):m()(),i=l.diff(s,"days"),n=calculateIR(t,o,i);a.push({type:`Aditivo ${r}`,amount:t,daysRentabilized:i,monthlyRate:o,irRate:n.irRate,irValue:n.irValue,totalReturn:n.totalReturn,status:e.contractStatus}),r++}})}let t=a.reduce((e,a)=>e+a.irValue,0);return{details:a,totalIR:t,contractType:e.tags||"MUTUO"}},[F]),X=function(e){if(!e)return 0;let a=e.replace(/[^0-9,]/g,"").replace(",",".");return parseFloat(a)||0}(P("valorInvestimento")),G=E?parseFloat(E.replace("%","").replace(",",".")):0,J=X&&G?X*(G/100):0;return I?(0,t.jsxs)(t.Fragment,{children:[t.jsx(i.Z,{}),t.jsx(n.Z,{children:t.jsx("div",{className:"w-full",children:t.jsx("div",{className:"p-8 flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4"}),t.jsx("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(i.Z,{}),t.jsx(n.Z,{children:t.jsx("div",{className:"w-full",children:(0,t.jsxs)("div",{className:"p-8",children:[t.jsx("div",{className:"flex items-center mb-6 w-full justify-center",children:(0,t.jsxs)("h1",{className:"text-2xl text-center w-full font-bold text-white",children:["Contratos",F&&t.jsx("span",{className:"text-sm text-gray-400 block mt-1",children:F.contracts?.[0]?.investorName})]})}),!r&&k?(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-6",children:"Escolha o tipo de altera\xe7\xe3o"}),t.jsx("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{e.push(`/contratos/alterar?tipo=rentabilidade&investorId=${c}`)},children:t.jsx("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsxs)("div",{className:"",children:[t.jsx("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Rentabilidade"}),t.jsx("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a rentabilidade do contrato do investidor"})]}),t.jsx("div",{className:"flex-1"}),t.jsx("div",{className:"flex items-center",children:t.jsx("div",{className:"",children:t.jsx("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})}),t.jsx("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{e.push(`/contratos/alterar?tipo=modalidade&investorId=${c}`)},children:t.jsx("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsxs)("div",{className:"",children:[t.jsx("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Modalidade"}),t.jsx("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a modalidade do contrato do investidor"})]}),t.jsx("div",{className:"flex-1"}),t.jsx("div",{className:"flex items-center",children:t.jsx("div",{className:"",children:t.jsx("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})})]}):r||k?1===A?(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Pessoais"}),t.jsx("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,t.jsxs)("div",{className:"w-full md:w-1/2 mb-4",children:[t.jsx("p",{className:"text-white mb-1",children:"Tipo de Contrato"}),(0,t.jsxs)(u.Z,{value:P("tipoContrato"),onChange:e=>q("tipoContrato",e.target.value,{shouldValidate:!0}),children:[t.jsx("option",{value:"pf",children:"Pessoa F\xedsica"}),t.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),t.jsx("div",{className:"w-full mb-4",children:t.jsx(x.Z,{register:M,name:"nomeCompleto",width:"100%",error:!!D.nomeCompleto,errorMessage:D?.nomeCompleto?.message,label:"Nome Completo"})}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"identidade",width:"100%",error:!!D.identidade,errorMessage:D?.identidade?.message,label:"Identidade"})}),t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"celular",width:"100%",maxLength:15,error:!!D.celular,errorMessage:D?.celular?.message,label:"Celular",onChange:e=>{q("celular",(0,d.gP)(e.target.value),{shouldValidate:!0})}})})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"cpf",width:"100%",error:!!D.cpf,errorMessage:D?.cpf?.message,label:"CPF/CNPJ",onChange:e=>{let a="pj"===P("tipoContrato")?(0,d.PK)(e.target.value):(0,d.VL)(e.target.value);q("cpf",a,{shouldValidate:!0})}})}),t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{type:"date",register:M,name:"dataNascimento",width:"100%",error:!!D.dataNascimento,errorMessage:D?.dataNascimento?.message,label:"Data de Nascimento (opcional)"})})]}),t.jsx("div",{className:"w-full mb-4",children:t.jsx(x.Z,{register:M,name:"nomeMae",width:"100%",error:!!D.nomeMae,errorMessage:D?.nomeMae?.message,label:"Nome da M\xe3e (opcional)"})}),t.jsx("div",{className:"w-full mb-4",children:t.jsx(x.Z,{register:M,name:"email",width:"100%",error:!!D.email,errorMessage:D?.email?.message,label:"E-mail"})}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"cep",width:"100%",error:!!D.cep,errorMessage:D?.cep?.message,label:"CEP",onChange:e=>{q("cep",(0,d.Tc)(e.target.value),{shouldValidate:!0})}})}),t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"cidade",width:"100%",error:!!D.cidade,errorMessage:D?.cidade?.message,label:"Cidade"})})]}),t.jsx("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"estado",width:"100%",error:!!D.estado,errorMessage:D?.estado?.message,label:"Estado",placeholder:"ex: SC"})})}),t.jsx("div",{className:"w-full mb-4",children:t.jsx(x.Z,{register:M,name:"endereco",width:"100%",error:!!D.endereco,errorMessage:D?.endereco?.message,label:"Endere\xe7o"})}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"numero",width:"100%",error:!!D.numero,errorMessage:D?.numero?.message,label:"N\xfamero"})}),t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"complemento",width:"100%",error:!!D.complemento,errorMessage:D?.complemento?.message,label:"Complemento"})})]})]})}),t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Banc\xe1rios"}),(0,t.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full mb-4",children:[t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"banco",width:"100%",error:!!D.banco,errorMessage:D?.banco?.message,label:"Nome do Banco"})}),t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"conta",width:"100%",error:!!D.conta,errorMessage:D?.conta?.message,label:"Conta"})})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full",children:[t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"agencia",width:"100%",error:!!D.agencia,errorMessage:D?.agencia?.message,label:"Ag\xeancia"})}),t.jsx("div",{className:"w-full md:w-1/2",children:t.jsx(x.Z,{register:M,name:"chavePix",width:"100%",error:!!D.chavePix,errorMessage:D?.chavePix?.message,label:"Chave PIX"})})]})]}),t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Observa\xe7\xf5es"}),(0,t.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[t.jsx("textarea",{...M("observacoes"),className:"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]",placeholder:"Observa\xe7\xf5es"}),D.observacoes&&t.jsx("span",{className:"text-red-500 text-xs",children:D.observacoes.message})]}),t.jsx("div",{className:"flex justify-end",children:t.jsx(o.z,{size:"lg",type:"button",className:"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]",onClick:handleNext,children:"Pr\xf3ximo"})})]}):(0,t.jsxs)("form",{onSubmit:R(onSubmit,e=>{console.log("Erros de valida\xe7\xe3o:",e),s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios")}),children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"my-8 flex flex-col gap-4",children:[t.jsx("label",{className:"text-white mb-1 block",children:"Modalidade de Investimento"}),(0,t.jsxs)("div",{className:"flex flex-col md:w-1/3",children:[(0,t.jsxs)(u.Z,{value:P("modalidade"),onChange:e=>q("modalidade",e.target.value,{shouldValidate:!0}),children:[t.jsx("option",{value:"MUTUO",children:"M\xfatuo"}),t.jsx("option",{value:"SCP",children:"SCP"})]}),t.jsx("span",{className:"text-[#FF9900] text-xs mt-1",children:"*Ao alterar modalidade, pressione o bot\xe3o calcular IR"})]}),"SCP"===P("modalidade")&&t.jsx("div",{className:"flex flex-col md:w-1/3 mt-2",children:t.jsx(x.Z,{register:M,name:"quotaQuantity",width:"100%",error:!!D.quotaQuantity,errorMessage:D?.quotaQuantity?.message,label:"Quantidade de cotas",placeholder:"ex: 10"})}),"MUTUO"===P("modalidade")&&t.jsx("div",{className:"mt-4 md:w-1/3",children:t.jsx(o.z,{type:"button",className:"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full",onClick:function(){let e=Number(P("prazoInvestimento"));if(!e||isNaN(e)){T(""),s.Am.warn("Preencha o prazo do investimento para calcular o IR.");return}let a=30*e;T(a<=180?"22,5%":a<=360?"20%":a<=720?"17,5%":"15%")},children:"Calcular IR"})})]}),"MUTUO"===P("modalidade")&&(0,t.jsxs)("div",{className:"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3",children:[(0,t.jsxs)("p",{className:"text-white text-xs mb-1",children:["Valor investido: ",X?X.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),(0,t.jsxs)("p",{className:"text-white font-bold",children:["Valor total de IR: ",J?J.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),E&&(0,t.jsxs)("p",{className:"text-[#FF9900] font-bold mt-2",children:["Al\xedquota do IR calculada: ",E]})]}),"MUTUO"===P("modalidade")&&(0,t.jsxs)("div",{className:"w-full flex flex-col gap-4",children:[t.jsx("h4",{className:"text-lg font-bold text-white",children:"Detalhamento"}),t.jsx("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent",children:[t.jsx("thead",{children:(0,t.jsxs)("tr",{className:"bg-[#232323]",children:[t.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Tipo de Contrato"}),t.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do Contrato"}),t.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Dias Rentabilizados"}),t.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Rentabilidade"}),t.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Taxa de IR"}),t.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do IR"}),t.jsx("th",{className:"px-2 py-2 font-semibold",children:"Anexos"})]})}),t.jsx("tbody",{children:I?t.jsx("tr",{children:t.jsx("td",{colSpan:7,className:"text-center px-2 py-4",children:"Carregando dados do contrato..."})}):Q?(0,t.jsxs)(t.Fragment,{children:[Q.details.length>0?Q.details.map((e,a)=>(0,t.jsxs)("tr",{children:[t.jsx("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:e.type}),t.jsx("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.amount)}),(0,t.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.daysRentabilized," Dias"]}),(0,t.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.monthlyRate,"%"]}),(0,t.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.irRate,"%"]}),t.jsx("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.irValue)}),t.jsx("td",{className:"px-2 py-2",children:"-"})]},a)):t.jsx("tr",{children:t.jsx("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Nenhum contrato ativo encontrado para c\xe1lculo de IR"})}),(0,t.jsxs)("tr",{children:[t.jsx("td",{colSpan:6,className:"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]",children:"Valor total do IR"}),t.jsx("td",{className:"px-2 py-2 font-bold text-white border-t border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(Q.totalIR)})]})]}):t.jsx("tr",{children:t.jsx("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Dados do contrato n\xe3o encontrados"})})})]})})]}),!I&&!k&&(0,t.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-4 mb-4",children:[t.jsx("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),t.jsx("p",{className:"text-white text-sm",children:'N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status "ATIVO" podem ser alterados.'})]}),"MUTUO"===P("modalidade")&&(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-2",children:[(0,t.jsxs)("label",{className:"flex items-center text-white text-xs",children:[t.jsx("input",{type:"checkbox",className:"mr-2",checked:P("irDeposito"),onChange:e=>{e.target.checked?(q("irDeposito",!0),q("irDesconto",!1)):q("irDeposito",!1)}}),"Investidor ir\xe1 depositar valor referente ao IR"]}),(0,t.jsxs)("label",{className:"flex items-center text-white text-xs",children:[t.jsx("input",{type:"checkbox",className:"mr-2",checked:P("irDesconto"),onChange:e=>{e.target.checked?(q("irDesconto",!0),q("irDeposito",!1)):q("irDesconto",!1)}}),"Investidor decidiu desconto do IR sobre o valor do contrato"]})]})]}),t.jsx("h3",{className:"text-lg font-bold text-white my-8",children:"Dados do Investimento"}),t.jsx("div",{className:"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:[t.jsx(x.Z,{register:M,name:"valorInvestimento",width:"100%",error:!!D.valorInvestimento,errorMessage:D?.valorInvestimento?.message,label:"Valor do Investimento",disabled:"SCP"===P("modalidade"),setValue:e=>"SCP"===P("modalidade")?void 0:q("valorInvestimento",(0,d.Ht)(e||""),{shouldValidate:!0})}),t.jsx(x.Z,{register:M,type:"text",name:"taxaRemuneracao",width:"100%",error:!!D.taxaRemuneracao,errorMessage:D?.taxaRemuneracao?.message,label:"Taxa de Remunera\xe7\xe3o Mensal %",placeholder:"ex: 2"}),t.jsx(x.Z,{type:"date",register:M,maxDate:m()().format("YYYY-MM-DD"),name:"inicioContrato",width:"100%",setValue:e=>q("inicioContrato",e,{shouldValidate:!0}),error:!!D.inicioContrato,errorMessage:D?.inicioContrato?.message,label:"In\xedcio do Contrato"}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-white mb-1 block",children:"Perfil"}),(0,t.jsxs)(u.Z,{value:P("perfil"),onChange:e=>q("perfil",e.target.value,{shouldValidate:!0}),children:[t.jsx("option",{value:"conservative",children:"Conservador"}),t.jsx("option",{value:"moderate",children:"Moderado"}),t.jsx("option",{value:"aggressive",children:"Agressivo"})]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:[t.jsx(x.Z,{register:M,type:"number",name:"prazoInvestimento",width:"100%",error:!!D.prazoInvestimento,errorMessage:D?.prazoInvestimento?.message,label:"Prazo do Investimento - em meses",placeholder:"ex: 12"}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-white mb-1 block",children:"Comprar com"}),(0,t.jsxs)(u.Z,{value:P("comprarCom"),onChange:e=>q("comprarCom",e.target.value,{shouldValidate:!0}),children:[t.jsx("option",{value:"pix",children:"PIX"}),t.jsx("option",{value:"boleto",children:"Boleto"}),t.jsx("option",{value:"bank_transfer",children:"Transfer\xeancia Banc\xe1ria"})]})]}),t.jsx(x.Z,{type:"date",register:M,name:"fimContrato",value:$?m()($,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"100%",disabled:!0,label:"Fim do Contrato"}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-white mb-1 block",children:"Deb\xeanture"}),(0,t.jsxs)(u.Z,{value:P("debenture"),onChange:e=>q("debenture",e.target.value,{shouldValidate:!0}),children:[t.jsx("option",{value:"s",children:"Sim"}),t.jsx("option",{value:"n",children:"N\xe3o"})]})]})]})]})}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx(o.z,{type:"button",variant:"outline",size:"lg",onClick:()=>V(1),className:"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white",children:"← Voltar"}),t.jsx(o.z,{size:"lg",type:"submit",className:`px-8 py-2 rounded-lg ${k?"bg-[#FF9900] text-white hover:bg-[#e68a00]":"bg-gray-500 text-gray-300 cursor-not-allowed"}`,onClick:()=>{console.log("Bot\xe3o Concluir clicado"),console.log("Estado do formul\xe1rio:",{isValid:_,errors:D}),console.log("Dados atuais:",P()),console.log("Contratos ativos:",k)},disabled:!k||S||O.isPending||Y,children:k?Y?"Redirecionando...":S||O.isPending?"Enviando...":"Alterar Contrato":"Nenhum contrato ativo encontrado"})]})]}):(0,t.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-6 text-center",children:[t.jsx("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),t.jsx("p",{className:"text-white text-sm",children:"N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados."})]})]})})})]})}},78618:(e,a,r)=>{"use strict";r.r(a),r.d(a,{$$typeof:()=>l,__esModule:()=>s,default:()=>n});var t=r(17536);let o=(0,t.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\contratos\alterar\page.tsx`),{__esModule:s,$$typeof:l}=o,i=o.default,n=i}};var a=require("../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),r=a.X(0,[4103,6426,4731,8813,6558,3356,4944,7207,278,7669,2307,2686],()=>__webpack_exec__(89497));module.exports=r})();