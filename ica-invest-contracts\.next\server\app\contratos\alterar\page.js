(()=>{var e={};e.id=6432,e.ids=[6432],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},89497:(e,a,o)=>{"use strict";o.r(a),o.d(a,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>m,routeModule:()=>p,tree:()=>c});var r=o(73137),t=o(54647),s=o(4183),l=o.n(s),n=o(71775),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);o.d(a,i);let d=r.AppPageRouteModule,c=["",{children:["contratos",{children:["alterar",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,78618)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\alterar\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,51918,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\alterar\\page.tsx"],x="/contratos/alterar/page",u={require:o,loadChunk:()=>Promise.resolve()},p=new d({definition:{kind:t.x.APP_PAGE,page:"/contratos/alterar/page",pathname:"/contratos/alterar",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},95319:(e,a,o)=>{Promise.resolve().then(o.bind(o,24597))},24597:(e,a,o)=>{"use strict";o.r(a),o.d(a,{default:()=>AlterarContrato});var r=o(60080),t=o(69957),s=o(34751),l=o(57114),n=o(97669),i=o(47956),d=o(96413),c=o(64731),m=o.n(c),x=o(32307),u=o(80223),p=o(9885),h=o(66558),g=o(57086),b=o(50298),f=o(42686),v=o(51778),j=o(34944),N=o(24577),C=o(85814),w=o(90682),y=o(17871);let phoneWithCountryMask=e=>{let a=e.replace(/\D/g,"");return a.startsWith("55")&&13===a.length?a.replace(/^(\d{2})(\d{2})(\d{5})(\d{4})$/,"$1 ($2) $3-$4"):a.startsWith("55")||11!==a.length?a.length<=13?a.startsWith("55")?a.length<=4?a.replace(/^(\d{2})(\d{0,2})$/,"$1 ($2"):a.length<=9?a.replace(/^(\d{2})(\d{2})(\d{0,5})$/,"$1 ($2) $3"):a.replace(/^(\d{2})(\d{2})(\d{5})(\d{0,4})$/,"$1 ($2) $3-$4"):(0,d.gP)(e):e:("55"+a).replace(/^(\d{2})(\d{2})(\d{5})(\d{4})$/,"$1 ($2) $3-$4")},I=b.Ry().shape({tipoContrato:b.Z_().required("Selecione o tipo de contrato"),nomeCompleto:b.Z_().required("Nome completo obrigat\xf3rio"),identidade:b.Z_().required("Identidade obrigat\xf3ria"),celular:b.Z_().required("Celular obrigat\xf3rio"),cpf:b.Z_().required("CPF/CNPJ obrigat\xf3rio"),dataNascimento:b.Z_().optional(),nomeMae:b.Z_().optional(),email:b.Z_().email("E-mail inv\xe1lido").required("E-mail obrigat\xf3rio"),cep:b.Z_().required("CEP obrigat\xf3rio"),cidade:b.Z_().required("Cidade obrigat\xf3ria"),endereco:b.Z_().required("Endere\xe7o obrigat\xf3rio"),numero:b.Z_().required("N\xfamero obrigat\xf3rio"),complemento:b.Z_().optional(),estado:b.Z_().required("Estado obrigat\xf3rio"),banco:b.Z_().required("Banco obrigat\xf3rio"),conta:b.Z_().required("Conta obrigat\xf3ria"),agencia:b.Z_().required("Ag\xeancia obrigat\xf3ria"),chavePix:b.Z_().required("Chave PIX obrigat\xf3ria"),modalidade:b.Z_().required("Modalidade obrigat\xf3ria").test("scp-to-mutuo-validation","N\xe3o \xe9 poss\xedvel alterar contratos SCP para modalidade M\xfatuo",function(e){return!0}),valorInvestimento:b.Z_().required("Valor do investimento obrigat\xf3rio").test("scp-multiple","Para contratos SCP, o valor deve ser m\xfaltiplo de R$ 5.000",function(e){let a=this.parent.modalidade;if("SCP"===a&&e){let a=Number(e.replaceAll(".","").replaceAll(",","."))||0;return a>0&&a%5e3==0}return!0}).test("scp-minimum","Para contratos SCP, o valor m\xednimo \xe9 de R$ 30.000",function(e){let a=this.parent.modalidade;if("SCP"===a&&e){let a=Number(e.replaceAll(".","").replaceAll(",","."))||0;return a>=3e4}return!0}),prazoInvestimento:b.Z_().required("Prazo do investimento obrigat\xf3rio"),taxaRemuneracao:b.Z_().required("Taxa de remunera\xe7\xe3o obrigat\xf3ria"),comprarCom:b.Z_().required("Forma de compra obrigat\xf3ria"),inicioContrato:b.Z_().required("In\xedcio do contrato obrigat\xf3rio"),fimContrato:b.Z_(),perfil:b.Z_().required("Perfil obrigat\xf3rio"),debenture:b.Z_().required("Deb\xeanture obrigat\xf3ria"),observacoes:b.Z_().optional(),quotaQuantity:b.Z_().optional(),irDeposito:b.O7().optional(),irDesconto:b.O7().optional()});function AlterarContrato(){let e=(0,l.useRouter)(),a=(0,l.useSearchParams)(),o=a.get("tipo"),c=a.get("investorId"),b=(0,w.e)(),{data:R,isLoading:P}=(0,v.a)({queryKey:["contract",c],queryFn:async()=>{if(!c)return null;let e=await C.Z.get(`/contract/${c}`);return e.data},enabled:!!c}),{register:M,handleSubmit:F,watch:D,setValue:S,trigger:A,formState:{errors:q,isValid:V,isSubmitting:Z}}=(0,h.cI)({resolver:(0,g.X)(I),mode:"all",defaultValues:{tipoContrato:"pf",nomeCompleto:"",identidade:"",celular:"",cpf:"",dataNascimento:"",nomeMae:"",email:"",cep:"",cidade:"",endereco:"",numero:"",complemento:"",estado:"",banco:"",conta:"",agencia:"",chavePix:"",observacoes:"",modalidade:"MUTUO",valorInvestimento:"",prazoInvestimento:"",taxaRemuneracao:"",comprarCom:"pix",inicioContrato:"",fimContrato:"",perfil:"conservative",debenture:"n",quotaQuantity:"",irDeposito:!1,irDesconto:!1}}),[T,_]=(0,p.useState)(1),[O,E]=(0,p.useState)(""),[U,$]=(0,p.useState)(!1),[Y,B]=(0,p.useState)(0),[k,z]=(0,p.useState)(!1),[L,G]=(0,p.useState)(!1),[W,Q]=(0,p.useState)(!1),[X,H]=(0,p.useState)(""),J=(0,p.useMemo)(()=>{if(!R||!R.contracts?.[0])return null;let e=R.contracts[0];return"P2P"===e.tags?"MUTUO":"SCP"},[R]);(0,p.useMemo)(()=>{if(R&&!P){let e=R.contracts?.[0];if(e){S("nomeCompleto",e.investorName||"");let a=R.document||"";if(a){let e=a.length<=11?(0,d.VL)(a):(0,d.PK)(a);S("cpf",e)}else S("cpf","");S("identidade",R.rg||""),S("email",R.email||"");let o=R.phone||"";S("celular",o?phoneWithCountryMask(o):""),S("nomeMae",R.motherName||""),S("dataNascimento",R.birthDate||"");let r=R.zipCode||"";S("cep",r?(0,d.Tc)(r):""),S("cidade",R.city||""),S("endereco",R.address||""),S("numero",R.addressNumber||""),S("complemento",R.complement||""),S("estado",R.state||""),S("banco",R.bank||""),S("agencia",R.branch||""),S("conta",R.accountNumber||""),S("chavePix",R.phone||R.email||"");let t=e.investmentValue||"";S("valorInvestimento",t?(0,d.Ht)(t):""),S("taxaRemuneracao",e.investmentYield||""),S("prazoInvestimento",e.investmentTerm||"");let s="pix";e.purchasedWith?.includes("TRANSFER\xcaNCIA")||e.purchasedWith?.includes("BANC\xc1RIA")?s="bank_transfer":e.purchasedWith?.includes("PIX")?s="pix":e.purchasedWith?.includes("BOLETO")&&(s="boleto"),S("comprarCom",s),S("inicioContrato",e.contractStart?m()(e.contractStart).format("YYYY-MM-DD"):""),S("modalidade","P2P"===e.tags?"MUTUO":"SCP"),console.log("Formul\xe1rio pr\xe9-preenchido com dados do contrato:",R)}}},[R,P,S]);let K=D("modalidade"),ee=D("valorInvestimento");(0,p.useEffect)(()=>{if("SCP"===K&&ee){let e=(0,y.Z)(ee)||0;S("quotaQuantity",Math.floor(e/5e3).toString())}},[K,ee,S]);let ea=(0,p.useMemo)(()=>{if(!R||!R.contracts?.[0])return!1;let e=R.contracts[0],a=e.contractStatus?.toUpperCase(),o=!1;return e.addendum&&e.addendum.length>0&&(o=e.addendum.some(e=>{let a=e.contractStatus?.toUpperCase();return"ACTIVE"===a||"ATIVO"===a})),"ACTIVE"===a||"ATIVO"===a||o},[R]),onSubmit=async e=>{if(console.log("Iniciando submiss\xe3o do formul\xe1rio...",e),"MUTUO"===e.modalidade){if(!L){s.Am.error("⚠️ Obrigat\xf3rio: Clique no bot\xe3o 'Calcular IR' antes de prosseguir.");return}if(!e.irDeposito&&!e.irDesconto){s.Am.error("⚠️ Obrigat\xf3rio: Selecione uma das op\xe7\xf5es de IR (dep\xf3sito ou desconto).");return}}if("SCP"===J&&"MUTUO"===e.modalidade){s.Am.error("N\xe3o \xe9 poss\xedvel alterar contratos SCP para modalidade M\xfatuo");return}console.log("Investor ID:",c);try{let a="pj"===e.tipoContrato,o=e.cpf.replace(/\D/g,"");console.log("Dados processados:",{isPJ:a,documento:o,userProfile:b});let r={name:e.nomeCompleto||"",rg:(()=>{let a=e.identidade;if(console.log("RG original:",a,"Tipo:",typeof a),!a)return"123456789";let o=a.toString().replace(/\D/g,"");console.log("RG limpo:",o);let r=o||"123456789";return console.log("RG final:",r,"Tipo:",typeof r),r})(),phoneNumber:(()=>{let a=e.celular.replace(/\D/g,"");if(console.log("Telefone original:",e.celular),console.log("Telefone limpo:",a),11===a.length&&!a.startsWith("55")){let e="55"+a;return console.log("Telefone com c\xf3digo do pa\xeds:",e),e}return a})(),motherName:e.nomeMae||"",dtBirth:(()=>{let a=e.dataNascimento;if(console.log("Data nascimento original:",a),!a)return new Date().toISOString();if(a.match(/^\d{4}-\d{2}-\d{2}$/)){let e=a+"T00:00:00.000Z";return console.log("Data nascimento ISO:",e),e}let o=new Date(a),r=o.toISOString();return console.log("Data nascimento convertida:",r),r})(),address:{zipCode:(()=>{let a=e.cep?e.cep.replace(/\D/g,""):"";return console.log("CEP processado:",a),a||"********"})(),neighborhood:"Centro",state:e.estado||"BA",city:e.cidade||"Cidade",complement:e.complemento||"",number:(()=>{let a=e.numero;if(console.log("N\xfamero original:",a,"Tipo:",typeof a),!a)return"1";let o=a.toString().replace(/\D/g,"");console.log("N\xfamero limpo:",o);let r=o||"1";return console.log("N\xfamero final:",r,"Tipo:",typeof r),r})()},accountBank:{bank:e.banco||"",accountNumber:e.conta||"",agency:e.agencia||"",pix:e.chavePix||""},document:o,contractType:e.modalidade,observations:"",placeOfBirth:e.cidade||"",occupation:"Investidor",documentType:a?"CNPJ":"CPF",issuer:"SSP",quota:"SCP"===e.modalidade&&parseInt(e.quotaQuantity||"0")||0,paymentPercentage:parseFloat(e.taxaRemuneracao)||0,parValue:parseInt(e.valorInvestimento.replace(/\D/g,""))||0};console.log("=== PAYLOAD COMPLETO ==="),console.log("Enviando dados para API...",JSON.stringify(r,null,2)),console.log("=== VALIDA\xc7\xd5ES ESPEC\xcdFICAS ==="),console.log("Data de nascimento final:",r.dtBirth,"Tipo:",typeof r.dtBirth),console.log("N\xfamero do endere\xe7o:",r.address.number,"Tipo:",typeof r.address.number),console.log("RG:",r.rg,"Tipo:",typeof r.rg),console.log("Quota:",r.quota,"Tipo:",typeof r.quota),console.log("PaymentPercentage:",r.paymentPercentage,"Tipo:",typeof r.paymentPercentage),console.log("ParValue:",r.parValue,"Tipo:",typeof r.parValue),console.log("=== VALIDA\xc7\xc3O NUMBER STRING ==="),console.log("RG \xe9 number string?",/^\d+$/.test(r.rg)),console.log("Number \xe9 number string?",/^\d+$/.test(r.address.number)),er.mutate(r)}catch(e){console.error("Erro ao processar dados:",e),s.Am.error("Erro ao processar dados do contrato")}},eo=["tipoContrato","nomeCompleto","identidade","celular","cpf","email","cep","cidade","endereco","numero","estado","banco","conta","agencia","chavePix"],handleNext=async()=>{console.log("Validando campos da p\xe1gina 1:",eo);let e=await A(eo);if(console.log("Resultado da valida\xe7\xe3o:",e),console.log("Erros atuais:",q),e)_(2),s.Am.success("Dados da p\xe1gina 1 validados com sucesso!");else{let e=eo.filter(e=>q[e]),a=e.length;a>0?s.Am.error(`Por favor, preencha ${a} campo(s) obrigat\xf3rio(s) antes de continuar.`):s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios antes de continuar.")}},er=(0,j.D)({mutationFn:async e=>{console.log("Mutation - Criando novo contrato"),console.log("Dados enviados:",e);let a=await C.Z.post("/contract",e);return console.log("Resposta da API:",a.data),a.data},onSuccess:a=>{console.log("Contrato criado com sucesso:",a);let o=a?.id;o?s.Am.success(`Contrato criado com sucesso! ID: ${o.substring(0,8)}... Redirecionando para a home...`):s.Am.success("Contrato criado com sucesso! Redirecionando para a home..."),$(!0),setTimeout(()=>{e.push("/")},3e3)},onError:e=>{(0,N.Z)(e,"Erro ao atualizar o contrato")}}),et=D("prazoInvestimento"),es=D("inicioContrato"),el=(0,p.useMemo)(()=>{if(es&&et){let e=(0,f.H)({investDate:String(et),startDate:es}),a=m()(e,"DD-MM-YYYY").format("DD/MM/YYYY");return S("fimContrato",m()(e,"DD-MM-YYYY").format("YYYY-MM-DD"),{shouldValidate:!0}),a}return""},[es,et,S]),en=(0,p.useMemo)(()=>{if(!R||!R.contracts?.[0])return null;let e=R.contracts[0],a=[],calculateIR=(e,a,o)=>{let r=e*a*(o/30)/100,t=0;t=o<=180?22.5:o<=360?20:o<=720?17.5:15;let s=r*t/100;return{totalReturn:r,irRate:t,irValue:s}},o=e.contractStatus?.toUpperCase();if("ACTIVE"===o||"ATIVO"===o){let o=parseInt(e.investmentValue)||0,r=parseFloat(e.investmentYield)||0,t=e.contractStart?m()(e.contractStart):m()(),s=e.contractEnd?m()(e.contractEnd):m()(),l=s.diff(t,"days"),n=calculateIR(o,r,l);a.push({type:"Contrato Inicial",amount:o,daysRentabilized:l,monthlyRate:r,irRate:n.irRate,irValue:n.irValue,totalReturn:n.totalReturn,status:e.contractStatus})}if(e.addendum&&e.addendum.length>0){let o=1;e.addendum.forEach(e=>{let r=e.contractStatus?.toUpperCase();if("ACTIVE"===r||"ATIVO"===r){let r=parseFloat(e.investmentValue)||0,t=parseFloat(e.investmentYield)||0,s=e.contractStart?m()(e.contractStart):m()(),l=e.contractEnd?m()(e.contractEnd):m()(),n=l.diff(s,"days"),i=calculateIR(r,t,n);a.push({type:`Aditivo ${o}`,amount:r,daysRentabilized:n,monthlyRate:t,irRate:i.irRate,irValue:i.irValue,totalReturn:i.totalReturn,status:e.contractStatus}),o++}})}let r=a.reduce((e,a)=>e+a.irValue,0);return{details:a,totalIR:r,contractType:e.tags||"MUTUO"}},[R]),ei=D("irDesconto");function parseValor(e){return e&&(0,y.Z)(e)||0}(0,p.useEffect)(()=>{if("SCP"===K&&ee&&ei&&en?.totalIR){let e=(0,y.Z)(ee)||0,a=e-en.totalIR,o=a%5e3;0!==o?(B(5e3-o),z(!0)):(z(!1),B(0))}else z(!1),B(0)},[K,ee,ei,en?.totalIR]),(0,p.useEffect)(()=>{"SCP"===K?(G(!0),Q(!0),S("irDeposito",!1),S("irDesconto",!1),H("")):"MUTUO"===K&&(G(!1),Q(!1),E(""),S("irDeposito",!1),S("irDesconto",!1),H(""))},[K,S]);let ed=parseValor(D("valorInvestimento")),ec=O?parseFloat(O.replace("%","").replace(",",".")):0,em=ed&&ec?ed*(ec/100):0;return P?(0,r.jsxs)(r.Fragment,{children:[r.jsx(n.Z,{}),r.jsx(i.Z,{children:r.jsx("div",{className:"w-full",children:r.jsx("div",{className:"p-8 flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4"}),r.jsx("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(n.Z,{}),r.jsx(i.Z,{children:r.jsx("div",{className:"w-full",children:(0,r.jsxs)("div",{className:"p-8",children:[r.jsx("div",{className:"flex items-center mb-6 w-full justify-center",children:(0,r.jsxs)("h1",{className:"text-2xl text-center w-full font-bold text-white",children:["Contratos",R&&r.jsx("span",{className:"text-sm text-gray-400 block mt-1",children:R.contracts?.[0]?.investorName})]})}),!o&&ea?(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h3",{className:"text-lg font-bold text-white mb-6",children:"Escolha o tipo de altera\xe7\xe3o"}),r.jsx("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{e.push(`/contratos/alterar?tipo=rentabilidade&investorId=${c}`)},children:r.jsx("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsxs)("div",{className:"",children:[r.jsx("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Rentabilidade"}),r.jsx("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a rentabilidade do contrato do investidor"})]}),r.jsx("div",{className:"flex-1"}),r.jsx("div",{className:"flex items-center",children:r.jsx("div",{className:"",children:r.jsx("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})}),r.jsx("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{e.push(`/contratos/alterar?tipo=modalidade&investorId=${c}`)},children:r.jsx("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsxs)("div",{className:"",children:[r.jsx("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Modalidade"}),r.jsx("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a modalidade do contrato do investidor"})]}),r.jsx("div",{className:"flex-1"}),r.jsx("div",{className:"flex items-center",children:r.jsx("div",{className:"",children:r.jsx("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})})]}):o||ea?1===T?(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Pessoais"}),r.jsx("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,r.jsxs)("div",{className:"w-full md:w-1/2 mb-4",children:[r.jsx("p",{className:"text-white mb-1",children:"Tipo de Contrato"}),(0,r.jsxs)(u.Z,{value:D("tipoContrato"),onChange:e=>S("tipoContrato",e.target.value,{shouldValidate:!0}),children:[r.jsx("option",{value:"pf",children:"Pessoa F\xedsica"}),r.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),r.jsx("div",{className:"w-full mb-4",children:r.jsx(x.Z,{register:M,name:"nomeCompleto",width:"100%",error:!!q.nomeCompleto,errorMessage:q?.nomeCompleto?.message,label:"Nome Completo"})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"identidade",width:"100%",error:!!q.identidade,errorMessage:q?.identidade?.message,label:"Identidade"})}),r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"celular",width:"100%",maxLength:18,error:!!q.celular,errorMessage:q?.celular?.message,label:"Celular",onChange:e=>{S("celular",phoneWithCountryMask(e.target.value),{shouldValidate:!0})}})})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"cpf",width:"100%",error:!!q.cpf,errorMessage:q?.cpf?.message,label:"CPF/CNPJ",onChange:e=>{let a="pj"===D("tipoContrato")?(0,d.PK)(e.target.value):(0,d.VL)(e.target.value);S("cpf",a,{shouldValidate:!0})}})}),r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{type:"date",register:M,name:"dataNascimento",width:"100%",error:!!q.dataNascimento,errorMessage:q?.dataNascimento?.message,label:"Data de Nascimento (opcional)"})})]}),r.jsx("div",{className:"w-full mb-4",children:r.jsx(x.Z,{register:M,name:"nomeMae",width:"100%",error:!!q.nomeMae,errorMessage:q?.nomeMae?.message,label:"Nome da M\xe3e (opcional)"})}),r.jsx("div",{className:"w-full mb-4",children:r.jsx(x.Z,{register:M,name:"email",width:"100%",error:!!q.email,errorMessage:q?.email?.message,label:"E-mail"})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"cep",width:"100%",error:!!q.cep,errorMessage:q?.cep?.message,label:"CEP",onChange:e=>{S("cep",(0,d.Tc)(e.target.value),{shouldValidate:!0})}})}),r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"cidade",width:"100%",error:!!q.cidade,errorMessage:q?.cidade?.message,label:"Cidade"})})]}),r.jsx("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"estado",width:"100%",error:!!q.estado,errorMessage:q?.estado?.message,label:"Estado",placeholder:"ex: SC"})})}),r.jsx("div",{className:"w-full mb-4",children:r.jsx(x.Z,{register:M,name:"endereco",width:"100%",error:!!q.endereco,errorMessage:q?.endereco?.message,label:"Endere\xe7o"})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"numero",width:"100%",error:!!q.numero,errorMessage:q?.numero?.message,label:"N\xfamero"})}),r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"complemento",width:"100%",error:!!q.complemento,errorMessage:q?.complemento?.message,label:"Complemento"})})]})]})}),r.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Banc\xe1rios"}),(0,r.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full mb-4",children:[r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"banco",width:"100%",error:!!q.banco,errorMessage:q?.banco?.message,label:"Nome do Banco"})}),r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"conta",width:"100%",error:!!q.conta,errorMessage:q?.conta?.message,label:"Conta"})})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full",children:[r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"agencia",width:"100%",error:!!q.agencia,errorMessage:q?.agencia?.message,label:"Ag\xeancia"})}),r.jsx("div",{className:"w-full md:w-1/2",children:r.jsx(x.Z,{register:M,name:"chavePix",width:"100%",error:!!q.chavePix,errorMessage:q?.chavePix?.message,label:"Chave PIX"})})]})]}),r.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Observa\xe7\xf5es"}),(0,r.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[r.jsx("textarea",{...M("observacoes"),className:"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]",placeholder:"Observa\xe7\xf5es"}),q.observacoes&&r.jsx("span",{className:"text-red-500 text-xs",children:q.observacoes.message})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx(t.z,{size:"lg",type:"button",className:"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]",onClick:handleNext,children:"Pr\xf3ximo"})})]}):(0,r.jsxs)("form",{onSubmit:F(onSubmit,e=>{console.log("Erros de valida\xe7\xe3o:",e),s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios")}),children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"my-8 flex flex-col gap-4",children:[r.jsx("label",{className:"text-white mb-1 block",children:"Modalidade de Investimento"}),(0,r.jsxs)("div",{className:"flex flex-col md:w-1/3",children:[(0,r.jsxs)(u.Z,{value:D("modalidade"),onChange:e=>{let a=e.target.value;if("SCP"===J&&"MUTUO"===a){s.Am.error("N\xe3o \xe9 poss\xedvel alterar contratos SCP para modalidade M\xfatuo");return}S("modalidade",a,{shouldValidate:!0})},children:[r.jsx("option",{value:"MUTUO",children:"M\xfatuo"}),r.jsx("option",{value:"SCP",children:"SCP"})]}),r.jsx("span",{className:"text-[#FF9900] text-xs mt-1",children:"*Ao alterar modalidade, pressione o bot\xe3o calcular IR"}),"SCP"===J&&r.jsx("div",{className:"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2",children:(0,r.jsxs)("p",{className:"text-yellow-200 text-xs",children:["⚠️ ",r.jsx("strong",{children:"Contrato atual \xe9 SCP:"})," N\xe3o \xe9 poss\xedvel alterar para modalidade M\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\xfatuo para SCP s\xe3o permitidos."]})})]}),"SCP"===D("modalidade")&&r.jsx("div",{className:"flex flex-col md:w-1/3 mt-2",children:r.jsx(x.Z,{register:M,name:"valorInvestimento",width:"100%",error:!!q.valorInvestimento,errorMessage:q?.valorInvestimento?.message,label:"Valor do Investimento",placeholder:"ex: R$ 50.000,00",setValue:e=>S("valorInvestimento",(0,d.Ht)(e||""),{shouldValidate:!0})})}),"MUTUO"===D("modalidade")&&r.jsx("div",{className:"mt-4 md:w-1/3",children:r.jsx(t.z,{type:"button",className:"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full",onClick:()=>(function(e=!0){let a=Number(D("prazoInvestimento"));if(!a||isNaN(a)){E(""),G(!1),e&&s.Am.warn("Preencha o prazo do investimento para calcular o IR.");return}let o=30*a;E(o<=180?"22,5%":o<=360?"20%":o<=720?"17,5%":"15%"),G(!0),e&&s.Am.success("IR calculado com sucesso! Agora selecione uma das op\xe7\xf5es de IR abaixo.")})(),children:"Calcular IR"})})]}),"MUTUO"===D("modalidade")&&(0,r.jsxs)("div",{className:"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3",children:[(0,r.jsxs)("p",{className:"text-white text-xs mb-1",children:["Valor investido: ",ed?ed.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),(0,r.jsxs)("p",{className:"text-white font-bold",children:["Valor total de IR: ",em?em.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),O&&(0,r.jsxs)("p",{className:"text-[#FF9900] font-bold mt-2",children:["Al\xedquota do IR calculada: ",O]})]}),"MUTUO"===D("modalidade")&&(0,r.jsxs)("div",{className:"w-full flex flex-col gap-4",children:[r.jsx("h4",{className:"text-lg font-bold text-white",children:"Detalhamento"}),r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent",children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{className:"bg-[#232323]",children:[r.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Tipo de Contrato"}),r.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do Contrato"}),r.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Dias Rentabilizados"}),r.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Rentabilidade"}),r.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Taxa de IR"}),r.jsx("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do IR"}),r.jsx("th",{className:"px-2 py-2 font-semibold",children:"Anexos"})]})}),r.jsx("tbody",{children:P?r.jsx("tr",{children:r.jsx("td",{colSpan:7,className:"text-center px-2 py-4",children:"Carregando dados do contrato..."})}):en?(0,r.jsxs)(r.Fragment,{children:[en.details.length>0?en.details.map((e,a)=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:e.type}),r.jsx("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.amount)}),(0,r.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.daysRentabilized," Dias"]}),(0,r.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.monthlyRate,"%"]}),(0,r.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.irRate,"%"]}),r.jsx("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.irValue)}),r.jsx("td",{className:"px-2 py-2",children:"-"})]},a)):r.jsx("tr",{children:r.jsx("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Nenhum contrato ativo encontrado para c\xe1lculo de IR"})}),(0,r.jsxs)("tr",{children:[r.jsx("td",{colSpan:6,className:"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]",children:"Valor total do IR"}),r.jsx("td",{className:"px-2 py-2 font-bold text-white border-t border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(en.totalIR)})]})]}):r.jsx("tr",{children:r.jsx("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Dados do contrato n\xe3o encontrados"})})})]})})]}),!P&&!ea&&(0,r.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-4 mb-4",children:[r.jsx("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),r.jsx("p",{className:"text-white text-sm",children:"N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados."})]}),"SCP"===D("modalidade")&&r.jsx("div",{className:"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4",children:(0,r.jsxs)("p",{className:"text-blue-200 text-sm",children:["ℹ️ ",r.jsx("strong",{children:"Modalidade SCP:"})," Contratos SCP n\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\xe3o ser\xe1 exibida para esta modalidade."]})}),"MUTUO"===D("modalidade")&&!L&&r.jsx("div",{className:"bg-red-900 border border-red-500 rounded-lg p-4 mb-4",children:(0,r.jsxs)("p",{className:"text-red-200 text-sm",children:["⚠️ ",r.jsx("strong",{children:"A\xe7\xe3o Obrigat\xf3ria:"}),' Clique no bot\xe3o ""Calcular IR"" acima antes de prosseguir.']})}),"MUTUO"===D("modalidade")&&L&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"bg-green-900 border border-green-500 rounded-lg p-3 mb-4",children:(0,r.jsxs)("p",{className:"text-green-200 text-sm",children:["✅ ",r.jsx("strong",{children:"IR Calculado:"})," Agora selecione uma das op\xe7\xf5es abaixo (obrigat\xf3rio):"]})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-2",children:[(0,r.jsxs)("label",{className:"flex items-center text-white text-xs",children:[r.jsx("input",{type:"checkbox",className:"mr-2",checked:D("irDeposito"),onChange:e=>{e.target.checked?(S("irDeposito",!0),S("irDesconto",!1),Q(!0),X&&(S("valorInvestimento",X),s.Am.info("Valor original do investimento restaurado. O IR ser\xe1 depositado separadamente."))):(S("irDeposito",!1),Q(!!D("irDesconto")))}}),"Investidor ir\xe1 depositar valor referente ao IR"]}),(0,r.jsxs)("label",{className:"flex items-center text-white text-xs",children:[r.jsx("input",{type:"checkbox",className:"mr-2",checked:D("irDesconto"),onChange:e=>{if(e.target.checked){S("irDesconto",!0),S("irDeposito",!1),Q(!0);let e=D("valorInvestimento");if(e&&en?.totalIR&&en.totalIR>0){X||H(e);let a=parseValor(e),o=en.totalIR;console.log("=== DEBUG DESCONTO IR ==="),console.log("Valor atual (string):",e),console.log("Valor num\xe9rico convertido:",a),console.log("Valor IR da tabela:",o);let r=a-o;if(console.log("Valor com desconto:",r),r<=0){s.Am.error("⚠️ Erro: O valor do IR \xe9 maior que o valor do investimento. Verifique os valores."),S("irDesconto",!1);return}let t=Math.round(100*r),l=(0,d.Ht)(t.toString());console.log("Valor em centavos:",t),console.log("Valor formatado:",l),S("valorInvestimento",l),s.Am.success(`✅ Desconto aplicado! IR de ${new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(o)} foi descontado do valor do investimento.`)}else s.Am.error("⚠️ N\xe3o foi poss\xedvel aplicar o desconto. Verifique se h\xe1 contratos ativos na tabela de IR."),S("irDesconto",!1);s.Am.info("⚠️ Aten\xe7\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\xe1rio criar um adendo para ajuste de valor.")}else S("irDesconto",!1),Q(!!D("irDeposito")),X&&(S("valorInvestimento",X),s.Am.info("Valor original do investimento restaurado."))}}),"Investidor decidiu desconto do IR sobre o valor do contrato"]})]})]}),"MUTUO"===D("modalidade")&&D("irDesconto")&&X&&en?.totalIR&&(0,r.jsxs)("div",{className:"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4",children:[r.jsx("h4",{className:"text-blue-200 font-bold mb-2",children:"\uD83D\uDCB0 Desconto de IR Aplicado"}),(0,r.jsxs)("div",{className:"text-blue-200 text-sm space-y-1",children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Valor original:"})," ",X]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Valor total do IR (da tabela):"})," ",new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(en.totalIR)]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Valor final (com desconto):"})," ",D("valorInvestimento")]}),r.jsx("p",{className:"text-yellow-200 text-xs mt-2",children:"ℹ️ O valor descontado \xe9 baseado no c\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos."})]})]}),k&&"SCP"===D("modalidade")&&D("irDesconto")&&(0,r.jsxs)("div",{className:"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4",children:[r.jsx("h4",{className:"text-yellow-200 font-bold mb-2",children:"⚠️ Ajuste de Valor Necess\xe1rio"}),r.jsx("p",{className:"text-yellow-200 text-sm mb-3",children:"Informamos que o valor atual, ap\xf3s a aplica\xe7\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\xe3o est\xe1 em conformidade com nossas regras de neg\xf3cio, pois n\xe3o \xe9 divis\xedvel por R$ 5.000,00."}),(0,r.jsxs)("p",{className:"text-yellow-200 text-sm font-semibold",children:["Para que as cotas sejam ajustadas corretamente, ser\xe1 necess\xe1rio o pagamento complementar no valor de"," ",r.jsx("span",{className:"text-yellow-100 font-bold",children:Y.toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}),"."]})]})]}),r.jsx("h3",{className:"text-lg font-bold text-white my-8",children:"Dados do Investimento"}),r.jsx("div",{className:"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:["MUTUO"===D("modalidade")&&r.jsx(x.Z,{register:M,name:"valorInvestimento",width:"100%",error:!!q.valorInvestimento,errorMessage:q?.valorInvestimento?.message,label:"Valor do Investimento",setValue:e=>S("valorInvestimento",(0,d.Ht)(e||""),{shouldValidate:!0})}),"SCP"===D("modalidade")&&r.jsx("div",{className:"flex flex-col",children:r.jsx(x.Z,{register:M,name:"quotaQuantity",width:"100%",error:!!q.quotaQuantity,errorMessage:q?.quotaQuantity?.message,label:"Quantidade de cotas (calculado automaticamente)",placeholder:"Calculado automaticamente",disabled:!0})}),r.jsx(x.Z,{register:M,type:"text",name:"taxaRemuneracao",width:"100%",error:!!q.taxaRemuneracao,errorMessage:q?.taxaRemuneracao?.message,label:"Taxa de Remunera\xe7\xe3o Mensal %",placeholder:"ex: 2"}),r.jsx(x.Z,{type:"date",register:M,maxDate:m()().format("YYYY-MM-DD"),name:"inicioContrato",width:"100%",setValue:e=>S("inicioContrato",e,{shouldValidate:!0}),error:!!q.inicioContrato,errorMessage:q?.inicioContrato?.message,label:"In\xedcio do Contrato"}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"text-white mb-1 block",children:"Perfil"}),(0,r.jsxs)(u.Z,{value:D("perfil"),onChange:e=>S("perfil",e.target.value,{shouldValidate:!0}),children:[r.jsx("option",{value:"conservative",children:"Conservador"}),r.jsx("option",{value:"moderate",children:"Moderado"}),r.jsx("option",{value:"aggressive",children:"Agressivo"})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:[r.jsx(x.Z,{register:M,type:"number",name:"prazoInvestimento",width:"100%",error:!!q.prazoInvestimento,errorMessage:q?.prazoInvestimento?.message,label:"Prazo do Investimento - em meses",placeholder:"ex: 12"}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"text-white mb-1 block",children:"Comprar com"}),(0,r.jsxs)(u.Z,{value:D("comprarCom"),onChange:e=>S("comprarCom",e.target.value,{shouldValidate:!0}),children:[r.jsx("option",{value:"pix",children:"PIX"}),r.jsx("option",{value:"boleto",children:"Boleto"}),r.jsx("option",{value:"bank_transfer",children:"Transfer\xeancia Banc\xe1ria"})]})]}),r.jsx(x.Z,{type:"date",register:M,name:"fimContrato",value:el?m()(el,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"100%",disabled:!0,label:"Fim do Contrato"}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"text-white mb-1 block",children:"Deb\xeanture"}),(0,r.jsxs)(u.Z,{value:D("debenture"),onChange:e=>S("debenture",e.target.value,{shouldValidate:!0}),children:[r.jsx("option",{value:"s",children:"Sim"}),r.jsx("option",{value:"n",children:"N\xe3o"})]})]})]})]})}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx(t.z,{type:"button",variant:"outline",size:"lg",onClick:()=>_(1),className:"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white",children:"← Voltar"}),r.jsx(t.z,{size:"lg",type:"submit",className:`px-8 py-2 rounded-lg ${ea?"bg-[#FF9900] text-white hover:bg-[#e68a00]":"bg-gray-500 text-gray-300 cursor-not-allowed"}`,onClick:()=>{console.log("Bot\xe3o Concluir clicado"),console.log("Estado do formul\xe1rio:",{isValid:V,errors:q}),console.log("Dados atuais:",D()),console.log("Contratos ativos:",ea)},disabled:!ea||Z||er.isPending||U||"MUTUO"===D("modalidade")&&(!L||!D("irDeposito")&&!D("irDesconto")),children:ea?U?"Redirecionando...":Z||er.isPending?"Enviando...":"MUTUO"!==D("modalidade")||L?"MUTUO"!==D("modalidade")||D("irDeposito")||D("irDesconto")?"Alterar Contrato":"Selecione uma op\xe7\xe3o de IR":"Calcule o IR primeiro":"Nenhum contrato ativo encontrado"})]})]}):(0,r.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-6 text-center",children:[r.jsx("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),r.jsx("p",{className:"text-white text-sm",children:"N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados."})]})]})})})]})}},78618:(e,a,o)=>{"use strict";o.r(a),o.d(a,{$$typeof:()=>l,__esModule:()=>s,default:()=>i});var r=o(17536);let t=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\contratos\alterar\page.tsx`),{__esModule:s,$$typeof:l}=t,n=t.default,i=n}};var a=require("../../../webpack-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),o=a.X(0,[4103,6426,4731,8813,6558,3356,4944,7207,278,7669,7913,2686],()=>__webpack_exec__(89497));module.exports=o})();