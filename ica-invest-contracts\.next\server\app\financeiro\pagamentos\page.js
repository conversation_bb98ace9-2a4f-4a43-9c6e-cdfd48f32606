(()=>{var e={};e.id=8059,e.ids=[8059],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},17909:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=a(73137),r=a(54647),n=a(4183),i=a.n(n),l=a(71775),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let d=s.AppPageRouteModule,c=["",{children:["financeiro",{children:["pagamentos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,32408)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamentos\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamentos\\page.tsx"],x="/financeiro/pagamentos/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new d({definition:{kind:r.x.APP_PAGE,page:"/financeiro/pagamentos/page",pathname:"/financeiro/pagamentos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},12601:(e,t,a)=>{Promise.resolve().then(a.bind(a,56632))},56632:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Pagamentos});var s=a(60080),r=a(99986),n=a(74352),i=a(97669),l=a(30170),o=a(47956),d=a(48764),c=a(26147),u=a(85814),x=a(24577),m=a(73294),p=a(96413),h=a(64731),f=a.n(h),g=a(9885),v=a(34751),Y=a(74644),j=a(57114),b=a(90682);function Pagamentos(){let e=(0,j.useRouter)(),t=(0,b.e)(),[a,h]=(0,g.useState)(),[P,w]=(0,g.useState)(!1);(0,g.useEffect)(()=>{"broker"===t.name&&(sessionStorage.clear(),localStorage.clear(),v.Am.warn("Usu\xe1rio sem permiss\xe3o"),e.push("/"))},[]);let[M,y]=(0,g.useState)(),[D,S]=(0,g.useState)(!1),[N,_]=(0,g.useState)(!1),[C,q]=(0,g.useState)(f()().startOf("month").format("YYYY-MM-DD")),[E,Z]=(0,g.useState)(f()().endOf("month").format("YYYY-MM-DD")),[F,k]=(0,g.useState)(1),[A,L]=(0,g.useState)(""),[R,B]=(0,g.useState)(""),[O,V]=(0,g.useState)(""),[$,I]=(0,g.useState)({total:0,lastPage:1,perPage:0});(0,g.useEffect)(()=>{getPayments()},[F,R]);let getPayments=()=>{S(!0),u.Z.get("income-payment/all",{params:{page:F,startScheduledDate:C,endScheduledDate:E,document:""===A?void 0:(0,p.p4)(A),status:""===R?void 0:R}}).then(e=>{h(e.data.data),y({totalPaid:e.data.totalPaid,totalScheduled:e.data.totalScheduled}),I({lastPage:e.data.lastPage,perPage:10,total:e.data.total})}).catch(e=>{(0,x.Z)(e,"N\xe3o foi possivel listar os pagamentos.")}).finally(()=>S(!1))};return(0,s.jsxs)("div",{children:[s.jsx(i.Z,{}),s.jsx(o.Z,{children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-white text-center mb-8 text-3xl",children:"Pagamentos"}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between m-auto w-full gap-4 md:w-[1100px]",children:[s.jsx("div",{className:"md:w-[40%] py-4 rounded-lg border-[#FF9900] border",children:(0,s.jsxs)("div",{className:"m-auto text-center flex flex-col items-center relative",children:[s.jsx("p",{className:"text-white mb-5 md:text-base text-sm",children:"Pagamentos Realizados"}),s.jsx(Y.Z,{loading:D,height:"65px",width:"50px",children:s.jsx("p",{className:"text-white mb-5 md:text-7xl font-semibold text-3xl",children:M?.totalPaid})})]})}),s.jsx("div",{className:"md:w-[40%] py-4 rounded-lg border-[#FF9900] border",children:(0,s.jsxs)("div",{className:"m-auto text-center flex flex-col items-center relative",children:[s.jsx("p",{className:"text-white mb-5 md:text-base text-sm",children:"Pagamentos Pendentes"}),s.jsx(Y.Z,{loading:D,height:"65px",width:"50px",children:s.jsx("p",{className:"text-white mb-5 md:text-7xl font-semibold text-3xl",children:M?.totalScheduled})})]})})]})]}),s.jsx("div",{className:"flex bg-[#1C1C1C] p-2 rounded-md m-auto mt-5 text-white w-full md:w-fit flex-wrap md:flex-nowrap gap-2",children:[{title:"Todos",value:""},{title:"Pagos",value:"PAID"},{title:"Pendentes",value:"SCHEDULED"}].map((e,t)=>s.jsx("div",{onClick:()=>{B(e.value),k(1)},className:`hover:bg-[#313131] px-2 py-3 rounded-md cursor-pointer text-center flex items-center ${R===e.value?"bg-[#313131]":""}`,children:s.jsx("p",{className:`${R===e.value?"text-[#FF9900]":""} text-xs`,children:e.title})},t))}),(0,s.jsxs)(c.Z,{children:[(0,s.jsxs)("div",{className:"w-full p-2 flex justify-end gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-1 w-full justify-between items-center gap-4",children:[s.jsx("div",{className:"md:w-4/12",children:s.jsx(l.Z,{handleSearch:getPayments,placeholder:"Pesquisar por CPF/CNPJ",setValue:e=>{(0,p.p4)(e).length<=11?L((0,p.VL)(e)):L((0,p.PK)(e))},value:A})}),s.jsx("div",{children:s.jsx(r.Z,{label:"Relat\xf3rio",className:"bg-orange-linear",loading:N,size:"sm",handleSubmit:()=>{_(!0),u.Z.post("/income-payment/report/all",{ownerRoleId:t.roleId,startScheduledDate:C,endScheduledDate:E}).then(e=>{window.open(e.data.url,"_blanck")}).catch(e=>{(0,x.Z)(e,"N\xe3o foi poss\xedvel emitir o relat\xf3rio. Tente novamente!")}).finally(()=>_(!1))}})})]}),s.jsx(n.Z,{activeModal:P,setActiveModal:w,handleSearch:()=>{getPayments()},children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"",children:[s.jsx("p",{className:"text-sm mb-1",children:"M\xeas"}),s.jsx("input",{value:O,className:"p-1 w-full rounded-md text-xs bg-transparent border",onChange:({target:e})=>{V(e.value),q(f()(e.value,"YYYY-MM").startOf("month").format("YYYY-MM-DD")),Z(f()(e.value,"YYYY-MM").endOf("month").format("YYYY-MM-DD"))},type:"month"})]}),""!==O&&(0,s.jsxs)("div",{className:"flex justify-between mt-2",children:[(0,s.jsxs)("div",{className:"",children:[s.jsx("p",{className:"text-xs mb-1",children:"Data Inicial"}),s.jsx("input",{value:C,className:"p-1 rounded-md text-xs bg-transparent border",onChange:({target:e})=>{if(f()(e.value).isAfter(E))return v.Am.warning("A data inicial n\xe3o pode ser posterior \xe0 data final!");q(e.value)},type:"date",min:f()(O,"YYYY-MM").startOf("month").format("YYYY-MM-DD"),max:f()(O,"YYYY-MM").endOf("month").format("YYYY-MM-DD")})]}),(0,s.jsxs)("div",{className:"",children:[s.jsx("p",{className:"text-xs mb-1",children:"Data Final"}),s.jsx("input",{value:E,className:"p-1 rounded-md text-xs bg-transparent border",onChange:({target:e})=>{if(f()(e.value,"YYYY-MM-DD").isBefore(f()(C,"YYYY-MM-DD")))return v.Am.warning("A data final n\xe3o pode ser anterior \xe0 data inicial!");Z(e.value)},type:"date",min:f()(C,"YYYY-MM").startOf("month").format("YYYY-MM-DD"),max:f()(O,"YYYY-MM").endOf("month").format("YYYY-MM-DD")})]})]})]})})]}),s.jsx(d.Z,{loading:D,pagination:{lastPage:$.lastPage,page:F,perPage:10,setPage:k,totalItems:String($.total)},data:a||[],headers:[{title:"Nome",component:"investor",width:"200px"},{title:"CPF/CNPJ",component:"document",render:e=>s.jsx("p",{children:String(e).length<=11?(0,p.VL)(String(e)):(0,p.PK)(String(e))})},{title:"E-mail",component:"email",width:"250px"},{title:"Status",component:"status",render:e=>s.jsx("div",{children:s.jsx("p",{className:`text-xs font-bold text-[${"SCHEDULED"!==e?"#1EF97C":"#FFB238"}] border border-[${"SCHEDULED"!==e?"#1EF97C":"#FFB238"}] text-center p-1 rounded-md`,children:"SCHEDULED"!==e?"Pago":"Pendente"})})},{title:"Rentabilidade",component:"profit",render:e=>s.jsx("p",{children:(0,m.Z)(Number(e))})},{title:"Dia pagamento",component:"scheduledDate",render:e=>s.jsx("p",{children:String(e).split("-")[2]})},{title:"",component:"id",render:(a,r)=>"broker"!==t.name?s.jsx("p",{className:"text-[#42A5F5] font-bold text-xs cursor-pointer",onClick:()=>e.push(`/financeiro/pagamento/${r.id}`),children:"Ver mais"}):s.jsx("p",{})}]})]})]})})]})}},99986:(e,t,a)=>{"use strict";a.d(t,{Z:()=>Button});var s=a(60080),r=a(69957);function Button({handleSubmit:e,loading:t,label:a,disabled:n,className:i,...l}){return s.jsx(r.z,{...l,onClick:e,loading:t,disabled:n,className:i,children:a})}},73294:(e,t,a)=>{"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}a.d(t,{A:()=>cleanValue,Z:()=>formatValue})},32408:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var s=a(17536);let r=(0,s.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\financeiro\pagamentos\page.tsx`),{__esModule:n,$$typeof:i}=r,l=r.default,o=l},64918:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var s=a(9885);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}),n=r}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,8394,7207,278,7669,8109,9012],()=>__webpack_exec__(17909));module.exports=a})();