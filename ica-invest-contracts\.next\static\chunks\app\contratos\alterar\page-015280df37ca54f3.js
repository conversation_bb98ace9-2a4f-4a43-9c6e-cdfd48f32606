(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6432],{3972:function(e,t,o){Promise.resolve().then(o.bind(o,241))},241:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return AlterarContrato}});var a=o(7437),r=o(8700),s=o(3014),i=o(4033),l=o(3877),n=o(8637),d=o(5968),c=o(2067),u=o.n(c),m=o(4207),x=o(4765),h=o(2265),p=o(1865),v=o(9701),f=o(5691),g=o(919),b=o(9891),j=o(3588),N=o(6654),C=o(4568),y=o(3256),w=o(3277);let phoneWithCountryMask=e=>{let t=e.replace(/\D/g,"");return t.startsWith("55")&&13===t.length?t.replace(/^(\d{2})(\d{2})(\d{5})(\d{4})$/,"$1 ($2) $3-$4"):t.startsWith("55")||11!==t.length?t.length<=13?t.startsWith("55")?t.length<=4?t.replace(/^(\d{2})(\d{0,2})$/,"$1 ($2"):t.length<=9?t.replace(/^(\d{2})(\d{2})(\d{0,5})$/,"$1 ($2) $3"):t.replace(/^(\d{2})(\d{2})(\d{5})(\d{0,4})$/,"$1 ($2) $3-$4"):(0,d.gP)(e):e:("55"+t).replace(/^(\d{2})(\d{2})(\d{5})(\d{4})$/,"$1 ($2) $3-$4")},M=f.Ry().shape({tipoContrato:f.Z_().required("Selecione o tipo de contrato"),nomeCompleto:f.Z_().required("Nome completo obrigat\xf3rio"),identidade:f.Z_().required("Identidade obrigat\xf3ria"),celular:f.Z_().required("Celular obrigat\xf3rio"),cpf:f.Z_().required("CPF/CNPJ obrigat\xf3rio"),dataNascimento:f.Z_().optional(),nomeMae:f.Z_().optional(),email:f.Z_().email("E-mail inv\xe1lido").required("E-mail obrigat\xf3rio"),cep:f.Z_().required("CEP obrigat\xf3rio"),cidade:f.Z_().required("Cidade obrigat\xf3ria"),endereco:f.Z_().required("Endere\xe7o obrigat\xf3rio"),numero:f.Z_().required("N\xfamero obrigat\xf3rio"),complemento:f.Z_().optional(),estado:f.Z_().required("Estado obrigat\xf3rio"),banco:f.Z_().required("Banco obrigat\xf3rio"),conta:f.Z_().required("Conta obrigat\xf3ria"),agencia:f.Z_().required("Ag\xeancia obrigat\xf3ria"),chavePix:f.Z_().required("Chave PIX obrigat\xf3ria"),modalidade:f.Z_().required("Modalidade obrigat\xf3ria").test("scp-to-mutuo-validation","N\xe3o \xe9 poss\xedvel alterar contratos SCP para modalidade M\xfatuo",function(e){return!0}),valorInvestimento:f.Z_().required("Valor do investimento obrigat\xf3rio").test("scp-multiple","Para contratos SCP, o valor deve ser m\xfaltiplo de R$ 5.000",function(e){let t=this.parent.modalidade;if("SCP"===t&&e){let t=Number(e.replaceAll(".","").replaceAll(",","."))||0;return t>0&&t%5e3==0}return!0}).test("scp-minimum","Para contratos SCP, o valor m\xednimo \xe9 de R$ 30.000",function(e){let t=this.parent.modalidade;if("SCP"===t&&e){let t=Number(e.replaceAll(".","").replaceAll(",","."))||0;return t>=3e4}return!0}),prazoInvestimento:f.Z_().required("Prazo do investimento obrigat\xf3rio"),taxaRemuneracao:f.Z_().required("Taxa de remunera\xe7\xe3o obrigat\xf3ria"),comprarCom:f.Z_().required("Forma de compra obrigat\xf3ria"),inicioContrato:f.Z_().required("In\xedcio do contrato obrigat\xf3rio"),fimContrato:f.Z_(),perfil:f.Z_().required("Perfil obrigat\xf3rio"),debenture:f.Z_().required("Deb\xeanture obrigat\xf3ria"),observacoes:f.Z_().optional(),quotaQuantity:f.Z_().optional(),irDeposito:f.O7().optional(),irDesconto:f.O7().optional()});function AlterarContrato(){var e,t,o,c,f,I,R,D,F,S,P,A,O,V,Y,Z,T,E,U,q,k,_,B,$,z;let L=(0,i.useRouter)(),W=(0,i.useSearchParams)(),G=W.get("tipo"),Q=W.get("investorId"),K=(0,y.e)(),{data:H,isLoading:J}=(0,b.a)({queryKey:["contract",Q],queryFn:async()=>{if(!Q)return null;let e=await C.Z.get("/contract/".concat(Q));return e.data},enabled:!!Q}),{register:X,handleSubmit:ee,watch:et,setValue:eo,trigger:ea,formState:{errors:er,isValid:es,isSubmitting:ei}}=(0,p.cI)({resolver:(0,v.X)(M),mode:"all",defaultValues:{tipoContrato:"pf",nomeCompleto:"",identidade:"",celular:"",cpf:"",dataNascimento:"",nomeMae:"",email:"",cep:"",cidade:"",endereco:"",numero:"",complemento:"",estado:"",banco:"",conta:"",agencia:"",chavePix:"",observacoes:"",modalidade:"MUTUO",valorInvestimento:"",prazoInvestimento:"",taxaRemuneracao:"",comprarCom:"pix",inicioContrato:"",fimContrato:"",perfil:"conservative",debenture:"n",quotaQuantity:"",irDeposito:!1,irDesconto:!1}}),[el,en]=(0,h.useState)(1),[ed,ec]=(0,h.useState)(""),[eu,em]=(0,h.useState)(!1),[ex,eh]=(0,h.useState)(0),[ep,ev]=(0,h.useState)(!1),[ef,eg]=(0,h.useState)(!1),[eb,ej]=(0,h.useState)(!1),[eN,eC]=(0,h.useState)(""),ey=(0,h.useMemo)(()=>{var e;if(!H||!(null===(e=H.contracts)||void 0===e?void 0:e[0]))return null;let t=H.contracts[0];return"P2P"===t.tags?"MUTUO":"SCP"},[H]);(0,h.useMemo)(()=>{if(H&&!J){var e,t,o,a,r;let s=null===(e=H.contracts)||void 0===e?void 0:e[0];if(s){eo("nomeCompleto",s.investorName||"");let e=H.document||"";if(e){let t=e.length<=11?(0,d.VL)(e):(0,d.PK)(e);eo("cpf",t)}else eo("cpf","");eo("identidade",H.rg||""),eo("email",H.email||"");let i=H.phone||"";eo("celular",i?phoneWithCountryMask(i):""),eo("nomeMae",H.motherName||""),eo("dataNascimento",H.birthDate||"");let l=H.zipCode||"";eo("cep",l?(0,d.Tc)(l):""),eo("cidade",H.city||""),eo("endereco",H.address||""),eo("numero",H.addressNumber||""),eo("complemento",H.complement||""),eo("estado",H.state||""),eo("banco",H.bank||""),eo("agencia",H.branch||""),eo("conta",H.accountNumber||""),eo("chavePix",H.phone||H.email||"");let n=s.investmentValue||"";eo("valorInvestimento",n?(0,d.Ht)(n):""),eo("taxaRemuneracao",s.investmentYield||""),eo("prazoInvestimento",s.investmentTerm||"");let c="pix";(null===(t=s.purchasedWith)||void 0===t?void 0:t.includes("TRANSFER\xcaNCIA"))||(null===(o=s.purchasedWith)||void 0===o?void 0:o.includes("BANC\xc1RIA"))?c="bank_transfer":(null===(a=s.purchasedWith)||void 0===a?void 0:a.includes("PIX"))?c="pix":(null===(r=s.purchasedWith)||void 0===r?void 0:r.includes("BOLETO"))&&(c="boleto"),eo("comprarCom",c),eo("inicioContrato",s.contractStart?u()(s.contractStart).format("YYYY-MM-DD"):""),eo("modalidade","P2P"===s.tags?"MUTUO":"SCP"),console.log("Formul\xe1rio pr\xe9-preenchido com dados do contrato:",H)}}},[H,J,eo]);let ew=et("modalidade"),eM=et("valorInvestimento");(0,h.useEffect)(()=>{if("SCP"===ew&&eM){let e=(0,w.Z)(eM)||0;eo("quotaQuantity",Math.floor(e/5e3).toString())}},[ew,eM,eo]);let eI=(0,h.useMemo)(()=>{var e,t;if(!H||!(null===(e=H.contracts)||void 0===e?void 0:e[0]))return!1;let o=H.contracts[0],a=null===(t=o.contractStatus)||void 0===t?void 0:t.toUpperCase(),r=!1;return o.addendum&&o.addendum.length>0&&(r=o.addendum.some(e=>{var t;let o=null===(t=e.contractStatus)||void 0===t?void 0:t.toUpperCase();return"ACTIVE"===o||"ATIVO"===o})),"ACTIVE"===a||"ATIVO"===a||r},[H]),onSubmit=async e=>{if(console.log("Iniciando submiss\xe3o do formul\xe1rio...",e),"MUTUO"===e.modalidade){if(!ef){s.Am.error("⚠️ Obrigat\xf3rio: Clique no bot\xe3o 'Calcular IR' antes de prosseguir.");return}if(!e.irDeposito&&!e.irDesconto){s.Am.error("⚠️ Obrigat\xf3rio: Selecione uma das op\xe7\xf5es de IR (dep\xf3sito ou desconto).");return}}if("SCP"===ey&&"MUTUO"===e.modalidade){s.Am.error("N\xe3o \xe9 poss\xedvel alterar contratos SCP para modalidade M\xfatuo");return}console.log("Investor ID:",Q);try{let t="pj"===e.tipoContrato,o=e.cpf.replace(/\D/g,"");console.log("Dados processados:",{isPJ:t,documento:o,userProfile:K});let a={name:e.nomeCompleto||"",rg:(()=>{let t=e.identidade;if(console.log("RG original:",t,"Tipo:",typeof t),!t)return"123456789";let o=t.toString().replace(/\D/g,"");console.log("RG limpo:",o);let a=o||"123456789";return console.log("RG final:",a,"Tipo:",typeof a),a})(),phoneNumber:(()=>{let t=e.celular.replace(/\D/g,"");if(console.log("Telefone original:",e.celular),console.log("Telefone limpo:",t),11===t.length&&!t.startsWith("55")){let e="55"+t;return console.log("Telefone com c\xf3digo do pa\xeds:",e),e}return t})(),motherName:e.nomeMae||"",dtBirth:(()=>{let t=e.dataNascimento;if(console.log("Data nascimento original:",t),!t)return new Date().toISOString();if(t.match(/^\d{4}-\d{2}-\d{2}$/)){let e=t+"T00:00:00.000Z";return console.log("Data nascimento ISO:",e),e}let o=new Date(t),a=o.toISOString();return console.log("Data nascimento convertida:",a),a})(),address:{zipCode:(()=>{let t=e.cep?e.cep.replace(/\D/g,""):"";return console.log("CEP processado:",t),t||"********"})(),neighborhood:"Centro",state:e.estado||"BA",city:e.cidade||"Cidade",complement:e.complemento||"",number:(()=>{let t=e.numero;if(console.log("N\xfamero original:",t,"Tipo:",typeof t),!t)return"1";let o=t.toString().replace(/\D/g,"");console.log("N\xfamero limpo:",o);let a=o||"1";return console.log("N\xfamero final:",a,"Tipo:",typeof a),a})()},accountBank:{bank:e.banco||"",accountNumber:e.conta||"",agency:e.agencia||"",pix:e.chavePix||""},document:o,contractType:e.modalidade,observations:"",placeOfBirth:e.cidade||"",occupation:"Investidor",documentType:t?"CNPJ":"CPF",issuer:"SSP",quota:"SCP"===e.modalidade&&parseInt(e.quotaQuantity||"0")||0,paymentPercentage:parseFloat(e.taxaRemuneracao)||0,parValue:parseInt(e.valorInvestimento.replace(/\D/g,""))||0};console.log("=== PAYLOAD COMPLETO ==="),console.log("Enviando dados para API...",JSON.stringify(a,null,2)),console.log("=== VALIDA\xc7\xd5ES ESPEC\xcdFICAS ==="),console.log("Data de nascimento final:",a.dtBirth,"Tipo:",typeof a.dtBirth),console.log("N\xfamero do endere\xe7o:",a.address.number,"Tipo:",typeof a.address.number),console.log("RG:",a.rg,"Tipo:",typeof a.rg),console.log("Quota:",a.quota,"Tipo:",typeof a.quota),console.log("PaymentPercentage:",a.paymentPercentage,"Tipo:",typeof a.paymentPercentage),console.log("ParValue:",a.parValue,"Tipo:",typeof a.parValue),console.log("=== VALIDA\xc7\xc3O NUMBER STRING ==="),console.log("RG \xe9 number string?",/^\d+$/.test(a.rg)),console.log("Number \xe9 number string?",/^\d+$/.test(a.address.number)),eD.mutate(a)}catch(e){console.error("Erro ao processar dados:",e),s.Am.error("Erro ao processar dados do contrato")}},eR=["tipoContrato","nomeCompleto","identidade","celular","cpf","email","cep","cidade","endereco","numero","estado","banco","conta","agencia","chavePix"],handleNext=async()=>{console.log("Validando campos da p\xe1gina 1:",eR);let e=await ea(eR);if(console.log("Resultado da valida\xe7\xe3o:",e),console.log("Erros atuais:",er),e)en(2),s.Am.success("Dados da p\xe1gina 1 validados com sucesso!");else{let e=eR.filter(e=>er[e]),t=e.length;t>0?s.Am.error("Por favor, preencha ".concat(t," campo(s) obrigat\xf3rio(s) antes de continuar.")):s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios antes de continuar.")}},eD=(0,j.D)({mutationFn:async e=>{console.log("Mutation - Criando novo contrato"),console.log("Dados enviados:",e);let t=await C.Z.post("/contract",e);return console.log("Resposta da API:",t.data),t.data},onSuccess:e=>{console.log("Contrato criado com sucesso:",e);let t=null==e?void 0:e.id;t?s.Am.success("Contrato criado com sucesso! ID: ".concat(t.substring(0,8),"... Redirecionando para a home...")):s.Am.success("Contrato criado com sucesso! Redirecionando para a home..."),em(!0),setTimeout(()=>{L.push("/")},3e3)},onError:e=>{(0,N.Z)(e,"Erro ao atualizar o contrato")}}),eF=et("prazoInvestimento"),eS=et("inicioContrato"),eP=(0,h.useMemo)(()=>{if(eS&&eF){let e=(0,g.H)({investDate:String(eF),startDate:eS}),t=u()(e,"DD-MM-YYYY").format("DD/MM/YYYY");return eo("fimContrato",u()(e,"DD-MM-YYYY").format("YYYY-MM-DD"),{shouldValidate:!0}),t}return""},[eS,eF,eo]),eA=(0,h.useMemo)(()=>{var e,t;if(!H||!(null===(e=H.contracts)||void 0===e?void 0:e[0]))return null;let o=H.contracts[0],a=[],calculateIR=(e,t,o)=>{let a=e*t*(o/30)/100,r=0;r=o<=180?22.5:o<=360?20:o<=720?17.5:15;let s=a*r/100;return{totalReturn:a,irRate:r,irValue:s}},r=null===(t=o.contractStatus)||void 0===t?void 0:t.toUpperCase();if("ACTIVE"===r||"ATIVO"===r){let e=parseInt(o.investmentValue)||0,t=parseFloat(o.investmentYield)||0,r=o.contractStart?u()(o.contractStart):u()(),s=o.contractEnd?u()(o.contractEnd):u()(),i=s.diff(r,"days"),l=calculateIR(e,t,i);a.push({type:"Contrato Inicial",amount:e,daysRentabilized:i,monthlyRate:t,irRate:l.irRate,irValue:l.irValue,totalReturn:l.totalReturn,status:o.contractStatus})}if(o.addendum&&o.addendum.length>0){let e=1;o.addendum.forEach(t=>{var o;let r=null===(o=t.contractStatus)||void 0===o?void 0:o.toUpperCase();if("ACTIVE"===r||"ATIVO"===r){let o=parseFloat(t.investmentValue)||0,r=parseFloat(t.investmentYield)||0,s=t.contractStart?u()(t.contractStart):u()(),i=t.contractEnd?u()(t.contractEnd):u()(),l=i.diff(s,"days"),n=calculateIR(o,r,l);a.push({type:"Aditivo ".concat(e),amount:o,daysRentabilized:l,monthlyRate:r,irRate:n.irRate,irValue:n.irValue,totalReturn:n.totalReturn,status:t.contractStatus}),e++}})}let s=a.reduce((e,t)=>e+t.irValue,0);return{details:a,totalIR:s,contractType:o.tags||"MUTUO"}},[H]),eO=et("irDesconto");function parseValor(e){return e&&(0,w.Z)(e)||0}(0,h.useEffect)(()=>{if("SCP"===ew&&eM&&eO&&(null==eA?void 0:eA.totalIR)){let e=(0,w.Z)(eM)||0,t=e-eA.totalIR,o=t%5e3;0!==o?(eh(5e3-o),ev(!0)):(ev(!1),eh(0))}else ev(!1),eh(0)},[ew,eM,eO,null==eA?void 0:eA.totalIR]),(0,h.useEffect)(()=>{"SCP"===ew?(eg(!0),ej(!0),eo("irDeposito",!1),eo("irDesconto",!1),eC("")):"MUTUO"===ew&&(eg(!1),ej(!1),ec(""),eo("irDeposito",!1),eo("irDesconto",!1),eC(""))},[ew,eo]);let eV=parseValor(et("valorInvestimento")),eY=ed?parseFloat(ed.replace("%","").replace(",",".")):0,eZ=eV&&eY?eV*(eY/100):0;return J?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.Z,{}),(0,a.jsx)(n.Z,{children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)("div",{className:"p-8 flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.Z,{}),(0,a.jsx)(n.Z,{children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsx)("div",{className:"flex items-center mb-6 w-full justify-center",children:(0,a.jsxs)("h1",{className:"text-2xl text-center w-full font-bold text-white",children:["Contratos",H&&(0,a.jsx)("span",{className:"text-sm text-gray-400 block mt-1",children:null===(t=H.contracts)||void 0===t?void 0:null===(e=t[0])||void 0===e?void 0:e.investorName})]})}),!G&&eI?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-white mb-6",children:"Escolha o tipo de altera\xe7\xe3o"}),(0,a.jsx)("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{L.push("/contratos/alterar?tipo=rentabilidade&investorId=".concat(Q))},children:(0,a.jsx)("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Rentabilidade"}),(0,a.jsx)("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a rentabilidade do contrato do investidor"})]}),(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("div",{className:"",children:(0,a.jsx)("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})}),(0,a.jsx)("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{L.push("/contratos/alterar?tipo=modalidade&investorId=".concat(Q))},children:(0,a.jsx)("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Modalidade"}),(0,a.jsx)("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a modalidade do contrato do investidor"})]}),(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("div",{className:"",children:(0,a.jsx)("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})})]}):G||eI?1===el?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Pessoais"}),(0,a.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,a.jsxs)("div",{className:"w-full md:w-1/2 mb-4",children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Tipo de Contrato"}),(0,a.jsxs)(x.Z,{value:et("tipoContrato"),onChange:e=>eo("tipoContrato",e.target.value,{shouldValidate:!0}),children:[(0,a.jsx)("option",{value:"pf",children:"Pessoa F\xedsica"}),(0,a.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,a.jsx)("div",{className:"w-full mb-4",children:(0,a.jsx)(m.Z,{register:X,name:"nomeCompleto",width:"100%",error:!!er.nomeCompleto,errorMessage:null==er?void 0:null===(o=er.nomeCompleto)||void 0===o?void 0:o.message,label:"Nome Completo"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"identidade",width:"100%",error:!!er.identidade,errorMessage:null==er?void 0:null===(c=er.identidade)||void 0===c?void 0:c.message,label:"Identidade"})}),(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"celular",width:"100%",maxLength:18,error:!!er.celular,errorMessage:null==er?void 0:null===(f=er.celular)||void 0===f?void 0:f.message,label:"Celular",onChange:e=>{eo("celular",phoneWithCountryMask(e.target.value),{shouldValidate:!0})}})})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"cpf",width:"100%",error:!!er.cpf,errorMessage:null==er?void 0:null===(I=er.cpf)||void 0===I?void 0:I.message,label:"CPF/CNPJ",onChange:e=>{let t="pj"===et("tipoContrato")?(0,d.PK)(e.target.value):(0,d.VL)(e.target.value);eo("cpf",t,{shouldValidate:!0})}})}),(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{type:"date",register:X,name:"dataNascimento",width:"100%",error:!!er.dataNascimento,errorMessage:null==er?void 0:null===(R=er.dataNascimento)||void 0===R?void 0:R.message,label:"Data de Nascimento (opcional)"})})]}),(0,a.jsx)("div",{className:"w-full mb-4",children:(0,a.jsx)(m.Z,{register:X,name:"nomeMae",width:"100%",error:!!er.nomeMae,errorMessage:null==er?void 0:null===(D=er.nomeMae)||void 0===D?void 0:D.message,label:"Nome da M\xe3e (opcional)"})}),(0,a.jsx)("div",{className:"w-full mb-4",children:(0,a.jsx)(m.Z,{register:X,name:"email",width:"100%",error:!!er.email,errorMessage:null==er?void 0:null===(F=er.email)||void 0===F?void 0:F.message,label:"E-mail"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"cep",width:"100%",error:!!er.cep,errorMessage:null==er?void 0:null===(S=er.cep)||void 0===S?void 0:S.message,label:"CEP",onChange:e=>{eo("cep",(0,d.Tc)(e.target.value),{shouldValidate:!0})}})}),(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"cidade",width:"100%",error:!!er.cidade,errorMessage:null==er?void 0:null===(P=er.cidade)||void 0===P?void 0:P.message,label:"Cidade"})})]}),(0,a.jsx)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"estado",width:"100%",error:!!er.estado,errorMessage:null==er?void 0:null===(A=er.estado)||void 0===A?void 0:A.message,label:"Estado",placeholder:"ex: SC"})})}),(0,a.jsx)("div",{className:"w-full mb-4",children:(0,a.jsx)(m.Z,{register:X,name:"endereco",width:"100%",error:!!er.endereco,errorMessage:null==er?void 0:null===(O=er.endereco)||void 0===O?void 0:O.message,label:"Endere\xe7o"})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"numero",width:"100%",error:!!er.numero,errorMessage:null==er?void 0:null===(V=er.numero)||void 0===V?void 0:V.message,label:"N\xfamero"})}),(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"complemento",width:"100%",error:!!er.complemento,errorMessage:null==er?void 0:null===(Y=er.complemento)||void 0===Y?void 0:Y.message,label:"Complemento"})})]})]})}),(0,a.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Banc\xe1rios"}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full mb-4",children:[(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"banco",width:"100%",error:!!er.banco,errorMessage:null==er?void 0:null===(Z=er.banco)||void 0===Z?void 0:Z.message,label:"Nome do Banco"})}),(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"conta",width:"100%",error:!!er.conta,errorMessage:null==er?void 0:null===(T=er.conta)||void 0===T?void 0:T.message,label:"Conta"})})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full",children:[(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"agencia",width:"100%",error:!!er.agencia,errorMessage:null==er?void 0:null===(E=er.agencia)||void 0===E?void 0:E.message,label:"Ag\xeancia"})}),(0,a.jsx)("div",{className:"w-full md:w-1/2",children:(0,a.jsx)(m.Z,{register:X,name:"chavePix",width:"100%",error:!!er.chavePix,errorMessage:null==er?void 0:null===(U=er.chavePix)||void 0===U?void 0:U.message,label:"Chave PIX"})})]})]}),(0,a.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"Observa\xe7\xf5es"}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[(0,a.jsx)("textarea",{...X("observacoes"),className:"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]",placeholder:"Observa\xe7\xf5es"}),er.observacoes&&(0,a.jsx)("span",{className:"text-red-500 text-xs",children:er.observacoes.message})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(r.z,{size:"lg",type:"button",className:"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]",onClick:handleNext,children:"Pr\xf3ximo"})})]}):(0,a.jsxs)("form",{onSubmit:ee(onSubmit,e=>{console.log("Erros de valida\xe7\xe3o:",e),s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios")}),children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"my-8 flex flex-col gap-4",children:[(0,a.jsx)("label",{className:"text-white mb-1 block",children:"Modalidade de Investimento"}),(0,a.jsxs)("div",{className:"flex flex-col md:w-1/3",children:[(0,a.jsxs)(x.Z,{value:et("modalidade"),onChange:e=>{let t=e.target.value;if("SCP"===ey&&"MUTUO"===t){s.Am.error("N\xe3o \xe9 poss\xedvel alterar contratos SCP para modalidade M\xfatuo");return}eo("modalidade",t,{shouldValidate:!0})},children:[(0,a.jsx)("option",{value:"MUTUO",children:"M\xfatuo"}),(0,a.jsx)("option",{value:"SCP",children:"SCP"})]}),(0,a.jsx)("span",{className:"text-[#FF9900] text-xs mt-1",children:"*Ao alterar modalidade, pressione o bot\xe3o calcular IR"}),"SCP"===ey&&(0,a.jsx)("div",{className:"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2",children:(0,a.jsxs)("p",{className:"text-yellow-200 text-xs",children:["⚠️ ",(0,a.jsx)("strong",{children:"Contrato atual \xe9 SCP:"})," N\xe3o \xe9 poss\xedvel alterar para modalidade M\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\xfatuo para SCP s\xe3o permitidos."]})})]}),"SCP"===et("modalidade")&&(0,a.jsx)("div",{className:"flex flex-col md:w-1/3 mt-2",children:(0,a.jsx)(m.Z,{register:X,name:"valorInvestimento",width:"100%",error:!!er.valorInvestimento,errorMessage:null==er?void 0:null===(q=er.valorInvestimento)||void 0===q?void 0:q.message,label:"Valor do Investimento",placeholder:"ex: R$ 50.000,00",setValue:e=>eo("valorInvestimento",(0,d.Ht)(e||""),{shouldValidate:!0})})}),"MUTUO"===et("modalidade")&&(0,a.jsx)("div",{className:"mt-4 md:w-1/3",children:(0,a.jsx)(r.z,{type:"button",className:"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full",onClick:()=>(function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=Number(et("prazoInvestimento"));if(!t||isNaN(t)){ec(""),eg(!1),e&&s.Am.warn("Preencha o prazo do investimento para calcular o IR.");return}let o=30*t;ec(o<=180?"22,5%":o<=360?"20%":o<=720?"17,5%":"15%"),eg(!0),e&&s.Am.success("IR calculado com sucesso! Agora selecione uma das op\xe7\xf5es de IR abaixo.")})(),children:"Calcular IR"})})]}),"MUTUO"===et("modalidade")&&(0,a.jsxs)("div",{className:"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3",children:[(0,a.jsxs)("p",{className:"text-white text-xs mb-1",children:["Valor investido: ",eV?eV.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),(0,a.jsxs)("p",{className:"text-white font-bold",children:["Valor total de IR: ",eZ?eZ.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),ed&&(0,a.jsxs)("p",{className:"text-[#FF9900] font-bold mt-2",children:["Al\xedquota do IR calculada: ",ed]})]}),"MUTUO"===et("modalidade")&&(0,a.jsxs)("div",{className:"w-full flex flex-col gap-4",children:[(0,a.jsx)("h4",{className:"text-lg font-bold text-white",children:"Detalhamento"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-[#232323]",children:[(0,a.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Tipo de Contrato"}),(0,a.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do Contrato"}),(0,a.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Dias Rentabilizados"}),(0,a.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Rentabilidade"}),(0,a.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Taxa de IR"}),(0,a.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do IR"}),(0,a.jsx)("th",{className:"px-2 py-2 font-semibold",children:"Anexos"})]})}),(0,a.jsx)("tbody",{children:J?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:7,className:"text-center px-2 py-4",children:"Carregando dados do contrato..."})}):eA?(0,a.jsxs)(a.Fragment,{children:[eA.details.length>0?eA.details.map((e,t)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:e.type}),(0,a.jsx)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.amount)}),(0,a.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.daysRentabilized," Dias"]}),(0,a.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.monthlyRate,"%"]}),(0,a.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.irRate,"%"]}),(0,a.jsx)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.irValue)}),(0,a.jsx)("td",{className:"px-2 py-2",children:"-"})]},t)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Nenhum contrato ativo encontrado para c\xe1lculo de IR"})}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{colSpan:6,className:"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]",children:"Valor total do IR"}),(0,a.jsx)("td",{className:"px-2 py-2 font-bold text-white border-t border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(eA.totalIR)})]})]}):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Dados do contrato n\xe3o encontrados"})})})]})})]}),!J&&!eI&&(0,a.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-4 mb-4",children:[(0,a.jsx)("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),(0,a.jsx)("p",{className:"text-white text-sm",children:"N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados."})]}),"SCP"===et("modalidade")&&(0,a.jsx)("div",{className:"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4",children:(0,a.jsxs)("p",{className:"text-blue-200 text-sm",children:["ℹ️ ",(0,a.jsx)("strong",{children:"Modalidade SCP:"})," Contratos SCP n\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\xe3o ser\xe1 exibida para esta modalidade."]})}),"MUTUO"===et("modalidade")&&!ef&&(0,a.jsx)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-4 mb-4",children:(0,a.jsxs)("p",{className:"text-red-200 text-sm",children:["⚠️ ",(0,a.jsx)("strong",{children:"A\xe7\xe3o Obrigat\xf3ria:"}),' Clique no bot\xe3o ""Calcular IR"" acima antes de prosseguir.']})}),"MUTUO"===et("modalidade")&&ef&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-green-900 border border-green-500 rounded-lg p-3 mb-4",children:(0,a.jsxs)("p",{className:"text-green-200 text-sm",children:["✅ ",(0,a.jsx)("strong",{children:"IR Calculado:"})," Agora selecione uma das op\xe7\xf5es abaixo (obrigat\xf3rio):"]})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-2",children:[(0,a.jsxs)("label",{className:"flex items-center text-white text-xs",children:[(0,a.jsx)("input",{type:"checkbox",className:"mr-2",checked:et("irDeposito"),onChange:e=>{e.target.checked?(eo("irDeposito",!0),eo("irDesconto",!1),ej(!0),eN&&(eo("valorInvestimento",eN),s.Am.info("Valor original do investimento restaurado. O IR ser\xe1 depositado separadamente."))):(eo("irDeposito",!1),ej(!!et("irDesconto")))}}),"Investidor ir\xe1 depositar valor referente ao IR"]}),(0,a.jsxs)("label",{className:"flex items-center text-white text-xs",children:[(0,a.jsx)("input",{type:"checkbox",className:"mr-2",checked:et("irDesconto"),onChange:e=>{if(e.target.checked){eo("irDesconto",!0),eo("irDeposito",!1),ej(!0);let e=et("valorInvestimento");if(e&&(null==eA?void 0:eA.totalIR)&&eA.totalIR>0){eN||eC(e);let t=parseValor(e),o=eA.totalIR;console.log("=== DEBUG DESCONTO IR ==="),console.log("Valor atual (string):",e),console.log("Valor num\xe9rico convertido:",t),console.log("Valor IR da tabela:",o);let a=t-o;if(console.log("Valor com desconto:",a),a<=0){s.Am.error("⚠️ Erro: O valor do IR \xe9 maior que o valor do investimento. Verifique os valores."),eo("irDesconto",!1);return}let r=Math.round(100*a),i=(0,d.Ht)(r.toString());console.log("Valor em centavos:",r),console.log("Valor formatado:",i),eo("valorInvestimento",i),s.Am.success("✅ Desconto aplicado! IR de ".concat(new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(o)," foi descontado do valor do investimento."))}else s.Am.error("⚠️ N\xe3o foi poss\xedvel aplicar o desconto. Verifique se h\xe1 contratos ativos na tabela de IR."),eo("irDesconto",!1);s.Am.info("⚠️ Aten\xe7\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\xe1rio criar um adendo para ajuste de valor.")}else eo("irDesconto",!1),ej(!!et("irDeposito")),eN&&(eo("valorInvestimento",eN),s.Am.info("Valor original do investimento restaurado."))}}),"Investidor decidiu desconto do IR sobre o valor do contrato"]})]})]}),"MUTUO"===et("modalidade")&&et("irDesconto")&&eN&&(null==eA?void 0:eA.totalIR)&&(0,a.jsxs)("div",{className:"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4",children:[(0,a.jsx)("h4",{className:"text-blue-200 font-bold mb-2",children:"\uD83D\uDCB0 Desconto de IR Aplicado"}),(0,a.jsxs)("div",{className:"text-blue-200 text-sm space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Valor original:"})," ",eN]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Valor total do IR (da tabela):"})," ",new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(eA.totalIR)]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Valor final (com desconto):"})," ",et("valorInvestimento")]}),(0,a.jsx)("p",{className:"text-yellow-200 text-xs mt-2",children:"ℹ️ O valor descontado \xe9 baseado no c\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos."})]})]}),ep&&"SCP"===et("modalidade")&&et("irDesconto")&&(0,a.jsxs)("div",{className:"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4",children:[(0,a.jsx)("h4",{className:"text-yellow-200 font-bold mb-2",children:"⚠️ Ajuste de Valor Necess\xe1rio"}),(0,a.jsx)("p",{className:"text-yellow-200 text-sm mb-3",children:"Informamos que o valor atual, ap\xf3s a aplica\xe7\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\xe3o est\xe1 em conformidade com nossas regras de neg\xf3cio, pois n\xe3o \xe9 divis\xedvel por R$ 5.000,00."}),(0,a.jsxs)("p",{className:"text-yellow-200 text-sm font-semibold",children:["Para que as cotas sejam ajustadas corretamente, ser\xe1 necess\xe1rio o pagamento complementar no valor de"," ",(0,a.jsx)("span",{className:"text-yellow-100 font-bold",children:ex.toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}),"."]})]})]}),(0,a.jsx)("h3",{className:"text-lg font-bold text-white my-8",children:"Dados do Investimento"}),(0,a.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:["MUTUO"===et("modalidade")&&(0,a.jsx)(m.Z,{register:X,name:"valorInvestimento",width:"100%",error:!!er.valorInvestimento,errorMessage:null==er?void 0:null===(k=er.valorInvestimento)||void 0===k?void 0:k.message,label:"Valor do Investimento",setValue:e=>eo("valorInvestimento",(0,d.Ht)(e||""),{shouldValidate:!0})}),"SCP"===et("modalidade")&&(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsx)(m.Z,{register:X,name:"quotaQuantity",width:"100%",error:!!er.quotaQuantity,errorMessage:null==er?void 0:null===(_=er.quotaQuantity)||void 0===_?void 0:_.message,label:"Quantidade de cotas (calculado automaticamente)",placeholder:"Calculado automaticamente",disabled:!0})}),(0,a.jsx)(m.Z,{register:X,type:"text",name:"taxaRemuneracao",width:"100%",error:!!er.taxaRemuneracao,errorMessage:null==er?void 0:null===(B=er.taxaRemuneracao)||void 0===B?void 0:B.message,label:"Taxa de Remunera\xe7\xe3o Mensal %",placeholder:"ex: 2"}),(0,a.jsx)(m.Z,{type:"date",register:X,maxDate:u()().format("YYYY-MM-DD"),name:"inicioContrato",width:"100%",setValue:e=>eo("inicioContrato",e,{shouldValidate:!0}),error:!!er.inicioContrato,errorMessage:null==er?void 0:null===($=er.inicioContrato)||void 0===$?void 0:$.message,label:"In\xedcio do Contrato"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-white mb-1 block",children:"Perfil"}),(0,a.jsxs)(x.Z,{value:et("perfil"),onChange:e=>eo("perfil",e.target.value,{shouldValidate:!0}),children:[(0,a.jsx)("option",{value:"conservative",children:"Conservador"}),(0,a.jsx)("option",{value:"moderate",children:"Moderado"}),(0,a.jsx)("option",{value:"aggressive",children:"Agressivo"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:[(0,a.jsx)(m.Z,{register:X,type:"number",name:"prazoInvestimento",width:"100%",error:!!er.prazoInvestimento,errorMessage:null==er?void 0:null===(z=er.prazoInvestimento)||void 0===z?void 0:z.message,label:"Prazo do Investimento - em meses",placeholder:"ex: 12"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-white mb-1 block",children:"Comprar com"}),(0,a.jsxs)(x.Z,{value:et("comprarCom"),onChange:e=>eo("comprarCom",e.target.value,{shouldValidate:!0}),children:[(0,a.jsx)("option",{value:"pix",children:"PIX"}),(0,a.jsx)("option",{value:"boleto",children:"Boleto"}),(0,a.jsx)("option",{value:"bank_transfer",children:"Transfer\xeancia Banc\xe1ria"})]})]}),(0,a.jsx)(m.Z,{type:"date",register:X,name:"fimContrato",value:eP?u()(eP,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"100%",disabled:!0,label:"Fim do Contrato"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-white mb-1 block",children:"Deb\xeanture"}),(0,a.jsxs)(x.Z,{value:et("debenture"),onChange:e=>eo("debenture",e.target.value,{shouldValidate:!0}),children:[(0,a.jsx)("option",{value:"s",children:"Sim"}),(0,a.jsx)("option",{value:"n",children:"N\xe3o"})]})]})]})]})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(r.z,{type:"button",variant:"outline",size:"lg",onClick:()=>en(1),className:"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white",children:"← Voltar"}),(0,a.jsx)(r.z,{size:"lg",type:"submit",className:"px-8 py-2 rounded-lg ".concat(eI?"bg-[#FF9900] text-white hover:bg-[#e68a00]":"bg-gray-500 text-gray-300 cursor-not-allowed"),onClick:()=>{console.log("Bot\xe3o Concluir clicado"),console.log("Estado do formul\xe1rio:",{isValid:es,errors:er}),console.log("Dados atuais:",et()),console.log("Contratos ativos:",eI)},disabled:!eI||ei||eD.isPending||eu||"MUTUO"===et("modalidade")&&(!ef||!et("irDeposito")&&!et("irDesconto")),children:eI?eu?"Redirecionando...":ei||eD.isPending?"Enviando...":"MUTUO"!==et("modalidade")||ef?"MUTUO"!==et("modalidade")||et("irDeposito")||et("irDesconto")?"Alterar Contrato":"Selecione uma op\xe7\xe3o de IR":"Calcule o IR primeiro":"Nenhum contrato ativo encontrado"})]})]}):(0,a.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-6 text-center",children:[(0,a.jsx)("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),(0,a.jsx)("p",{className:"text-white text-sm",children:"N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados."})]})]})})})]})}},4207:function(e,t,o){"use strict";o.d(t,{Z:function(){return InputText}});var a=o(7437);function InputText(e){let{label:t,setValue:o,error:r,errorMessage:s,width:i="auto",register:l,name:n,placeholder:d="",type:c="text",disabled:u=!1,minDate:m,minLength:x,maxLength:h,maxDate:p,disableErrorMessage:v=!1,onBlur:f,value:g,onChange:b}=e;return(0,a.jsxs)("div",{className:"input relative group",style:{width:i},children:[(0,a.jsxs)("p",{className:"text-white mb-1 text-sm",children:[t,r&&!v&&(0,a.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",s]})]}),(0,a.jsx)("input",{...l(n),placeholder:d,type:c,id:n,disabled:u,min:m,max:p,minLength:x,maxLength:h,...o?{onChange:e=>{let{target:t}=e;return o(t.value)}}:{},onBlur:f,className:"h-12 w-full px-4 ".concat(u?"text-zinc-400":"text-white"," rounded-xl ").concat(r?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),...void 0!==g?{value:g}:{},...b?{onChange:b}:{}}),r&&(0,a.jsx)("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:s})]})}o(9514)},919:function(e,t,o){"use strict";o.d(t,{H:function(){return getFinalDataWithMount},Z:function(){return getDataFilter}});var a=o(2067),r=o.n(a);function getDataFilter(e){switch(e){case"TODAY":{let e=r()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}case"WEEK":{let e=r()().startOf("isoWeek").format("YYYY-MM-DD"),t=r()().endOf("isoWeek").format("YYYY-MM-DD");return{startDate:e,endDate:t}}case"MONTH":{let e=r()().startOf("month").format("YYYY-MM-DD"),t=r()().endOf("month").format("YYYY-MM-DD");return{startDate:e,endDate:t}}default:{let e=r()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}}}function getFinalDataWithMount(e){let{investDate:t,startDate:o}=e,a=r()(o).format("DD/MM/YYYY"),s=Number(t),i=r()(a,"DD/MM/YYYY").add(s,"months").format("DD/MM/YYYY");return i}},6654:function(e,t,o){"use strict";o.d(t,{Z:function(){return returnError}});var a=o(3014);function returnError(e,t){var o,r,s,i;let l=(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(o=r.data)||void 0===o?void 0:o.message)||(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.error);if(Array.isArray(l))return l.forEach(e=>{a.Am.error(e,{toastId:e})}),l.join("\n");if("string"==typeof l)return a.Am.error(l,{toastId:l}),l;if("object"==typeof l&&null!==l){let e=Object.values(l).flat().join("\n");return a.Am.error(e,{toastId:e}),e}return a.Am.error(t,{toastId:t}),t}},3277:function(e,t,o){"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}o.d(t,{Z:function(){return formatNumberValue}})},9514:function(){},7470:function(e,t,o){"use strict";o.d(t,{R:function(){return getDefaultState},m:function(){return i}});var a=o(7987),r=o(9024),s=o(1640),i=class extends r.F{#e;#t;#o;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||getDefaultState(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#o?.continue()??this.execute(this.state.variables)}async execute(e){let onContinue=()=>{this.#a({type:"continue"})};this.#o=(0,s.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let t="pending"===this.state.status,o=!this.#o.canStart();try{if(t)onContinue();else{this.#a({type:"pending",variables:e,isPaused:o}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#a({type:"pending",context:t,variables:e,isPaused:o})}let a=await this.#o.start();return await this.#t.config.onSuccess?.(a,e,this.state.context,this),await this.options.onSuccess?.(a,e,this.state.context),await this.#t.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,e,this.state.context),this.#a({type:"success",data:a}),a}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#a({type:"error",error:t})}}finally{this.#t.runNext(this)}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.Vr.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},3588:function(e,t,o){"use strict";o.d(t,{D:function(){return useMutation}});var a=o(2265),r=o(7470),s=o(7987),i=o(2996),l=o(300),n=class extends i.l{#r;#s=void 0;#i;#l;constructor(e,t){super(),this.#r=e,this.setOptions(t),this.bindMethods(),this.#n()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#r.defaultMutationOptions(e),(0,l.VS)(this.options,t)||this.#r.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#i,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.Ym)(t.mutationKey)!==(0,l.Ym)(this.options.mutationKey)?this.reset():this.#i?.state.status==="pending"&&this.#i.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#i?.removeObserver(this)}onMutationUpdate(e){this.#n(),this.#d(e)}getCurrentResult(){return this.#s}reset(){this.#i?.removeObserver(this),this.#i=void 0,this.#n(),this.#d()}mutate(e,t){return this.#l=t,this.#i?.removeObserver(this),this.#i=this.#r.getMutationCache().build(this.#r,this.options),this.#i.addObserver(this),this.#i.execute(e)}#n(){let e=this.#i?.state??(0,r.R)();this.#s={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#d(e){s.Vr.batch(()=>{if(this.#l&&this.hasListeners()){let t=this.#s.variables,o=this.#s.context;e?.type==="success"?(this.#l.onSuccess?.(e.data,t,o),this.#l.onSettled?.(e.data,null,t,o)):e?.type==="error"&&(this.#l.onError?.(e.error,t,o),this.#l.onSettled?.(void 0,e.error,t,o))}this.listeners.forEach(e=>{e(this.#s)})})}},d=o(8038);function useMutation(e,t){let o=(0,d.NL)(t),[r]=a.useState(()=>new n(o,e));a.useEffect(()=>{r.setOptions(e)},[r,e]);let i=a.useSyncExternalStore(a.useCallback(e=>r.subscribe(s.Vr.batchCalls(e)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),c=a.useCallback((e,t)=>{r.mutate(e,t).catch(l.ZT)},[r]);if(i.error&&(0,l.L3)(r.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:c,mutateAsync:i.mutate}}}},function(e){e.O(0,[6990,8276,5371,6946,1865,3964,9891,3151,2971,7864,1744],function(){return e(e.s=3972)}),_N_E=e.O()}]);