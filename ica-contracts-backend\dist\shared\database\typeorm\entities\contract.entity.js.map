{"version": 3, "file": "contract.entity.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/entities/contract.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAUiB;AAEjB,uDAAmD;AACnD,uEAAkE;AAClE,mEAA8D;AAC9D,yEAAoE;AACpE,mEAA8D;AAC9D,uFAAiF;AACjF,uFAAiF;AACjF,+DAA2D;AAC3D,6EAAuE;AACvE,+DAA0D;AAGnD,IAAM,cAAc,GAApB,MAAM,cAAc;CAgI1B,CAAA;AAhIY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;0CACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC7B;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACvC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC7B;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BAClC,IAAI;qDAAC;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BAClC,IAAI;mDAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC7B;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAC9B;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDAC7B;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDAC7B;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC7B;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;8CAC/C;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;4CAC5B;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;mDAC5B;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;sDACzB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDAC9B;AASzB;IAPC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,iCAAiC;QACvC,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,CAAC;QACZ,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,IAAI;KACf,CAAC;;qEACoC;AAStC;IAPC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,kCAAkC;QACxC,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,CAAC;QACZ,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,IAAI;KACf,CAAC;;sEACqC;AAGvC;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;AAShB;IAPC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,oDAAuB,EAAE;QACxC,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,oBAAU,EAAC;QACV,IAAI,EAAE,qBAAqB;QAC3B,oBAAoB,EAAE,IAAI;KAC3B,CAAC;;yDACmD;AAMrD;IAJC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,uCAAiB,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE;QAC/D,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,IAAI;KACd,CAAC;;mDAC+B;AAOjC;IALC,IAAA,mBAAS,EACR,GAAG,EAAE,CAAC,oDAAuB,EAC7B,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CACxC;IACA,IAAA,oBAAU,EAAC,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,CAAC;;gDACtB;AAG5C;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAmB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;;8CACxB;AAMxC;IAJC,IAAA,mBAAS,EACR,GAAG,EAAE,CAAC,8DAA4B,EAClC,CAAC,iBAAiB,EAAE,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAClD;;yDAC2D;AAG5D;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,gCAAc,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;;gDAC5B;AAMrC;IAJC,IAAA,mBAAS,EACR,GAAG,EAAE,CAAC,+CAAqB,EAC3B,CAAC,eAAe,EAAE,EAAE,CAAC,eAAe,CAAC,QAAQ,CAC9C;;wDACmD;AAKpD;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,wCAAkB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;;qDACzB;AAMpC;IAJC,IAAA,mBAAS,EACR,GAAG,EAAE,CAAC,8DAA4B,EAClC,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC,SAAS,CAC3C;;8DACsD;AAGvD;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAmB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;;8CACxB;AAExC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iDAAsB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;;iDAC3B;yBA/HnC,cAAc;IAD1B,IAAA,gBAAM,EAAC,UAAU,CAAC;GACN,cAAc,CAgI1B", "sourcesContent": ["import {\r\n  <PERSON>umn,\r\n  CreateDate<PERSON><PERSON>umn,\r\n  <PERSON>tity,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  ManyToOne,\r\n  OneToMany,\r\n  PrimaryGeneratedColumn,\r\n  Relation,\r\n  UpdateDateColumn,\r\n} from 'typeorm';\r\n\r\nimport { AddendumEntity } from './addendum.entity';\r\nimport { ContractAdvisorEntity } from './contract-advisor.entity';\r\nimport { ContractAuditEntity } from './contract-audit.entity';\r\nimport { ContractDeletionEntity } from './contract-deletion.entity';\r\nimport { ContractEventEntity } from './contract-event.entity';\r\nimport { IncomePaymentScheduledEntity } from './income-payment-scheduled.entity';\r\nimport { IncomeReportsContractsEntity } from './income-reports-contracts.entity';\r\nimport { NotificationEntity } from './notification.entity';\r\nimport { OwnerRoleRelationEntity } from './owner-role-relation.entity';\r\nimport { PreRegisterEntity } from './pre-register.entity';\r\n\r\n@Entity('contract')\r\nexport class ContractEntity {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ name: 'external_id', nullable: true })\r\n  externalId: number;\r\n\r\n  @Column({ name: 'owner_role_relation', nullable: true })\r\n  brokerId: string;\r\n\r\n  @Column({ name: 'investor_id', nullable: true })\r\n  investorId: string;\r\n\r\n  @Column({ name: 'start_contract', type: 'date' })\r\n  startContract: Date;\r\n\r\n  @Column({ name: 'end_contract', type: 'date' })\r\n  endContract: Date;\r\n\r\n  @Column({ name: 'contract_pdf', nullable: true })\r\n  contractPdf: string;\r\n\r\n  @Column({ name: 'old_contract_pdf', nullable: true })\r\n  oldContractPdf: string;\r\n\r\n  @Column({ name: 'proof_payment', nullable: true })\r\n  proofPayment: string;\r\n\r\n  @Column({ name: 'sign_investor', nullable: true })\r\n  signInvestor: string;\r\n\r\n  @Column({ name: 'sign_ica', nullable: true })\r\n  signIca: string;\r\n\r\n  @Column({ name: 'status', nullable: true, default: 'aberto' })\r\n  status: string;\r\n\r\n  @Column({ name: 'type', default: 'p2p' })\r\n  type: string;\r\n\r\n  @Column({ name: 'is_debenture', default: false })\r\n  isDebenture: boolean;\r\n\r\n  @Column({ name: 'contract_number', default: 1 })\r\n  contractNumber: number;\r\n\r\n  @Column({ name: 'duration_in_months', nullable: true })\r\n  durationInMonths: number;\r\n  \r\n  @Column({\r\n    name: 'broker_participation_percentage',\r\n    type: 'decimal',\r\n    precision: 5,\r\n    scale: 2,\r\n    nullable: true,\r\n  })\r\n  brokerParticipationPercentage: string;\r\n\r\n  @Column({\r\n    name: 'advisor_participation_percentage',\r\n    type: 'decimal',\r\n    precision: 5,\r\n    scale: 2,\r\n    nullable: true,\r\n  })\r\n  advisorParticipationPercentage: string;\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  @ManyToOne(() => OwnerRoleRelationEntity, {\r\n    nullable: true,\r\n  })\r\n  @JoinColumn({\r\n    name: 'owner_role_relation',\r\n    referencedColumnName: 'id',\r\n  })\r\n  ownerRoleRelation: Relation<OwnerRoleRelationEntity>;\r\n\r\n  @OneToMany(() => PreRegisterEntity, (signed) => signed.contract, {\r\n    nullable: true,\r\n    cascade: true,\r\n  })\r\n  signataries: PreRegisterEntity[];\r\n\r\n  @ManyToOne(\r\n    () => OwnerRoleRelationEntity,\r\n    (investor) => investor.contractInvestor,\r\n  )\r\n  @JoinColumn([{ name: 'investor_id', referencedColumnName: 'id' }])\r\n  investor: Relation<OwnerRoleRelationEntity>;\r\n\r\n  @OneToMany(() => ContractEventEntity, (event) => event.contract)\r\n  events: Relation<ContractEventEntity[]>;\r\n\r\n  @OneToMany(\r\n    () => IncomePaymentScheduledEntity,\r\n    (scheduledPayments) => scheduledPayments.contract,\r\n  )\r\n  scheduledPayments: Relation<IncomePaymentScheduledEntity[]>;\r\n\r\n  @OneToMany(() => AddendumEntity, (addendum) => addendum.contract)\r\n  addendum: Relation<AddendumEntity[]>;\r\n\r\n  @OneToMany(\r\n    () => ContractAdvisorEntity,\r\n    (contractAdvisor) => contractAdvisor.contract,\r\n  )\r\n  contractAdvisors: Relation<ContractAdvisorEntity[]>;\r\n\r\n  latestEvent?: ContractEventEntity;\r\n\r\n  @OneToMany(() => NotificationEntity, (item) => item.contract)\r\n  notifications: NotificationEntity[];\r\n\r\n  @OneToMany(\r\n    () => IncomeReportsContractsEntity,\r\n    (incomeReports) => incomeReports.contracts,\r\n  )\r\n  incomeReportsContracts: IncomeReportsContractsEntity[];\r\n\r\n  @OneToMany(() => ContractAuditEntity, (audit) => audit.contract)\r\n  audits: Relation<ContractAuditEntity[]>;\r\n  @OneToMany(() => ContractDeletionEntity, (deletion) => deletion.contract)\r\n  deletions: Relation<ContractDeletionEntity[]>;\r\n}\r\n"]}