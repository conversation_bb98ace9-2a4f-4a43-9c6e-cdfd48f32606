import { Relation } from 'typeorm';
import { OwnerRoleRelationEntity } from './owner-role-relation.entity';
import { ContractEntity } from './contract.entity';
import { AddendumEntity } from './addendum.entity';
export declare enum NotificationTypeEnum {
    DUPLICATED_DOCUMENT = "DUPLICATED_DOCUMENT",
    NEW_CONTRACT = "NEW_CONTRACT",
    INCLUDE_ADDITIVE = "INCLUDE_ADDITIVE"
}
export declare class NotificationEntity {
    id: string;
    type: NotificationTypeEnum;
    title: string;
    description: string;
    viewed: boolean;
    contractId: string;
    addendumId: number;
    investorId: string;
    contractValue: number | null;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
    brokerOwnerRoleRelation: Relation<OwnerRoleRelationEntity>;
    investor: Relation<OwnerRoleRelationEntity>;
    adminOwnerRoleRelation: Relation<OwnerRoleRelationEntity>;
    contract: Relation<ContractEntity>;
    addendum: Relation<AddendumEntity>;
}
