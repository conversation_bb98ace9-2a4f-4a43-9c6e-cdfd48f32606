(()=>{var e={};e.id=3069,e.ids=[3069],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},24202:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>m,routeModule:()=>p,tree:()=>c});var r=t(73137),a=t(54647),i=t(4183),o=t.n(i),n=t(71775),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let l=r.AppPageRouteModule,c=["",{children:["usuarios",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27727)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51918,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\usuarios\\page.tsx"],u="/usuarios/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new l({definition:{kind:a.x.APP_PAGE,page:"/usuarios/page",pathname:"/usuarios",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70578:(e,s,t)=>{Promise.resolve().then(t.bind(t,68287))},68287:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Usuarios});var r=t(60080),a=t(97669),i=t(47956),o=t(9885),n=t(85814),d=t(34751),l=t(28168),c=t(1808),m=t(57048),u=t(96413),x=t(34692),p=t(74644),h=t(24577),j=t(58470),v=t(18109),b=t(90682),f=t(24774);function Usuarios(){let[e,s]=(0,o.useState)(!1),[t,g]=(0,o.useState)(),[N,w]=(0,o.useState)(),[y,k]=(0,o.useState)(),[P,_]=(0,o.useState)(),[q,I]=(0,o.useState)(),[Z,C]=(0,o.useState)(!1),[S,A]=(0,o.useState)("broker"),[F,E]=(0,o.useState)(!1),[$,M]=(0,o.useState)("Todos"),[D,K]=(0,o.useState)(1),[L,V]=(0,o.useState)(),[B,T]=(0,o.useState)(),[G,U]=(0,o.useState)(),[z,O]=(0,o.useState)(),[J,R]=(0,o.useState)(),X=(0,b.e)();async function getAcessorsInvestors(){s(!0);try{let e=await n.Z.get("superadmin"===X.name?"/wallets/list-investors-broker":"/wallets/admin/investors-broker",{params:{adviserId:"superadmin"!==X.name?X.roleId:void 0,brokerId:P?.id}}),t=await n.Z.get("superadmin"===X.name?"/wallets/list-advisors-broker":"/wallets/admin/advisors-broker",{params:{adviserId:"superadmin"!==X.name?X.roleId:void 0,brokerId:P?.id}}),r=e?.data,a=t?.data,i=[],o=[];r.map(e=>{i.push({...e,document:e.document.length<=11?(0,u.VL)(e.document||""):(0,u.PK)(e.document||""),type:"investidor"})}),a.map(e=>{o.push({...e,document:e.document.length<=11?(0,u.VL)(e.document||""):(0,u.PK)(e.document||""),type:"assessor"})}),g([...o,...i]),s(!1)}catch(e){s(!1),(0,h.Z)(e,"Erro ao buscar os assessores")}}let getInvestorData=e=>{d.Am.info("Buscando dados do investidor..."),n.Z.get(`/contract/${e.id}`).then(s=>{U({...s.data,...e}),C(!0)}).catch(e=>{(0,h.Z)(e,"N\xe3o foi possivel buscar o investidor")})},getBrokerData=e=>{d.Am.info("Buscando dados do broker...",{toastId:"broker"}),n.Z.get(`/wallets/broker/one?brokerId=${e.id}`).then(e=>{d.Am.dismiss("broker"),C(!0),O(e.data)}).catch(e=>{(0,h.Z)(e,"N\xe3o foi possivel buscar o investidor"),d.Am.dismiss("broker")})},getAdvisorData=e=>{d.Am.info("Buscando dados do assessor...",{toastId:"advisor"}),n.Z.get(`/wallets/advisor/one?advisorId=${e.id}`).then(e=>{d.Am.dismiss("advisor"),C(!0),O(e.data)}).catch(e=>{(0,h.Z)(e,"N\xe3o foi possivel buscar o investidor"),d.Am.dismiss("broadvisorker")})};(0,o.useEffect)(()=>{"superadmin"===X.name?V(0):V(1)},[]),(0,o.useEffect)(()=>{0===L?(s(!0),n.Z.get("/wallets/list-admin").then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e?.document?.length<=11?(0,u.VL)(e?.document||""):(0,u.PK)(e?.document||""),type:"gestor de carteira"})}),g(s)}).catch(e=>{d.Am.error("Erro ao buscar os brokers")}).finally(()=>s(!1))):1===L?(s(!0),n.Z.get("superadmin"!==X.name?"/wallets/admin/brokers":"/wallets/list-brokers-admin",{params:{adminId:"superadmin"===X.name?y?.id:void 0,adviserId:"superadmin"!==X.name?X.roleId:void 0}}).then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e?.document?.length<=11?(0,u.VL)(e.document||""):(0,u.PK)(e.document||""),type:"broker"})}),g(s)}).catch(e=>{(0,h.Z)(e,"Erro ao buscar os brokers")}).finally(()=>s(!1))):2===L?getAcessorsInvestors():3===L&&(s(!0),n.Z.get("superadmin"===X.name?"/wallets/list-investors-advisor":"/wallets/admin/investors-advisor-broker",{params:"superadmin"===X.name?{advisorId:q?.id}:{adviserId:X.roleId,brokerId:P?.id,advisorId:q?.id}}).then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e.document.length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document),type:"investidor"})}),g(s)}).catch(e=>{(0,h.Z)(e,"Erro ao buscar os investidores")}).finally(()=>s(!1)))},[L]);let renderTable=(s,a)=>(0,r.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] w-full text-white mt-10 overflow-x-auto rounded-b-md border border-[#FF9900]",children:[r.jsx("div",{className:"flex w-full justify-end p-2",children:r.jsx("div",{className:"w-80"})}),(0,r.jsxs)("table",{className:"w-full relative min-h-20",children:[r.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,r.jsxs)("tr",{className:"w-full",children:[r.jsx("th",{className:"w-10",children:r.jsx("p",{className:"font-bold text-sm"})}),r.jsx("th",{className:"w-10",children:r.jsx("p",{className:"font-bold text-sm"})}),r.jsx("th",{className:"py-2 max-w-[350px] text-center",children:r.jsx("p",{className:"font-bold text-sm text-center",children:"Nome"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"E-mail"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Perfil"})})]})}),r.jsx(r.Fragment,{children:e?(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[r.jsx("td",{className:"w-10 px-1",children:r.jsx(p.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(p.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(p.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(p.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(p.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(p.j,{height:"25px"})})]}):r.jsx(r.Fragment,{children:t?.length>0?r.jsx("tbody",{className:"w-full",children:t.map((e,s)=>(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[r.jsx("td",{className:"w-10",children:"investidor"!==e.type&&r.jsx("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{"investidor"!==e.type&&(0===L?(k(e),V(1)):1===L?(_(e),V(2)):2===L&&(I(e),V(3)))},children:r.jsx(l.Z,{color:"#fff",width:20})})}),r.jsx("td",{className:"w-10",children:("investidor"===e.type||"broker"===e.type||"assessor"===e.type)&&("superadmin"===X.name||"admin"===X.name)&&r.jsx("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{"investidor"===e.type?(A("investor"),getInvestorData(e)):"broker"===e.type?(A("broker"),getBrokerData(e)):"assessor"===e.type&&(A("advisor"),getAdvisorData(e))},children:r.jsx(c.Z,{color:"#fff",width:20})})}),r.jsx("td",{className:"max-w-[350px]",children:r.jsx("p",{className:"text-sm text-center py-1 ",children:e.name||"N\xe3o encontrado"})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:e.document?(0,u.p4)(e.document).length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document):"N\xe3o encontrado"})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:e.email||"N\xe3o encontrado"})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:e.type})})]},s))}):r.jsx("div",{className:"text-center mt-5 absolute w-full pb-5",children:r.jsx("p",{children:"Nenhum dado encontrado"})})})})]}),r.jsx(v.Z,{page:D,setPage:K,lastPage:Number(B?.lastPage||1),perPage:Number(B?.perPage||10),totalItems:B?.total||"0"})]});function returnName(){return 1===L?{name:y?.name,letters:`${y?.name.split(" ")[0][0]}${y?.name.split(" ")[1]?y?.name.split(" ")[1][0]:""}`,type:y?.type}:2===L?{name:P?.name,letters:`${P?.name.split(" ")[0][0]}${P?.name?.split(" ")[1]?P?.name?.split(" ")[1][0]:""}`,type:P?.type}:3===L?{name:q?.name,letters:`${q?.name.split(" ")[0][0]}${q?.name.split(" ")[1]?q?.name.split(" ")[1][0]:""}`,type:q?.type}:void 0}return r.jsx("div",{children:(0,r.jsxs)(f.yo,{open:Z,onOpenChange:C,children:[r.jsx(a.Z,{}),r.jsx(i.Z,{children:(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:r.jsx("h1",{className:"m-auto font-bold text-2xl",children:"Usu\xe1rios"})}),(y||P)&&r.jsx("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border text-white",children:(0,r.jsxs)("div",{className:"flex w-full",children:[r.jsx("div",{className:"w-[25px] h-[25px] bg-white rounded-full flex items-center justify-center",children:r.jsx("p",{className:"text-[#FF9900] text-xs font-bold",children:returnName()?.letters})}),r.jsx("div",{className:"w-full",children:(0,r.jsxs)("div",{children:[r.jsx("p",{className:"ml-3 text-base",children:returnName()?.name}),r.jsx("p",{className:"ml-3 text-sm",children:returnName()?.type})]})}),r.jsx("div",{className:"cursor-pointer",onClick:()=>{"superadmin"===X.name?V(0):V(1),_(void 0),k(void 0)},children:r.jsx(m.Z,{width:20})})]})}),renderTable(t,!1),r.jsx(f.ue,{className:"w-full md:w-[500px]",children:Z?"investor"===S?r.jsx(x.Z,{investor:G,setModal:C}):z&&r.jsx(j.Z,{typeModal:S,broker:z,setModal:C}):r.jsx(r.Fragment,{})}),F&&r.jsx("div",{className:"z-30 fixed top-0 left-0 w-screen min-h-screen bg-[#1c1c1c71]",children:r.jsx("div",{className:"z-40 w-8/12 bg-[#181818] fixed top-1/2 right-1/2 translate-x-1/2 translate-y-[-50%] border border-[#FF9900] p-10 text-white overflow-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-y-3 relative",children:[r.jsx("p",{className:"text-2xl",children:"Usu\xe1rios"}),r.jsx("p",{className:"absolute right-0 cursor-pointer",onClick:()=>E(!1),children:"x"}),renderTable([],!0)]})})})]})})]})})}},27727:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>o,__esModule:()=>i,default:()=>d});var r=t(17536);let a=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\usuarios\page.tsx`),{__esModule:i,$$typeof:o}=a,n=a.default,d=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[4103,6426,4731,8813,5081,8394,6558,1808,5459,7207,278,7669,8109,6774,6440],()=>__webpack_exec__(24202));module.exports=t})();