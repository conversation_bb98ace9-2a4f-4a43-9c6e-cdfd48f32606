(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{2480:function(){},95:function(e,t,r){Promise.resolve().then(r.bind(r,8329))},8329:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return RootLayout}});var n=r(7437),o=r(9175),s=r.n(o);let AuthModel=class AuthModel{static fromJson(e){var t,r;let n=new AuthModel;return n.accessToken=null==e?void 0:null===(r=e.data)||void 0===r?void 0:null===(t=r.AuthenticationResult)||void 0===t?void 0:t.AccessToken,n}};var a=r(2265),i=r(4734),u=r(4033),c=r(3014),l=r(4568),d=r(6654),f=r(9588),provider_AuthProvider=e=>{let{children:t}=e,[r,o]=(0,a.useState)(AuthModel.fromJson({})),[s,m]=(0,a.useState)(),[h,v]=(0,a.useState)(!1),{push:p}=(0,u.useRouter)(),getAccount=async()=>{try{var e;let t=await l.Z.get("account/profile");m(t.data);let r=t.data.roles.filter(e=>"investor"!==e.name);if(0===r.length)return c.Am.error("Erro ao retornar as permiss\xf5es de usu\xe1rio"),!1;let n=r.filter(e=>"superadmin"===e.name);n.length>0?(0,f.jE)(n[0]):(0,f.jE)(r[0]);let o={id:t.data.id,name:t.data.name,document:null===(e=t.data)||void 0===e?void 0:e.document,roles:[...r]};return(0,f.Tl)(o),(0,f.zO)(),!0}catch(e){return(0,d.Z)(e,"N\xe3o conseguimos pegar os dados do usu\xe1rio informado!"),!1}},handleSignInUser=e=>{let{setLoading:t,password:r,document:n}=e;t(!0),l.Z.post("/auth-backoffice/login",{document:n,password:r}).then(async e=>{var r,n,o;(0,l.o)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.accessToken);let s=await getAccount();s?(t(!1),sessionStorage.setItem("isAuthenticated","true"),sessionStorage.setItem("token",null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.accessToken),sessionStorage.setItem("refreshToken",null==e?void 0:null===(o=e.data)||void 0===o?void 0:o.refreshToken),(0,f.zO)(),p("/home")):t(!1)}).catch(e=>{var r,n,o;c.Am.error((null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(n=o.data)||void 0===n?void 0:null===(r=n.error)||void 0===r?void 0:r.message)||"Usu\xe1rio ou senha incorreto!"),t(!1)})},g=(0,a.useMemo)(()=>({handleSignInUser,auth:r,setAuth:o,user:s,setUser:m,notificationModal:h,setNotificationModal:v}),[handleSignInUser,r,o,s,m,h,v]);return(0,n.jsx)(i.Z.Provider,{value:g,children:t})};r(3054),r(4193);let m={admin:["/home","/contratos","/cadastro-manual","/monitoramento","/rendimentos","/usuarios","/metas"],superadmin:["/home","/contratos","/cadastro-manual","/monitoramento","/rendimentos","/usuarios","/metas","/pagamentos-previstos","/financeiro","/financeiro/pagamentos","/informe-rendimentos"],broker:["/home","/investidores","/meus-contratos","/metas","/cadastro-manual","/financeiro","/informe-rendimentos"],advisor:["/home","/contratos","/investidores","/meus-contratos","/metas","/informe-rendimentos"],investor:["/home","/meus-contratos","/movimentacoes","/metas"],retention:["/home","/retention","/contratos","/usuarios"],financial:["/home","/pagamentos-previstos"],public:{login:"/",registro:"/registro"}},checkIsPublicRoute=e=>{let t=Object.values(m.public);return t.includes(e)};var h=r(9891),v=r(3256),p=r(8038),components_PrivateRoute=e=>{let{children:t}=e,r=(0,u.usePathname)(),{push:o}=(0,u.useRouter)(),[s,i]=(0,a.useState)(null),[d,g]=(0,a.useState)(!1),[y,S]=(0,a.useState)(!1),E=(0,p.NL)();(0,a.useEffect)(()=>{(0,f.Uh)()},[]);let syncAuthFromSession=()=>{let e=sessionStorage.getItem("token");e?(l.Z.defaults.headers.common.Authorization="Bearer ".concat(e),i(e),g(!0)):(sessionStorage.clear(),i(null),g(!1),o(m.public.login))};(0,a.useEffect)(()=>{syncAuthFromSession()},[o]),(0,a.useEffect)(()=>{let handleStorageChange=()=>{syncAuthFromSession(),S(!1)};return window.addEventListener("storage",handleStorageChange),()=>{window.removeEventListener("storage",handleStorageChange)}},[]),(0,a.useEffect)(()=>{S(!1)},[s]),(0,a.useEffect)(()=>{s&&E.removeQueries({queryKey:["user"]})},[s]);let{data:A,isLoading:x,isError:P}=(0,h.a)({queryKey:["user",s],queryFn:v.e,enabled:d&&!!s,retry:!1,staleTime:3e5});return((0,a.useEffect)(()=>{if(d&&!x){if(!s||P){sessionStorage.clear(),i(null),g(!1),S(!1),o(m.public.login);return}if(A&&"string"==typeof A.name){let e="/".concat(r.split("/")[1]),t=m[A.name];(null==t?void 0:t.includes(e))?S(!0):(c.Am.warning("Voc\xea n\xe3o tem permiss\xe3o para acessar essa p\xe1gina.",{toastId:"not_permission_warn"}),S(!1),o("/home"))}}},[d,x,P,s,r,A,o]),y)?(0,n.jsx)("div",{style:{opacity:y?1:0,transition:"opacity 0.2s ease-in-out"},children:t}):(0,n.jsx)("div",{style:{display:"none"}})},g=r(8908);let ReactQueryClientProvider=e=>{let{children:t}=e,[r]=(0,a.useState)(()=>new g.S({defaultOptions:{queries:{staleTime:6e4}}}));return(0,n.jsx)(p.aH,{client:r,children:t})};function RootLayout(e){let{children:t}=e,r=(0,u.usePathname)(),o=checkIsPublicRoute("/".concat(r.split("/")[1]));return(0,n.jsxs)("html",{lang:"pt-br",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("title",{children:"Ica Invest Contracts"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,n.jsx)("body",{className:s().variable,children:(0,n.jsxs)(ReactQueryClientProvider,{children:[(0,n.jsx)(c.Ix,{theme:"light"}),(0,n.jsxs)(provider_AuthProvider,{children:[o&&t,!o&&(0,n.jsx)(components_PrivateRoute,{children:t})]})]})})]})}},4568:function(e,t,r){"use strict";r.d(t,{o:function(){return setToken}});var n=r(3256),o=r(4829),s=r(3014);let a=o.Z.create({baseURL:"http://localhost:3001"}),setToken=e=>{a.defaults.headers.common.Authorization="Bearer ".concat(e)},i=!1,u=[],processQueue=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;u.forEach(r=>{t?r.resolve(t):r.reject(e)}),u=[]};a.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!r._retry){let t=(0,n.P)(),c=sessionStorage.getItem("refreshToken");if(!c)return s.Am.error("Sess\xe3o expirada. Fa\xe7a login novamente!"),sessionStorage.clear(),window.location.href="/",Promise.reject(e);if(r._retry=!0,i)return new Promise(function(e,t){u.push({resolve:e,reject:t})}).then(e=>(r.headers.Authorization="Bearer ".concat(e),(0,o.Z)(r))).catch(e=>Promise.reject(e));i=!0;try{let{data:e}=await a.post("/auth-backoffice/login",{document:t.document,refreshToken:c}),n=e.accessToken;return setToken(n),sessionStorage.setItem("token",n),processQueue(null,n),r.headers.Authorization="Bearer ".concat(n),(0,o.Z)(r)}catch(e){s.Am.error("Erro ao validar token, fa\xe7a login novamente!"),setTimeout(()=>(processQueue(e,null),sessionStorage.clear(),window.location.href="/",Promise.reject(e)),1500)}finally{i=!1}}return Promise.reject(e)}),t.Z=a},3256:function(e,t,r){"use strict";r.d(t,{P:function(){return getUser},e:function(){return getUserProfile}});var n=r(9588);function getUserProfile(){(0,n.Uh)();let e=(0,n.aA)();return e||(console.warn("Perfil n\xe3o encontrado, usando padr\xe3o"),{name:"investor",roleId:""})}function getUser(){(0,n.Uh)();let e=(0,n.mo)();return e||{name:"investor",id:"",document:"",roles:[]}}},6654:function(e,t,r){"use strict";r.d(t,{Z:function(){return returnError}});var n=r(3014);function returnError(e,t){var r,o,s,a;let i=(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(r=o.data)||void 0===r?void 0:r.message)||(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.error);if(Array.isArray(i))return i.forEach(e=>{n.Am.error(e,{toastId:e})}),i.join("\n");if("string"==typeof i)return n.Am.error(i,{toastId:i}),i;if("object"==typeof i&&null!==i){let e=Object.values(i).flat().join("\n");return n.Am.error(e,{toastId:e}),e}return n.Am.error(t,{toastId:t}),t}},9588:function(e,t,r){"use strict";r.d(t,{mo:function(){return getDecryptedUser},aA:function(){return getDecryptedUserProfile},zO:function(){return persistSessionData},Uh:function(){return restoreSessionData},Tl:function(){return setEncryptedUser},jE:function(){return setEncryptedUserProfile}});var n=r(4155),o=r.n(n);let s="f5beb0e0-8b97-4440-9ef2-cd6b2557345a";if(!s)throw Error("Chave de criptografia n\xe3o definida em NEXT_PUBLIC_CRYPTO_SECRET_KEY");function encryptData(e){let t=JSON.stringify(e);return o().AES.encrypt(t,s).toString()}function decryptData(e){try{let t=o().AES.decrypt(e,s),r=t.toString(o().enc.Utf8);return JSON.parse(r)}catch(e){return console.error("Erro ao descriptografar dados:",e),null}}var a=r(2601);let i=null,u=null,c=a.env.NEXT_PUBLIC_MEMORY_KEY||"secure-memory-key",l="session_token";function setEncryptedUser(e){let t=encryptData(e);i=o().AES.encrypt(t,c).toString()}function getDecryptedUser(){if(!i)return null;try{let e=o().AES.decrypt(i,c).toString(o().enc.Utf8);return decryptData(e)}catch(e){return console.error("Erro ao descriptografar usu\xe1rio:",e),null}}function setEncryptedUserProfile(e){let t=encryptData(e);u=o().AES.encrypt(t,c).toString()}function getDecryptedUserProfile(){if(!u)return null;try{let e=o().AES.decrypt(u,c).toString(o().enc.Utf8);return decryptData(e)}catch(e){return console.error("Erro ao descriptografar perfil de usu\xe1rio:",e),null}}function persistSessionData(){if(!i||!u)return;let e={user:i,profile:u},t=o().AES.encrypt(JSON.stringify(e),c).toString();sessionStorage.setItem(l,t)}function restoreSessionData(){let e=sessionStorage.getItem(l);if(!e)return!1;try{let t=o().AES.decrypt(e,c).toString(o().enc.Utf8),r=JSON.parse(t);return i=r.user,u=r.profile,!0}catch(e){return console.error("Erro ao restaurar dados da sess\xe3o:",e),!1}}},4734:function(e,t,r){"use strict";var n=r(2265);let o=n.createContext({});t.Z=o},3054:function(){}},function(e){e.O(0,[8276,9891,9153,2971,7864,1744],function(){return e(e.s=95)}),_N_E=e.O()}]);