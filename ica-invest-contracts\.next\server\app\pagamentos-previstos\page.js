(()=>{var e={};e.id=9440,e.ids=[9440],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},71983:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>u,routeModule:()=>x,tree:()=>d});var r=a(73137),s=a(54647),n=a(4183),l=a.n(n),o=a(71775),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);a.d(t,i);let c=r.AppPageRouteModule,d=["",{children:["pagamentos-previstos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94981)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\pagamentos-previstos\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\pagamentos-previstos\\page.tsx"],m="/pagamentos-previstos/page",p={require:a,loadChunk:()=>Promise.resolve()},x=new c({definition:{kind:s.x.APP_PAGE,page:"/pagamentos-previstos/page",pathname:"/pagamentos-previstos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},56121:(e,t,a)=>{Promise.resolve().then(a.bind(a,45515))},45515:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>PagamentosPrevistos});var r=a(60080),s=a(74352),n=a(97669),l=a(30170),o=a(47956),i=a(48764),c=a(26147),d=a(85814),u=a(24577),m=a(64731),p=a.n(m),x=a(9885);a(73079);var Y=a(73294),h=a(96413),v=a(34751);let f=[{label:"Janeiro",value:"1"},{label:"Fevereiro",value:"2"},{label:"Mar\xe7o",value:"3"},{label:"Abril",value:"4"},{label:"Maio",value:"5"},{label:"Junho",value:"6"},{label:"Julho",value:"7"},{label:"Agosto",value:"8"},{label:"Setembro",value:"9"},{label:"Outubro",value:"10"},{label:"Novembro",value:"11"},{label:"Dezembro",value:"12"}];var g=a(90682),M=a(80223),b=a(69957);function PagamentosPrevistos(){p().locale("pt-br");let[e,t]=(0,x.useState)(1),[a,m]=(0,x.useState)(!1),[j,y]=(0,x.useState)(""),[P,D]=(0,x.useState)(0),[N,w]=(0,x.useState)(p()().toString()),[S,_]=(0,x.useState)(String(p()().month()+1)),[q,$]=(0,x.useState)(String(p()().year())),[O,A]=(0,x.useState)(p()().startOf("month").format("YYYY-MM-DD")),[C,Z]=(0,x.useState)(p()().endOf("month").format("YYYY-MM-DD")),[k,E]=(0,x.useState)(!1),[R,F]=(0,x.useState)(!1),[L,V]=(0,x.useState)(),[B,z]=(0,x.useState)({total:0,lastPage:1,perPage:0}),I=(0,g.e)(),getPayments=()=>{E(!0),d.Z.get("income-payment/simulation",{params:{paymentStart:O||p()().startOf("month").format("YYYY-MM-DD"),paymentEnd:C||O,page:e,perPage:10,document:""!==j?(0,h.p4)(j):void 0}}).then(e=>{V(e.data.data),D(e.data.totalYieldSum),z({total:e.data.total,lastPage:e.data.lastPage,perPage:e.data.limit}),t(e.data.page)}).catch(e=>{(0,u.Z)(e,"Erro ao buscar pagamentos previstos")}).finally(()=>E(!1))};return(0,x.useEffect)(()=>{getPayments()},[e,S,q,O,C]),(0,r.jsxs)("div",{children:[r.jsx(n.Z,{}),r.jsx(o.Z,{children:(0,r.jsxs)("div",{className:"text-white",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[r.jsx("h1",{className:"text-white text-center text-2xl",children:"Pagamentos Previstos"}),(0,r.jsxs)("div",{className:"border-[#FF9900] border rounded-2xl p-6 mt-5 text-center",children:[r.jsx("p",{className:"text-sm mb-2",children:"Total de Pagamentos"}),r.jsx("p",{className:"text-3xl font-bold",children:(0,Y.Z)(P)})]}),r.jsx("div",{children:r.jsx("div",{className:"border-[#FF9900] border rounded-2xl p-4 mt-5 text-center",children:(0,r.jsxs)("p",{className:"text-sm capitalize",children:[p()(N).format("MMMM")," / ",p()(N).format("YYYY")]})})})]}),(0,r.jsxs)(c.Z,{children:[(0,r.jsxs)("div",{className:"w-full p-2 flex justify-end gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-1 w-full justify-between items-center gap-4",children:[r.jsx("div",{className:"md:w-4/12",children:r.jsx(l.Z,{handleSearch:getPayments,placeholder:"Pesquisar por CPF/CNPJ",setValue:e=>{(0,h.p4)(e).length<=11?y((0,h.VL)(e)):y((0,h.PK)(e))},value:j})}),r.jsx("div",{children:r.jsx(b.z,{className:"bg-orange-linear",loading:R,size:"sm",onClick:()=>{F(!0),d.Z.post("income-payment/simulation/report",{},{params:{ownerRoleId:I.roleId,paymentStart:O,paymentEnd:C,document:""!==j?(0,h.p4)(j):void 0}}).then(e=>{if(""===e.data.url)return v.Am.warning("N\xe3o foram encontrados dados dispon\xedveis para a gera\xe7\xe3o do relat\xf3rio.");window.open(e.data.url,"_blanck")}).catch(e=>{(0,u.Z)(e,"Tivemos um erro ao gerar o relat\xf3rio")}).finally(()=>F(!1))},children:"Relat\xf3rio"})})]}),r.jsx(s.Z,{activeModal:a,setActiveModal:m,isNew:!0,hidenButton:!0,handleSearch:()=>{getPayments(),w(`${q}-${1===S.length?`0${S}`:N}`)},children:(0,r.jsxs)("div",{className:"w-[300px]",children:[(0,r.jsxs)("div",{className:"flex justify-between gap-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm mb-1",children:"M\xeas"}),r.jsx(M.Z,{value:S,onChange:({target:e})=>{_(e.value),A(p()(`${q}-${e.value}`,"YYYY-MM").startOf("month").format("YYYY-MM-DD")),Z(p()(`${q}-${e.value}`,"YYYY-MM").endOf("month").format("YYYY-MM-DD"))},className:"w-36 pl-3",children:f.map((e,t)=>r.jsx("option",{value:e.value,children:e.label},t))})]}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm mb-1",children:"Ano"}),r.jsx(M.Z,{value:q,onChange:({target:e})=>{$(e.value),A(p()(`${e.value}-${S}`,"YYYY-MM").startOf("month").format("YYYY-MM-DD")),Z(p()(`${e.value}-${S}`,"YYYY-MM").endOf("month").format("YYYY-MM-DD"))},className:"w-36 pl-3",children:(function({currentYear:e,quantityYears:t}){let a=e-t,r=Array.from({length:e+t-a+1},(e,t)=>{let r=(a+t).toString();return{label:r,value:r}});return r})({currentYear:p()().year(),quantityYears:10}).map((e,t)=>r.jsx("option",{value:e.value,children:e.label},t))})]})]}),""!==S&&(0,r.jsxs)("div",{className:"flex justify-between mt-3 gap-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-xs mb-1",children:"Data Inicial"}),r.jsx("input",{value:O,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:({target:e})=>{if(p()(e.value).isAfter(C))return v.Am.warning("A data inicial n\xe3o pode ser posterior \xe0 data final!");A(e.value)},type:"date",min:p()(`${q}-${S}`,"YYYY-MM").startOf("month").format("YYYY-MM-DD"),max:p()(`${q}-${S}`,"YYYY-MM").endOf("month").format("YYYY-MM-DD")})]}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-xs mb-1",children:"Data Final"}),r.jsx("input",{value:C,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:({target:e})=>{if(p()(e.value,"YYYY-MM-DD").isBefore(p()(O,"YYYY-MM-DD")))return v.Am.warning("A data final n\xe3o pode ser anterior \xe0 data inicial!");Z(e.value)},type:"date",min:p()(O,"YYYY-MM").startOf("month").format("YYYY-MM-DD"),max:p()(`${q}-${S}`,"YYYY-MM").endOf("month").format("YYYY-MM-DD")})]})]}),r.jsx("div",{className:"my-3",children:!!(O!==p()().startOf("month").format("YYYY-MM-DD")||C!==p()().endOf("month").format("YYYY-MM-DD"))&&r.jsx(b.z,{variant:"secondary",onClick:()=>{m(!1),t(1),A(p()().startOf("month").format("YYYY-MM-DD")),Z(p()().endOf("month").format("YYYY-MM-DD")),_(String(p()().month()+1)),$(String(p()().year()))},className:"w-full",children:"Resetar"})})]})})]}),r.jsx(i.Z,{loading:k,pagination:{lastPage:B.lastPage,page:e,perPage:B.perPage,setPage:t,totalItems:String(B.total)},data:L||[],headers:[{title:"Nome",component:"investor",width:"200px"},{title:"CPF/CNPJ",component:"document",render:e=>r.jsx("p",{children:String(e).length<=11?(0,h.VL)(String(e)):(0,h.PK)(String(e))})},{title:"E-mail",component:"email",width:"250px"},{title:"Rentabilidade",component:"totalYield",render:e=>r.jsx("p",{children:(0,Y.Z)(Number(e))})},{title:"Valor do Aporte",component:"currentInvested",render:e=>r.jsx("p",{children:(0,Y.Z)(Number(e))})},{title:"Data pagamento",component:"paymentDate",render:e=>r.jsx("p",{children:p()(e).format("DD/MM/YYYY")})}]})]})]})})]})}},73294:(e,t,a)=>{"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}a.d(t,{A:()=>cleanValue,Z:()=>formatValue})},94981:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>l,__esModule:()=>n,default:()=>i});var r=a(17536);let s=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\pagamentos-previstos\page.tsx`),{__esModule:n,$$typeof:l}=s,o=s.default,i=o},64918:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var r=a(9885);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}),n=s}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,8394,3079,7207,278,7669,8109,9012],()=>__webpack_exec__(71983));module.exports=a})();