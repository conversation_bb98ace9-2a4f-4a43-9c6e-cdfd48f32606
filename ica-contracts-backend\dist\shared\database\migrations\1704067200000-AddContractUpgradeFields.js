"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddContractUpgradeFields1704067200000 = void 0;
const typeorm_1 = require("typeorm");
class AddContractUpgradeFields1704067200000 {
    async up(queryRunner) {
        await queryRunner.addColumns('contract', [
            new typeorm_1.TableColumn({
                name: 'contract_type',
                type: 'varchar',
                length: '50',
                isNullable: true,
                comment: 'Tipo do contrato: MUTUO ou SCP',
            }),
            new typeorm_1.TableColumn({
                name: 'investment_value',
                type: 'decimal',
                precision: 15,
                scale: 2,
                isNullable: true,
                comment: 'Valor do investimento',
            }),
            new typeorm_1.TableColumn({
                name: 'quota_quantity',
                type: 'int',
                isNullable: true,
                comment: 'Quantidade de cotas para contratos SCP',
            }),
            new typeorm_1.TableColumn({
                name: 'payment_proof_url',
                type: 'varchar',
                length: '500',
                isNullable: true,
                comment: 'URL do comprovante de pagamento',
            }),
            new typeorm_1.TableColumn({
                name: 'payment_proof_uploaded_at',
                type: 'timestamp',
                isNullable: true,
                comment: 'Data de upload do comprovante',
            }),
            new typeorm_1.TableColumn({
                name: 'audit_approved_at',
                type: 'timestamp',
                isNullable: true,
                comment: 'Data de aprovação pela auditoria',
            }),
            new typeorm_1.TableColumn({
                name: 'audit_approved_by',
                type: 'varchar',
                length: '255',
                isNullable: true,
                comment: 'ID do usuário que aprovou',
            }),
            new typeorm_1.TableColumn({
                name: 'audit_rejected_at',
                type: 'timestamp',
                isNullable: true,
                comment: 'Data de rejeição pela auditoria',
            }),
            new typeorm_1.TableColumn({
                name: 'audit_rejected_by',
                type: 'varchar',
                length: '255',
                isNullable: true,
                comment: 'ID do usuário que rejeitou',
            }),
            new typeorm_1.TableColumn({
                name: 'audit_observations',
                type: 'text',
                isNullable: true,
                comment: 'Observações da auditoria',
            }),
            new typeorm_1.TableColumn({
                name: 'observations',
                type: 'text',
                isNullable: true,
                comment: 'Observações gerais do contrato',
            }),
            new typeorm_1.TableColumn({
                name: 'ir_deposito',
                type: 'boolean',
                default: false,
                comment: 'Investidor irá depositar valor referente ao IR',
            }),
            new typeorm_1.TableColumn({
                name: 'ir_desconto',
                type: 'boolean',
                default: false,
                comment: 'Investidor decidiu desconto do IR sobre o valor do contrato',
            }),
            new typeorm_1.TableColumn({
                name: 'valor_complementar',
                type: 'decimal',
                precision: 15,
                scale: 2,
                isNullable: true,
                comment: 'Valor complementar necessário para ajuste de cotas SCP',
            }),
        ]);
        await queryRunner.query(`
      ALTER TABLE contract 
      MODIFY COLUMN status ENUM(
        'aberto',
        'DRAFT',
        'GENERATED',
        'SIGNATURE_SENT',
        'AWAITING_INVESTOR_SIGNATURE',
        'AWAITING_DEPOSIT',
        'AWAITING_PAYMENT_PROOF',
        'AWAITING_AUDIT',
        'AWAITING_AUDIT_SIGNATURE',
        'ACTIVE',
        'SIGNATURE_FAILED',
        'EXPIRED_BY_INVESTOR',
        'EXPIRED_BY_AUDIT',
        'EXPIRED_FAILURE_PROOF_PAYMENT',
        'REJECTED',
        'REJECTED_BY_AUDIT',
        'GENERATE_CONTRACT_FAILED',
        'EXPIRED',
        'DELETED'
      ) DEFAULT 'aberto'
    `);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('contract', [
            'contract_type',
            'investment_value',
            'quota_quantity',
            'payment_proof_url',
            'payment_proof_uploaded_at',
            'audit_approved_at',
            'audit_approved_by',
            'audit_rejected_at',
            'audit_rejected_by',
            'audit_observations',
            'observations',
            'ir_deposito',
            'ir_desconto',
            'valor_complementar',
        ]);
        await queryRunner.query(`
      ALTER TABLE contract 
      MODIFY COLUMN status ENUM(
        'aberto',
        'DRAFT',
        'GENERATED',
        'SIGNATURE_SENT',
        'AWAITING_INVESTOR_SIGNATURE',
        'AWAITING_DEPOSIT',
        'AWAITING_AUDIT',
        'AWAITING_AUDIT_SIGNATURE',
        'ACTIVE',
        'SIGNATURE_FAILED',
        'EXPIRED_BY_INVESTOR',
        'EXPIRED_BY_AUDIT',
        'EXPIRED_FAILURE_PROOF_PAYMENT',
        'REJECTED',
        'REJECTED_BY_AUDIT',
        'GENERATE_CONTRACT_FAILED',
        'EXPIRED',
        'DELETED'
      ) DEFAULT 'aberto'
    `);
    }
}
exports.AddContractUpgradeFields1704067200000 = AddContractUpgradeFields1704067200000;
//# sourceMappingURL=1704067200000-AddContractUpgradeFields.js.map