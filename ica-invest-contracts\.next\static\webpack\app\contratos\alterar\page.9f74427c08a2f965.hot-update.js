"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-multiple\", \"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue > 0 && numericValue % 5000 === 0;\n        }\n        return true;\n    }).test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (mutuamente exclusivos)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento) {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const cotas = Math.floor(valorNumerico / 5000);\n            setValue(\"quotaQuantity\", cotas.toString());\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto usando a estrutura do CreateContractDto\n            const requestData = {\n                name: data.nomeCompleto,\n                rg: data.identidade,\n                phoneNumber: (()=>{\n                    const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                    console.log(\"Telefone original:\", data.celular);\n                    console.log(\"Telefone limpo:\", cleanPhone);\n                    // Garantir que tenha 13 dígitos (55 + DDD + número)\n                    if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                        const phoneWithCountry = \"55\" + cleanPhone;\n                        console.log(\"Telefone com c\\xf3digo do pa\\xeds:\", phoneWithCountry);\n                        return phoneWithCountry;\n                    }\n                    return cleanPhone;\n                })(),\n                motherName: data.nomeMae || \"\",\n                dtBirth: (()=>{\n                    // Converter data para formato ISO 8601\n                    const birthDate = data.dataNascimento;\n                    console.log(\"Data nascimento original:\", birthDate);\n                    if (!birthDate) return new Date().toISOString();\n                    // Se já está no formato YYYY-MM-DD, adicionar horário\n                    if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                        const isoDate = birthDate + \"T00:00:00.000Z\";\n                        console.log(\"Data nascimento ISO:\", isoDate);\n                        return isoDate;\n                    }\n                    // Se está em outro formato, tentar converter\n                    const date = new Date(birthDate);\n                    const isoDate = date.toISOString();\n                    console.log(\"Data nascimento convertida:\", isoDate);\n                    return isoDate;\n                })(),\n                address: {\n                    zipCode: data.cep.replace(/\\D/g, \"\"),\n                    neighborhood: \"Centro\",\n                    state: data.estado || \"\",\n                    city: data.cidade,\n                    complement: data.complemento || \"\",\n                    number: data.numero\n                },\n                accountBank: {\n                    bank: data.banco,\n                    accountNumber: data.conta,\n                    agency: data.agencia,\n                    pix: data.chavePix\n                },\n                document: documento,\n                contractType: data.modalidade,\n                observations: \"\",\n                placeOfBirth: data.cidade || \"\",\n                occupation: \"Investidor\",\n                documentType: isPJ ? \"CNPJ\" : \"CPF\",\n                issuer: \"SSP\",\n                quota: data.modalidade === \"SCP\" ? parseInt(data.quotaQuantity || \"0\") : 0,\n                paymentPercentage: parseFloat(data.taxaRemuneracao) || 0,\n                parValue: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0\n            };\n            console.log(\"Enviando dados para API...\", requestData);\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                const valorComplementarNecessario = 5000 - resto;\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Remove tudo que não for número ou vírgula\n        const limpo = valor.replace(/[^0-9,]/g, \"\").replace(\",\", \".\");\n        return parseFloat(limpo) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 761,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 784,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 848,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 857,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 907,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 905,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 763,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 762,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 930,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 956,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 966,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 954,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 931,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 978,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 979,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 992,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 990,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 760,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1021,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1022,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message,\n                                        label: \"Valor do Investimento\",\n                                        placeholder: \"ex: R$ 50.000,00\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1038,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1037,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: calcularAliquotaIR,\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1052,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1006,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1059,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1076,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1080,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1074,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1073,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1086,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1098,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1105,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1106,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1094,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1118,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1124,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1125,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1123,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1084,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1072,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1069,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1148,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1161,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1160,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1159,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDeposito\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDeposito\", true);\n                                                    setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDeposito\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1171,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDesconto\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDesconto\", true);\n                                                    setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDesconto\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1187,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1169,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: \"Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1215,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1207,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1005,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1222,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1229,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1242,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1264,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1283,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1277,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1307,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1301,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1325,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1326,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1321,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1319,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1288,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1224,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1223,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1333,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting,\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1342,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1332,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1001,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1370,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1375,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1376,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1374,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1373,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1372,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1371,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1387,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1395,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1392,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1391,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1407,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1406,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1390,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1389,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1388,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"zi/CXSEPwkBmT5MW8EVnCE5yTH0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});