"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractStatusEnum = void 0;
var ContractStatusEnum;
(function (ContractStatusEnum) {
    ContractStatusEnum["ABERTO"] = "aberto";
    ContractStatusEnum["DRAFT"] = "DRAFT";
    ContractStatusEnum["GENERATED"] = "GENERATED";
    ContractStatusEnum["SIGNATURE_SENT"] = "SIGNATURE_SENT";
    ContractStatusEnum["AWAITING_INVESTOR_SIGNATURE"] = "AWAITING_INVESTOR_SIGNATURE";
    ContractStatusEnum["AWAITING_DEPOSIT"] = "AWAITING_DEPOSIT";
    ContractStatusEnum["AWAITING_AUDIT"] = "AWAITING_AUDIT";
    ContractStatusEnum["AWAITING_AUDIT_SIGNATURE"] = "AWAITING_AUDIT_SIGNATURE";
    ContractStatusEnum["ACTIVE"] = "ACTIVE";
    ContractStatusEnum["SIGNATURE_FAILED"] = "SIGNATURE_FAILED";
    ContractStatusEnum["EXPIRED_BY_INVESTOR"] = "EXPIRED_BY_INVESTOR";
    ContractStatusEnum["EXPIRED_BY_AUDIT"] = "EXPIRED_BY_AUDIT";
    ContractStatusEnum["EXPIRED_FAILURE_PROOF_PAYMENT"] = "EXPIRED_FAILURE_PROOF_PAYMENT";
    ContractStatusEnum["REJECTED"] = "REJECTED";
    ContractStatusEnum["REJECTED_BY_AUDIT"] = "REJECTED_BY_AUDIT";
    ContractStatusEnum["GENERATE_CONTRACT_FAILED"] = "GENERATE_CONTRACT_FAILED";
    ContractStatusEnum["EXPIRED"] = "EXPIRED";
    ContractStatusEnum["DELETED"] = "DELETED";
})(ContractStatusEnum || (exports.ContractStatusEnum = ContractStatusEnum = {}));
//# sourceMappingURL=contract-status.enum.js.map