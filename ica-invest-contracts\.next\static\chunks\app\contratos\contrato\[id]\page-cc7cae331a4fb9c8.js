(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1148],{4279:function(e,t,n){(e.exports=n(1223)).tz.load(n(6564))},1223:function(e,t,n){var o,s,i;i=function(e){"use strict";void 0===e.version&&e.default&&(e=e.default);var t,n,o={},s={},i={},l={},a={};e&&"string"==typeof e.version||logError("Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/");var r=e.version.split("."),d=+r[0],c=+r[1];function charCodeToInt(e){return e>96?e-87:e>64?e-29:e-48}function unpackBase60(e){var t,n=0,o=e.split("."),s=o[0],i=o[1]||"",l=1,a=0,r=1;for(45===e.charCodeAt(0)&&(n=1,r=-1);n<s.length;n++)a=60*a+(t=charCodeToInt(s.charCodeAt(n)));for(n=0;n<i.length;n++)l/=60,a+=(t=charCodeToInt(i.charCodeAt(n)))*l;return a*r}function arrayToInt(e){for(var t=0;t<e.length;t++)e[t]=unpackBase60(e[t])}function mapIndices(e,t){var n,o=[];for(n=0;n<t.length;n++)o[n]=e[t[n]];return o}function unpack(e){var t=e.split("|"),n=t[2].split(" "),o=t[3].split(""),s=t[4].split(" ");return arrayToInt(n),arrayToInt(o),arrayToInt(s),function(e,t){for(var n=0;n<t;n++)e[n]=Math.round((e[n-1]||0)+6e4*e[n]);e[t-1]=1/0}(s,o.length),{name:t[0],abbrs:mapIndices(t[1].split(" "),o),offsets:mapIndices(n,o),untils:s,population:0|t[5]}}function Zone(e){e&&this._set(unpack(e))}function Country(e,t){this.name=e,this.zones=t}function OffsetAt(e){var t=e.toTimeString(),n=t.match(/\([a-z ]+\)/i);"GMT"===(n=n&&n[0]?(n=n[0].match(/[A-Z]/g))?n.join(""):void 0:(n=t.match(/[A-Z]{3,5}/g))?n[0]:void 0)&&(n=void 0),this.at=+e,this.abbr=n,this.offset=e.getTimezoneOffset()}function ZoneScore(e){this.zone=e,this.offsetScore=0,this.abbrScore=0}function sortZoneScores(e,t){return e.offsetScore!==t.offsetScore?e.offsetScore-t.offsetScore:e.abbrScore!==t.abbrScore?e.abbrScore-t.abbrScore:e.zone.population!==t.zone.population?t.zone.population-e.zone.population:t.zone.name.localeCompare(e.zone.name)}function normalizeName(e){return(e||"").toLowerCase().replace(/\//g,"_")}function addZone(e){var t,n,s,i;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)o[i=normalizeName(n=(s=e[t].split("|"))[0])]=e[t],l[i]=n,function(e,t){var n,o;for(arrayToInt(t),n=0;n<t.length;n++)a[o=t[n]]=a[o]||{},a[o][e]=!0}(i,s[2].split(" "))}function getZone(e,t){var n,i=o[e=normalizeName(e)];return i instanceof Zone?i:"string"==typeof i?(i=new Zone(i),o[e]=i,i):s[e]&&t!==getZone&&(n=getZone(s[e],getZone))?((i=o[e]=new Zone)._set(n),i.name=l[e],i):null}function addLink(e){var t,n,o,i;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)o=normalizeName((n=e[t].split("|"))[0]),i=normalizeName(n[1]),s[o]=i,l[o]=n[0],s[i]=o,l[i]=n[1]}function zoneExists(e){return zoneExists.didShowError||(zoneExists.didShowError=!0,logError("moment.tz.zoneExists('"+e+"') has been deprecated in favor of !moment.tz.zone('"+e+"')")),!!getZone(e)}function needsOffset(e){var t="X"===e._f||"x"===e._f;return!!(e._a&&void 0===e._tzm&&!t)}function logError(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}function tz(t){var n,o=Array.prototype.slice.call(arguments,0,-1),s=arguments[arguments.length-1],i=e.utc.apply(null,o);return!e.isMoment(t)&&needsOffset(i)&&(n=getZone(s))&&i.add(n.parse(i),"minutes"),i.tz(s),i}(d<2||2===d&&c<6)&&logError("Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js "+e.version+". See momentjs.com"),Zone.prototype={_set:function(e){this.name=e.name,this.abbrs=e.abbrs,this.untils=e.untils,this.offsets=e.offsets,this.population=e.population},_index:function(e){var t;if((t=function(e,t){var n,o=t.length;if(e<t[0])return 0;if(o>1&&t[o-1]===1/0&&e>=t[o-2])return o-1;if(e>=t[o-1])return -1;for(var s=0,i=o-1;i-s>1;)t[n=Math.floor((s+i)/2)]<=e?s=n:i=n;return i}(+e,this.untils))>=0)return t},countries:function(){var e=this.name;return Object.keys(i).filter(function(t){return -1!==i[t].zones.indexOf(e)})},parse:function(e){var t,n,o,s,i=+e,l=this.offsets,a=this.untils,r=a.length-1;for(s=0;s<r;s++)if(t=l[s],n=l[s+1],o=l[s?s-1:s],t<n&&tz.moveAmbiguousForward?t=n:t>o&&tz.moveInvalidForward&&(t=o),i<a[s]-6e4*t)return l[s];return l[r]},abbr:function(e){return this.abbrs[this._index(e)]},offset:function(e){return logError("zone.offset has been deprecated in favor of zone.utcOffset"),this.offsets[this._index(e)]},utcOffset:function(e){return this.offsets[this._index(e)]}},ZoneScore.prototype.scoreOffsetAt=function(e){this.offsetScore+=Math.abs(this.zone.utcOffset(e.at)-e.offset),this.zone.abbr(e.at).replace(/[^A-Z]/g,"")!==e.abbr&&this.abbrScore++},tz.version="0.5.48",tz.dataVersion="",tz._zones=o,tz._links=s,tz._names=l,tz._countries=i,tz.add=addZone,tz.link=addLink,tz.load=function(e){addZone(e.zones),addLink(e.links),function(e){var t,n,o,s;if(e&&e.length)for(t=0;t<e.length;t++)n=(s=e[t].split("|"))[0].toUpperCase(),o=s[1].split(" "),i[n]=new Country(n,o)}(e.countries),tz.dataVersion=e.version},tz.zone=getZone,tz.zoneExists=zoneExists,tz.guess=function(e){return(!n||e)&&(n=function(){try{var e=Intl.DateTimeFormat().resolvedOptions().timeZone;if(e&&e.length>3){var t=l[normalizeName(e)];if(t)return t;logError("Moment Timezone found "+e+" from the Intl api, but did not have that data loaded.")}}catch(e){}var n,o,s,i=function(){var e,t,n,o,s=new Date().getFullYear()-2,i=new OffsetAt(new Date(s,0,1)),l=i.offset,a=[i];for(o=1;o<48;o++)(n=new Date(s,o,1).getTimezoneOffset())!==l&&(a.push(e=function(e,t){for(var n,o;o=((t.at-e.at)/12e4|0)*6e4;)(n=new OffsetAt(new Date(e.at+o))).offset===e.offset?e=n:t=n;return e}(i,t=new OffsetAt(new Date(s,o,1)))),a.push(new OffsetAt(new Date(e.at+6e4))),i=t,l=n);for(o=0;o<4;o++)a.push(new OffsetAt(new Date(s+o,0,1))),a.push(new OffsetAt(new Date(s+o,6,1)));return a}(),r=i.length,d=function(e){var t,n,o,s,i=e.length,r={},d=[],c={};for(t=0;t<i;t++)if(o=e[t].offset,!c.hasOwnProperty(o)){for(n in s=a[o]||{})s.hasOwnProperty(n)&&(r[n]=!0);c[o]=!0}for(t in r)r.hasOwnProperty(t)&&d.push(l[t]);return d}(i),c=[];for(o=0;o<d.length;o++){for(s=0,n=new ZoneScore(getZone(d[o]),r);s<r;s++)n.scoreOffsetAt(i[s]);c.push(n)}return c.sort(sortZoneScores),c.length>0?c[0].zone.name:void 0}()),n},tz.names=function(){var e,t=[];for(e in l)l.hasOwnProperty(e)&&(o[e]||o[s[e]])&&l[e]&&t.push(l[e]);return t.sort()},tz.Zone=Zone,tz.unpack=unpack,tz.unpackBase60=unpackBase60,tz.needsOffset=needsOffset,tz.moveInvalidForward=!0,tz.moveAmbiguousForward=!1,tz.countries=function(){return Object.keys(i)},tz.zonesForCountry=function(e,t){if(!(e=i[e.toUpperCase()]||null))return null;var n=e.zones.sort();return t?n.map(function(e){var t=getZone(e);return{name:e,offset:t.utcOffset(new Date)}}):n};var u=e.fn;function abbrWrap(e){return function(){return this._z?this._z.abbr(this):e.call(this)}}function resetZoneWrap(e){return function(){return this._z=null,e.apply(this,arguments)}}e.tz=tz,e.defaultZone=null,e.updateOffset=function(t,n){var o,s=e.defaultZone;if(void 0===t._z&&(s&&needsOffset(t)&&!t._isUTC&&t.isValid()&&(t._d=e.utc(t._a)._d,t.utc().add(s.parse(t),"minutes")),t._z=s),t._z){if(16>Math.abs(o=t._z.utcOffset(t))&&(o/=60),void 0!==t.utcOffset){var i=t._z;t.utcOffset(-o,n),t._z=i}else t.zone(o,n)}},u.tz=function(t,n){if(t){if("string"!=typeof t)throw Error("Time zone name must be a string, got "+t+" ["+typeof t+"]");return this._z=getZone(t),this._z?e.updateOffset(this,n):logError("Moment Timezone has no data for "+t+". See http://momentjs.com/timezone/docs/#/data-loading/."),this}if(this._z)return this._z.name},u.zoneName=abbrWrap(u.zoneName),u.zoneAbbr=abbrWrap(u.zoneAbbr),u.utc=resetZoneWrap(u.utc),u.local=resetZoneWrap(u.local),u.utcOffset=(t=u.utcOffset,function(){return arguments.length>0&&(this._z=null),t.apply(this,arguments)}),e.tz.setDefault=function(t){return(d<2||2===d&&c<9)&&logError("Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js "+e.version+"."),e.defaultZone=t?getZone(t):null,e};var f=e.momentProperties;return"[object Array]"===Object.prototype.toString.call(f)?(f.push("_z"),f.push("_a")):f&&(f._z=null),e},e.exports?e.exports=i(n(2067)):(o=[n(2067)],void 0===(s=i.apply(t,o))||(e.exports=s))},4628:function(e,t,n){Promise.resolve().then(n.bind(n,5843))},5843:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return CreateInvestor}});var o=n(7437),s=n(2265),i=n(3877),l=n(8637),a=n(5233),r=n(2875),d=n(5968),c=n(4568),u=n(6654),f=n(3014),p=n(2067),x=n.n(p),m=n(4984),h=n(4033),v=n(8610);function InputSelectText(e){let{label:t,width:n="auto",name:i,placeholder:l="",fieldsReasons:a,setFieldReason:r,setFieldText:d}=e,c=null==a?void 0:a.find(e=>e.field===i),[u,f]=(0,s.useState)("");return(0,s.useEffect)(()=>{(null==c?void 0:c.reason)!==void 0&&f(c.reason)},[null==c?void 0:c.reason]),(0,o.jsxs)("div",{className:"input",style:{width:n},children:[(0,o.jsx)("p",{className:"text-white mb-1 text-sm",children:t}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{onClick:()=>{r&&((null==c?void 0:c.field)?r({field:"",reason:""},i):r({field:i,reason:""},i))},className:"py-3 min-h-[48px] w-full select-none cursor-pointer px-4 text-white rounded-xl ".concat((null==c?void 0:c.field)?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),children:l}),(null==c?void 0:c.field)&&(0,o.jsx)("input",{type:"text",onChange:e=>{let{target:t}=e;f(t.value)},onBlur:()=>{d&&d({filter:i,text:u})},value:u,placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})]})]})}n(7881);var R=n(1458);let b={pix:"PIX",boleto:"Boleto",bank_transfer:"Transfer\xeancia Banc\xe1ria"};var T=n(8700);function PhysicalContract(e){var t;let{modalityContract:n,contractData:i}=e,[l,p]=(0,s.useState)(!1),{id:j}=(0,h.useParams)(),[F,g]=(0,s.useState)(),[w,S]=(0,s.useState)(),[z,y]=(0,s.useState)(),[A,P]=(0,s.useState)(),[N,O]=(0,s.useState)(!1),[I,D]=(0,s.useState)(!1),[C,Z]=(0,s.useState)({contract:{auditAprove:!0,reason:""},proofPayment:{auditAprove:!0,reason:""},documentPdf:{auditAprove:!0,reason:""},proofOfResidence:{auditAprove:!0,reason:""}}),{navigation:E}=(0,v.H)(),[_,M]=(0,s.useState)([]),onSubmit=e=>{let t={field:"contract",reason:C.contract.reason},n={field:"proofPayment",reason:C.proofPayment.reason},o={field:"documentPdf",reason:C.documentPdf.reason},s={field:"proofOfResidence",reason:C.proofOfResidence.reason};if(C.contract.auditAprove){let e=_.filter(e=>"contract"!==e.field);M(e)}else _.push(t);if(C.proofPayment.auditAprove){let e=_.filter(e=>"proofPayment"!==e.field);M(e)}else _.push(n);if(C.documentPdf.auditAprove){let e=_.filter(e=>"documentPdf"!==e.field);M(e)}else _.push(o);if(C.proofOfResidence.auditAprove){let e=_.filter(e=>"proofOfResidence"!==e.field);M(e)}else _.push(s);for(let e of _)if(""===e.reason){f.Am.warn("O campo de motivo n\xe3o pode estar vazio.");return}return(_.map(e=>{if(""===e.reason)return f.Am.warn("O campo de motivo n\xe3o pode estar vazio.")}),"APPROVED"===e&&_.length>0)?f.Am.warn("Para aprovar o contrato n\xe3o pode ter nenhum campo inv\xe1lido marcado."):"REJECTED"===e&&0===_.length?f.Am.warn("Para rejeitar um contrato selecione pelo menos um motivo."):void("APPROVED"===e&&O(!0),"REJECTED"===e&&D(!0),p(!0),c.Z.post("/audit/contract",{contractId:j,decision:e,rejectionReasons:{reasons:_}}).then(t=>{f.Am.success("Contrato ".concat("APPROVED"===e?"aprovado":"rejeitado"," com sucesso!")),E("/contratos")}).catch(e=>{(0,u.Z)(e,"Falha ao auditar o contrato.")}).finally(()=>{O(!1),D(!1)}))},filterFieldReasons=(e,t)=>{if(""!==e.field)M([..._,e]);else{let e=_.filter(e=>e.field!==t);M(e)}},fieldReasonText=e=>{let t=_.some(t=>t.field===e.filter);if(t){let t=_.map(t=>t.field===e.filter?{...t,reason:e.text}:t);M(t)}else M([..._,{field:e.filter,reason:e.text}])};return(0,o.jsxs)("div",{children:[(0,o.jsx)(a.Z,{title:"Dados Pessoais",children:(0,o.jsxs)("div",{className:"flex items-start gap-4 flex-wrap ",children:[(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.name,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"name",width:"300px",label:"Nome completo"}),(0,o.jsx)(InputSelectText,{placeholder:(0,d.VL)((null==i?void 0:i.investor.document)||""),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"document",width:"200px",label:"CPF"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.rg,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"rg",width:"200px",label:"Identidade"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.issuingAgency,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"issuer",width:"200px",label:"Org\xe3o emissor"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.nationality,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"placeOfBirth",width:"200px",label:"Nacionalidade"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.occupation,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"occupation",width:"200px",label:"Ocupa\xe7\xe3o"}),(0,o.jsx)(InputSelectText,{placeholder:(0,d.gP)((null==i?void 0:i.investor.phone.replace("+55",""))||""),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,width:"200px",name:"phoneNumber",label:"Celular"}),(0,o.jsx)(InputSelectText,{placeholder:x()(null==i?void 0:i.investor.birthDate).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"dtBirth",width:"200px",label:"Data de Nascimento"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.email,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"email",width:"300px",label:"E-mail"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.motherName,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"motherName",width:"300px",label:"Nome da m\xe3e"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.address.zipcode,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"zipCode",width:"200px",label:"CEP"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.address.neighborhood,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"neighborhood",width:"200px",label:"Bairro"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.address.street,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"street",width:"300px",label:"Rua"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.address.city,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"city",width:"200px",label:"Cidade"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.address.state,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"state",width:"150px",label:"Estado"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.address.number,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"number",width:"200px",label:"N\xfamero"}),(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investor.address.complement,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"complement",width:"200px",label:"Complemento"})]})}),(0,o.jsx)(a.Z,{title:"Dados de Investimento",children:(0,o.jsxs)("div",{className:"flex items-start gap-4 flex-wrap",children:[(0,o.jsx)(InputSelectText,{placeholder:"".concat((0,R.Z)(Number(null==i?void 0:i.investment.value))),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"value",width:"200px",label:"Valor"}),(0,o.jsx)(InputSelectText,{placeholder:function(){let e=x()(null==i?void 0:i.investment.start),t=x()(null==i?void 0:i.investment.end);return String(t.diff(e,"months"))}(),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"term",width:"250px",label:"Prazo investimento - em meses"}),(0,o.jsx)(InputSelectText,{placeholder:"".concat((null==i?void 0:i.investment.yield)||0,"%"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"yield",width:"250px",label:"Taxa Remunera\xe7\xe3o Mensal - em %"}),(0,o.jsx)(InputSelectText,{placeholder:(t=(null==i?void 0:i.investment.purchasedWith)||"")in b?b[t]:t,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"purchasedWith",width:"250px",label:"Comprar com"}),"scp"===n&&(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(InputSelectText,{placeholder:null==i?void 0:i.investment.quotesAmount,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"amountQuotes",width:"150px",label:"Quantidade de cotas"})}),(0,o.jsx)(InputSelectText,{placeholder:x()(null==i?void 0:i.investment.start).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"initDate",width:"200px",label:"Inicio do contrato"}),(0,o.jsx)(InputSelectText,{placeholder:x()(null==i?void 0:i.investment.end).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"endDate",width:"200px",label:"Final do contrato"}),(0,o.jsx)(InputSelectText,{placeholder:(null==i?void 0:i.investment.isdebenture)?"Sim":"N\xe3o",setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"isDebenture",width:"200px",label:"\xc9 Deb\xeanture?"})]})}),(0,o.jsx)(a.Z,{title:"Anexo de documentos",children:(0,o.jsx)("div",{children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.contract.auditAprove?"":"text-red-600"),children:"Contrato"}),(0,o.jsx)("div",{onClick:()=>Z({...C,contract:{...C.contract,auditAprove:!C.contract.auditAprove}}),className:"".concat(C.contract.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{disable:!0,onFileUploaded:g})}),!C.contract.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,contract:{...C.contract,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==i?void 0:i.files.contractPdf)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==i?void 0:i.files.contractPdf,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.proofPayment.auditAprove?"":"text-red-600"),children:"Comprovante"}),(0,o.jsx)("div",{onClick:()=>Z({...C,proofPayment:{...C.proofPayment,auditAprove:!C.proofPayment.auditAprove}}),className:"".concat(C.proofPayment.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{disable:!0,onFileUploaded:S})}),!C.proofPayment.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,proofPayment:{...C.proofPayment,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==i?void 0:i.files.proofPaymentPdf)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==i?void 0:i.files.proofPaymentPdf,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.documentPdf.auditAprove?"":"text-red-600"),children:"Documento de identidade"}),(0,o.jsx)("div",{onClick:()=>Z({...C,documentPdf:{...C.documentPdf,auditAprove:!C.documentPdf.auditAprove}}),className:"".concat(C.documentPdf.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{isPdf:!0,disable:!0,onFileUploaded:y})}),!C.documentPdf.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,documentPdf:{...C.documentPdf,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==i?void 0:i.files.personalDocument)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==i?void 0:i.files.personalDocument,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.proofOfResidence.auditAprove?"":"text-red-600"),children:"Comprovante de resid\xeancia"}),(0,o.jsx)("div",{onClick:()=>Z({...C,proofOfResidence:{...C.proofOfResidence,auditAprove:!C.proofOfResidence.auditAprove}}),className:"".concat(C.proofOfResidence.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{disable:!0,onFileUploaded:y})}),!C.proofOfResidence.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,proofOfResidence:{...C.proofOfResidence,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==i?void 0:i.files.proofOfResidence)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==i?void 0:i.files.proofOfResidence,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]})]})})}),(0,o.jsxs)("div",{className:"flex gap-4 w-full justify-end",children:[(0,o.jsx)(T.z,{loading:I,onClick:()=>onSubmit("REJECTED"),variant:"destructive",size:"lg",children:"Rejeitar"}),(0,o.jsx)(T.z,{loading:N,size:"lg",onClick:()=>onSubmit("APPROVED"),children:"Aprovar"})]})]})}var j=n(6121);function BusinessContract(e){let{modalityContract:t,contractData:n}=e,[i,l]=(0,s.useState)(!1),{id:p}=(0,h.useParams)(),[b,T]=(0,s.useState)(),[F,g]=(0,s.useState)(),[w,S]=(0,s.useState)(),[z,y]=(0,s.useState)(),[A,P]=(0,s.useState)(!1),[N,O]=(0,s.useState)(!1),[I,D]=(0,s.useState)(""),[C,Z]=(0,s.useState)({contract:{auditAprove:!0,reason:""},proofPayment:{auditAprove:!0,reason:""},documentPdf:{auditAprove:!0,reason:""},proofOfResidence:{auditAprove:!0,reason:""}}),{navigation:E}=(0,v.H)(),[_,M]=(0,s.useState)([]),onSubmit=e=>{let t={field:"contract",reason:C.contract.reason},n={field:"proofPayment",reason:C.proofPayment.reason},o={field:"documentPdf",reason:C.documentPdf.reason},s={field:"proofOfResidence",reason:C.proofOfResidence.reason};if(C.contract.auditAprove){let e=_.filter(e=>"contract"!==e.field);M(e)}else _.push(t);if(C.proofPayment.auditAprove){let e=_.filter(e=>"proofPayment"!==e.field);M(e)}else _.push(n);if(C.documentPdf.auditAprove){let e=_.filter(e=>"documentPdf"!==e.field);M(e)}else _.push(o);if(C.proofOfResidence.auditAprove){let e=_.filter(e=>"proofOfResidence"!==e.field);M(e)}else _.push(s);for(let e of _)if(""===e.reason){f.Am.warn("O campo de motivo n\xe3o pode estar vazio.");return}return(_.map(e=>{if(""===e.reason)return f.Am.warn("O campo de motivo n\xe3o pode estar vazio.")}),"APPROVED"===e&&_.length>0)?f.Am.warn("Para aprovar o contrato n\xe3o pode ter nenhum campo inv\xe1lido marcado."):"REJECTED"===e&&0===_.length?f.Am.warn("Para rejeitar um contrato selecione pelo menos um motivo."):void("APPROVED"===e&&P(!0),"REJECTED"===e&&O(!0),l(!0),c.Z.post("/audit/contract",{contractId:p,decision:e,rejectionReasons:{reasons:_}}).then(t=>{f.Am.success("Contrato ".concat("APPROVED"===e?"aprovado":"rejeitado"," com sucesso!")),E("/contratos")}).catch(e=>{(0,u.Z)(e,"Falha ao auditar o contrato.")}).finally(()=>{"APPROVED"===e&&P(!1),"REJECTED"===e&&O(!1)}))},filterFieldReasons=(e,t)=>{if(""!==e.field)M([..._,e]);else{let e=_.filter(e=>e.field!==t);M(e)}},fieldReasonText=e=>{let t=_.some(t=>t.field===e.filter);if(t){let t=_.map(t=>t.field===e.filter?{...t,reason:e.text}:t);M(t)}else M([..._,{field:e.filter,reason:e.text}])};return(0,o.jsxs)("div",{children:[(0,o.jsx)(a.Z,{title:"Dados do representante",children:(0,o.jsxs)("div",{className:"flex items-start gap-4 flex-wrap ",children:[(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.name,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"ownerName",width:"300px",label:"Nome completo"}),(0,o.jsx)(InputSelectText,{placeholder:(0,d.VL)((null==n?void 0:n.investor.responsibleOwner.document)||""),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"ownerDocument",width:"200px",label:"CPF"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.rg,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"rg",width:"200px",label:"Identidade"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.issuingAgency,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"issuer",width:"200px",label:"Org\xe3o emissor"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.nationality,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"placeOfBirth",width:"200px",label:"Nacionalidade"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.occupation,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"occupation",width:"200px",label:"Ocupa\xe7\xe3o"}),(0,o.jsx)(InputSelectText,{placeholder:(0,d.gP)((null==n?void 0:n.investor.responsibleOwner.phone.replace("+55",""))||""),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,width:"200px",name:"phoneNumber",label:"Celular"}),(0,o.jsx)(InputSelectText,{placeholder:x()(null==n?void 0:n.investor.responsibleOwner.birthDate).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"dtBirth",width:"200px",label:"Data de Nascimento"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.email,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"email",width:"300px",label:"E-mail"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.motherName,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"motherName",width:"300px",label:"Nome da m\xe3e"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.address.zipcode,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"zipCode",width:"200px",label:"CEP"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.address.neighborhood,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"neighborhood",width:"200px",label:"Bairro"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.address.street,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"street",width:"300px",label:"Rua"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.address.city,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"city",width:"200px",label:"Cidade"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.address.state,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"state",width:"150px",label:"Estado"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.address.number,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"number",width:"200px",label:"N\xfamero"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.responsibleOwner.address.complement,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"complement",width:"200px",label:"Complemento"})]})}),(0,o.jsx)(a.Z,{title:"Dados da empresa",children:(0,o.jsxs)("div",{className:"flex items-start gap-4 flex-wrap ",children:[(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.companyName,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"companyName",width:"300px",label:"Nome"}),(0,o.jsx)(InputSelectText,{placeholder:(0,d.PK)((null==n?void 0:n.investor.document)||""),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"document",width:"200px",label:"CNPJ"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.fantasyName,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"fantasyName",width:"300px",label:"Nome fantasia"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.businessType,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"businessType",width:"150px",label:"Tipo"}),(0,o.jsx)(InputSelectText,{placeholder:(0,j.Z)((null==n?void 0:n.investor.openingDate)||""),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"openingDate",width:"180px",label:"Data de abertura"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.email,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"email",width:"300px",label:"E-mail"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.address.zipcode,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"zipCode",width:"200px",label:"CEP"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.address.neighborhood,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"neighborhood",width:"200px",label:"Bairro"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.address.street,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"street",width:"300px",label:"Rua"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.address.city,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"city",width:"200px",label:"Cidade"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.address.state,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"state",width:"150px",label:"Estado"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.address.number,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"number",width:"200px",label:"N\xfamero"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investor.address.complement,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"complement",width:"200px",label:"Complemento"})]})}),(0,o.jsx)(a.Z,{title:"Dados de Investimento",children:(0,o.jsxs)("div",{className:"flex items-start gap-4 flex-wrap",children:[(0,o.jsx)(InputSelectText,{placeholder:"".concat((0,R.Z)(Number(null==n?void 0:n.investment.value))),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"value",width:"200px",label:"Valor"}),(0,o.jsx)(InputSelectText,{placeholder:function(){let e=x()(null==n?void 0:n.investment.start),t=x()(null==n?void 0:n.investment.end);return String(t.diff(e,"months"))}(),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"term",width:"250px",label:"Prazo investimento - em meses"}),(0,o.jsx)(InputSelectText,{placeholder:"".concat((null==n?void 0:n.investment.yield)||0,"%"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"yield",width:"250px",label:"Taxa Remunera\xe7\xe3o Mensal - em %"}),(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investment.purchasedWith,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"purchasedWith",width:"250px",label:"Comprar com"}),"scp"===t&&(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(InputSelectText,{placeholder:null==n?void 0:n.investment.quotesAmount,setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"amountQuotes",width:"150px",label:"Quantidade de cotas"})}),(0,o.jsx)(InputSelectText,{placeholder:x()(null==n?void 0:n.investment.start).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"initDate",width:"200px",label:"Inicio do contrato"}),(0,o.jsx)(InputSelectText,{placeholder:x()(null==n?void 0:n.investment.end).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"endDate",width:"200px",label:"Final do contrato"}),(0,o.jsx)(InputSelectText,{placeholder:(null==n?void 0:n.investment.isdebenture)?"Sim":"N\xe3o",setFieldText:fieldReasonText,fieldsReasons:_,setFieldReason:filterFieldReasons,name:"isDebenture",width:"200px",label:"\xc9 Deb\xeanture?"})]})}),(0,o.jsx)(a.Z,{title:"Anexo de documentos",children:(0,o.jsx)("div",{children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.contract.auditAprove?"":"text-red-600"),children:"Contrato"}),(0,o.jsx)("div",{onClick:()=>Z({...C,contract:{...C.contract,auditAprove:!C.contract.auditAprove}}),className:"".concat(C.contract.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{disable:!0,onFileUploaded:T})}),!C.contract.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,contract:{...C.contract,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==n?void 0:n.files.contractPdf)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==n?void 0:n.files.contractPdf,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.proofPayment.auditAprove?"":"text-red-600"),children:"Comprovante"}),(0,o.jsx)("div",{onClick:()=>Z({...C,proofPayment:{...C.proofPayment,auditAprove:!C.proofPayment.auditAprove}}),className:"".concat(C.proofPayment.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{disable:!0,onFileUploaded:g})}),!C.proofPayment.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,proofPayment:{...C.proofPayment,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==n?void 0:n.files.proofPaymentPdf)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==n?void 0:n.files.proofPaymentPdf,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.documentPdf.auditAprove?"":"text-red-600"),children:"Documento de identidade"}),(0,o.jsx)("div",{onClick:()=>Z({...C,documentPdf:{...C.documentPdf,auditAprove:!C.documentPdf.auditAprove}}),className:"".concat(C.documentPdf.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{disable:!0,onFileUploaded:S})}),!C.documentPdf.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,documentPdf:{...C.documentPdf,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==n?void 0:n.files.personalDocument)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==n?void 0:n.files.personalDocument,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"mb-1 ".concat(C.proofOfResidence.auditAprove?"":"text-red-600"),children:"Comprovante de resid\xeancia"}),(0,o.jsx)("div",{onClick:()=>Z({...C,proofOfResidence:{...C.proofOfResidence,auditAprove:!C.proofOfResidence.auditAprove}}),className:"".concat(C.proofOfResidence.auditAprove?"":"border-red-600 border-[3px]"),children:(0,o.jsx)(m.Z,{disable:!0,onFileUploaded:S})}),!C.proofOfResidence.auditAprove&&(0,o.jsx)("div",{children:(0,o.jsx)("input",{onChange:e=>{let{target:t}=e;Z({...C,proofOfResidence:{...C.proofOfResidence,reason:t.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),(null==n?void 0:n.files.proofOfResidence)?(0,o.jsx)("div",{children:(0,o.jsx)(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(null==n?void 0:n.files.proofOfResidence,"_blanck")})}):(0,o.jsx)("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]})]})})}),(0,o.jsxs)("div",{className:"flex gap-4 w-full justify-end",children:[(0,o.jsx)("div",{className:"md:w-52 mb-10",children:(0,o.jsx)(r.Z,{label:"Rejeitar",loading:N,disabled:i,handleSubmit:()=>onSubmit("REJECTED"),className:"bg-red-700 hover:bg-red-800"})}),(0,o.jsx)("div",{className:"md:w-52 mb-10",children:(0,o.jsx)(r.Z,{label:"Aprovar",loading:A,disabled:i,handleSubmit:()=>onSubmit("APPROVED")})})]})]})}var F=n(3256);function CreateInvestor(){let{id:e}=(0,h.useParams)(),[t,n]=(0,s.useState)("pf"),[r,d]=(0,s.useState)("mutuo"),[p,x]=(0,s.useState)(),[m,R]=(0,s.useState)(),[b,T]=(0,s.useState)(!0),j=(0,F.e)(),{navigation:g}=(0,v.H)();(0,s.useEffect)(()=>{let checkAuth=async()=>{try{if((null==j?void 0:j.name)!=="superadmin"){f.Am.error("Voc\xea n\xe3o tem permiss\xe3o para acessar essa p\xe1gina"),g("/home");return}T(!1)}catch(e){f.Am.error("Erro ao verificar permiss\xf5es"),g("/home")}};checkAuth()},[]);let getContractData=()=>{c.Z.get("/contract/get-detail/".concat(e)).then(e=>{d(e.data.investment.type),"business"===e.data.investor.type?(x(e.data),n("pj")):(R(e.data),n("pf"))}).catch(e=>{(0,u.Z)(e,"Erro ao buscar dados do contrato.")})};return((0,s.useEffect)(()=>{getContractData()},[]),b)?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.Z,{}),(0,o.jsx)(l.Z,{children:(0,o.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,o.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"}),(0,o.jsx)("p",{className:"text-white text-lg",children:"Verificando permiss\xf5es..."})]})})})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.Z,{}),(0,o.jsx)(l.Z,{children:(0,o.jsxs)("div",{className:"m-3",children:[(0,o.jsx)(a.Z,{title:"Auditoria de contrato",children:(0,o.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,o.jsxs)("select",{value:t,disabled:!0,onChange:e=>{let{target:t}=e;return n(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[(0,o.jsx)("option",{value:"pf",selected:!0,children:"Pessoa F\xedsica"}),(0,o.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-white mb-1",children:"Tipo de contrato"}),(0,o.jsxs)("select",{value:r,disabled:!0,onChange:e=>{let{target:t}=e;return d("mutuo"===t.value?"mutuo":"scp")},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[(0,o.jsx)("option",{selected:!0,value:"mutuo",children:"M\xfatuo"}),(0,o.jsx)("option",{value:"scp",children:"SCP"})]})]})]})}),"pj"===t?(0,o.jsx)(BusinessContract,{contractData:p,modalityContract:r}):(0,o.jsx)(PhysicalContract,{contractData:m,modalityContract:r})]})})]})}},2875:function(e,t,n){"use strict";n.d(t,{Z:function(){return Button}});var o=n(7437),s=n(8700);function Button(e){let{handleSubmit:t,loading:n,label:i,disabled:l,className:a,...r}=e;return(0,o.jsx)(s.z,{...r,onClick:t,loading:n,disabled:l,className:a,children:i})}},5233:function(e,t,n){"use strict";n.d(t,{Z:function(){return CoverForm}});var o=n(7437);function CoverForm(e){let{title:t,children:n,width:s="auto",color:i="withe"}=e;return(0,o.jsxs)("div",{className:"md:w-auto w-full",children:[(0,o.jsx)("p",{className:"text-xl text-white mb-2",children:t}),(0,o.jsx)("div",{className:"mb-10 m-auto bg-opacity-90 rounded-2xl shadow-current ".concat("withe"===i?"bg-zinc-900 border border-[#FF9900] p-5":"pt-1"),children:n})]})}},6121:function(e,t,n){"use strict";n.d(t,{Z:function(){return formatDate},l:function(){return formatDateToEnglishType}});var o=n(4279),s=n.n(o);function formatDate(e){return s().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?s().utc(e).format("YYYY-MM-DD"):s().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},6654:function(e,t,n){"use strict";n.d(t,{Z:function(){return returnError}});var o=n(3014);function returnError(e,t){var n,s,i,l;let a=(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(n=s.data)||void 0===n?void 0:n.message)||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(i=l.data)||void 0===i?void 0:i.error);if(Array.isArray(a))return a.forEach(e=>{o.Am.error(e,{toastId:e})}),a.join("\n");if("string"==typeof a)return o.Am.error(a,{toastId:a}),a;if("object"==typeof a&&null!==a){let e=Object.values(a).flat().join("\n");return o.Am.error(e,{toastId:e}),e}return o.Am.error(t,{toastId:t}),t}},8610:function(e,t,n){"use strict";n.d(t,{H:function(){return useNavigation}});var o=n(4033);let useNavigation=()=>{let e=(0,o.useRouter)();return{navigation:t=>e.push(t)}}},1458:function(e,t,n){"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}n.d(t,{A:function(){return cleanValue},Z:function(){return formatValue}})},7881:function(){}},function(e){e.O(0,[6990,7326,8276,5371,6946,3151,2971,7864,1744],function(){return e(e.s=4628)}),_N_E=e.O()}]);