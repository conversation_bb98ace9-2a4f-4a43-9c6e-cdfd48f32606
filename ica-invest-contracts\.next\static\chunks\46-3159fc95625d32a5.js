"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[46],{6553:function(e,t,a){a.d(t,{Z:function(){return FilterModal}});var r=a(7437),n=a(5969),l=a(8700),s=a(4765),o=a(3495),i=a(5781);let d={startData:"",endData:"",type:"all",status:"Todos"},c=[{label:"Todos",value:"all"},{label:"Contratos SCP",value:"SCP"},{label:"Contratos M\xfatuo",value:"P2P"}];function FilterModal(e){let{activeModal:t,setActiveModal:a,filterData:u,setFilterData:m,handleSearch:f,hidenButton:x,setPage:p,signatarie:h}=e,updateFilter=e=>{m(e),p(1),f(h,e)};return(0,r.jsxs)(i.h_,{children:[(0,r.jsxs)(i.$F,{onClick:()=>a(!t),className:"flex w-24 text-sm justify-around p-2 rounded-lg bg-[#3A3A3A] cursor-pointer",children:[(0,r.jsx)(n.Z,{width:15,color:"#fff"}),(0,r.jsx)("p",{children:"Filtros"})]}),(0,r.jsxs)(i.AW,{className:"mr-1 max-w-[270px] px-3",children:[(0,r.jsx)(i.Ju,{className:"text-md",children:"Filtros"}),(0,r.jsx)(i.VD,{}),(0,r.jsxs)("div",{className:"flex flex-col justify-between mt-2",children:[(0,r.jsxs)("div",{className:"flex md:flex-row flex-col justify-between mb-4",children:[(0,r.jsxs)("div",{className:"mb-2 md:mb-0 md:w-[48%]",children:[(0,r.jsx)("p",{className:"text-xs",children:"In\xedcio"}),(0,r.jsx)("input",{value:u.startData,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:e=>{let{target:t}=e,a={...u,startData:t.value};updateFilter(a)},type:"date"})]}),(0,r.jsxs)("div",{className:"mb-2 md:mb-0 md:w-[48%]",children:[(0,r.jsx)("p",{className:"text-xs",children:"Fim"}),(0,r.jsx)("input",{value:u.endData,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:e=>{let{target:t}=e,a={...u,endData:t.value};updateFilter(a)},type:"date"})]})]}),(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("p",{className:"text-xs",children:"Tipo de contrato"}),(0,r.jsx)(s.Z,{value:u.type,onChange:e=>{let t={...u,type:e.target.value};updateFilter(t)},children:c.map((e,t)=>(0,r.jsx)("option",{value:e.value,children:e.label},t))})]}),(0,r.jsxs)("div",{className:"w-full mt-4",children:[(0,r.jsx)("p",{className:"text-xs",children:"Status do contrato"}),(0,r.jsx)(s.Z,{value:u.status,onChange:e=>{let t={...u,status:e.target.value};updateFilter(t)},children:o.Z.map((e,t)=>(0,r.jsx)("option",{value:e.value,children:e.label},t))})]})]}),!x&&(0,r.jsx)("div",{className:"m-auto mt-5 flex gap-2 mb-1",children:(""!==u.startData||""!==u.endData||"all"!==u.type||"Todos"!==u.status)&&(0,r.jsx)(l.z,{variant:"secondary",onClick:()=>{m(d),p(1),f(h,d),a(!1)},className:"w-full",children:"Resetar"})})]})]})}},2875:function(e,t,a){a.d(t,{Z:function(){return Button}});var r=a(7437),n=a(8700);function Button(e){let{handleSubmit:t,loading:a,label:l,disabled:s,className:o,...i}=e;return(0,r.jsx)(n.z,{...i,onClick:t,loading:a,disabled:s,className:o,children:l})}},7152:function(e,t,a){a.d(t,{Z:function(){return InputTextArea}});var r=a(7437),n=a(2265);function InputTextArea(e){let{setValue:t,value:a,error:l,errorMessage:s,width:o="100%",register:i,name:d,placeholder:c="",className:u=""}=e;return(0,n.useEffect)(()=>{},[o]),(0,r.jsx)("div",{className:"input",style:{width:o},children:(0,r.jsx)("textarea",{...i&&{...i(d)},value:a&&a,placeholder:c,id:d,onChange:e=>{let{target:a}=e;t&&t(a.value)},className:"".concat(u," h-12 w-full min-h-40 p-2 text-white rounded-xl ").concat(l?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})})}},9715:function(e,t,a){a.d(t,{Z:function(){return Pagination}});var r=a(7437),n=a(13),l=a(3217);function Pagination(e){let{lastPage:t,page:a,setPage:s,totalItems:o,perPage:i,loading:d=!1}=e;return d?(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:"Carregando..."})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(n.Z,{color:"#555",width:15})}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818]",children:"..."}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(l.Z,{color:"#555",width:15})})]})]}):(isNaN(t),(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:(()=>{let e=parseInt(o,10);if(0===e)return"Nenhum resultado";let t=(a-1)*i+1,r=Math.min(a*i,e);return"Exibindo ".concat(t," a ").concat(r," de ").concat(e," resultados")})()})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(a>1?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>a>1&&s(a-1),children:(0,r.jsx)(n.Z,{color:"#fff",width:15})}),(0,r.jsx)("div",{className:"flex gap-2 select-none",children:(()=>{let e=new Set;e.add(1),e.add(t),a>1&&e.add(a-1),e.add(a),a<t&&e.add(a+1);let n=Array.from(e).sort((e,t)=>e-t);return n.map((e,t,n)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[t>0&&n[t-1]!==e-1&&(0,r.jsx)("div",{className:"flex items-center justify-center",children:"..."}),Number.isFinite(e)&&(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer bg-[#262626] ".concat(a===e?"text-[#FF9900]":"text-white"),onClick:()=>s(e),children:e})]},e))})()}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(a<t?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>a<t&&s(a+1),children:(0,r.jsx)(l.Z,{color:"#fff",width:15})})]})]}))}},4209:function(e,t,a){a.d(t,{Z:function(){return StatusWithDescription}});var r=a(7437),n=a(7057);function StatusWithDescription(e){let{description:t,text:a,textColor:l}=e;return(0,r.jsxs)("div",{className:"relative group select-none",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,r.jsx)("p",{className:"text-xs text-center ".concat(l),children:a}),(0,r.jsx)(n.Z,{width:15,color:"#FF9900"})]}),(0,r.jsx)("div",{className:"absolute max-w-[250px] text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[100%] hidden w-max bg-gray-700 text-white text-xs rounded px-2 py-1 group-hover:block group-hover:mb-[-100%]",children:t})]})}},5465:function(e,t,a){a.d(t,{Z:function(){return TableFormat}});var r=a(7437);function TableFormat(e){let{children:t}=e;return(0,r.jsx)("div",{className:"rounded-t-md bg-[#1C1C1C] w-full text-white mt-5 overflow-x-auto rounded-b-md border border-[#FF9900] min-h-[400px] flex flex-col mb-10",children:t})}},3401:function(e,t,a){var r=a(7437);a(2265);var n=a(8689),l=a(9715),s=a(7805);t.Z=e=>{var t;let{data:a,headers:o,loading:i,pagination:d,selectable:c=!1,checked:u=!1,setChecked:m=()=>{}}=e;return(0,r.jsxs)("div",{className:"bg-[#1C1C1C] w-full text-white min-h-[350px] h-full relative flex flex-col",children:[(0,r.jsx)("div",{className:"flex-1 mb-1 h-full",children:(0,r.jsx)("div",{className:"w-full overflow-auto",children:(0,r.jsxs)("table",{className:"min-w-[700px] w-full table-auto",children:[(0,r.jsxs)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900] relative",children:[c&&(0,r.jsx)("div",{className:"absolute p-1 pl-5 md:pt-2 pt-4 md:pl-4 z-10 select-none",children:(0,r.jsx)("div",{className:"w-5 h-5 border rounded-md border-[#FF9900] cursor-pointer flex items-center justify-center",onClick:()=>{m(!u)},children:u&&(0,r.jsx)(s.Z,{width:20,color:"#1EF97C"})})}),(0,r.jsx)("tr",{className:"w-full",children:o.map((e,t)=>(0,r.jsx)("th",{className:"py-2 pl-4 min-w-[".concat(null==e?void 0:e.width,"]"),children:(0,r.jsx)("p",{className:"font-bold text-sm ".concat(e.position&&"text-".concat(e.position)),children:e.title})},t))})]}),(0,r.jsx)("tbody",{className:"w-full",children:i?(0,r.jsx)("tr",{className:"",children:o.map(e=>(0,r.jsx)("td",{className:"p-1",children:(0,r.jsx)(n.j,{height:"25px"})},e.component))}):(null!==(t=null==a?void 0:a.length)&&void 0!==t?t:0)>0?a.map((e,t)=>(0,r.jsx)("tr",{className:"",children:o.map((t,a)=>(0,r.jsx)("td",{style:{width:t.width},className:"text-sm py-2 pl-4",children:(null==t?void 0:t.render)?t.render(e[t.component],e):(0,r.jsx)("p",{style:{width:t.width},className:"".concat(t.position&&"text-".concat(t.position)," truncate whitespace-nowrap overflow-hidden"),children:e[t.component]})},t.component))},t)):(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("p",{className:"py-2 px-4 text-center pt-10 absolute left-[50%] translate-x-[-50%]",children:"Nenhum dado encontrado"})})})]})})}),d&&(0,r.jsx)(l.Z,{lastPage:d.lastPage,page:d.page,perPage:d.perPage,setPage:d.setPage,totalItems:d.totalItems,loading:i})]})}},2359:function(e,t,a){a.d(t,{aM:function(){return c},ue:function(){return x},yo:function(){return d}});var r=a(7437),n=a(2265),l=a(8712),s=a(6061),o=a(2549),i=a(992);let d=l.fC,c=l.xz;l.x8;let u=l.h_,m=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(l.aV,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...n,ref:t})});m.displayName=l.aV.displayName;let f=(0,s.j)("fixed z-50 gap-4 bg-[#1C1C1C] border-l overflow-auto border-[#FF9900] p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right"}},defaultVariants:{side:"right"}}),x=n.forwardRef((e,t)=>{let{side:a="right",className:n,children:s,...d}=e;return(0,r.jsxs)(u,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(l.VY,{ref:t,className:(0,i.cn)(f({side:a}),n),...d,children:[(0,r.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(o.Z,{className:"h-4 w-4 text-white"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]}),s]})]})});x.displayName=l.VY.displayName;let p=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(l.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold text-foreground",a),...n})});p.displayName=l.Dx.displayName;let h=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(l.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...n})});h.displayName=l.dk.displayName},7395:function(e,t,a){a.d(t,{l:function(){return r}});let r="Shayra Madalena Lyra de Pinho"},8928:function(e,t,a){a.d(t,{f:function(){return profileCanDeleteContract}});var r=a(8440),n=a(3256);function profileCanDeleteContract(e){let t=(0,n.e)();if("superadmin"===t.name){if(e===r.rd.DRAFT||e===r.rd.AWAITING_AUDIT||e===r.rd.REJECTED_BY_AUDIT||e===r.rd.AWAITING_AUDIT_SIGNATURE||e===r.rd.AWAITING_INVESTOR_SIGNATURE||e===r.rd.EXPIRED_BY_AUDIT||e===r.rd.EXPIRED_BY_INVESTOR||e===r.rd.EXPIRED_FAILURE_PROOF_PAYMENT||e===r.rd.GENERATE_CONTRACT_FAILED)return!0}else if("broker"===t.name&&e===r.rd.DRAFT)return!0}},6121:function(e,t,a){a.d(t,{Z:function(){return formatDate},l:function(){return formatDateToEnglishType}});var r=a(4279),n=a.n(r);function formatDate(e){return n().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?n().utc(e).format("YYYY-MM-DD"):n().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},6654:function(e,t,a){a.d(t,{Z:function(){return returnError}});var r=a(3014);function returnError(e,t){var a,n,l,s;let o=(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.message)||(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.error);if(Array.isArray(o))return o.forEach(e=>{r.Am.error(e,{toastId:e})}),o.join("\n");if("string"==typeof o)return r.Am.error(o,{toastId:o}),o;if("object"==typeof o&&null!==o){let e=Object.values(o).flat().join("\n");return r.Am.error(e,{toastId:e}),e}return r.Am.error(t,{toastId:t}),t}},8610:function(e,t,a){a.d(t,{H:function(){return useNavigation}});var r=a(4033);let useNavigation=()=>{let e=(0,r.useRouter)();return{navigation:t=>e.push(t)}}},9784:function(e,t,a){a.d(t,{F:function(){return calculateTotalContractValue}});function calculateTotalContractValue(e){var t;let a=Number((null==e?void 0:e.valorInvestimento)||0),r=(null==e?void 0:null===(t=e.addendum)||void 0===t?void 0:t.reduce((e,t)=>e+Number(t.value||0),0))||0;return(a+r).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}},3277:function(e,t,a){a.d(t,{Z:function(){return formatNumberValue}});function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}},7626:function(e,t,a){a.d(t,{U:function(){return r}});let r={CONTRACTS:(e,t,a,r,n)=>["contracts",e,t,a,...Object.values(r),n]}}}]);