import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { AddFavoritePixDto } from '../dto/add-favorite-pix.dto';
import { DeleteFavoritePixDto } from '../dto/delete-favorite-pix.dto';
import { GetFavoritePixAccountDto } from '../dto/get-favorite-pix-account.dto';
import { AddFavoritePixService } from '../service/add-favorite-pix.service';
import { DeleteFavoritePixService } from '../service/delete-favorite-pix.service';
import { GetFavoritePixAccountService } from '../service/get-favorite-pix-account.service';
export declare class FavoritePixController {
    private addFavoriteService;
    private getFavoriteService;
    private deleteFavoriteService;
    constructor(addFavoriteService: AddFavoritePixService, getFavoriteService: GetFavoritePixAccountService, deleteFavoriteService: DeleteFavoritePixService);
    addFavorite(body: AddFavoritePixDto, request: IRequestUser): Promise<void>;
    deleteFavorite(query: DeleteFavoritePixDto, request: IRequestUser): Promise<void>;
    getFavoriteAccount(query: GetFavoritePixAccountDto, request: IRequestUser): Promise<import("../response/get-favorite-pix-account.response").IGetFavoritePixAccountResponse[]>;
}
