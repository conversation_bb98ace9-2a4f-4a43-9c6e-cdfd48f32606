"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractEntity = void 0;
const typeorm_1 = require("typeorm");
const addendum_entity_1 = require("./addendum.entity");
const contract_advisor_entity_1 = require("./contract-advisor.entity");
const contract_audit_entity_1 = require("./contract-audit.entity");
const contract_deletion_entity_1 = require("./contract-deletion.entity");
const contract_event_entity_1 = require("./contract-event.entity");
const income_payment_scheduled_entity_1 = require("./income-payment-scheduled.entity");
const income_reports_contracts_entity_1 = require("./income-reports-contracts.entity");
const notification_entity_1 = require("./notification.entity");
const owner_role_relation_entity_1 = require("./owner-role-relation.entity");
const pre_register_entity_1 = require("./pre-register.entity");
let ContractEntity = class ContractEntity {
};
exports.ContractEntity = ContractEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ContractEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'external_id', nullable: true }),
    __metadata("design:type", Number)
], ContractEntity.prototype, "externalId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'owner_role_relation', nullable: true }),
    __metadata("design:type", String)
], ContractEntity.prototype, "brokerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'investor_id', nullable: true }),
    __metadata("design:type", String)
], ContractEntity.prototype, "investorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'start_contract', type: 'date' }),
    __metadata("design:type", Date)
], ContractEntity.prototype, "startContract", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'end_contract', type: 'date' }),
    __metadata("design:type", Date)
], ContractEntity.prototype, "endContract", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contract_pdf', nullable: true }),
    __metadata("design:type", String)
], ContractEntity.prototype, "contractPdf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'old_contract_pdf', nullable: true }),
    __metadata("design:type", String)
], ContractEntity.prototype, "oldContractPdf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'proof_payment', nullable: true }),
    __metadata("design:type", String)
], ContractEntity.prototype, "proofPayment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sign_investor', nullable: true }),
    __metadata("design:type", String)
], ContractEntity.prototype, "signInvestor", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sign_ica', nullable: true }),
    __metadata("design:type", String)
], ContractEntity.prototype, "signIca", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'status', nullable: true, default: 'aberto' }),
    __metadata("design:type", String)
], ContractEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'type', default: 'p2p' }),
    __metadata("design:type", String)
], ContractEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_debenture', default: false }),
    __metadata("design:type", Boolean)
], ContractEntity.prototype, "isDebenture", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contract_number', default: 1 }),
    __metadata("design:type", Number)
], ContractEntity.prototype, "contractNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'duration_in_months', nullable: true }),
    __metadata("design:type", Number)
], ContractEntity.prototype, "durationInMonths", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'broker_participation_percentage',
        type: 'decimal',
        precision: 5,
        scale: 2,
        nullable: true,
    }),
    __metadata("design:type", String)
], ContractEntity.prototype, "brokerParticipationPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'advisor_participation_percentage',
        type: 'decimal',
        precision: 5,
        scale: 2,
        nullable: true,
    }),
    __metadata("design:type", String)
], ContractEntity.prototype, "advisorParticipationPercentage", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ContractEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ContractEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => owner_role_relation_entity_1.OwnerRoleRelationEntity, {
        nullable: true,
    }),
    (0, typeorm_1.JoinColumn)({
        name: 'owner_role_relation',
        referencedColumnName: 'id',
    }),
    __metadata("design:type", Object)
], ContractEntity.prototype, "ownerRoleRelation", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => pre_register_entity_1.PreRegisterEntity, (signed) => signed.contract, {
        nullable: true,
        cascade: true,
    }),
    __metadata("design:type", Array)
], ContractEntity.prototype, "signataries", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => owner_role_relation_entity_1.OwnerRoleRelationEntity, (investor) => investor.contractInvestor),
    (0, typeorm_1.JoinColumn)([{ name: 'investor_id', referencedColumnName: 'id' }]),
    __metadata("design:type", Object)
], ContractEntity.prototype, "investor", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => contract_event_entity_1.ContractEventEntity, (event) => event.contract),
    __metadata("design:type", Object)
], ContractEntity.prototype, "events", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => income_payment_scheduled_entity_1.IncomePaymentScheduledEntity, (scheduledPayments) => scheduledPayments.contract),
    __metadata("design:type", Object)
], ContractEntity.prototype, "scheduledPayments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => addendum_entity_1.AddendumEntity, (addendum) => addendum.contract),
    __metadata("design:type", Object)
], ContractEntity.prototype, "addendum", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => contract_advisor_entity_1.ContractAdvisorEntity, (contractAdvisor) => contractAdvisor.contract),
    __metadata("design:type", Object)
], ContractEntity.prototype, "contractAdvisors", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => notification_entity_1.NotificationEntity, (item) => item.contract),
    __metadata("design:type", Array)
], ContractEntity.prototype, "notifications", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => income_reports_contracts_entity_1.IncomeReportsContractsEntity, (incomeReports) => incomeReports.contracts),
    __metadata("design:type", Array)
], ContractEntity.prototype, "incomeReportsContracts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => contract_audit_entity_1.ContractAuditEntity, (audit) => audit.contract),
    __metadata("design:type", Object)
], ContractEntity.prototype, "audits", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => contract_deletion_entity_1.ContractDeletionEntity, (deletion) => deletion.contract),
    __metadata("design:type", Object)
], ContractEntity.prototype, "deletions", void 0);
exports.ContractEntity = ContractEntity = __decorate([
    (0, typeorm_1.Entity)('contract')
], ContractEntity);
//# sourceMappingURL=contract.entity.js.map