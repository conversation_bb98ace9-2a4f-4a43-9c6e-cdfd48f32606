{"version": 3, "file": "location.module.js", "sourceRoot": "/", "sources": ["modules/location/location.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,8DAAwD;AAExD,2EAAuE;AACvE,oFAA8E;AAC9E,oFAA2E;AAOpE,IAAM,cAAc,GAApB,MAAM,cAAc;CAAG,CAAA;AAAjB,wCAAc;yBAAd,cAAc;IAL1B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,CAAC;QACvB,SAAS,EAAE,CAAC,kDAAsB,EAAE,+CAAmB,CAAC;QACxD,WAAW,EAAE,CAAC,wCAAkB,CAAC;KAClC,CAAC;GACW,cAAc,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { LocationController } from './controllers/location.controller';\r\nimport { CreateStateCityService } from './services/create-state-city.service';\r\nimport { StatesCitiesService } from './services/get-states-cities.service';\r\n\r\n@Module({\r\n  imports: [SharedModule],\r\n  providers: [CreateStateCityService, StatesCitiesService],\r\n  controllers: [LocationController],\r\n})\r\nexport class LocationModule {}\r\n"]}