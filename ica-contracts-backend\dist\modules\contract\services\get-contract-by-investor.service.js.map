{"version": 3, "file": "get-contract-by-investor.service.js", "sourceRoot": "/", "sources": ["modules/contract/services/get-contract-by-investor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,+FAAsF;AACtF,qHAA0G;AAC1G,qCAAqC;AAG9B,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IACxC,YAEmB,2BAAgE,EAEhE,kBAA8C;QAF9C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,uBAAkB,GAAlB,kBAAkB,CAA4B;IAC9D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,UAAkB;QAC9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,SAAS,EAAE;gBACT,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;gBACvC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;gBAC1C,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACnD,KAAK,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,EAAE;YAC3C,SAAS,EAAE;gBACT,iBAAiB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;gBAClD,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBAC3C,gBAAgB,EAAE;oBAChB,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;iBACzC;aACF;SACF,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACxC,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAE1C,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,eAAe,EAAE,SAAS,EAAE,eAAe;gBAC3C,cAAc,EAAE,SAAS,EAAE,cAAc;gBACzC,eAAe,EAAE,SAAS,EAAE,eAAe;gBAC3C,YAAY,EAAE,SAAS,EAAE,IAAI;gBAC7B,gBAAgB,EAAE,SAAS,EAAE,QAAQ;gBACrC,IAAI,EAAE,SAAS,EAAE,kBAAkB;gBACnC,aAAa,EAAE,SAAS,EAAE,YAAY;gBACtC,YAAY,EAAE,SAAS,EAAE,YAAY;gBACrC,WAAW,EAAE,SAAS,EAAE,WAAW;gBACnC,qBAAqB,EACnB,QAAQ,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI;oBACvC,QAAQ,CAAC,iBAAiB,EAAE,QAAQ,EAAE,WAAW;oBACjD,YAAY;gBACd,cAAc,EAAE,QAAQ,CAAC,MAAM;gBAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,WAAW,EAAE,QAAQ,CAAC,SAAS;gBAC/B,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,eAAe,EAAE,QAAQ,CAAC,YAAY;gBACtC,QAAQ,EAAE,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC9D,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE,WAAW;oBAC1D,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,EAAE,IAAI;oBACtD,IAAI;iBACL,CAAC,CAAC;gBACH,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;oBAC7C,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,eAAe,EAAE,QAAQ,CAAC,KAAK;oBAC/B,eAAe,EAAE,QAAQ,CAAC,SAAS;oBACnC,aAAa,EAAE,QAAQ,CAAC,eAAe;oBACvC,WAAW,EAAE,QAAQ,CAAC,SAAS;oBAC/B,cAAc,EAAE,QAAQ,CAAC,MAAM;oBAC/B,qBAAqB,EACnB,QAAQ,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI;wBACvC,QAAQ,CAAC,iBAAiB,EAAE,QAAQ,EAAE,WAAW;oBACnD,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;wBACnD,IAAI,EAAE,YAAY,CAAC,IAAI;wBACvB,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG;qBAC3B,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,QAAQ,EAAE,WAAW,CAAC,KAAK,EAAE,GAAG,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI;YAC9D,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,KAAK,IAAI,WAAW,CAAC,QAAQ,EAAE,KAAK;YAC9D,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,KAAK;YAC/B,OAAO,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI;YAC/B,OAAO,EACL,SAAS,CAAC,CAAC,CAAC,EAAE,iBAAiB,EAAE,KAAK,EAAE,IAAI;gBAC5C,SAAS,CAAC,CAAC,CAAC,EAAE,iBAAiB,EAAE,QAAQ,EAAE,WAAW;YACxD,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE;YAEpC,UAAU,EAAE,WAAW,CAAC,KAAK,EAAE,UAAU;YACzC,SAAS,EAAE,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;gBAC5C,IAAI,CAAC;oBAEH,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,YAAY,IAAI,EAAE,CAAC;wBAC9C,OAAO,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/D,CAAC;oBAED,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBACtD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;wBAChC,OAAO,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/C,CAAC;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAC;oBAC1C,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;YAChB,OAAO,EACL,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;gBACnC,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;YACxC,IAAI,EACF,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI;gBACnC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI;YACxC,OAAO,EACL,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;gBACrC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;YAC1C,aAAa,EACX,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;gBACrC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;YAC1C,UAAU,EACR,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU;gBACzC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU;YAC9C,YAAY,EACV,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;gBAC3C,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;YAChD,KAAK,EACH,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK;gBACpC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK;YACzC,IAAI,EACF,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI;gBACnC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI;gBACtC,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI;YACpC,aAAa,EACX,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;gBACrC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;gBACxC,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO;YACvC,MAAM,EACJ,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;gBACrC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM;gBACxC,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM;YACtC,SAAS,EAAE,MAAM;SAClB,CAAC;IACJ,CAAC;CACF,CAAA;AAlJY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCADa,oBAAU;QAEnB,oBAAU;GALtC,6BAA6B,CAkJzC", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\n@Injectable()\r\nexport class GetContractsByInvestorService {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n  ) {}\r\n\r\n  async perform(investorId: string) {\r\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\r\n      where: { id: investorId },\r\n      relations: {\r\n        owner: { address: true, account: true },\r\n        business: { address: true, account: true },\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (!userProfile) {\r\n      throw new NotFoundException(`Usuario nāo encontrado.`);\r\n    }\r\n\r\n    const contracts = await this.contractRepository.find({\r\n      where: { investor: { id: userProfile.id } },\r\n      relations: {\r\n        ownerRoleRelation: { business: true, owner: true },\r\n        signataries: true,\r\n        addendum: { addendumFiles: { file: true } },\r\n        contractAdvisors: {\r\n          advisor: { owner: true, business: true },\r\n        },\r\n      },\r\n    });\r\n\r\n    const result = contracts.map((contract) => {\r\n      const signatary = contract.signataries[0];\r\n\r\n      return {\r\n        id: contract.id,\r\n        investmentValue: signatary?.investmentValue,\r\n        investmentTerm: signatary?.investmentTerm,\r\n        investmentYield: signatary?.investmentYield,\r\n        investorName: signatary?.name,\r\n        investorDocument: signatary?.document,\r\n        tags: signatary?.investmentModality,\r\n        purchasedWith: signatary?.purchaseWith,\r\n        quotesAmount: signatary?.amountQuotes,\r\n        gracePeriod: signatary?.gracePeriod,\r\n        responsibleConsultant:\r\n          contract.ownerRoleRelation?.owner?.name ||\r\n          contract.ownerRoleRelation?.business?.fantasyName ||\r\n          'Nāo possui',\r\n        contractStatus: contract.status,\r\n        contractStart: contract.startContract,\r\n        contractEnd: contract.endContract,\r\n        finalizedAt: contract.updatedAt,\r\n        pdfDocument: contract.contractPdf,\r\n        proofPaymentPdf: contract.proofPayment,\r\n        advisors: contract.contractAdvisors.map(({ advisor, rate }) => ({\r\n          id: advisor.id,\r\n          name: advisor.owner?.name ?? advisor.business?.fantasyName,\r\n          document: advisor.owner?.cpf ?? advisor.business?.cnpj,\r\n          rate,\r\n        })),\r\n        addendum: contract.addendum.map((addendum) => ({\r\n          id: addendum.id,\r\n          investmentValue: addendum.value,\r\n          investmentYield: addendum.yieldRate,\r\n          contractStart: addendum.applicationDate,\r\n          contractEnd: addendum.expiresIn,\r\n          contractStatus: addendum.status,\r\n          responsibleConsultant:\r\n            contract.ownerRoleRelation?.owner?.name ||\r\n            contract.ownerRoleRelation?.business?.fantasyName,\r\n          files: addendum.addendumFiles.map((addendumFile) => ({\r\n            type: addendumFile.type,\r\n            url: addendumFile.file.url,\r\n          })),\r\n        })),\r\n      };\r\n    });\r\n\r\n    return {\r\n      document: userProfile.owner?.cpf || userProfile.business?.cnpj,\r\n      email: userProfile.owner?.email || userProfile.business?.email,\r\n      phone: userProfile.owner?.phone,\r\n      profile: userProfile.role?.name,\r\n      advisor:\r\n        contracts[0]?.ownerRoleRelation?.owner?.name ||\r\n        contracts[0]?.ownerRoleRelation?.business?.fantasyName,\r\n      rg: contracts[0]?.signataries[0]?.rg,\r\n      // Adicionar campos motherName e birthDate para pré-preenchimento no upgrade\r\n      motherName: userProfile.owner?.motherName,\r\n      birthDate: userProfile.owner?.dtBirth ? (() => {\r\n        try {\r\n          // Verificar se já é uma instância de Date\r\n          if (userProfile.owner.dtBirth instanceof Date) {\r\n            return userProfile.owner.dtBirth.toISOString().split('T')[0];\r\n          }\r\n          // Se for string, tentar converter\r\n          const birthDate = new Date(userProfile.owner.dtBirth);\r\n          if (!isNaN(birthDate.getTime())) {\r\n            return birthDate.toISOString().split('T')[0];\r\n          }\r\n          return undefined;\r\n        } catch (e) {\r\n          console.error('Error parsing dtBirth', e);\r\n          return undefined;\r\n        }\r\n      })() : undefined,\r\n      zipCode:\r\n        userProfile?.owner?.address[0]?.cep ||\r\n        userProfile?.business?.address[0]?.cep,\r\n      city:\r\n        userProfile.owner?.address[0]?.city ||\r\n        userProfile.business?.address[0]?.city,\r\n      address:\r\n        userProfile.owner?.address[0]?.street ||\r\n        userProfile.business?.address[0]?.street,\r\n      addressNumber:\r\n        userProfile.owner?.address[0]?.number ||\r\n        userProfile.business?.address[0]?.number,\r\n      complement:\r\n        userProfile.owner?.address[0]?.complement ||\r\n        userProfile.business?.address[0]?.complement,\r\n      neighborhood:\r\n        userProfile.owner?.address[0]?.neighborhood ||\r\n        userProfile.business?.address[0]?.neighborhood,\r\n      state:\r\n        userProfile.owner?.address[0]?.state ||\r\n        userProfile.business?.address[0]?.state,\r\n      bank:\r\n        userProfile.owner?.account[0]?.bank ||\r\n        userProfile.business?.account[0]?.bank ||\r\n        contracts[0]?.signataries[0]?.bank,\r\n      accountNumber:\r\n        userProfile.owner?.account[0]?.number ||\r\n        userProfile.business?.account[0]?.number ||\r\n        contracts[0]?.signataries[0]?.account,\r\n      branch:\r\n        userProfile.owner?.account[0]?.branch ||\r\n        userProfile.business?.account[0]?.branch ||\r\n        contracts[0]?.signataries[0]?.agency,\r\n      contracts: result,\r\n    };\r\n  }\r\n}\r\n"]}