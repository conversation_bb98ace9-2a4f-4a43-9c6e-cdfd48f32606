(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2508],{6221:function(e,t,a){Promise.resolve().then(a.bind(a,631))},631:function(e,t,a){"use strict";a.r(t),a.d(t,{Screen:function(){return Screen}});var s=a(7437),n=a(3877),l=a(8637),o=a(2265),r=a(4568),i=a(3042),c=a(1122),d=a(1980),x=a(4033),m=a(5968),u=a(6121),p=a(4984),h=a(2067),v=a.n(h),f=a(3014);function RenewContract(e){let{contract:t,setOpenModal:a}=e,[n,l]=(0,o.useState)(),[i,c]=(0,o.useState)(),[d,x]=(0,o.useState)(v()().format("YYYY-MM-DD")),[m,u]=(0,o.useState)(v()().format("YYYY-MM-DD"));return(0,s.jsx)("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"w-5/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[(0,s.jsx)("p",{className:"text-lg font-bold",children:"Renova\xe7\xe3o de contrato"}),(0,s.jsxs)("div",{className:"flex gap-4 mt-5",children:[(0,s.jsxs)("div",{className:"md:w-2/4",children:[(0,s.jsx)("p",{className:"text-white mb-1",children:"Inicio do contrato"}),(0,s.jsx)("input",{value:d,onChange:e=>{let{target:t}=e;return x(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]}),(0,s.jsxs)("div",{className:"md:w-2/4",children:[(0,s.jsx)("p",{className:"text-white mb-1",children:"Fim do contrato"}),(0,s.jsx)("input",{value:m,onChange:e=>{let{target:t}=e;return u(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 mb-10 text-white items-center justify-around mt-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Contrato antigo"}),(0,s.jsx)(p.Z,{onFileUploaded:l})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Novo Contrato"}),(0,s.jsx)(p.Z,{onFileUploaded:c})]})]}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[(0,s.jsx)("div",{className:"px-10 bg-orange-linear flex items-center cursor-pointer",onClick:()=>{if(null===d)return f.Am.warning("Precisa informar a data de inicio de contrato");let e=new FormData;e.append("newStartDate",d),e.append("newEndDate",m),n&&e.append("oldContractPdf",n[0]),i&&e.append("newContractPdf",i[0]),r.Z.put("/contract/".concat(t.id,"/upgrade"),e).then(e=>{f.Am.success("Contrato atualizado com sucesso!"),a(!1)}).catch(e=>{})},children:(0,s.jsx)("p",{className:"text-sm",children:"Renovar contrato"})}),(0,s.jsx)("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer",onClick:()=>a(!1),children:(0,s.jsx)("p",{className:"text-sm",children:"Fechar"})})]})]})})}var j=a(6610),g=a(4209),N=a(8440);function ContractData(e){var t;let{contract:a,setAditive:n,setModal:l,setModalPayment:o,setRenew:r,loading:i,resendContract:c}=e;return(0,s.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5 w-full",children:[a.statusContrato===N.rd.DELETED&&(0,s.jsx)("div",{className:"flex w-full border-2 border-red-500 rounded-md p-2 mb-2 pb-10",children:(0,s.jsx)("p",{className:"text-sm text-white",children:null==a?void 0:a.cancelledReason})}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"ID"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.idContrato})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Investidor"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.nomeInvestidor})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"CPF/CNPJ"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(null==a?void 0:a.documentoInvestidor)!==void 0&&(null===(t=(0,m.p4)(null==a?void 0:a.documentoInvestidor))||void 0===t?void 0:t.length)<=11?(0,m.VL)(String(null==a?void 0:a.documentoInvestidor)):(0,m.PK)(String(null==a?void 0:a.documentoInvestidor))})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Consultor Respons\xe1vel"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Comprado com"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.compradoCom})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Assinaturas"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Valor"}),(0,s.jsx)("p",{className:"text-xs text-end",children:Number(null==a?void 0:a.valorInvestimento).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Contrato Solicitado em"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(0,u.Z)(null==a?void 0:a.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Final do contrato"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(0,u.Z)(null==a?void 0:a.fimContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Status de Contrato"}),(0,s.jsx)("div",{children:(0,s.jsx)(g.Z,{description:(0,N.mP)(a.statusContrato).description,text:(0,N.mP)(a.statusContrato).title,textColor:(0,N.mP)(a.statusContrato).title})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Qtd de Cotas/Participa\xe7\xf5es"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(null==a?void 0:a.cotas)||"N\xe3o encontrado"})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Data de Car\xeancia"}),(0,s.jsx)("p",{className:"text-xs text-end",children:null==a?void 0:a.periodoCarencia})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Porcentagens"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:[null==a?void 0:a.rendimentoInvestimento,"%"]})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Ativado em"}),(0,s.jsx)("p",{className:"text-xs text-end",children:(0,u.Z)(null==a?void 0:a.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("p",{className:"flex-1 font-bold text-xs",children:"Tags"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:["#",(null==a?void 0:a.tags)==="P2P"?"M\xfatuo":(null==a?void 0:a.tags)||"NE"]})]}),a.contratoPdf&&(0,s.jsx)("div",{className:"flex w-full mt-3",children:(0,s.jsx)("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(a.contratoPdf,"_blank"),children:"Ver contrato"})}),a.comprovamentePagamento&&(0,s.jsx)("div",{className:"flex w-full mt-3",children:(0,s.jsx)("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(a.comprovamentePagamento,"_blank"),children:"Ver comprovante anexado"})})]})}var b=a(2875),w=a(3220),C=a(7395),y=a(6654),S=a(3277),F=a(4279),A=a.n(F);function AditiveContract(e){let{contract:t,setOpenModal:a}=e,[n,l]=(0,o.useState)(),[i,c]=(0,o.useState)(),[d,x]=(0,o.useState)("new"),[u,h]=(0,o.useState)(!1),[v,j]=(0,o.useState)({value:"",profile:"",yield:"",date:"",bank:"",agency:"",accountNumber:"",pix:"",comment:"",ownerName:"",ownerCpf:""}),createAditive=()=>{h(!0);let e={contractId:null==t?void 0:t.idContrato,investment:{value:(0,S.Z)(v.value),profile:v.profile,yield:Number(t.rendimentoInvestimento),date:A()(v.date).format("YYYY-MM-DD")},accountBank:{bank:v.bank,accountNumber:v.accountNumber,agency:v.agency,pix:v.pix},owner:t.documentoInvestidor.length>11?{name:v.ownerName,cpf:(0,m.p4)(v.ownerCpf)}:void 0,observations:v.comment,signIca:C.l};r.Z.post("/contract/additive",e).then(e=>{f.Am.success("Contrato de aditivo criado com sucesso!"),a(!1)}).catch(e=>{(0,y.Z)(e.message,"N\xe3o conseguimos criar o contrato de aditivo!")}).finally(()=>h(!1))},registerAditiveExists=()=>{if("exist"===d&&(!i||!n))return f.Am.error("\xc9 necess\xe1rio anexar o contrato e o comprovante de pagamento");h(!0);let e=new FormData;i&&e.append("contractPdf",i[0]),n&&e.append("proofPayment",n[0]),e.append("investment[yield]",""!==v.yield||Number(v.yield)>0?v.yield:t.rendimentoInvestimento),e.append("contractId",null==t?void 0:t.idContrato),e.append("investment[value]",v.value.replace(".","").replace(",",".")),e.append("investment[date]",A()(v.date).format("YYYY-MM-DD")),e.append("accountBank[bank]",v.bank),e.append("accountBank[accountNumber]",v.accountNumber),e.append("accountBank[agency]",v.agency),e.append("accountBank[pix]",v.pix),e.append("observations",v.comment),r.Z.post("/contract/additive-manual",e).then(e=>{f.Am.success("Contrato de aditivo cadastrado com sucesso!"),a(!1)}).catch(e=>{var t,a;f.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o foi poss\xedvel cadastrar o aditivo")}).finally(()=>h(!1))};return(0,s.jsx)("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-20",children:(0,s.jsxs)("div",{className:"md:w-6/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900] overflow-auto h-[88%]",children:[(0,s.jsx)("p",{className:"text-2xl font-bold",children:"Criar contrato aditivo"}),(0,s.jsxs)("div",{className:"mt-5 w-full",children:[(0,s.jsxs)("div",{className:"mb-5",children:[(0,s.jsxs)("div",{className:"flex gap-4 mb-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("input",{type:"checkbox",name:"",checked:"new"===d,onChange:()=>x("new"),id:"novo",className:"mr-2 cursor-pointer"}),(0,s.jsx)("label",{htmlFor:"novo",className:"cursor-pointer select-none",children:"Criar novo aditivo"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("input",{type:"checkbox",name:"",checked:"exist"===d,onChange:()=>x("exist"),id:"manual",className:"mr-2 cursor-pointer"}),(0,s.jsx)("label",{htmlFor:"manual",className:"cursor-pointer select-none",children:"Cadastrar aditivo existente"})]})]}),"new"===d&&t.documentoInvestidor.length>11&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"mb-3 text-xl",children:"Dados do Representante Legal"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 mb-5",children:[(0,s.jsx)(w.Z,{id:"",label:"Nome",name:"",type:"text",value:v.ownerName,onChange:e=>{j({...v,ownerName:e.target.value})}}),(0,s.jsx)(w.Z,{id:"",label:"CPF",name:"",type:"text",value:v.ownerCpf,onChange:e=>{j({...v,ownerCpf:(0,m.VL)(e.target.value)})}})]})]}),(0,s.jsx)("p",{className:"mb-3 text-xl",children:"Dados de Investimento"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(w.Z,{id:"",label:"Valor do investimento",name:"",type:"text",value:v.value,onChange:e=>{j({...v,value:(0,m.Ht)(e.target.value)})}}),(0,s.jsx)(w.Z,{id:"",label:"Perfil investidor",name:"",type:"text",value:v.profile,onChange:e=>{j({...v,profile:e.target.value})}}),(0,s.jsx)(w.Z,{id:"",label:"Data da aplica\xe7\xe3o",name:"",type:"date",value:v.date,min:"exist"!==d?A().utc().format("YYYY-MM-DD"):void 0,max:"exist"===d?A().utc().format("YYYY-MM-DD"):void 0,onChange:e=>{j({...v,date:e.target.value})}})]})]}),(0,s.jsxs)("div",{className:"mb-10",children:[(0,s.jsx)("p",{className:"mb-3 text-xl",children:"Dados bancarios"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(w.Z,{id:"",label:"Nome do banco",name:"",type:"text",value:v.bank,onChange:e=>{j({...v,bank:e.target.value})}}),(0,s.jsx)(w.Z,{id:"",label:"Conta",name:"",type:"text",value:v.accountNumber,onChange:e=>{j({...v,accountNumber:e.target.value})}}),(0,s.jsx)(w.Z,{id:"",label:"Ag\xeancia",name:"",type:"text",value:v.agency,onChange:e=>{j({...v,agency:e.target.value})}}),(0,s.jsx)(w.Z,{id:"",label:"Chave pix",name:"",type:"text",value:v.pix,onChange:e=>{j({...v,pix:e.target.value})}})]})]}),(0,s.jsxs)("div",{className:"w-10/12",children:[(0,s.jsx)("p",{children:"Observa\xe7\xf5es"}),(0,s.jsx)("textarea",{value:v.comment,onChange:e=>{let{target:t}=e;return j({...v,comment:t.value})},className:"w-full text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 p-2 mt-2"})]})]}),"exist"===d&&(0,s.jsxs)("div",{className:"md:flex-row flex flex-col gap-2 mb-10 text-white jus  mt-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Aditivo"}),(0,s.jsx)(p.Z,{onFileUploaded:c})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Comprovante de pagamento"}),(0,s.jsx)(p.Z,{onFileUploaded:l})]})]}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[(0,s.jsx)("div",{children:(0,s.jsx)(b.Z,{label:"Criar aditivo",loading:u,className:"bg-orange-linear",disabled:u,handleSubmit:()=>{"new"===d?createAditive():registerAditiveExists()}})}),(0,s.jsx)("div",{children:(0,s.jsx)(b.Z,{label:"Fechar",loading:!1,handleSubmit:()=>a(!1)})})]})]})})}var k=a(3256),D=a(5838),Z=a(8647);function AditiveData(e){let{contract:t,aditives:a,getAditives:n}=e,[l,c]=(0,o.useState)(),[d,x]=(0,o.useState)(),[m,p]=(0,o.useState)(!1),h=(0,k.e)();(0,o.useEffect)(()=>{d&&l&&onUploadFile()},[d,l]);let v=(0,o.useCallback)(e=>{c(e)},[]),onUploadFile=()=>{if(!l)return f.Am.warning("Selecione um comprovante para anexar!");p(!0),f.Am.info("Enviando comprovante...");let e=new FormData;e.append("addendumId",String(d)),l&&e.append("proofPayment",l[0]),r.Z.post("/contract/addendum/proof-payment",e).then(e=>{f.Am.success("Comprovante anexado com sucesso!"),n(),c(void 0)}).catch(e=>{(0,y.Z)(e,"Erro ao enviar comprovante")}).finally(()=>{p(!1)})},{getRootProps:j,getInputProps:b}=(0,Z.uI)({onDrop:v,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&f.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{c(void 0)},disabled:m}),returnAddendumFiles=e=>{let{files:t,type:a}=e,n=t.filter(e=>e.type===a)[0];return n?(0,s.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,s.jsx)(i.Z,{className:"cursor-pointer",onClick:()=>{window.open(n.url,"_blank")},color:"#fff",width:20})}):n||"PAYMENT"!==a||"broker"!==h.name&&"advisor"!==h.name?(0,s.jsx)("p",{className:"w-full flex items-center justify-center",children:(0,s.jsx)(D.Z,{color:"#FF9900",width:20})}):(0,s.jsxs)("div",{...j(),children:[(0,s.jsx)("input",{...b(),disabled:m,accept:".png,.jpg,.pdf"}),(0,s.jsx)("p",{className:"w-full flex items-center justify-center bg-orange-linear py-1 rounded-lg text-sm ".concat(m?"opacity-50":"cursor-pointer"),children:"Anexar"})]})};return(0,s.jsx)("div",{className:"mt-5",children:(0,s.jsxs)("table",{className:"w-full relative min-h-20",children:[(0,s.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,s.jsxs)("tr",{className:"w-full py-2",children:[(0,s.jsx)("th",{className:"min-w-[100px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Valor"})}),(0,s.jsx)("th",{className:"min-w-[150px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Rendimento"})}),(0,s.jsx)("th",{className:"min-w-[250px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Consultor"})}),(0,s.jsx)("th",{className:"min-w-[150px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Criado em"})}),(0,s.jsx)("th",{className:"min-w-[100px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Assinatura"})}),(0,s.jsx)("th",{className:"min-w-[100px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Contrato"})}),(0,s.jsx)("th",{className:"min-w-[120px]",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Comprovante"})})]})}),a.length>=1?(0,s.jsx)("tbody",{className:"w-full",children:a.map((e,a)=>(0,s.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-xs text-center",children:Number(e.value||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),(0,s.jsx)("td",{children:(0,s.jsxs)("p",{className:"text-xs text-center",children:[e.yieldRate||"0","%"]})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-xs text-center",children:t.consultorResponsavel})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-xs text-center",children:(0,u.Z)(e.applicationDate)})}),(0,s.jsx)("td",{className:"select-none",children:(0,s.jsx)(g.Z,{description:(0,N.XW)(e.status).description,text:(0,N.XW)(e.status).title,textColor:(0,N.XW)(e.status).textColor})}),(0,s.jsx)("td",{children:returnAddendumFiles({files:null==e?void 0:e.addendumFiles,type:"ADDENDUM"})}),(0,s.jsx)("td",{onClick:()=>x(e.id),children:returnAddendumFiles({files:null==e?void 0:e.addendumFiles,type:"PAYMENT",addendumId:e.id})})]},a))}):(0,s.jsx)("div",{className:"text-center mt-5 absolute w-full",children:(0,s.jsx)("p",{children:"Nenhum dado encontrado"})})]})})}var P=a(8610),I=a(8700),M=a(7152),R=a(8928);function ModalContract(e){let{contract:t,setModal:a,setRenew:n,setModalPayment:l}=e,[i,c]=(0,o.useState)(0),[d,x]=(0,o.useState)(!1),[m,u]=(0,o.useState)([]),[p,h]=(0,o.useState)(!1),{navigation:v}=(0,P.H)(),[g,b]=(0,o.useState)(!1),[w,C]=(0,o.useState)(""),[S,F]=(0,o.useState)(!1),A=(0,k.e)(),getAditives=()=>{r.Z.get("/contract/".concat(t.idContrato,"/addendum")).then(e=>{u(e.data.addendums)}).catch(e=>{var t,a;f.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o conseguimos carregar os aditivos do contrato.")})};return(0,o.useEffect)(()=>{!1===p&&getAditives()},[p]),(0,s.jsxs)("div",{className:"",children:[(0,s.jsxs)("div",{className:"w-full text-white overflow-auto",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center",children:(0,s.jsx)(j.Z,{color:"#000",width:20})}),(0,s.jsx)("div",{className:"gap-y-1 flex flex-col",children:(0,s.jsx)("p",{className:"font-bold text-xs",children:"Detalhes do Contrato"})})]}),(0,s.jsxs)("div",{className:"w-full flex flex-wrap mt-4 gap-4 justify-start",children:[(0,s.jsx)("div",{className:"cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ".concat(0===i?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>c(0),children:(0,s.jsx)("p",{className:"md:text-sm text-xs",children:"Dados do Contrato"})}),(0,s.jsx)("div",{className:"cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ".concat(1===i?"bg-zinc-800 text-[#FF9900]":""),onClick:()=>c(1),children:(0,s.jsx)("p",{className:"md:text-sm text-xs",children:"Aditivos"})})]}),g?(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-2 mt-5",children:"Digite o motivo da exclus\xe3o do contrato"}),(0,s.jsx)(M.Z,{name:"",value:w,setValue:C,className:"h-20"})]}):0===i?(0,s.jsx)(ContractData,{contract:t,loading:d,resendContract:()=>{x(!0),r.Z.post("/contract/send-notification/".concat(t.idContrato)).then(e=>{f.Am.success("Contrato encaminhado novamente para o investidor.")}).catch(e=>{var t,a;f.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o conseguimos encaminhar o contrato para o investidor.")}).finally(()=>x(!1))},setAditive:h,setModal:a,setModalPayment:l,setRenew:n}):(0,s.jsx)("div",{className:"min-h-[300px]",children:(0,s.jsx)(AditiveData,{contract:t,aditives:m,getAditives:getAditives})})]}),(0,s.jsxs)("div",{className:"w-full flex mt-10 gap-2 justify-start",children:[t.statusContrato===N.rd.AWAITING_AUDIT&&!g&&(0,s.jsx)(I.z,{onClick:()=>{v("/contratos/contrato/".concat(t.idContrato))},children:"Auditar contrato"}),g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(I.z,{loading:S,disabled:S,variant:"destructive",onClick:()=>{if(""===w)return f.Am.warn("Digite o motivo para excluir o contrato");F(!0),r.Z.post("/contract/".concat(t.idContrato,"/delete"),{role:A.roleId,reason:w}).then(e=>{f.Am.success("Contrato excluido com sucesso!"),C(""),b(!1),setTimeout(()=>{window.location.reload()},2e3)}).catch(e=>{(0,y.Z)(e,"Erro ao excluir contrato!")}).finally(()=>{F(!1)})},children:"Confirmar"}),(0,s.jsx)(I.z,{disabled:S,variant:"secondary",onClick:()=>{b(!1),C("")},children:"Fechar"})]}):(0,R.f)(t.statusContrato)?(0,s.jsx)(I.z,{variant:"destructive",onClick:()=>b(!0),children:"Excluir Contrato"}):void 0]}),p&&t&&(0,s.jsx)(AditiveContract,{contract:t,setOpenModal:h})]})}function AddPayment(e){let{contract:t,setOpenModal:a,documentSearch:n,getContracts:l}=e,[i,c]=(0,o.useState)(),[d,x]=(0,o.useState)(!1),renewContract=async()=>{if(!i)return f.Am.warning("Selecione um comprovante para anexar!");x(!0);let e=new FormData;e.append("contractId",t.idContrato),i&&e.append("file",i[0]),r.Z.put("/contract/upload/proof-payment",e).then(e=>{f.Am.success("Comprovante anexado com sucesso!"),x(!1),a(!1),setTimeout(()=>{window.location.reload()},1e3)}).catch(e=>{var t,a;f.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o foi possivel anexar o comprovante a esse contrato"),x(!1)})};return(0,s.jsx)("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"md:w-3/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[(0,s.jsx)("p",{className:"text-lg font-bold",children:"Anexar comprovante"}),(0,s.jsx)("p",{className:"text-xs",children:"*Anexe neste campo o comprovante de Pagamento do seu\xa0Investidor"}),(0,s.jsx)("div",{className:"flex gap-2 mb-10 text-white items-start justify-center mt-5",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"mb-1",children:"Comprovante"}),(0,s.jsx)(p.Z,{onFileUploaded:c})]})}),(0,s.jsxs)("div",{className:"flex w-full mt-10 justify-between gap-10",children:[(0,s.jsx)(b.Z,{label:"Anexar",loading:d,className:"bg-orange-linear",handleSubmit:renewContract}),(0,s.jsx)("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer flex items-center",onClick:()=>a(!1),children:(0,s.jsx)("p",{className:"text-sm",children:"Fechar"})})]})]})})}var E=a(5465),Y=a(3401),L=a(9891),T=a(7626),U=a(439),z=a(6553),O=a(9784),q=a(2359);function Screen(e){var t,a,p;let{initialSignatarie:h,initialPage:v="1",initialType:f="all",initialStartData:j="",initialEndData:b="",initialStatus:w="Todos"}=e,C=(0,x.useRouter)(),y=(0,x.useSearchParams)(),[S,F]=(0,o.useState)(),[A,D]=(0,o.useState)(!1),[Z,P]=(0,o.useState)(!1),[I,M]=(0,o.useState)(!1),[R,V]=(0,o.useState)(!1),[B,_]=(0,o.useState)(w),[W,K]=(0,o.useState)(Number(v)||1),[X,J]=(0,o.useState)(h||""),H=(0,U.Nr)(X,300),[G,Q]=(0,o.useState)({startData:j,endData:b,type:f,status:w}),[$,ee]=(0,o.useState)(!1),et=(0,k.e)(),{data:ea,isLoading:es}=(0,L.a)({queryKey:T.U.CONTRACTS(W,H,B,G,et.roleId),queryFn:async()=>{var e;console.log("API call dateTo:",G.endData);let t=await r.Z.get(returnRoute(),{params:{roleId:et.roleId,limit:"10",page:W,status:"Todos"===B?void 0:B,signatarie:H?(0,m.p4)(H):void 0,dateFrom:""===G.startData?void 0:G.startData,dateTo:""===G.endData?void 0:G.endData,contractType:"all"===G.type?void 0:G.type}}),a=Number(null===(e=t.data)||void 0===e?void 0:e.totalPaginas)||1;return W>a?(K(1),{data:{documentos:[],totalPaginas:a,total:0}}):t},staleTime:6e4});(0,o.useEffect)(()=>{if(localStorage.setItem("typeCreateContract",""),h){let e=new URLSearchParams(y.toString());e.set("signatarie",h),e.set("page","1"),C.replace("?".concat(e.toString()))}},[h,C,y]);let returnRoute=()=>{switch(et.name){case"admin":return"/admin/list-contracts";case"superadmin":return"/contract/list-contracts/superadmin";default:return""}},translateTag=e=>(null==e?void 0:e.toUpperCase())==="P2P"?"MUTUO":e.toUpperCase(),handleSearch=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{...G,status:B},a=new URLSearchParams(y.toString());a.set("signatarie",e),a.set("page","1"),a.set("type",t.type||"all"),a.set("startData",t.startData||""),a.set("endData",t.endData||""),a.set("status",t.status||"Todos"),console.log("handleSearch params:",a.toString()),C.replace("?".concat(a.toString()))};return(0,s.jsx)("div",{children:(0,s.jsxs)(q.yo,{children:[(0,s.jsx)(n.Z,{}),(0,s.jsx)(l.Z,{children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:(0,s.jsx)("h1",{className:"m-auto font-bold text-2xl",children:"Contratos"})}),(0,s.jsx)(E.Z,{children:(0,s.jsxs)("div",{className:"w-full p-2 gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-1 w-full justify-between items-center gap-4 mb-2",children:[("advisor"===et.name||"broker"===et.name)&&(0,s.jsx)("div",{className:"w-32 h-10 bg-orange-linear px-10 flex items-center justify-center md:mr-5 mb-2 md:mb-0 rounded-lg cursor-pointer",onClick:()=>{"advisor"===et.name?(localStorage.setItem("typeCreateContract","broker"),C.push("/meus-contratos/registro-manual")):ee(!0)},children:(0,s.jsx)("p",{children:"Criar"})}),(0,s.jsxs)("div",{className:"flex items-center gap-4 w-full justify-end",children:[(0,s.jsx)("div",{className:"md:w-3/12",children:(0,s.jsx)(d.Z,{isDocument:!0,handleSearch:()=>handleSearch(X),setValue:e=>{J(e);let t=new URLSearchParams(y.toString());""===e?t.delete("signatarie"):t.set("signatarie",e),t.set("page","1"),C.replace("?".concat(t.toString()))},placeholder:"Pesquisar por CPF/CNPJ",value:X})}),(0,s.jsx)(z.Z,{activeModal:A,setActiveModal:D,filterData:{...G,status:B},setFilterData:e=>{Q({startData:e.startData,endData:e.endData,type:e.type,status:e.status}),_(e.status)},handleSearch:handleSearch,setPage:K,signatarie:X})]})]}),(0,s.jsx)(Y.Z,{data:(null==ea?void 0:null===(t=ea.data)||void 0===t?void 0:t.documentos)||[],headers:[{title:"",component:"id",width:"30px",render:(e,t)=>(0,s.jsx)("div",{className:"cursor-pointer",onClick:()=>{P(!0),F(t)},children:(0,s.jsx)(q.aM,{children:(0,s.jsx)(i.Z,{color:"#fff",width:20})})})},{title:"Investidor",component:"nomeInvestidor",width:"150px"},{title:"CPF/CNPJ",component:"document",position:"center",width:"150px",render:(e,t)=>(0,s.jsx)("p",{className:"text-center",children:(0,m.p4)(t.documentoInvestidor||"").length<=11?(0,m.VL)(t.documentoInvestidor||""):(0,m.PK)(t.documentoInvestidor||"")})},{title:"Valor",component:"valorInvestimento",position:"center",render:(e,t)=>(0,s.jsx)("p",{className:"text-center",children:(0,O.F)(t)})},{title:"Rendimento",component:"rendimentoInvestimento",position:"center",render:e=>(0,s.jsxs)("p",{className:"text-center",children:[String(e)||"0","%"]})},{title:"Consultor",component:"consultorResponsavel",position:"center",width:"100px"},{title:"Criado em",component:"inicioContrato",position:"center",render:e=>(0,s.jsx)("p",{className:"text-center",children:(0,u.Z)(String(e))})},{title:"Status",component:"statusContrato",position:"center",width:"100px",render:(e,t)=>(0,s.jsx)(g.Z,{description:(0,N.mP)(t.statusContrato).description,text:(0,N.mP)(t.statusContrato).title,textColor:(0,N.mP)(t.statusContrato).textColor})},{title:"Modelo",component:"inicioContrato",position:"center",render:(e,t)=>(0,s.jsx)("div",{className:"px-2",children:(0,s.jsx)("div",{className:"bg-white py-[5px] px-[10px] rounded-md text-center",children:(0,s.jsx)("p",{className:"text-xs text-[#FF9900] font-bold",children:translateTag(t.tags)||"NE"})})})}],loading:es,pagination:{page:W,lastPage:Number(null==ea?void 0:null===(a=ea.data)||void 0===a?void 0:a.totalPaginas)||1,perPage:10,setPage:e=>{K(e);let t=new URLSearchParams(y.toString());t.set("page",String(e)),C.replace("?".concat(t.toString()))},totalItems:String((null==ea?void 0:null===(p=ea.data)||void 0===p?void 0:p.total)||0)}})]})}),(0,s.jsx)(q.ue,{className:"w-full md:w-[500px]",children:(0,s.jsx)(ModalContract,{contract:S,setRenew:V,setModal:P,setModalPayment:M})}),I&&S&&(0,s.jsx)(AddPayment,{contract:S,setOpenModal:M,documentSearch:X,getContracts:handleSearch}),R&&S&&(0,s.jsx)(RenewContract,{contract:S,setOpenModal:V}),$&&(0,s.jsx)("div",{className:"fixed z-40 top-0 left-0 w-full h-full bg-[#3A3A3AAB]",children:(0,s.jsxs)("div",{className:"absolute w-3/6 bg-[#1C1C1C] z-50 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] border border-[#FF9900] rounded-lg",children:[(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center border-b border-[#FF9900]",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Individual"}),(0,s.jsx)("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com negocia\xe7\xe3o exclusiva para voc\xea."})]}),(0,s.jsx)("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer animate-moveXRight",onClick:()=>{localStorage.setItem("typeCreateContract","broker"),C.push("/meus-contratos/registro-manual")},children:(0,s.jsx)(c.Z,{width:20})})]}),(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Compartilhado"}),(0,s.jsx)("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com a negocia\xe7\xe3o personalizada de m\xfaltiplos assessores."})]}),(0,s.jsx)("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer",onClick:()=>{localStorage.setItem("typeCreateContract","advisors"),C.push("/meus-contratos/registro-manual")},children:(0,s.jsx)(c.Z,{width:20})})]})]})})]})})]})})}},3220:function(e,t,a){"use strict";var s=a(7437),n=a(2265),l=a(1543),o=a(9367);let r=(0,n.forwardRef)((e,t)=>{let{label:a,bg:r,type:i,...c}=e,[d,x]=(0,n.useState)(!1);return(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white mb-1",children:a}),(0,s.jsxs)("div",{className:"custom-input-wrapper h-12 w-full text-white rounded-xl ring-[#FF9900] ring-1 ".concat("month"===i?"":"ring-inset"," ").concat("transparent"===r?"bg-black":"bg-[#1C1C1C]"," flex-1 flex relative"),children:[(0,s.jsx)("input",{ref:t,type:d&&"password"===i?"text":i,...c,className:"w-full h-12 flex-1 px-4 bg-transparent rounded-xl"}),"password"===i&&(0,s.jsx)("div",{className:"mr-2 cursor-pointer absolute right-0 top-[50%] translate-y-[-50%]",onClick:()=>x(!d),children:d?(0,s.jsx)(l.Z,{width:20}):(0,s.jsx)(o.Z,{width:20})})]})]})});r.displayName="Input",t.Z=r},9367:function(e,t,a){"use strict";var s=a(2265);let n=s.forwardRef(function({title:e,titleId:t,...a},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=n},1543:function(e,t,a){"use strict";var s=a(2265);let n=s.forwardRef(function({title:e,titleId:t,...a},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});t.Z=n}},function(e){e.O(0,[6990,7326,8276,5371,6946,9891,1306,6955,3151,46,2971,7864,1744],function(){return e(e.s=6221)}),_N_E=e.O()}]);