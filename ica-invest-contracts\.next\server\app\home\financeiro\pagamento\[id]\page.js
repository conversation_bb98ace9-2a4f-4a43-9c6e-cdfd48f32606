(()=>{var e={};e.id=7048,e.ids=[7048],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},1819:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>x,routeModule:()=>u,tree:()=>d});var s=a(73137),r=a(54647),n=a(4183),l=a.n(n),o=a(71775),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);a.d(t,i);let c=s.AppPageRouteModule,d=["",{children:["home",{children:["financeiro",{children:["pagamento",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,64235)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamento\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\financeiro\\pagamento\\[id]\\page.tsx"],m="/home/<USER>/pagamento/[id]/page",p={require:a,loadChunk:()=>Promise.resolve()},u=new c({definition:{kind:r.x.APP_PAGE,page:"/home/<USER>/pagamento/[id]/page",pathname:"/home/<USER>/pagamento/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},119:(e,t,a)=>{Promise.resolve().then(a.bind(a,98436))},98436:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Payment});var s=a(60080),r=a(97669),n=a(47956),l=a(52451),o=a.n(l),i=a(68012),c=a(9885),d=a(99986),x=a(57114),m=a(52274),p=a(64731),u=a.n(p),h=a(96413),j=a(92170),b=a(34751),v=a(85814),g=a(74644),f=a(24577),N=a(30475),y=a(20161),w=a(69888),P=a(33050),C=a(26050),D=a(5193),F=a(90682);function Payment({params:e}){let[t,a]=(0,c.useState)(""),[l,p]=(0,c.useState)("e"),[V,_]=(0,c.useState)(!1),[q,Z]=(0,c.useState)(!1),[A,R]=(0,c.useState)(!0),[L,k]=(0,c.useState)(),[S,B]=(0,c.useState)(),I=(0,x.useRouter)(),E=(0,F.e)(),DataValues=({label:e,value:t})=>(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-bold text-sm",children:e}),s.jsx(g.Z,{maxWidth:"200px",minWidth:"80px",loading:A,height:"25px",children:s.jsx("p",{className:" text-md",children:t})})]}),getPaymentData=()=>{b.Am.info("Buscando dados do pagamento!",{toastId:"search",autoClose:!1}),v.Z.get(`/income-payment-scheduled/${e.id}`).then(e=>{B(e.data),e.data?.status==="PAID"?p("p"):p("e"),b.Am.dismiss("search")}).catch(e=>{b.Am.error("Tivemos um erro ao buscar os dados do agendamento!",{toastId:"agendamento"})}).finally(()=>R(!1))};(0,c.useEffect)(()=>{getPaymentData()},[]);let M=(0,c.useCallback)(e=>{k(e);let t=URL.createObjectURL(e[0]);a(t)},[]),{getRootProps:U,getInputProps:$}=(0,j.uI)({onDrop:M,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&b.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{a("")}});return(0,s.jsxs)("div",{className:`${V?"fixed w-full":"relative"}`,children:[s.jsx(r.Z,{}),s.jsx(n.Z,{children:(0,s.jsxs)("div",{className:"w-full text-white",children:[(0,s.jsxs)("div",{className:"w-full text-white flex md:flex-row flex-col flex-wrap gap-8 justify-start",children:[(0,s.jsxs)("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[s.jsx("p",{className:"my-4 font-bold text-xl",children:"Valor a ser Pago"}),s.jsx("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:s.jsx("div",{className:"flex w-full items-center justify-center py-3",children:s.jsx(g.Z,{loading:A,height:"25px",width:"100px",children:s.jsx("p",{className:"font-bold text-xl text-[#FF9900]",children:Number(S?.paymentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})})})]}),(0,s.jsxs)("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[s.jsx("p",{className:"my-4 font-bold text-xl",children:"Pagamento"}),s.jsx("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:s.jsx("div",{className:"flex w-full items-center justify-center py-3",children:s.jsx(g.Z,{loading:A,height:"25px",width:"100px",children:(0,s.jsxs)("p",{className:"font-bold text-xl text-[#FF9900]",children:["Dia ",S?.paymentDate?u()(S?.paymentDate).format("DD"):"00"]})})})})]}),"p"===l?(0,s.jsxs)("div",{onClick:()=>{S?.investorPayment.payment&&window.open(S?.investorPayment.payment.file,"_blank")},className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center flex flex-col items-center justify-center cursor-pointer",children:[s.jsx("p",{className:"mb-5 font-bold text-lg select-none",children:"Ultimo Comprovante"}),s.jsx(N.Z,{width:40,color:"#fff"})]}):(0,s.jsxs)("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center flex flex-col items-center justify-center cursor-pointer",...U(),children:[s.jsx("p",{className:"mb-5 font-bold text-lg mx-3 select-none",children:t?"Comprovante anexado":"Anexar Comprovante"}),s.jsx("input",{type:"text",...$()}),t?s.jsx(y.Z,{width:40,color:"#fff"}):s.jsx(o(),{className:"select-none",src:i.Z,width:30,alt:""})]})]}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"my-5",children:"p"===l?s.jsx("div",{className:"bg-[#1ef97d31] w-28 border border-[#1EF97C] rounded-lg",children:s.jsx("p",{className:"text-center text-[#1EF97C] p-1",children:"Pago"})}):s.jsx("div",{className:"bg-[#ffb3382a] w-28 border border-[#FFB238] rounded-lg",children:s.jsx("p",{className:"text-center text-[#FFB238] p-1",children:"Pendente"})})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("p",{className:"text-xl font-bold",children:"Dados Pessoais"}),s.jsx("div",{className:"mb-3 mt-2",children:s.jsx(DataValues,{label:"Nome",value:S?.investorPayment.name||"N\xe3o encontrado"})}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3",children:[s.jsx(DataValues,{label:"E-mail",value:S?.investorPayment.email||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"CPF",value:(0,h.VL)(S?.investorPayment.document||"N\xe3o encontrado")}),S?.advisors.map((e,t)=>s.jsx(DataValues,{label:`Consultor ${t+1}`,value:e.name||"N\xe3o encontrado"},t))]}),s.jsx("div",{children:s.jsx(DataValues,{label:"Broker",value:S?.broker||"N\xe3o encontrado"})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es sobre o Investimento"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2 items-center",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-bold text-sm",children:"Valor Investido"}),s.jsx(g.Z,{maxWidth:"200px",minWidth:"80px",loading:A,height:"25px",children:(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[s.jsx("p",{className:" text-md",children:Number(S?.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),s.jsx("div",{className:"cursor-pointer",children:s.jsx(o(),{alt:"",src:C.Z,width:15})})]})})]}),s.jsx("div",{className:"flex bg-orange-linear px-3 py-2 rounded-lg items-center cursor-pointer ml-[-15px]",onClick:()=>{_(!0)},children:s.jsx("p",{className:"font-bold text-sm",children:"Ver mais"})}),S?.addendsProRata&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(DataValues,{label:"Pro Rata",value:Number(S?.addendsProRataValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-bold text-sm",children:"Valor Total"}),s.jsx(g.Z,{maxWidth:"200px",minWidth:"80px",loading:A,height:"25px",children:s.jsx("p",{className:"text-md text-[#FF9900]",children:(Number(S?.investmentValue||0)+(S?.addendsProRataValue||0)).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]}),s.jsx(DataValues,{label:"Porcentagem",value:`${S?.investmentYield||"N\xe3o encontrado"}%`}),s.jsx(DataValues,{label:"Rentabilidade",value:Number(S?.paymentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es Banc\xe1rias"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2",children:[s.jsx(DataValues,{label:"Banco",value:S?.investorPayment.account.bank||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"Ag\xeancia",value:S?.investorPayment.account.agency||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"Conta",value:S?.investorPayment.account.account||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"Chave PIX",value:S?.investorPayment.account.pixKey||"N\xe3o encontrado"})]})]}),(0,s.jsxs)("div",{className:"mb-6 w-[30%]",children:[s.jsx("div",{children:s.jsx("p",{className:"text-xl font-bold mb-3",children:"Anexos"})}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-end mb-20",children:[S?.attachments.contract&&s.jsx("div",{children:s.jsx(D.Z,{file:S?.attachments.contract,title:"Contrato"})}),S?.attachments.residenceProof&&s.jsx("div",{children:s.jsx(D.Z,{file:S?.attachments.residenceProof,title:"Comprovante de resid\xeancia"})}),S?.attachments.rg&&s.jsx("div",{children:s.jsx(D.Z,{file:S?.attachments.rg,title:"RG"})})]})]})]}),(0,s.jsxs)("div",{className:"w-full flex flex-wrap gap-8 justify-end mb-4",children:["p"!==l&&s.jsx("div",{children:s.jsx(d.Z,{label:"Aprovar pagamento",className:"bg-orange-linear",loading:q,handleSubmit:()=>{if(Z(!0),!t)return b.Am.warning("Para aprovar o pagamento \xe9 preciso anexar o comprovante!");let e=new FormData;L&&e.append("proof",L[0]),e.append("paymentId",S?.investorPayment.id||""),b.Am.info("Aprovando o pagamento...",{toastId:"aprovando-pagamento"}),v.Z.post("/income-payment",e,{headers:{RoleId:E.roleId}}).then(e=>{b.Am.success("Pagamento aprovado!"),b.Am.dismiss("aprovando-pagamento"),I.back()}).catch(e=>{(0,f.Z)(e,"N\xe3o foi poss\xedvel aprovar o pagamento!")}).finally(()=>Z(!1))}})}),s.jsx("div",{children:s.jsx(d.Z,{label:"Voltar",handleSubmit:()=>{I.back()},loading:!1})})]})]})]})}),s.jsx("div",{children:s.jsx(m.Z,{width:"7/12",openModal:V,setOpenModal:_,children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-5",children:[s.jsx("p",{className:"mb-2",children:"Dados do contrato principal."}),(0,s.jsxs)("table",{className:"border border-[#FF9900] w-full",children:[(0,s.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[s.jsx("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Rendimento"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),(0,s.jsxs)("tr",{className:"text-xs",children:[s.jsx("td",{className:"text-center px-4 py-2",children:Number(S?.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,s.jsxs)("td",{className:"text-center px-4 py-2",children:[S?.investmentYield,"%"]}),s.jsx("td",{className:"text-center px-4 py-2",children:(0,P.Z)(S?.contractStartDate||"")}),s.jsx("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>window.open(S?.attachments.contract,"_blanck"),children:"ver mais"})]})]})]}),S?.addendumData&&S?.addendumData.length>0&&(0,s.jsxs)("div",{children:[s.jsx("div",{className:"mb-2",children:s.jsx("p",{children:"Contratos aditivos adicionados no c\xe1lculo"})}),(0,s.jsxs)("table",{className:" border border-[#FF9900] ",children:[(0,s.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[s.jsx("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Valor rentabilizado"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Dias rentabilizados"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),S?.addendumData.map(e=>s.jsxs("tr",{className:"text-xs",children:[s.jsx("td",{className:"text-center px-4 py-2",children:Number(e?.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),s.jsx("td",{className:"text-center px-4 py-2",children:P.Z(e.date)}),s.jsx("td",{className:"text-center px-4 py-2",children:e.totalAmount?Number(e?.totalAmount||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"}):"-"}),s.jsx("td",{className:"text-center px-4 py-2",children:`${e.validDays} dias`}),e?.contractUrl?s.jsx("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>window.open(e.contractUrl,"_blanck"),children:"ver mais"}):s.jsx("td",{className:"flex justify-center",children:s.jsx(w.Z,{className:"mt-1",color:"#FF9900",width:20})})]},e.id))]})]})]})})})]})}},99986:(e,t,a)=>{"use strict";a.d(t,{Z:()=>Button});var s=a(60080),r=a(69957);function Button({handleSubmit:e,loading:t,label:a,disabled:n,className:l,...o}){return s.jsx(r.z,{...o,onClick:e,loading:t,disabled:n,className:l,children:a})}},64235:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>l,__esModule:()=>n,default:()=>i});var s=a(17536);let r=(0,s.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\home\financeiro\pagamento\[id]\page.tsx`),{__esModule:n,$$typeof:l}=r,o=r.default,i=o}};var t=require("../../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,5081,7207,278,7669,658],()=>__webpack_exec__(1819));module.exports=a})();