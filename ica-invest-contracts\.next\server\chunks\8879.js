"use strict";exports.id=8879,exports.ids=[8879],exports.modules={54986:(e,r,i)=>{i.d(r,{m:()=>isUnderage});var t=i(64731),a=i.n(t);function isUnderage(e){let r=a()(e),i=a()().diff(r,"years");return i<18}},88879:(e,r,i)=>{i.d(r,{WF:()=>d,_n:()=>u,bs:()=>o});var t=i(50298),a=i(96413);let o=t.Ry().shape({document:t.Z_().required("Obrigat\xf3rio"),email:t.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),value:t.Z_().required("Obrigat\xf3rio"),term:t.Z_().required("Obrigat\xf3rio"),modality:t.Z_().required("Obrigat\xf3rio"),yield:t.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchaseWith:t.Z_().required("Obrigat\xf3rio"),amountQuotes:t.Z_().default(""),startContract:t.Z_().required("Obrigat\xf3rio"),endContract:t.Z_().required("Obrigat\xf3rio"),profile:t.Z_().required("Obrigat\xf3rio"),details:t.Z_().notRequired(),debenture:t.Z_().required("Obrigat\xf3rio")}).required();t.Ry().shape({name:t.Z_().required("Obrigat\xf3rio"),rg:t.Z_().required("Obrigat\xf3rio"),document:t.Z_().required("Obrigat\xf3rio"),phoneNumber:t.Z_().min(15,"N\xfamero de telefone inv\xe1lido!").max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:t.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,t,a]=e.split("-");if(!r||!t||!a||4!==r.length)return!1;let o=Number(r);if(isNaN(o)||o<1900||o>new Date().getFullYear())return!1;let d=new Date(e);return!(isNaN(d.getTime())||i(54986).m(e))}),email:t.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:t.Z_().required("Obrigat\xf3rio"),neighborhood:t.Z_().required("Obrigat\xf3rio"),state:t.Z_().required("Obrigat\xf3rio"),city:t.Z_().required("Obrigat\xf3rio"),complement:t.Z_().default(""),number:t.Z_().required("Obrigat\xf3rio"),value:t.Z_().required("Obrigat\xf3rio"),term:t.Z_().required("Obrigat\xf3rio"),modality:t.Z_().required("Obrigat\xf3rio"),yield:t.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),observations:t.Z_().default(""),purchaseWith:t.Z_().required("Obrigat\xf3rio"),amountQuotes:t.Z_(),initDate:t.Z_().required("Obrigat\xf3rio"),endDate:t.Z_().required("Obrigat\xf3rio"),gracePeriod:t.Z_().required("Obrigat\xf3rio"),profile:t.Z_().required("Obrigat\xf3rio"),bank:t.Z_().required("Obrigat\xf3rio"),accountNumber:t.Z_().required("Obrigat\xf3rio"),agency:t.Z_().required("Obrigat\xf3rio"),pix:t.Z_().required("Obrigat\xf3rio"),debenture:t.Z_().required("Obrigat\xf3rio"),motherName:t.Z_(),placeOfBirth:t.Z_(),occupation:t.Z_(),issuer:t.Z_(),testifyPrimaryName:t.Z_(),testifyPrimaryCpf:t.Z_(),testifySecondaryName:t.Z_(),testifySecondaryCpf:t.Z_(),companyAddress:t.Z_(),companyCity:t.Z_(),companyUF:t.Z_(),companyType:t.Z_()}).required(),t.Ry().shape({value:t.Z_().required("Obrigat\xf3rio"),profile:t.Z_().required("Obrigat\xf3rio"),yield:t.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),date:t.Z_().required("Obrigat\xf3rio"),bank:t.Z_().required("Obrigat\xf3rio"),accountNumber:t.Z_().required("Obrigat\xf3rio"),agency:t.Z_().required("Obrigat\xf3rio"),pix:t.Z_().required("Obrigat\xf3rio")}),t.Ry().shape({name:t.Z_().required("Obrigat\xf3rio"),document:t.Z_().required("Obrigat\xf3rio"),phoneNumber:t.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:t.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,t,a]=e.split("-");if(!r||!t||!a||4!==r.length)return!1;let o=Number(r);if(isNaN(o)||o<1900||o>new Date().getFullYear())return!1;let d=new Date(e);return!(isNaN(d.getTime())||i(54986).m(e))}),email:t.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:t.Z_().required("Obrigat\xf3rio"),neighborhood:t.Z_().required("Obrigat\xf3rio"),state:t.Z_().required("Obrigat\xf3rio"),city:t.Z_().required("Obrigat\xf3rio"),complement:t.Z_().default(""),number:t.Z_().required("Obrigat\xf3rio"),profile:t.Z_().required("Obrigat\xf3rio"),term:t.Z_().required("Obrigat\xf3rio"),yield:t.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),value:t.Z_().required("Obrigat\xf3rio"),bank:t.Z_().required("Obrigat\xf3rio"),agency:t.Z_().required("Obrigat\xf3rio"),accountNumber:t.Z_().required("Obrigat\xf3rio"),pix:t.Z_().required("Obrigat\xf3rio"),debenture:t.Z_().required("Obrigat\xf3rio"),observations:t.Z_().default(""),details:t.Z_().default(""),initDate:t.Z_().required("Obrigat\xf3rio"),endDate:t.Z_().required("Obrigat\xf3rio"),amountQuotes:t.Z_().default(""),modality:t.Z_().required("Obrigat\xf3rio"),purchaseWith:t.Z_().required("Obrigat\xf3rio"),motherName:t.Z_().when("document",(e,r)=>e[0]&&(0,a.p4)(e[0]).length<=11?r.required("Campo obrigat\xf3rio"):r.notRequired())}).required();let d=t.Ry().shape({isPf:t.O7().default(!1),birthDate:t.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return!1;let r=new Date(e),t=r.getFullYear();return!(isNaN(t)||t<1900||t>new Date().getFullYear()||r>new Date||i(54986).m(e))}),socialName:t.Z_(),isTaxable:t.Z_().required("Obrigat\xf3rio"),fullName:t.Z_().required("Obrigat\xf3rio"),cpf:t.Z_().required("Obrigat\xf3rio"),email:t.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),phoneNumber:t.Z_().required("Obrigat\xf3rio"),motherName:t.Z_().required("Obrigat\xf3rio"),pep:t.Z_().required("Obrigat\xf3rio"),ownerCep:t.Z_().required("Obrigat\xf3rio"),ownerCity:t.Z_().required("Obrigat\xf3rio"),ownerState:t.Z_().required("Obrigat\xf3rio"),ownerNeighborhood:t.Z_().required("Obrigat\xf3rio"),ownerStreet:t.Z_().required("Obrigat\xf3rio"),ownerComplement:t.Z_().notRequired(),ownerNumber:t.Z_().required("Obrigat\xf3rio"),fantasyName:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),cnpj:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),companyName:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessPhoneNumber:t.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),dtOpening:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessEmail:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"):r.notRequired()),type:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),size:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessCep:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessCity:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessState:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessNeighborhood:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessStreet:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),businessComplement:t.Z_().when("isPf",(e,r)=>(e[0],r.notRequired())),businessNumber:t.Z_().when("isPf",(e,r)=>!1===e[0]?r.required("Campo obrigat\xf3rio"):r.notRequired()),participationPercentage:t.Rx().min(0,"O valor m\xednimo \xe9 0").max(15,"O valor m\xe1ximo \xe9 15").typeError("O valor deve ser um n\xfamero v\xe1lido").when("$hide",{is:!0,then:e=>e.notRequired(),otherwise:e=>e.required("Obrigat\xf3rio")})}).required(),u=t.Ry().shape({name:t.Z_().required("Obrigat\xf3rio"),rg:t.Z_().required("Obrigat\xf3rio"),document:t.Z_().required("Obrigat\xf3rio"),phoneNumber:t.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:t.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[r,t,a]=e.split("-");if(!r||!t||!a||4!==r.length)return!1;let o=Number(r);if(isNaN(o)||o<1900||o>new Date().getFullYear())return!1;let d=new Date(e);return!(isNaN(d.getTime())||i(54986).m(e))}),email:t.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:t.Z_().required("Obrigat\xf3rio"),neighborhood:t.Z_().required("Obrigat\xf3rio"),state:t.Z_().required("Obrigat\xf3rio"),city:t.Z_().required("Obrigat\xf3rio"),complement:t.Z_().default(""),number:t.Z_().required("Obrigat\xf3rio"),bank:t.Z_().required("Obrigat\xf3rio"),accountNumber:t.Z_().required("Obrigat\xf3rio"),agency:t.Z_().required("Obrigat\xf3rio"),pix:t.Z_().required("Obrigat\xf3rio"),motherName:t.Z_().required("Obrigat\xf3rio"),placeOfBirth:t.Z_(),occupation:t.Z_(),issuer:t.Z_(),testifyPrimaryName:t.Z_(),testifyPrimaryCpf:t.Z_(),testifySecondaryName:t.Z_(),testifySecondaryCpf:t.Z_()}).required()}};