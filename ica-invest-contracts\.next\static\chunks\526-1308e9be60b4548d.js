(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[526],{4279:function(e,t,n){(e.exports=n(1223)).tz.load(n(6564))},1223:function(e,t,n){var r,o,a;a=function(e){"use strict";void 0===e.version&&e.default&&(e=e.default);var t,n,r={},o={},a={},i={},s={};e&&"string"==typeof e.version||logError("Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/");var u=e.version.split("."),l=+u[0],f=+u[1];function charCodeToInt(e){return e>96?e-87:e>64?e-29:e-48}function unpackBase60(e){var t,n=0,r=e.split("."),o=r[0],a=r[1]||"",i=1,s=0,u=1;for(45===e.charCodeAt(0)&&(n=1,u=-1);n<o.length;n++)s=60*s+(t=charCodeToInt(o.charCodeAt(n)));for(n=0;n<a.length;n++)i/=60,s+=(t=charCodeToInt(a.charCodeAt(n)))*i;return s*u}function arrayToInt(e){for(var t=0;t<e.length;t++)e[t]=unpackBase60(e[t])}function mapIndices(e,t){var n,r=[];for(n=0;n<t.length;n++)r[n]=e[t[n]];return r}function unpack(e){var t=e.split("|"),n=t[2].split(" "),r=t[3].split(""),o=t[4].split(" ");return arrayToInt(n),arrayToInt(r),arrayToInt(o),function(e,t){for(var n=0;n<t;n++)e[n]=Math.round((e[n-1]||0)+6e4*e[n]);e[t-1]=1/0}(o,r.length),{name:t[0],abbrs:mapIndices(t[1].split(" "),r),offsets:mapIndices(n,r),untils:o,population:0|t[5]}}function Zone(e){e&&this._set(unpack(e))}function Country(e,t){this.name=e,this.zones=t}function OffsetAt(e){var t=e.toTimeString(),n=t.match(/\([a-z ]+\)/i);"GMT"===(n=n&&n[0]?(n=n[0].match(/[A-Z]/g))?n.join(""):void 0:(n=t.match(/[A-Z]{3,5}/g))?n[0]:void 0)&&(n=void 0),this.at=+e,this.abbr=n,this.offset=e.getTimezoneOffset()}function ZoneScore(e){this.zone=e,this.offsetScore=0,this.abbrScore=0}function sortZoneScores(e,t){return e.offsetScore!==t.offsetScore?e.offsetScore-t.offsetScore:e.abbrScore!==t.abbrScore?e.abbrScore-t.abbrScore:e.zone.population!==t.zone.population?t.zone.population-e.zone.population:t.zone.name.localeCompare(e.zone.name)}function normalizeName(e){return(e||"").toLowerCase().replace(/\//g,"_")}function addZone(e){var t,n,o,a;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)r[a=normalizeName(n=(o=e[t].split("|"))[0])]=e[t],i[a]=n,function(e,t){var n,r;for(arrayToInt(t),n=0;n<t.length;n++)s[r=t[n]]=s[r]||{},s[r][e]=!0}(a,o[2].split(" "))}function getZone(e,t){var n,a=r[e=normalizeName(e)];return a instanceof Zone?a:"string"==typeof a?(a=new Zone(a),r[e]=a,a):o[e]&&t!==getZone&&(n=getZone(o[e],getZone))?((a=r[e]=new Zone)._set(n),a.name=i[e],a):null}function addLink(e){var t,n,r,a;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)r=normalizeName((n=e[t].split("|"))[0]),a=normalizeName(n[1]),o[r]=a,i[r]=n[0],o[a]=r,i[a]=n[1]}function zoneExists(e){return zoneExists.didShowError||(zoneExists.didShowError=!0,logError("moment.tz.zoneExists('"+e+"') has been deprecated in favor of !moment.tz.zone('"+e+"')")),!!getZone(e)}function needsOffset(e){var t="X"===e._f||"x"===e._f;return!!(e._a&&void 0===e._tzm&&!t)}function logError(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}function tz(t){var n,r=Array.prototype.slice.call(arguments,0,-1),o=arguments[arguments.length-1],a=e.utc.apply(null,r);return!e.isMoment(t)&&needsOffset(a)&&(n=getZone(o))&&a.add(n.parse(a),"minutes"),a.tz(o),a}(l<2||2===l&&f<6)&&logError("Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js "+e.version+". See momentjs.com"),Zone.prototype={_set:function(e){this.name=e.name,this.abbrs=e.abbrs,this.untils=e.untils,this.offsets=e.offsets,this.population=e.population},_index:function(e){var t;if((t=function(e,t){var n,r=t.length;if(e<t[0])return 0;if(r>1&&t[r-1]===1/0&&e>=t[r-2])return r-1;if(e>=t[r-1])return -1;for(var o=0,a=r-1;a-o>1;)t[n=Math.floor((o+a)/2)]<=e?o=n:a=n;return a}(+e,this.untils))>=0)return t},countries:function(){var e=this.name;return Object.keys(a).filter(function(t){return -1!==a[t].zones.indexOf(e)})},parse:function(e){var t,n,r,o,a=+e,i=this.offsets,s=this.untils,u=s.length-1;for(o=0;o<u;o++)if(t=i[o],n=i[o+1],r=i[o?o-1:o],t<n&&tz.moveAmbiguousForward?t=n:t>r&&tz.moveInvalidForward&&(t=r),a<s[o]-6e4*t)return i[o];return i[u]},abbr:function(e){return this.abbrs[this._index(e)]},offset:function(e){return logError("zone.offset has been deprecated in favor of zone.utcOffset"),this.offsets[this._index(e)]},utcOffset:function(e){return this.offsets[this._index(e)]}},ZoneScore.prototype.scoreOffsetAt=function(e){this.offsetScore+=Math.abs(this.zone.utcOffset(e.at)-e.offset),this.zone.abbr(e.at).replace(/[^A-Z]/g,"")!==e.abbr&&this.abbrScore++},tz.version="0.5.48",tz.dataVersion="",tz._zones=r,tz._links=o,tz._names=i,tz._countries=a,tz.add=addZone,tz.link=addLink,tz.load=function(e){addZone(e.zones),addLink(e.links),function(e){var t,n,r,o;if(e&&e.length)for(t=0;t<e.length;t++)n=(o=e[t].split("|"))[0].toUpperCase(),r=o[1].split(" "),a[n]=new Country(n,r)}(e.countries),tz.dataVersion=e.version},tz.zone=getZone,tz.zoneExists=zoneExists,tz.guess=function(e){return(!n||e)&&(n=function(){try{var e=Intl.DateTimeFormat().resolvedOptions().timeZone;if(e&&e.length>3){var t=i[normalizeName(e)];if(t)return t;logError("Moment Timezone found "+e+" from the Intl api, but did not have that data loaded.")}}catch(e){}var n,r,o,a=function(){var e,t,n,r,o=new Date().getFullYear()-2,a=new OffsetAt(new Date(o,0,1)),i=a.offset,s=[a];for(r=1;r<48;r++)(n=new Date(o,r,1).getTimezoneOffset())!==i&&(s.push(e=function(e,t){for(var n,r;r=((t.at-e.at)/12e4|0)*6e4;)(n=new OffsetAt(new Date(e.at+r))).offset===e.offset?e=n:t=n;return e}(a,t=new OffsetAt(new Date(o,r,1)))),s.push(new OffsetAt(new Date(e.at+6e4))),a=t,i=n);for(r=0;r<4;r++)s.push(new OffsetAt(new Date(o+r,0,1))),s.push(new OffsetAt(new Date(o+r,6,1)));return s}(),u=a.length,l=function(e){var t,n,r,o,a=e.length,u={},l=[],f={};for(t=0;t<a;t++)if(r=e[t].offset,!f.hasOwnProperty(r)){for(n in o=s[r]||{})o.hasOwnProperty(n)&&(u[n]=!0);f[r]=!0}for(t in u)u.hasOwnProperty(t)&&l.push(i[t]);return l}(a),f=[];for(r=0;r<l.length;r++){for(o=0,n=new ZoneScore(getZone(l[r]),u);o<u;o++)n.scoreOffsetAt(a[o]);f.push(n)}return f.sort(sortZoneScores),f.length>0?f[0].zone.name:void 0}()),n},tz.names=function(){var e,t=[];for(e in i)i.hasOwnProperty(e)&&(r[e]||r[o[e]])&&i[e]&&t.push(i[e]);return t.sort()},tz.Zone=Zone,tz.unpack=unpack,tz.unpackBase60=unpackBase60,tz.needsOffset=needsOffset,tz.moveInvalidForward=!0,tz.moveAmbiguousForward=!1,tz.countries=function(){return Object.keys(a)},tz.zonesForCountry=function(e,t){if(!(e=a[e.toUpperCase()]||null))return null;var n=e.zones.sort();return t?n.map(function(e){var t=getZone(e);return{name:e,offset:t.utcOffset(new Date)}}):n};var c=e.fn;function abbrWrap(e){return function(){return this._z?this._z.abbr(this):e.call(this)}}function resetZoneWrap(e){return function(){return this._z=null,e.apply(this,arguments)}}e.tz=tz,e.defaultZone=null,e.updateOffset=function(t,n){var r,o=e.defaultZone;if(void 0===t._z&&(o&&needsOffset(t)&&!t._isUTC&&t.isValid()&&(t._d=e.utc(t._a)._d,t.utc().add(o.parse(t),"minutes")),t._z=o),t._z){if(16>Math.abs(r=t._z.utcOffset(t))&&(r/=60),void 0!==t.utcOffset){var a=t._z;t.utcOffset(-r,n),t._z=a}else t.zone(r,n)}},c.tz=function(t,n){if(t){if("string"!=typeof t)throw Error("Time zone name must be a string, got "+t+" ["+typeof t+"]");return this._z=getZone(t),this._z?e.updateOffset(this,n):logError("Moment Timezone has no data for "+t+". See http://momentjs.com/timezone/docs/#/data-loading/."),this}if(this._z)return this._z.name},c.zoneName=abbrWrap(c.zoneName),c.zoneAbbr=abbrWrap(c.zoneAbbr),c.utc=resetZoneWrap(c.utc),c.local=resetZoneWrap(c.local),c.utcOffset=(t=c.utcOffset,function(){return arguments.length>0&&(this._z=null),t.apply(this,arguments)}),e.tz.setDefault=function(t){return(l<2||2===l&&f<9)&&logError("Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js "+e.version+"."),e.defaultZone=t?getZone(t):null,e};var d=e.momentProperties;return"[object Array]"===Object.prototype.toString.call(d)?(d.push("_z"),d.push("_a")):d&&(d._z=null),e},e.exports?e.exports=a(n(2067)):(r=[n(2067)],void 0===(o=a.apply(t,r))||(e.exports=o))},667:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{d:"M8.5 4.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM10.9 12.006c.11.542-.348.994-.9.994H2c-.553 0-1.01-.452-.902-.994a5.002 5.002 0 0 1 9.803 0ZM14.002 12h-1.59a2.556 2.556 0 0 0-.04-.29 6.476 6.476 0 0 0-1.167-2.603 3.002 3.002 0 0 1 3.633 1.911c.18.522-.283.982-.836.982ZM12 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"}))});t.Z=o},2015:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 9-3 3m0 0 3 3m-3-3h7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=o},3042:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"}))});t.Z=o},3352:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"}))});t.Z=o},7031:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))});t.Z=o},13:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))});t.Z=o},3217:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))});t.Z=o},2929:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=o},9367:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=o},1543:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});t.Z=o},5587:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))});t.Z=o},1615:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});t.Z=o},5792:function(e,t,n){"use strict";n.d(t,{bY:function(){return a}});var r=n(2265),o=n(3754);function getOptions(e,t){return{...e,container:t.current}}var a=function(e,t){let n=(0,r.forwardRef)(function(t,n){let{options:o,style:a,className:i}=t,s=(0,r.useRef)(null),u=(0,r.useRef)();(0,r.useLayoutEffect)(()=>{let t=e(getOptions(o,s));return u.current=t,()=>{t.destroy()}},[]);let l=void 0===u.current;return(0,r.useEffect)(()=>{l||u.current?.update(getOptions(o,s))},[o]),(0,r.useImperativeHandle)(n,()=>u.current,[]),(0,r.useMemo)(()=>(0,r.createElement)("div",{ref:s,style:a,className:i}),[a,i])});return n.displayName=t,n}(e=>o.bY.create(e),"AgCharts")}}]);