import { Relation } from 'typeorm';
import { AddendumEntity } from './addendum.entity';
import { ContractAdvisorEntity } from './contract-advisor.entity';
import { ContractAuditEntity } from './contract-audit.entity';
import { ContractDeletionEntity } from './contract-deletion.entity';
import { ContractEventEntity } from './contract-event.entity';
import { IncomePaymentScheduledEntity } from './income-payment-scheduled.entity';
import { IncomeReportsContractsEntity } from './income-reports-contracts.entity';
import { NotificationEntity } from './notification.entity';
import { OwnerRoleRelationEntity } from './owner-role-relation.entity';
import { PreRegisterEntity } from './pre-register.entity';
export declare class ContractEntity {
    id: string;
    externalId: number;
    brokerId: string;
    investorId: string;
    startContract: Date;
    endContract: Date;
    contractPdf: string;
    oldContractPdf: string;
    proofPayment: string;
    signInvestor: string;
    signIca: string;
    status: string;
    type: string;
    isDebenture: boolean;
    contractNumber: number;
    durationInMonths: number;
    brokerParticipationPercentage: string;
    advisorParticipationPercentage: string;
    createdAt: Date;
    updatedAt: Date;
    ownerRoleRelation: Relation<OwnerRoleRelationEntity>;
    signataries: PreRegisterEntity[];
    investor: Relation<OwnerRoleRelationEntity>;
    events: Relation<ContractEventEntity[]>;
    scheduledPayments: Relation<IncomePaymentScheduledEntity[]>;
    addendum: Relation<AddendumEntity[]>;
    contractAdvisors: Relation<ContractAdvisorEntity[]>;
    latestEvent?: ContractEventEntity;
    notifications: NotificationEntity[];
    incomeReportsContracts: IncomeReportsContractsEntity[];
    audits: Relation<ContractAuditEntity[]>;
    deletions: Relation<ContractDeletionEntity[]>;
}
