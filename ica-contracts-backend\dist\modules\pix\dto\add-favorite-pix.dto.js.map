{"version": 3, "file": "add-favorite-pix.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/add-favorite-pix.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAAwD;AACxD,yDAAyC;AACzC,qDAOyB;AAEzB,MAAM,OAAO;CAqBZ;AAjBC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,uCAAW,GAAE;;yCACG;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;uCACF;AAIf;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;uCACF;AAIf;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;qCACJ;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qCACE;AAGf,MAAa,iBAAiB;CAc7B;AAdD,8CAcC;AAXC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;+CACE;AAIb;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACG;AAMd;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,OAAO,CAAC;8BACX,OAAO;kDAAC", "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>J } from 'brazilian-class-validator';\r\nimport { Type } from 'class-transformer';\r\nimport {\r\n  IsDefined,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\n\r\nclass Account {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  number: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  branch: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  bank: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  type: string;\r\n}\r\n\r\nexport class AddFavoritePixDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  alias: string;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Account)\r\n  account: Account;\r\n}\r\n"]}