(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6432],{3972:function(e,t,a){Promise.resolve().then(a.bind(a,241))},241:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return AlterarContrato}});var o=a(7437),r=a(8700),s=a(3014),i=a(4033),n=a(3877),l=a(8637),d=a(5968),c=a(2067),u=a.n(c),m=a(4207),h=a(4765),x=a(2265),p=a(1865),v=a(9701),f=a(5691),b=a(919),g=a(9891),j=a(3588),N=a(6654),C=a(4568),w=a(3256);let y=f.Ry().shape({tipoContrato:f.Z_().required("Selecione o tipo de contrato"),nomeCompleto:f.Z_().required("Nome completo obrigat\xf3rio"),identidade:f.Z_().required("Identidade obrigat\xf3ria"),celular:f.Z_().required("Celular obrigat\xf3rio"),cpf:f.Z_().required("CPF/CNPJ obrigat\xf3rio"),dataNascimento:f.Z_().optional(),nomeMae:f.Z_().optional(),email:f.Z_().email("E-mail inv\xe1lido").required("E-mail obrigat\xf3rio"),cep:f.Z_().required("CEP obrigat\xf3rio"),cidade:f.Z_().required("Cidade obrigat\xf3ria"),endereco:f.Z_().required("Endere\xe7o obrigat\xf3rio"),numero:f.Z_().required("N\xfamero obrigat\xf3rio"),complemento:f.Z_().optional(),estado:f.Z_().required("Estado obrigat\xf3rio"),banco:f.Z_().required("Banco obrigat\xf3rio"),conta:f.Z_().required("Conta obrigat\xf3ria"),agencia:f.Z_().required("Ag\xeancia obrigat\xf3ria"),chavePix:f.Z_().required("Chave PIX obrigat\xf3ria"),modalidade:f.Z_().required("Modalidade obrigat\xf3ria"),valorInvestimento:f.Z_().required("Valor do investimento obrigat\xf3rio").test("scp-multiple","Para contratos SCP, o valor deve ser m\xfaltiplo de R$ 5.000",function(e){let t=this.parent.modalidade;if("SCP"===t&&e){let t=parseInt(e.replace(/\D/g,""));return t>0&&t%5e3==0}return!0}),prazoInvestimento:f.Z_().required("Prazo do investimento obrigat\xf3rio"),taxaRemuneracao:f.Z_().required("Taxa de remunera\xe7\xe3o obrigat\xf3ria"),comprarCom:f.Z_().required("Forma de compra obrigat\xf3ria"),inicioContrato:f.Z_().required("In\xedcio do contrato obrigat\xf3rio"),fimContrato:f.Z_(),perfil:f.Z_().required("Perfil obrigat\xf3rio"),debenture:f.Z_().required("Deb\xeanture obrigat\xf3ria"),observacoes:f.Z_().optional(),quotaQuantity:f.Z_().when("modalidade",{is:e=>"SCP"===e,then:e=>e.required("Quantidade de cotas obrigat\xf3ria"),otherwise:e=>e.notRequired()}),irDeposito:f.O7().optional(),irDesconto:f.O7().optional()});function AlterarContrato(){var e,t,a,c,f,M,D,F,I,R,Y,Z,P,S,E,A,O,V,T,q,k,_,z,U;let B=(0,i.useRouter)(),L=(0,i.useSearchParams)(),Q=L.get("tipo"),W=L.get("investorId"),K=(0,w.e)(),{data:G,isLoading:X}=(0,g.a)({queryKey:["contract",W],queryFn:async()=>{if(!W)return null;let e=await C.Z.get("/contract/".concat(W));return e.data},enabled:!!W}),{register:H,handleSubmit:J,watch:$,setValue:ee,trigger:et,formState:{errors:ea,isValid:eo,isSubmitting:er}}=(0,p.cI)({resolver:(0,v.X)(y),mode:"all",defaultValues:{tipoContrato:"pf",nomeCompleto:"",identidade:"",celular:"",cpf:"",dataNascimento:"",nomeMae:"",email:"",cep:"",cidade:"",endereco:"",numero:"",complemento:"",estado:"",banco:"",conta:"",agencia:"",chavePix:"",observacoes:"",modalidade:"MUTUO",valorInvestimento:"",prazoInvestimento:"",taxaRemuneracao:"",comprarCom:"pix",inicioContrato:"",fimContrato:"",perfil:"conservative",debenture:"n",quotaQuantity:"",irDeposito:!1,irDesconto:!1}}),[es,ei]=(0,x.useState)(1),[en,el]=(0,x.useState)(""),[ed,ec]=(0,x.useState)(!1);(0,x.useMemo)(()=>{if(G&&!X){var e,t,a,o,r;let s=null===(e=G.contracts)||void 0===e?void 0:e[0];if(s){ee("nomeCompleto",s.investorName||""),ee("cpf",G.document||""),ee("identidade",G.rg||""),ee("email",G.email||""),ee("celular",G.phone||""),ee("nomeMae",G.motherName||""),ee("dataNascimento",G.birthDate||""),ee("cep",G.zipCode||""),ee("cidade",G.city||""),ee("endereco",G.address||""),ee("numero",G.addressNumber||""),ee("complemento",G.complement||""),ee("estado",G.state||""),ee("banco",G.bank||""),ee("agencia",G.branch||""),ee("conta",G.accountNumber||""),ee("chavePix",G.phone||G.email||""),ee("valorInvestimento",s.investmentValue||""),ee("taxaRemuneracao",s.investmentYield||""),ee("prazoInvestimento",s.investmentTerm||"");let e="pix";(null===(t=s.purchasedWith)||void 0===t?void 0:t.includes("TRANSFER\xcaNCIA"))||(null===(a=s.purchasedWith)||void 0===a?void 0:a.includes("BANC\xc1RIA"))?e="bank_transfer":(null===(o=s.purchasedWith)||void 0===o?void 0:o.includes("PIX"))?e="pix":(null===(r=s.purchasedWith)||void 0===r?void 0:r.includes("BOLETO"))&&(e="boleto"),ee("comprarCom",e),ee("inicioContrato",s.contractStart?u()(s.contractStart).format("YYYY-MM-DD"):""),ee("modalidade","P2P"===s.tags?"MUTUO":"SCP"),console.log("Formul\xe1rio pr\xe9-preenchido com dados do contrato:",G)}}},[G,X,ee,$]),(0,x.useEffect)(()=>{let e=$("modalidade"),t=$("quotaQuantity");if("SCP"===e&&t){let e=parseInt(t)||0;ee("valorInvestimento",(5e3*e).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}))}},[$("modalidade"),$("quotaQuantity"),ee]);let eu=(0,x.useMemo)(()=>{var e,t;if(!G||!(null===(e=G.contracts)||void 0===e?void 0:e[0]))return!1;let a=G.contracts[0],o=null===(t=a.contractStatus)||void 0===t?void 0:t.toUpperCase(),r=!1;return a.addendum&&a.addendum.length>0&&(r=a.addendum.some(e=>{var t;let a=null===(t=e.contractStatus)||void 0===t?void 0:t.toUpperCase();return"ACTIVE"===a||"ATIVO"===a})),"ACTIVE"===o||"ATIVO"===o||r},[G]),onSubmit=async e=>{var t,a;console.log("Iniciando submiss\xe3o do formul\xe1rio...",e);let o=null==G?void 0:null===(a=G.contracts)||void 0===a?void 0:null===(t=a[0])||void 0===t?void 0:t.id;if(console.log("Contract ID:",o),console.log("Investor ID:",W),!o){s.Am.error("ID do contrato n\xe3o encontrado nos dados carregados");return}try{let t="pj"===e.tipoContrato,a=e.cpf.replace(/\D/g,"");console.log("Dados processados:",{isPJ:t,documento:a,userProfile:K});let r={role:(null==K?void 0:K.name)||"",personType:t?"PJ":"PF",contractType:e.modalidade,bankAccount:{bank:e.banco,agency:e.agencia,account:e.conta,pix:e.chavePix,type:"CORRENTE"},investment:{amount:parseInt(e.valorInvestimento.replace(/\D/g,"")),monthlyRate:parseFloat(e.taxaRemuneracao),durationInMonths:parseInt(e.prazoInvestimento),paymentMethod:e.comprarCom,startDate:e.inicioContrato,endDate:e.fimContrato,profile:e.perfil,isDebenture:"s"===e.debenture,..."SCP"===e.modalidade&&{quotaQuantity:parseInt(e.quotaQuantity||"0")}}};t?r.company={corporateName:e.nomeCompleto,cnpj:a}:r.individual={fullName:e.nomeCompleto,cpf:a,rg:e.identidade,birthDate:e.dataNascimento,email:e.email,phone:e.celular.replace(/\D/g,""),motherName:e.nomeMae,nationality:"brasileira",occupation:"Investidor",issuingAgency:"SSP",address:{street:e.endereco,city:e.cidade,state:e.estado||"",postalCode:e.cep.replace(/\D/g,""),number:e.numero,neighborhood:"Centro",complement:e.complemento||""}},console.log("Enviando dados para API...",r),console.log("Usando Contract ID para upgrade:",o),eh.mutate(r)}catch(e){console.error("Erro ao processar dados:",e),s.Am.error("Erro ao processar dados do contrato")}},em=["tipoContrato","nomeCompleto","identidade","celular","cpf","email","cep","cidade","endereco","numero","estado","banco","conta","agencia","chavePix"],handleNext=async()=>{console.log("Validando campos da p\xe1gina 1:",em);let e=await et(em);if(console.log("Resultado da valida\xe7\xe3o:",e),console.log("Erros atuais:",ea),e)ei(2),s.Am.success("Dados da p\xe1gina 1 validados com sucesso!");else{let e=em.filter(e=>ea[e]),t=e.length;t>0?s.Am.error("Por favor, preencha ".concat(t," campo(s) obrigat\xf3rio(s) antes de continuar.")):s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios antes de continuar.")}},eh=(0,j.D)({mutationFn:async e=>{var t,a;let o=null==G?void 0:null===(a=G.contracts)||void 0===a?void 0:null===(t=a[0])||void 0===t?void 0:t.id;if(console.log("Mutation - Contract Data:",G),console.log("Mutation - Contract ID:",o),!o)throw Error("ID do contrato n\xe3o encontrado nos dados carregados");return console.log("Fazendo PUT para:","/contract/".concat(o,"/upgrade")),C.Z.put("/contract/"+o+"/upgrade",e)},onSuccess:e=>{var t;console.log("Upgrade realizado com sucesso:",e.data);let a=null===(t=e.data)||void 0===t?void 0:t.id;a?s.Am.success("Upgrade realizado com sucesso! Novo contrato: ".concat(a.substring(0,8),"... Redirecionando para a home...")):s.Am.success("Upgrade realizado com sucesso! Redirecionando para a home..."),ec(!0),setTimeout(()=>{B.push("/")},3e3)},onError:e=>{(0,N.Z)(e,"Erro ao atualizar o contrato")}}),ex=$("prazoInvestimento"),ep=$("inicioContrato"),ev=(0,x.useMemo)(()=>{if(ep&&ex){let e=(0,b.H)({investDate:String(ex),startDate:ep}),t=u()(e,"DD-MM-YYYY").format("DD/MM/YYYY");return ee("fimContrato",u()(e,"DD-MM-YYYY").format("YYYY-MM-DD"),{shouldValidate:!0}),t}return""},[ep,ex,ee]),ef=(0,x.useMemo)(()=>{var e,t;if(!G||!(null===(e=G.contracts)||void 0===e?void 0:e[0]))return null;let a=G.contracts[0],o=[],calculateIR=(e,t,a)=>{let o=e*t*(a/30)/100,r=0;r=a<=180?22.5:a<=360?20:a<=720?17.5:15;let s=o*r/100;return{totalReturn:o,irRate:r,irValue:s}},r=null===(t=a.contractStatus)||void 0===t?void 0:t.toUpperCase();if("ACTIVE"===r||"ATIVO"===r){let e=parseInt(a.investmentValue)||0,t=parseFloat(a.investmentYield)||0,r=a.contractStart?u()(a.contractStart):u()(),s=a.contractEnd?u()(a.contractEnd):u()(),i=s.diff(r,"days"),n=calculateIR(e,t,i);o.push({type:"Contrato Inicial",amount:e,daysRentabilized:i,monthlyRate:t,irRate:n.irRate,irValue:n.irValue,totalReturn:n.totalReturn,status:a.contractStatus})}if(a.addendum&&a.addendum.length>0){let e=1;a.addendum.forEach(t=>{var a;let r=null===(a=t.contractStatus)||void 0===a?void 0:a.toUpperCase();if("ACTIVE"===r||"ATIVO"===r){let a=parseFloat(t.investmentValue)||0,r=parseFloat(t.investmentYield)||0,s=t.contractStart?u()(t.contractStart):u()(),i=t.contractEnd?u()(t.contractEnd):u()(),n=i.diff(s,"days"),l=calculateIR(a,r,n);o.push({type:"Aditivo ".concat(e),amount:a,daysRentabilized:n,monthlyRate:r,irRate:l.irRate,irValue:l.irValue,totalReturn:l.totalReturn,status:t.contractStatus}),e++}})}let s=o.reduce((e,t)=>e+t.irValue,0);return{details:o,totalIR:s,contractType:a.tags||"MUTUO"}},[G]),eb=function(e){if(!e)return 0;let t=e.replace(/[^0-9,]/g,"").replace(",",".");return parseFloat(t)||0}($("valorInvestimento")),eg=en?parseFloat(en.replace("%","").replace(",",".")):0,ej=eb&&eg?eb*(eg/100):0;return X?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.Z,{}),(0,o.jsx)(l.Z,{children:(0,o.jsx)("div",{className:"w-full",children:(0,o.jsx)("div",{className:"p-8 flex items-center justify-center min-h-[400px]",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4"}),(0,o.jsx)("p",{className:"text-white",children:"Carregando dados do contrato..."})]})})})})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.Z,{}),(0,o.jsx)(l.Z,{children:(0,o.jsx)("div",{className:"w-full",children:(0,o.jsxs)("div",{className:"p-8",children:[(0,o.jsx)("div",{className:"flex items-center mb-6 w-full justify-center",children:(0,o.jsxs)("h1",{className:"text-2xl text-center w-full font-bold text-white",children:["Contratos",G&&(0,o.jsx)("span",{className:"text-sm text-gray-400 block mt-1",children:null===(t=G.contracts)||void 0===t?void 0:null===(e=t[0])||void 0===e?void 0:e.investorName})]})}),!Q&&eu?(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-white mb-6",children:"Escolha o tipo de altera\xe7\xe3o"}),(0,o.jsx)("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{B.push("/contratos/alterar?tipo=rentabilidade&investorId=".concat(W))},children:(0,o.jsx)("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsxs)("div",{className:"",children:[(0,o.jsx)("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Rentabilidade"}),(0,o.jsx)("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a rentabilidade do contrato do investidor"})]}),(0,o.jsx)("div",{className:"flex-1"}),(0,o.jsx)("div",{className:"flex items-center",children:(0,o.jsx)("div",{className:"",children:(0,o.jsx)("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})}),(0,o.jsx)("div",{className:"flex md:flex-row flex-col gap-2 justify-between cursor-pointer",onClick:()=>{B.push("/contratos/alterar?tipo=modalidade&investorId=".concat(W))},children:(0,o.jsx)("div",{className:"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsxs)("div",{className:"",children:[(0,o.jsx)("p",{className:"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white",children:"Mudan\xe7a de Modalidade"}),(0,o.jsx)("p",{className:"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300",children:"Clique aqui para mudar a modalidade do contrato do investidor"})]}),(0,o.jsx)("div",{className:"flex-1"}),(0,o.jsx)("div",{className:"flex items-center",children:(0,o.jsx)("div",{className:"",children:(0,o.jsx)("p",{className:"text-white text-lg font-bold",children:"→"})})})]})})})]}):Q||eu?1===es?(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Pessoais"}),(0,o.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:(0,o.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,o.jsxs)("div",{className:"w-full md:w-1/2 mb-4",children:[(0,o.jsx)("p",{className:"text-white mb-1",children:"Tipo de Contrato"}),(0,o.jsxs)(h.Z,{value:$("tipoContrato"),onChange:e=>ee("tipoContrato",e.target.value,{shouldValidate:!0}),children:[(0,o.jsx)("option",{value:"pf",children:"Pessoa F\xedsica"}),(0,o.jsx)("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,o.jsx)("div",{className:"w-full mb-4",children:(0,o.jsx)(m.Z,{register:H,name:"nomeCompleto",width:"100%",error:!!ea.nomeCompleto,errorMessage:null==ea?void 0:null===(a=ea.nomeCompleto)||void 0===a?void 0:a.message,label:"Nome Completo"})}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"identidade",width:"100%",error:!!ea.identidade,errorMessage:null==ea?void 0:null===(c=ea.identidade)||void 0===c?void 0:c.message,label:"Identidade"})}),(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"celular",width:"100%",maxLength:15,error:!!ea.celular,errorMessage:null==ea?void 0:null===(f=ea.celular)||void 0===f?void 0:f.message,label:"Celular",onChange:e=>{ee("celular",(0,d.gP)(e.target.value),{shouldValidate:!0})}})})]}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"cpf",width:"100%",error:!!ea.cpf,errorMessage:null==ea?void 0:null===(M=ea.cpf)||void 0===M?void 0:M.message,label:"CPF/CNPJ",onChange:e=>{let t="pj"===$("tipoContrato")?(0,d.PK)(e.target.value):(0,d.VL)(e.target.value);ee("cpf",t,{shouldValidate:!0})}})}),(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{type:"date",register:H,name:"dataNascimento",width:"100%",error:!!ea.dataNascimento,errorMessage:null==ea?void 0:null===(D=ea.dataNascimento)||void 0===D?void 0:D.message,label:"Data de Nascimento (opcional)"})})]}),(0,o.jsx)("div",{className:"w-full mb-4",children:(0,o.jsx)(m.Z,{register:H,name:"nomeMae",width:"100%",error:!!ea.nomeMae,errorMessage:null==ea?void 0:null===(F=ea.nomeMae)||void 0===F?void 0:F.message,label:"Nome da M\xe3e (opcional)"})}),(0,o.jsx)("div",{className:"w-full mb-4",children:(0,o.jsx)(m.Z,{register:H,name:"email",width:"100%",error:!!ea.email,errorMessage:null==ea?void 0:null===(I=ea.email)||void 0===I?void 0:I.message,label:"E-mail"})}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"cep",width:"100%",error:!!ea.cep,errorMessage:null==ea?void 0:null===(R=ea.cep)||void 0===R?void 0:R.message,label:"CEP",onChange:e=>{ee("cep",(0,d.Tc)(e.target.value),{shouldValidate:!0})}})}),(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"cidade",width:"100%",error:!!ea.cidade,errorMessage:null==ea?void 0:null===(Y=ea.cidade)||void 0===Y?void 0:Y.message,label:"Cidade"})})]}),(0,o.jsx)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"estado",width:"100%",error:!!ea.estado,errorMessage:null==ea?void 0:null===(Z=ea.estado)||void 0===Z?void 0:Z.message,label:"Estado",placeholder:"ex: SC"})})}),(0,o.jsx)("div",{className:"w-full mb-4",children:(0,o.jsx)(m.Z,{register:H,name:"endereco",width:"100%",error:!!ea.endereco,errorMessage:null==ea?void 0:null===(P=ea.endereco)||void 0===P?void 0:P.message,label:"Endere\xe7o"})}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"numero",width:"100%",error:!!ea.numero,errorMessage:null==ea?void 0:null===(S=ea.numero)||void 0===S?void 0:S.message,label:"N\xfamero"})}),(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"complemento",width:"100%",error:!!ea.complemento,errorMessage:null==ea?void 0:null===(E=ea.complemento)||void 0===E?void 0:E.message,label:"Complemento"})})]})]})}),(0,o.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"Dados Banc\xe1rios"}),(0,o.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full mb-4",children:[(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"banco",width:"100%",error:!!ea.banco,errorMessage:null==ea?void 0:null===(A=ea.banco)||void 0===A?void 0:A.message,label:"Nome do Banco"})}),(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"conta",width:"100%",error:!!ea.conta,errorMessage:null==ea?void 0:null===(O=ea.conta)||void 0===O?void 0:O.message,label:"Conta"})})]}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 w-full",children:[(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"agencia",width:"100%",error:!!ea.agencia,errorMessage:null==ea?void 0:null===(V=ea.agencia)||void 0===V?void 0:V.message,label:"Ag\xeancia"})}),(0,o.jsx)("div",{className:"w-full md:w-1/2",children:(0,o.jsx)(m.Z,{register:H,name:"chavePix",width:"100%",error:!!ea.chavePix,errorMessage:null==ea?void 0:null===(T=ea.chavePix)||void 0===T?void 0:T.message,label:"Chave PIX"})})]})]}),(0,o.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"Observa\xe7\xf5es"}),(0,o.jsxs)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8",children:[(0,o.jsx)("textarea",{...H("observacoes"),className:"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]",placeholder:"Observa\xe7\xf5es"}),ea.observacoes&&(0,o.jsx)("span",{className:"text-red-500 text-xs",children:ea.observacoes.message})]}),(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsx)(r.z,{size:"lg",type:"button",className:"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]",onClick:handleNext,children:"Pr\xf3ximo"})})]}):(0,o.jsxs)("form",{onSubmit:J(onSubmit,e=>{console.log("Erros de valida\xe7\xe3o:",e),s.Am.error("Por favor, preencha todos os campos obrigat\xf3rios")}),children:[(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"my-8 flex flex-col gap-4",children:[(0,o.jsx)("label",{className:"text-white mb-1 block",children:"Modalidade de Investimento"}),(0,o.jsxs)("div",{className:"flex flex-col md:w-1/3",children:[(0,o.jsxs)(h.Z,{value:$("modalidade"),onChange:e=>ee("modalidade",e.target.value,{shouldValidate:!0}),children:[(0,o.jsx)("option",{value:"MUTUO",children:"M\xfatuo"}),(0,o.jsx)("option",{value:"SCP",children:"SCP"})]}),(0,o.jsx)("span",{className:"text-[#FF9900] text-xs mt-1",children:"*Ao alterar modalidade, pressione o bot\xe3o calcular IR"})]}),"SCP"===$("modalidade")&&(0,o.jsx)("div",{className:"flex flex-col md:w-1/3 mt-2",children:(0,o.jsx)(m.Z,{register:H,name:"quotaQuantity",width:"100%",error:!!ea.quotaQuantity,errorMessage:null==ea?void 0:null===(q=ea.quotaQuantity)||void 0===q?void 0:q.message,label:"Quantidade de cotas",placeholder:"ex: 10"})}),"MUTUO"===$("modalidade")&&(0,o.jsx)("div",{className:"mt-4 md:w-1/3",children:(0,o.jsx)(r.z,{type:"button",className:"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full",onClick:function(){let e=Number($("prazoInvestimento"));if(!e||isNaN(e)){el(""),s.Am.warn("Preencha o prazo do investimento para calcular o IR.");return}let t=30*e;el(t<=180?"22,5%":t<=360?"20%":t<=720?"17,5%":"15%")},children:"Calcular IR"})})]}),"MUTUO"===$("modalidade")&&(0,o.jsxs)("div",{className:"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3",children:[(0,o.jsxs)("p",{className:"text-white text-xs mb-1",children:["Valor investido: ",eb?eb.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),(0,o.jsxs)("p",{className:"text-white font-bold",children:["Valor total de IR: ",ej?ej.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):"R$ 0,00"]}),en&&(0,o.jsxs)("p",{className:"text-[#FF9900] font-bold mt-2",children:["Al\xedquota do IR calculada: ",en]})]}),"MUTUO"===$("modalidade")&&(0,o.jsxs)("div",{className:"w-full flex flex-col gap-4",children:[(0,o.jsx)("h4",{className:"text-lg font-bold text-white",children:"Detalhamento"}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"bg-[#232323]",children:[(0,o.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Tipo de Contrato"}),(0,o.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do Contrato"}),(0,o.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Dias Rentabilizados"}),(0,o.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Rentabilidade"}),(0,o.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Taxa de IR"}),(0,o.jsx)("th",{className:"px-2 py-2 border-r border-[#FF9900] font-semibold",children:"Valor do IR"}),(0,o.jsx)("th",{className:"px-2 py-2 font-semibold",children:"Anexos"})]})}),(0,o.jsx)("tbody",{children:X?(0,o.jsx)("tr",{children:(0,o.jsx)("td",{colSpan:7,className:"text-center px-2 py-4",children:"Carregando dados do contrato..."})}):ef?(0,o.jsxs)(o.Fragment,{children:[ef.details.length>0?ef.details.map((e,t)=>(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:e.type}),(0,o.jsx)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.amount)}),(0,o.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.daysRentabilized," Dias"]}),(0,o.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.monthlyRate,"%"]}),(0,o.jsxs)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:[e.irRate,"%"]}),(0,o.jsx)("td",{className:"px-2 py-2 border-r border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e.irValue)}),(0,o.jsx)("td",{className:"px-2 py-2",children:"-"})]},t)):(0,o.jsx)("tr",{children:(0,o.jsx)("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Nenhum contrato ativo encontrado para c\xe1lculo de IR"})}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{colSpan:6,className:"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]",children:"Valor total do IR"}),(0,o.jsx)("td",{className:"px-2 py-2 font-bold text-white border-t border-[#FF9900]",children:new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(ef.totalIR)})]})]}):(0,o.jsx)("tr",{children:(0,o.jsx)("td",{colSpan:7,className:"text-center px-2 py-4 text-gray-400",children:"Dados do contrato n\xe3o encontrados"})})})]})})]}),!X&&!eu&&(0,o.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-4 mb-4",children:[(0,o.jsx)("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),(0,o.jsx)("p",{className:"text-white text-sm",children:'N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status "ATIVO" podem ser alterados.'})]}),"MUTUO"===$("modalidade")&&(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-2",children:[(0,o.jsxs)("label",{className:"flex items-center text-white text-xs",children:[(0,o.jsx)("input",{type:"checkbox",className:"mr-2",checked:$("irDeposito"),onChange:e=>{e.target.checked?(ee("irDeposito",!0),ee("irDesconto",!1)):ee("irDeposito",!1)}}),"Investidor ir\xe1 depositar valor referente ao IR"]}),(0,o.jsxs)("label",{className:"flex items-center text-white text-xs",children:[(0,o.jsx)("input",{type:"checkbox",className:"mr-2",checked:$("irDesconto"),onChange:e=>{e.target.checked?(ee("irDesconto",!0),ee("irDeposito",!1)):ee("irDesconto",!1)}}),"Investidor decidiu desconto do IR sobre o valor do contrato"]})]})]}),(0,o.jsx)("h3",{className:"text-lg font-bold text-white my-8",children:"Dados do Investimento"}),(0,o.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8",children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,o.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:[(0,o.jsx)(m.Z,{register:H,name:"valorInvestimento",width:"100%",error:!!ea.valorInvestimento,errorMessage:null==ea?void 0:null===(k=ea.valorInvestimento)||void 0===k?void 0:k.message,label:"Valor do Investimento",disabled:"SCP"===$("modalidade"),setValue:e=>"SCP"===$("modalidade")?void 0:ee("valorInvestimento",(0,d.Ht)(e||""),{shouldValidate:!0})}),(0,o.jsx)(m.Z,{register:H,type:"text",name:"taxaRemuneracao",width:"100%",error:!!ea.taxaRemuneracao,errorMessage:null==ea?void 0:null===(_=ea.taxaRemuneracao)||void 0===_?void 0:_.message,label:"Taxa de Remunera\xe7\xe3o Mensal %",placeholder:"ex: 2"}),(0,o.jsx)(m.Z,{type:"date",register:H,maxDate:u()().format("YYYY-MM-DD"),name:"inicioContrato",width:"100%",setValue:e=>ee("inicioContrato",e,{shouldValidate:!0}),error:!!ea.inicioContrato,errorMessage:null==ea?void 0:null===(z=ea.inicioContrato)||void 0===z?void 0:z.message,label:"In\xedcio do Contrato"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"text-white mb-1 block",children:"Perfil"}),(0,o.jsxs)(h.Z,{value:$("perfil"),onChange:e=>ee("perfil",e.target.value,{shouldValidate:!0}),children:[(0,o.jsx)("option",{value:"conservative",children:"Conservador"}),(0,o.jsx)("option",{value:"moderate",children:"Moderado"}),(0,o.jsx)("option",{value:"aggressive",children:"Agressivo"})]})]})]}),(0,o.jsxs)("div",{className:"flex flex-col gap-4 w-full md:w-1/2",children:[(0,o.jsx)(m.Z,{register:H,type:"number",name:"prazoInvestimento",width:"100%",error:!!ea.prazoInvestimento,errorMessage:null==ea?void 0:null===(U=ea.prazoInvestimento)||void 0===U?void 0:U.message,label:"Prazo do Investimento - em meses",placeholder:"ex: 12"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"text-white mb-1 block",children:"Comprar com"}),(0,o.jsxs)(h.Z,{value:$("comprarCom"),onChange:e=>ee("comprarCom",e.target.value,{shouldValidate:!0}),children:[(0,o.jsx)("option",{value:"pix",children:"PIX"}),(0,o.jsx)("option",{value:"boleto",children:"Boleto"}),(0,o.jsx)("option",{value:"bank_transfer",children:"Transfer\xeancia Banc\xe1ria"})]})]}),(0,o.jsx)(m.Z,{type:"date",register:H,name:"fimContrato",value:ev?u()(ev,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"100%",disabled:!0,label:"Fim do Contrato"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"text-white mb-1 block",children:"Deb\xeanture"}),(0,o.jsxs)(h.Z,{value:$("debenture"),onChange:e=>ee("debenture",e.target.value,{shouldValidate:!0}),children:[(0,o.jsx)("option",{value:"s",children:"Sim"}),(0,o.jsx)("option",{value:"n",children:"N\xe3o"})]})]})]})]})}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)(r.z,{type:"button",variant:"outline",size:"lg",onClick:()=>ei(1),className:"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white",children:"← Voltar"}),(0,o.jsx)(r.z,{size:"lg",type:"submit",className:"px-8 py-2 rounded-lg ".concat(eu?"bg-[#FF9900] text-white hover:bg-[#e68a00]":"bg-gray-500 text-gray-300 cursor-not-allowed"),onClick:()=>{console.log("Bot\xe3o Concluir clicado"),console.log("Estado do formul\xe1rio:",{isValid:eo,errors:ea}),console.log("Dados atuais:",$()),console.log("Contratos ativos:",eu)},disabled:!eu||er||eh.isPending||ed,children:eu?ed?"Redirecionando...":er||eh.isPending?"Enviando...":"Alterar Contrato":"Nenhum contrato ativo encontrado"})]})]}):(0,o.jsxs)("div",{className:"bg-red-900 border border-red-500 rounded-lg p-6 text-center",children:[(0,o.jsx)("h4",{className:"text-white font-bold mb-2",children:"⚠️ Nenhum Contrato Ativo Encontrado"}),(0,o.jsx)("p",{className:"text-white text-sm",children:"N\xe3o \xe9 poss\xedvel realizar upgrade pois n\xe3o h\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados."})]})]})})})]})}},4207:function(e,t,a){"use strict";a.d(t,{Z:function(){return InputText}});var o=a(7437);function InputText(e){let{label:t,setValue:a,error:r,errorMessage:s,width:i="auto",register:n,name:l,placeholder:d="",type:c="text",disabled:u=!1,minDate:m,minLength:h,maxLength:x,maxDate:p,disableErrorMessage:v=!1,onBlur:f,value:b,onChange:g}=e;return(0,o.jsxs)("div",{className:"input relative group",style:{width:i},children:[(0,o.jsxs)("p",{className:"text-white mb-1 text-sm",children:[t,r&&!v&&(0,o.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",s]})]}),(0,o.jsx)("input",{...n(l),placeholder:d,type:c,id:l,disabled:u,min:m,max:p,minLength:h,maxLength:x,...a?{onChange:e=>{let{target:t}=e;return a(t.value)}}:{},onBlur:f,className:"h-12 w-full px-4 ".concat(u?"text-zinc-400":"text-white"," rounded-xl ").concat(r?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),...void 0!==b?{value:b}:{},...g?{onChange:g}:{}}),r&&(0,o.jsx)("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:s})]})}a(9514)},919:function(e,t,a){"use strict";a.d(t,{H:function(){return getFinalDataWithMount},Z:function(){return getDataFilter}});var o=a(2067),r=a.n(o);function getDataFilter(e){switch(e){case"TODAY":{let e=r()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}case"WEEK":{let e=r()().startOf("isoWeek").format("YYYY-MM-DD"),t=r()().endOf("isoWeek").format("YYYY-MM-DD");return{startDate:e,endDate:t}}case"MONTH":{let e=r()().startOf("month").format("YYYY-MM-DD"),t=r()().endOf("month").format("YYYY-MM-DD");return{startDate:e,endDate:t}}default:{let e=r()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}}}function getFinalDataWithMount(e){let{investDate:t,startDate:a}=e,o=r()(a).format("DD/MM/YYYY"),s=Number(t),i=r()(o,"DD/MM/YYYY").add(s,"months").format("DD/MM/YYYY");return i}},6654:function(e,t,a){"use strict";a.d(t,{Z:function(){return returnError}});var o=a(3014);function returnError(e,t){var a,r,s,i;let n=(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.error);if(Array.isArray(n))return n.forEach(e=>{o.Am.error(e,{toastId:e})}),n.join("\n");if("string"==typeof n)return o.Am.error(n,{toastId:n}),n;if("object"==typeof n&&null!==n){let e=Object.values(n).flat().join("\n");return o.Am.error(e,{toastId:e}),e}return o.Am.error(t,{toastId:t}),t}},9514:function(){},7470:function(e,t,a){"use strict";a.d(t,{R:function(){return getDefaultState},m:function(){return i}});var o=a(7987),r=a(9024),s=a(1640),i=class extends r.F{#e;#t;#a;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||getDefaultState(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#a?.continue()??this.execute(this.state.variables)}async execute(e){let onContinue=()=>{this.#o({type:"continue"})};this.#a=(0,s.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let t="pending"===this.state.status,a=!this.#a.canStart();try{if(t)onContinue();else{this.#o({type:"pending",variables:e,isPaused:a}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#o({type:"pending",context:t,variables:e,isPaused:a})}let o=await this.#a.start();return await this.#t.config.onSuccess?.(o,e,this.state.context,this),await this.options.onSuccess?.(o,e,this.state.context),await this.#t.config.onSettled?.(o,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(o,null,e,this.state.context),this.#o({type:"success",data:o}),o}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#o({type:"error",error:t})}}finally{this.#t.runNext(this)}}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),o.Vr.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},3588:function(e,t,a){"use strict";a.d(t,{D:function(){return useMutation}});var o=a(2265),r=a(7470),s=a(7987),i=a(2996),n=a(300),l=class extends i.l{#r;#s=void 0;#i;#n;constructor(e,t){super(),this.#r=e,this.setOptions(t),this.bindMethods(),this.#l()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#r.defaultMutationOptions(e),(0,n.VS)(this.options,t)||this.#r.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#i,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,n.Ym)(t.mutationKey)!==(0,n.Ym)(this.options.mutationKey)?this.reset():this.#i?.state.status==="pending"&&this.#i.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#i?.removeObserver(this)}onMutationUpdate(e){this.#l(),this.#d(e)}getCurrentResult(){return this.#s}reset(){this.#i?.removeObserver(this),this.#i=void 0,this.#l(),this.#d()}mutate(e,t){return this.#n=t,this.#i?.removeObserver(this),this.#i=this.#r.getMutationCache().build(this.#r,this.options),this.#i.addObserver(this),this.#i.execute(e)}#l(){let e=this.#i?.state??(0,r.R)();this.#s={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#d(e){s.Vr.batch(()=>{if(this.#n&&this.hasListeners()){let t=this.#s.variables,a=this.#s.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,a),this.#n.onSettled?.(e.data,null,t,a)):e?.type==="error"&&(this.#n.onError?.(e.error,t,a),this.#n.onSettled?.(void 0,e.error,t,a))}this.listeners.forEach(e=>{e(this.#s)})})}},d=a(8038);function useMutation(e,t){let a=(0,d.NL)(t),[r]=o.useState(()=>new l(a,e));o.useEffect(()=>{r.setOptions(e)},[r,e]);let i=o.useSyncExternalStore(o.useCallback(e=>r.subscribe(s.Vr.batchCalls(e)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),c=o.useCallback((e,t)=>{r.mutate(e,t).catch(n.ZT)},[r]);if(i.error&&(0,n.L3)(r.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:c,mutateAsync:i.mutate}}}},function(e){e.O(0,[6990,8276,5371,6946,1865,3964,9891,3151,2971,7864,1744],function(){return e(e.s=3972)}),_N_E=e.O()}]);