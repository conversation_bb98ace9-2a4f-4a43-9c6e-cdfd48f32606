"use strict";exports.id=2796,exports.ids=[2796],exports.modules={22796:(e,s,t)=>{t.d(s,{Z:()=>SelectSearch});var r=t(60080),l=t(34129),a=t(88401),n=t(9885),c=t(74644);function SelectSearch({items:e,setValue:s,label:t,selectable:i=!1,disabled:d=!1,handleChange:o,loading:u=!1}){let[h,m]=(0,n.useState)(!1),[x,f]=(0,n.useState)(""),[v,p]=(0,n.useState)([]),j=(0,n.useRef)(null),g=(0,n.useRef)(null);return(0,n.useEffect)(()=>{f("")},[u]),(0,n.useEffect)(()=>{if(""===x)return p(e);let s=e?.filter(e=>e.name.toLowerCase().includes(x.toLowerCase()));p(s)},[x]),(0,n.useEffect)(()=>{p(e)},[e]),(0,n.useEffect)(()=>{let handleFocus=()=>{m(!0)},e=j.current;return e&&e.addEventListener("focus",handleFocus),()=>{e&&e.removeEventListener("focus",handleFocus)}},[]),(0,n.useEffect)(()=>{let handleClickOutside=e=>{g.current&&!g.current.contains(e.target)&&j.current&&!j.current.contains(e.target)&&m(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]),r.jsx("div",{className:"w-full",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx("p",{className:"text-white mb-1",children:t}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{disabled:d||u,ref:j,type:"text",value:u?"Carregando...":x,onChange:({target:e})=>f(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer"}),r.jsx("div",{className:"absolute right-2 top-[50%] translate-y-[-50%]",children:h?r.jsx(a.Z,{width:20,color:"#fff"}):r.jsx(l.Z,{width:20,color:"#fff"})})]}),h&&r.jsx("div",{ref:g,className:"text-white absolute w-full border-b border-x px-2 top-[98%] z-40 bg-black max-h-52 overflow-y-auto scroll-",children:u?(0,r.jsxs)("div",{className:"py-1",children:[r.jsx(c.j,{height:"25px",className:"my-1"}),r.jsx(c.j,{height:"25px",className:"my-1"})]}):v?.length>0?v?.map(e=>r.jsx("div",{onClick:()=>{m(!1),o&&o(e),i?(f(""),s(`${e.name} - ${e.id}`)):(s(e.id),f(e.name))},className:"cursor-pointer my-1 hover:bg-zinc-900",children:e.name},e.id)):r.jsx("div",{className:"py-3",children:r.jsx("p",{className:"text-zinc-400",children:"Nenhum dado encontrado!"})})})]})})}}};