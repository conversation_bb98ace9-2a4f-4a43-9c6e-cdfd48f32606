(()=>{var e={};e.id=1148,e.ids=[1148],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},24813:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>f,originalPathname:()=>p,pages:()=>x,routeModule:()=>m,tree:()=>c});var a=s(73137),n=s(54647),i=s(4183),o=s.n(i),l=s(71775),r={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(r[e]=()=>l[e]);s.d(t,r);let d=a.AppPageRouteModule,c=["",{children:["contratos",{children:["contrato",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36179)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\contrato\\[id]\\page.tsx"],p="/contratos/contrato/[id]/page",f={require:s,loadChunk:()=>Promise.resolve()},m=new d({definition:{kind:n.x.APP_PAGE,page:"/contratos/contrato/[id]/page",pathname:"/contratos/contrato/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31026:(e,t,s)=>{Promise.resolve().then(s.bind(s,95188))},95188:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>CreateInvestor});var a=s(60080),n=s(9885),i=s(97669),o=s(47956),l=s(9993),r=s(99986),d=s(96413),c=s(85814),x=s(24577),p=s(34751),f=s(64731),m=s.n(f),u=s(15455),h=s(57114),R=s(85334);function InputSelectText({label:e,width:t="auto",name:s,placeholder:i="",fieldsReasons:o,setFieldReason:l,setFieldText:r}){let d=o?.find(e=>e.field===s),[c,x]=(0,n.useState)("");return(0,n.useEffect)(()=>{d?.reason!==void 0&&x(d.reason)},[d?.reason]),(0,a.jsxs)("div",{className:"input",style:{width:t},children:[a.jsx("p",{className:"text-white mb-1 text-sm",children:e}),(0,a.jsxs)("div",{children:[a.jsx("p",{onClick:()=>{l&&(d?.field?l({field:"",reason:""},s):l({field:s,reason:""},s))},className:`py-3 min-h-[48px] w-full select-none cursor-pointer px-4 text-white rounded-xl ${d?.field?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`,children:i}),d?.field&&a.jsx("input",{type:"text",onChange:({target:e})=>{x(e.value)},onBlur:()=>{r&&r({filter:s,text:c})},value:c,placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})]})]})}s(50496);var v=s(73294);let b={pix:"PIX",boleto:"Boleto",bank_transfer:"Transfer\xeancia Banc\xe1ria"};var j=s(69957);function PhysicalContract({modalityContract:e,contractData:t}){var s;let[i,o]=(0,n.useState)(!1),{id:f}=(0,h.useParams)(),[F,T]=(0,n.useState)(),[g,w]=(0,n.useState)(),[P,S]=(0,n.useState)(),[N,y]=(0,n.useState)(),[A,D]=(0,n.useState)(!1),[I,C]=(0,n.useState)(!1),[O,E]=(0,n.useState)({contract:{auditAprove:!0,reason:""},proofPayment:{auditAprove:!0,reason:""},documentPdf:{auditAprove:!0,reason:""},proofOfResidence:{auditAprove:!0,reason:""}}),{navigation:Z}=(0,R.H)(),[Y,M]=(0,n.useState)([]),onSubmit=e=>{let t={field:"contract",reason:O.contract.reason},s={field:"proofPayment",reason:O.proofPayment.reason},a={field:"documentPdf",reason:O.documentPdf.reason},n={field:"proofOfResidence",reason:O.proofOfResidence.reason};if(O.contract.auditAprove){let e=Y.filter(e=>"contract"!==e.field);M(e)}else Y.push(t);if(O.proofPayment.auditAprove){let e=Y.filter(e=>"proofPayment"!==e.field);M(e)}else Y.push(s);if(O.documentPdf.auditAprove){let e=Y.filter(e=>"documentPdf"!==e.field);M(e)}else Y.push(a);if(O.proofOfResidence.auditAprove){let e=Y.filter(e=>"proofOfResidence"!==e.field);M(e)}else Y.push(n);for(let e of Y)if(""===e.reason){p.Am.warn("O campo de motivo n\xe3o pode estar vazio.");return}return(Y.map(e=>{if(""===e.reason)return p.Am.warn("O campo de motivo n\xe3o pode estar vazio.")}),"APPROVED"===e&&Y.length>0)?p.Am.warn("Para aprovar o contrato n\xe3o pode ter nenhum campo inv\xe1lido marcado."):"REJECTED"===e&&0===Y.length?p.Am.warn("Para rejeitar um contrato selecione pelo menos um motivo."):void("APPROVED"===e&&D(!0),"REJECTED"===e&&C(!0),o(!0),c.Z.post("/audit/contract",{contractId:f,decision:e,rejectionReasons:{reasons:Y}}).then(t=>{p.Am.success(`Contrato ${"APPROVED"===e?"aprovado":"rejeitado"} com sucesso!`),Z("/contratos")}).catch(e=>{(0,x.Z)(e,"Falha ao auditar o contrato.")}).finally(()=>{D(!1),C(!1)}))},filterFieldReasons=(e,t)=>{if(""!==e.field)M([...Y,e]);else{let e=Y.filter(e=>e.field!==t);M(e)}},fieldReasonText=e=>{let t=Y.some(t=>t.field===e.filter);if(t){let t=Y.map(t=>t.field===e.filter?{...t,reason:e.text}:t);M(t)}else M([...Y,{field:e.filter,reason:e.text}])};return(0,a.jsxs)("div",{children:[a.jsx(l.Z,{title:"Dados Pessoais",children:(0,a.jsxs)("div",{className:"flex items-start gap-4 flex-wrap ",children:[a.jsx(InputSelectText,{placeholder:t?.investor.name,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"name",width:"300px",label:"Nome completo"}),a.jsx(InputSelectText,{placeholder:(0,d.VL)(t?.investor.document||""),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"document",width:"200px",label:"CPF"}),a.jsx(InputSelectText,{placeholder:t?.investor.rg,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"rg",width:"200px",label:"Identidade"}),a.jsx(InputSelectText,{placeholder:t?.investor.issuingAgency,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"issuer",width:"200px",label:"Org\xe3o emissor"}),a.jsx(InputSelectText,{placeholder:t?.investor.nationality,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"placeOfBirth",width:"200px",label:"Nacionalidade"}),a.jsx(InputSelectText,{placeholder:t?.investor.occupation,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"occupation",width:"200px",label:"Ocupa\xe7\xe3o"}),a.jsx(InputSelectText,{placeholder:(0,d.gP)(t?.investor.phone.replace("+55","")||""),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,width:"200px",name:"phoneNumber",label:"Celular"}),a.jsx(InputSelectText,{placeholder:m()(t?.investor.birthDate).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"dtBirth",width:"200px",label:"Data de Nascimento"}),a.jsx(InputSelectText,{placeholder:t?.investor.email,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"email",width:"300px",label:"E-mail"}),a.jsx(InputSelectText,{placeholder:t?.investor.motherName,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"motherName",width:"300px",label:"Nome da m\xe3e"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.zipcode,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"zipCode",width:"200px",label:"CEP"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.neighborhood,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"neighborhood",width:"200px",label:"Bairro"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.street,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"street",width:"300px",label:"Rua"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.city,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"city",width:"200px",label:"Cidade"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.state,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"state",width:"150px",label:"Estado"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.number,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"number",width:"200px",label:"N\xfamero"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.complement,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"complement",width:"200px",label:"Complemento"})]})}),a.jsx(l.Z,{title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-start gap-4 flex-wrap",children:[a.jsx(InputSelectText,{placeholder:`${(0,v.Z)(Number(t?.investment.value))}`,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"value",width:"200px",label:"Valor"}),a.jsx(InputSelectText,{placeholder:function(){let e=m()(t?.investment.start),s=m()(t?.investment.end);return String(s.diff(e,"months"))}(),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"term",width:"250px",label:"Prazo investimento - em meses"}),a.jsx(InputSelectText,{placeholder:`${t?.investment.yield||0}%`,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"yield",width:"250px",label:"Taxa Remunera\xe7\xe3o Mensal - em %"}),a.jsx(InputSelectText,{placeholder:(s=t?.investment.purchasedWith||"")in b?b[s]:s,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"purchasedWith",width:"250px",label:"Comprar com"}),"scp"===e&&a.jsx(a.Fragment,{children:a.jsx(InputSelectText,{placeholder:t?.investment.quotesAmount,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"amountQuotes",width:"150px",label:"Quantidade de cotas"})}),a.jsx(InputSelectText,{placeholder:m()(t?.investment.start).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"initDate",width:"200px",label:"Inicio do contrato"}),a.jsx(InputSelectText,{placeholder:m()(t?.investment.end).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"endDate",width:"200px",label:"Final do contrato"}),a.jsx(InputSelectText,{placeholder:t?.investment.isdebenture?"Sim":"N\xe3o",setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"isDebenture",width:"200px",label:"\xc9 Deb\xeanture?"})]})}),a.jsx(l.Z,{title:"Anexo de documentos",children:a.jsx("div",{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.contract.auditAprove?"":"text-red-600"}`,children:"Contrato"}),a.jsx("div",{onClick:()=>E({...O,contract:{...O.contract,auditAprove:!O.contract.auditAprove}}),className:`${O.contract.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{disable:!0,onFileUploaded:T})}),!O.contract.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,contract:{...O.contract,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.contractPdf?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.contractPdf,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.proofPayment.auditAprove?"":"text-red-600"}`,children:"Comprovante"}),a.jsx("div",{onClick:()=>E({...O,proofPayment:{...O.proofPayment,auditAprove:!O.proofPayment.auditAprove}}),className:`${O.proofPayment.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{disable:!0,onFileUploaded:w})}),!O.proofPayment.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,proofPayment:{...O.proofPayment,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.proofPaymentPdf?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.proofPaymentPdf,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.documentPdf.auditAprove?"":"text-red-600"}`,children:"Documento de identidade"}),a.jsx("div",{onClick:()=>E({...O,documentPdf:{...O.documentPdf,auditAprove:!O.documentPdf.auditAprove}}),className:`${O.documentPdf.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{isPdf:!0,disable:!0,onFileUploaded:S})}),!O.documentPdf.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,documentPdf:{...O.documentPdf,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.personalDocument?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.personalDocument,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.proofOfResidence.auditAprove?"":"text-red-600"}`,children:"Comprovante de resid\xeancia"}),a.jsx("div",{onClick:()=>E({...O,proofOfResidence:{...O.proofOfResidence,auditAprove:!O.proofOfResidence.auditAprove}}),className:`${O.proofOfResidence.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{disable:!0,onFileUploaded:S})}),!O.proofOfResidence.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,proofOfResidence:{...O.proofOfResidence,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.proofOfResidence?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.proofOfResidence,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]})]})})}),(0,a.jsxs)("div",{className:"flex gap-4 w-full justify-end",children:[a.jsx(j.z,{loading:I,onClick:()=>onSubmit("REJECTED"),variant:"destructive",size:"lg",children:"Rejeitar"}),a.jsx(j.z,{loading:A,size:"lg",onClick:()=>onSubmit("APPROVED"),children:"Aprovar"})]})]})}var F=s(33050);function BusinessContract({modalityContract:e,contractData:t}){let[s,i]=(0,n.useState)(!1),{id:o}=(0,h.useParams)(),[f,b]=(0,n.useState)(),[j,T]=(0,n.useState)(),[g,w]=(0,n.useState)(),[P,S]=(0,n.useState)(),[N,y]=(0,n.useState)(!1),[A,D]=(0,n.useState)(!1),[I,C]=(0,n.useState)(""),[O,E]=(0,n.useState)({contract:{auditAprove:!0,reason:""},proofPayment:{auditAprove:!0,reason:""},documentPdf:{auditAprove:!0,reason:""},proofOfResidence:{auditAprove:!0,reason:""}}),{navigation:Z}=(0,R.H)(),[Y,M]=(0,n.useState)([]),onSubmit=e=>{let t={field:"contract",reason:O.contract.reason},s={field:"proofPayment",reason:O.proofPayment.reason},a={field:"documentPdf",reason:O.documentPdf.reason},n={field:"proofOfResidence",reason:O.proofOfResidence.reason};if(O.contract.auditAprove){let e=Y.filter(e=>"contract"!==e.field);M(e)}else Y.push(t);if(O.proofPayment.auditAprove){let e=Y.filter(e=>"proofPayment"!==e.field);M(e)}else Y.push(s);if(O.documentPdf.auditAprove){let e=Y.filter(e=>"documentPdf"!==e.field);M(e)}else Y.push(a);if(O.proofOfResidence.auditAprove){let e=Y.filter(e=>"proofOfResidence"!==e.field);M(e)}else Y.push(n);for(let e of Y)if(""===e.reason){p.Am.warn("O campo de motivo n\xe3o pode estar vazio.");return}return(Y.map(e=>{if(""===e.reason)return p.Am.warn("O campo de motivo n\xe3o pode estar vazio.")}),"APPROVED"===e&&Y.length>0)?p.Am.warn("Para aprovar o contrato n\xe3o pode ter nenhum campo inv\xe1lido marcado."):"REJECTED"===e&&0===Y.length?p.Am.warn("Para rejeitar um contrato selecione pelo menos um motivo."):void("APPROVED"===e&&y(!0),"REJECTED"===e&&D(!0),i(!0),c.Z.post("/audit/contract",{contractId:o,decision:e,rejectionReasons:{reasons:Y}}).then(t=>{p.Am.success(`Contrato ${"APPROVED"===e?"aprovado":"rejeitado"} com sucesso!`),Z("/contratos")}).catch(e=>{(0,x.Z)(e,"Falha ao auditar o contrato.")}).finally(()=>{"APPROVED"===e&&y(!1),"REJECTED"===e&&D(!1)}))},filterFieldReasons=(e,t)=>{if(""!==e.field)M([...Y,e]);else{let e=Y.filter(e=>e.field!==t);M(e)}},fieldReasonText=e=>{let t=Y.some(t=>t.field===e.filter);if(t){let t=Y.map(t=>t.field===e.filter?{...t,reason:e.text}:t);M(t)}else M([...Y,{field:e.filter,reason:e.text}])};return(0,a.jsxs)("div",{children:[a.jsx(l.Z,{title:"Dados do representante",children:(0,a.jsxs)("div",{className:"flex items-start gap-4 flex-wrap ",children:[a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.name,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"ownerName",width:"300px",label:"Nome completo"}),a.jsx(InputSelectText,{placeholder:(0,d.VL)(t?.investor.responsibleOwner.document||""),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"ownerDocument",width:"200px",label:"CPF"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.rg,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"rg",width:"200px",label:"Identidade"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.issuingAgency,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"issuer",width:"200px",label:"Org\xe3o emissor"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.nationality,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"placeOfBirth",width:"200px",label:"Nacionalidade"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.occupation,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"occupation",width:"200px",label:"Ocupa\xe7\xe3o"}),a.jsx(InputSelectText,{placeholder:(0,d.gP)(t?.investor.responsibleOwner.phone.replace("+55","")||""),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,width:"200px",name:"phoneNumber",label:"Celular"}),a.jsx(InputSelectText,{placeholder:m()(t?.investor.responsibleOwner.birthDate).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"dtBirth",width:"200px",label:"Data de Nascimento"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.email,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"email",width:"300px",label:"E-mail"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.motherName,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"motherName",width:"300px",label:"Nome da m\xe3e"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.address.zipcode,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"zipCode",width:"200px",label:"CEP"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.address.neighborhood,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"neighborhood",width:"200px",label:"Bairro"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.address.street,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"street",width:"300px",label:"Rua"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.address.city,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"city",width:"200px",label:"Cidade"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.address.state,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"state",width:"150px",label:"Estado"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.address.number,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"number",width:"200px",label:"N\xfamero"}),a.jsx(InputSelectText,{placeholder:t?.investor.responsibleOwner.address.complement,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"complement",width:"200px",label:"Complemento"})]})}),a.jsx(l.Z,{title:"Dados da empresa",children:(0,a.jsxs)("div",{className:"flex items-start gap-4 flex-wrap ",children:[a.jsx(InputSelectText,{placeholder:t?.investor.companyName,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"companyName",width:"300px",label:"Nome"}),a.jsx(InputSelectText,{placeholder:(0,d.PK)(t?.investor.document||""),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"document",width:"200px",label:"CNPJ"}),a.jsx(InputSelectText,{placeholder:t?.investor.fantasyName,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"fantasyName",width:"300px",label:"Nome fantasia"}),a.jsx(InputSelectText,{placeholder:t?.investor.businessType,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"businessType",width:"150px",label:"Tipo"}),a.jsx(InputSelectText,{placeholder:(0,F.Z)(t?.investor.openingDate||""),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"openingDate",width:"180px",label:"Data de abertura"}),a.jsx(InputSelectText,{placeholder:t?.investor.email,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"email",width:"300px",label:"E-mail"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.zipcode,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"zipCode",width:"200px",label:"CEP"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.neighborhood,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"neighborhood",width:"200px",label:"Bairro"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.street,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"street",width:"300px",label:"Rua"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.city,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"city",width:"200px",label:"Cidade"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.state,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"state",width:"150px",label:"Estado"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.number,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"number",width:"200px",label:"N\xfamero"}),a.jsx(InputSelectText,{placeholder:t?.investor.address.complement,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"complement",width:"200px",label:"Complemento"})]})}),a.jsx(l.Z,{title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-start gap-4 flex-wrap",children:[a.jsx(InputSelectText,{placeholder:`${(0,v.Z)(Number(t?.investment.value))}`,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"value",width:"200px",label:"Valor"}),a.jsx(InputSelectText,{placeholder:function(){let e=m()(t?.investment.start),s=m()(t?.investment.end);return String(s.diff(e,"months"))}(),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"term",width:"250px",label:"Prazo investimento - em meses"}),a.jsx(InputSelectText,{placeholder:`${t?.investment.yield||0}%`,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"yield",width:"250px",label:"Taxa Remunera\xe7\xe3o Mensal - em %"}),a.jsx(InputSelectText,{placeholder:t?.investment.purchasedWith,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"purchasedWith",width:"250px",label:"Comprar com"}),"scp"===e&&a.jsx(a.Fragment,{children:a.jsx(InputSelectText,{placeholder:t?.investment.quotesAmount,setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"amountQuotes",width:"150px",label:"Quantidade de cotas"})}),a.jsx(InputSelectText,{placeholder:m()(t?.investment.start).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"initDate",width:"200px",label:"Inicio do contrato"}),a.jsx(InputSelectText,{placeholder:m()(t?.investment.end).format("DD/MM/YYYY"),setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"endDate",width:"200px",label:"Final do contrato"}),a.jsx(InputSelectText,{placeholder:t?.investment.isdebenture?"Sim":"N\xe3o",setFieldText:fieldReasonText,fieldsReasons:Y,setFieldReason:filterFieldReasons,name:"isDebenture",width:"200px",label:"\xc9 Deb\xeanture?"})]})}),a.jsx(l.Z,{title:"Anexo de documentos",children:a.jsx("div",{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.contract.auditAprove?"":"text-red-600"}`,children:"Contrato"}),a.jsx("div",{onClick:()=>E({...O,contract:{...O.contract,auditAprove:!O.contract.auditAprove}}),className:`${O.contract.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{disable:!0,onFileUploaded:b})}),!O.contract.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,contract:{...O.contract,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.contractPdf?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.contractPdf,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.proofPayment.auditAprove?"":"text-red-600"}`,children:"Comprovante"}),a.jsx("div",{onClick:()=>E({...O,proofPayment:{...O.proofPayment,auditAprove:!O.proofPayment.auditAprove}}),className:`${O.proofPayment.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{disable:!0,onFileUploaded:T})}),!O.proofPayment.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,proofPayment:{...O.proofPayment,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.proofPaymentPdf?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.proofPaymentPdf,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.documentPdf.auditAprove?"":"text-red-600"}`,children:"Documento de identidade"}),a.jsx("div",{onClick:()=>E({...O,documentPdf:{...O.documentPdf,auditAprove:!O.documentPdf.auditAprove}}),className:`${O.documentPdf.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{disable:!0,onFileUploaded:w})}),!O.documentPdf.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,documentPdf:{...O.documentPdf,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.personalDocument?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.personalDocument,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:`mb-1 ${O.proofOfResidence.auditAprove?"":"text-red-600"}`,children:"Comprovante de resid\xeancia"}),a.jsx("div",{onClick:()=>E({...O,proofOfResidence:{...O.proofOfResidence,auditAprove:!O.proofOfResidence.auditAprove}}),className:`${O.proofOfResidence.auditAprove?"":"border-red-600 border-[3px]"}`,children:a.jsx(u.Z,{disable:!0,onFileUploaded:w})}),!O.proofOfResidence.auditAprove&&a.jsx("div",{children:a.jsx("input",{onChange:({target:e})=>{E({...O,proofOfResidence:{...O.proofOfResidence,reason:e.value}})},placeholder:"Motivo",className:"h-12 w-full px-4 my-2 text-white rounded-xl ring-1 ring-white ring-inset bg-zinc-900 flex-1"})}),t?.files.proofOfResidence?a.jsx("div",{children:a.jsx(r.Z,{label:"Ver documento",className:"mt-2 bg-orange-linear",loading:!1,size:"sm",handleSubmit:()=>window.open(t?.files.proofOfResidence,"_blanck")})}):a.jsx("p",{className:"text-sm",children:"Documento n\xe3o anexado"})]})]})})}),(0,a.jsxs)("div",{className:"flex gap-4 w-full justify-end",children:[a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(r.Z,{label:"Rejeitar",loading:A,disabled:s,handleSubmit:()=>onSubmit("REJECTED"),className:"bg-red-700 hover:bg-red-800"})}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(r.Z,{label:"Aprovar",loading:N,disabled:s,handleSubmit:()=>onSubmit("APPROVED")})})]})]})}var T=s(90682);function CreateInvestor(){let{id:e}=(0,h.useParams)(),[t,s]=(0,n.useState)("pf"),[r,d]=(0,n.useState)("mutuo"),[f,m]=(0,n.useState)(),[u,v]=(0,n.useState)(),[b,j]=(0,n.useState)(!0),F=(0,T.e)(),{navigation:g}=(0,R.H)();(0,n.useEffect)(()=>{let checkAuth=async()=>{try{if(F?.name!=="superadmin"){p.Am.error("Voc\xea n\xe3o tem permiss\xe3o para acessar essa p\xe1gina"),g("/home");return}j(!1)}catch(e){p.Am.error("Erro ao verificar permiss\xf5es"),g("/home")}};checkAuth()},[]);let getContractData=()=>{c.Z.get(`/contract/get-detail/${e}`).then(e=>{d(e.data.investment.type),"business"===e.data.investor.type?(m(e.data),s("pj")):(v(e.data),s("pf"))}).catch(e=>{(0,x.Z)(e,"Erro ao buscar dados do contrato.")})};return((0,n.useEffect)(()=>{getContractData()},[]),b)?(0,a.jsxs)(a.Fragment,{children:[a.jsx(i.Z,{}),a.jsx(o.Z,{children:a.jsx("div",{className:"flex items-center justify-center h-screen",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"}),a.jsx("p",{className:"text-white text-lg",children:"Verificando permiss\xf5es..."})]})})})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(i.Z,{}),a.jsx(o.Z,{children:(0,a.jsxs)("div",{className:"m-3",children:[a.jsx(l.Z,{title:"Auditoria de contrato",children:(0,a.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)("select",{value:t,disabled:!0,onChange:({target:e})=>s(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[a.jsx("option",{value:"pf",selected:!0,children:"Pessoa F\xedsica"}),a.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white mb-1",children:"Tipo de contrato"}),(0,a.jsxs)("select",{value:r,disabled:!0,onChange:({target:e})=>d("mutuo"===e.value?"mutuo":"scp"),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer",children:[a.jsx("option",{selected:!0,value:"mutuo",children:"M\xfatuo"}),a.jsx("option",{value:"scp",children:"SCP"})]})]})]})}),"pj"===t?a.jsx(BusinessContract,{contractData:f,modalityContract:r}):a.jsx(PhysicalContract,{contractData:u,modalityContract:r})]})})]})}},33050:(e,t,s)=>{"use strict";s.d(t,{Z:()=>formatDate,l:()=>formatDateToEnglishType});var a=s(95081),n=s.n(a);function formatDate(e){return n().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?n().utc(e).format("YYYY-MM-DD"):n().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},85334:(e,t,s)=>{"use strict";s.d(t,{H:()=>useNavigation});var a=s(57114);let useNavigation=()=>{let e=(0,a.useRouter)();return{navigation:t=>e.push(t)}}},73294:(e,t,s)=>{"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}s.d(t,{A:()=>cleanValue,Z:()=>formatValue})},36179:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>i,default:()=>r});var a=s(17536);let n=(0,a.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\contratos\contrato\[id]\page.tsx`),{__esModule:i,$$typeof:o}=n,l=n.default,r=l},50496:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[4103,6426,4731,8813,5081,7207,278,7669,87],()=>__webpack_exec__(24813));module.exports=s})();