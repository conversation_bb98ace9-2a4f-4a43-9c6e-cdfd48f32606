"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UploadPaymentProofService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadPaymentProofService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const typeorm_2 = require("typeorm");
const create_notification_service_1 = require("../../notifications/services/create-notification.service");
const notification_entity_1 = require("../../../shared/database/typeorm/entities/notification.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
let UploadPaymentProofService = UploadPaymentProofService_1 = class UploadPaymentProofService {
    constructor(contractRepository, ownerRoleRelationRepository, createNotificationService) {
        this.contractRepository = contractRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.createNotificationService = createNotificationService;
        this.logger = new common_1.Logger(UploadPaymentProofService_1.name);
    }
    async perform(contractId, file, data, userId) {
        this.logger.log(`Iniciando upload de comprovante para contrato ${contractId} pelo usuário ${userId}`);
        const contract = await this.contractRepository.findOne({
            where: { id: contractId },
            relations: ['investor', 'investor.owner', 'investor.business'],
        });
        if (!contract) {
            throw new common_1.NotFoundException('Contrato não encontrado');
        }
        if (contract.status !== contract_status_enum_1.ContractStatusEnum.AWAITING_PAYMENT_PROOF) {
            throw new common_1.BadRequestException('Apenas contratos aguardando comprovante de pagamento podem receber anexos');
        }
        const userProfile = await this.ownerRoleRelationRepository.findOne({
            where: [
                { ownerId: userId },
                { businessId: userId },
            ],
            relations: {
                business: true,
                owner: true,
                role: true,
            },
        });
        if (!userProfile) {
            throw new common_1.BadRequestException('Perfil de usuário não encontrado.');
        }
        try {
            const fileUrl = await this.uploadFileToStorage(file);
            await this.contractRepository.update(contractId, {
                paymentProofUrl: fileUrl,
                paymentProofUploadedAt: new Date(),
                observations: data.observations,
                status: contract_status_enum_1.ContractStatusEnum.AWAITING_AUDIT,
                updatedAt: new Date(),
            });
            this.logger.log(`Comprovante anexado com sucesso para contrato ${contractId}`);
            await this.createNotificationService.create({
                userOwnerRoleRelationId: userProfile.id,
                description: `Comprovante de pagamento anexado para contrato ${contractId}. Aguardando análise da auditoria.`,
                title: `Comprovante de Pagamento Anexado`,
                type: notification_entity_1.NotificationTypeEnum.PAYMENT_PROOF_UPLOADED,
                contractId: contractId,
                contractValue: contract.investmentValue || 0,
                investorId: contract.investorId,
            });
            this.logger.log('Notificação de auditoria criada com sucesso');
            return {
                success: true,
                message: 'Comprovante anexado com sucesso. Contrato enviado para auditoria.',
            };
        }
        catch (error) {
            this.logger.error('Erro ao anexar comprovante:', error);
            throw new common_1.BadRequestException(`Erro ao anexar comprovante: ${error.message}`);
        }
    }
    async uploadFileToStorage(file) {
        const timestamp = Date.now();
        const fileName = `payment-proof-${timestamp}-${file.originalname}`;
        return `https://storage.example.com/payment-proofs/${fileName}`;
    }
    async getContractsAwaitingPaymentProof(userId) {
        this.logger.log(`Buscando contratos aguardando comprovante para usuário ${userId}`);
        const userProfile = await this.ownerRoleRelationRepository.findOne({
            where: [
                { ownerId: userId },
                { businessId: userId },
            ],
            relations: {
                role: true,
            },
        });
        if (!userProfile) {
            throw new common_1.BadRequestException('Perfil de usuário não encontrado.');
        }
        const queryBuilder = this.contractRepository
            .createQueryBuilder('contract')
            .leftJoinAndSelect('contract.investor', 'investor')
            .where('contract.status = :status', {
            status: contract_status_enum_1.ContractStatusEnum.AWAITING_PAYMENT_PROOF
        });
        if (userProfile.role.name !== 'superadmin') {
            queryBuilder.andWhere('contract.brokerId = :brokerId', {
                brokerId: userProfile.businessId || userProfile.ownerId,
            });
        }
        const contracts = await queryBuilder
            .leftJoinAndSelect('investor.owner', 'owner')
            .leftJoinAndSelect('investor.business', 'business')
            .orderBy('contract.createdAt', 'DESC')
            .getMany();
        this.logger.log(`Encontrados ${contracts.length} contratos aguardando comprovante`);
        return contracts;
    }
};
exports.UploadPaymentProofService = UploadPaymentProofService;
exports.UploadPaymentProofService = UploadPaymentProofService = UploadPaymentProofService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        create_notification_service_1.CreateNotificationService])
], UploadPaymentProofService);
//# sourceMappingURL=upload-payment-proof.service.js.map