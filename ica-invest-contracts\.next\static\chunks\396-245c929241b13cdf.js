(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[396],{2875:function(e,t,r){"use strict";r.d(t,{Z:function(){return Button}});var i=r(7437),o=r(8700);function Button(e){let{handleSubmit:t,loading:r,label:a,disabled:n,className:s,...u}=e;return(0,i.jsx)(o.z,{...u,onClick:t,loading:r,disabled:n,className:s,children:a})}},5233:function(e,t,r){"use strict";r.d(t,{Z:function(){return CoverForm}});var i=r(7437);function CoverForm(e){let{title:t,children:r,width:o="auto",color:a="withe"}=e;return(0,i.jsxs)("div",{className:"md:w-auto w-full",children:[(0,i.jsx)("p",{className:"text-xl text-white mb-2",children:t}),(0,i.jsx)("div",{className:"mb-10 m-auto bg-opacity-90 rounded-2xl shadow-current ".concat("withe"===a?"bg-zinc-900 border border-[#FF9900] p-5":"pt-1"),children:r})]})}},3220:function(e,t,r){"use strict";var i=r(7437),o=r(2265),a=r(1543),n=r(9367);let s=(0,o.forwardRef)((e,t)=>{let{label:r,bg:s,type:u,...d}=e,[l,c]=(0,o.useState)(!1);return(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white mb-1",children:r}),(0,i.jsxs)("div",{className:"custom-input-wrapper h-12 w-full text-white rounded-xl ring-[#FF9900] ring-1 ".concat("month"===u?"":"ring-inset"," ").concat("transparent"===s?"bg-black":"bg-[#1C1C1C]"," flex-1 flex relative"),children:[(0,i.jsx)("input",{ref:t,type:l&&"password"===u?"text":u,...d,className:"w-full h-12 flex-1 px-4 bg-transparent rounded-xl"}),"password"===u&&(0,i.jsx)("div",{className:"mr-2 cursor-pointer absolute right-0 top-[50%] translate-y-[-50%]",onClick:()=>c(!l),children:l?(0,i.jsx)(a.Z,{width:20}):(0,i.jsx)(n.Z,{width:20})})]})]})});s.displayName="Input",t.Z=s},5554:function(e,t,r){"use strict";r.d(t,{Z:function(){return InputSelect}});var i=r(7437);function InputSelect(e){let{optionSelected:t,options:r,setOptionSelected:o,label:a,placeHolder:n="",width:s="100%",register:u=()=>{},error:d,errorMessage:l,name:c="",disableErrorMessage:m=!1,disabled:h=!1}=e;return(0,i.jsxs)("div",{className:"inputSelect relative group",style:{width:s},children:[(0,i.jsx)("p",{className:"text-white mb-1 text-sm",children:a}),(0,i.jsxs)("select",{disabled:m&&!l,...u(c),value:t,onChange:e=>{let{target:t}=e;o&&o(t.value)},id:c,className:"h-12 w-full px-4 ".concat(h?"text-zinc-400":"text-white"," rounded-xl ").concat(d?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),children:[n&&(0,i.jsx)("option",{selected:!0,disabled:!0,value:"",children:n}),r.map((e,t)=>(0,i.jsx)("option",{className:"cursor-pointer",value:e.value,children:e.label},t))]}),d&&(0,i.jsx)("div",{className:" absolute gr max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[90%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:l})]})}},4207:function(e,t,r){"use strict";r.d(t,{Z:function(){return InputText}});var i=r(7437);function InputText(e){let{label:t,setValue:r,error:o,errorMessage:a,width:n="auto",register:s,name:u,placeholder:d="",type:l="text",disabled:c=!1,minDate:m,minLength:h,maxLength:x,maxDate:f,disableErrorMessage:p=!1,onBlur:b,value:g,onChange:v}=e;return(0,i.jsxs)("div",{className:"input relative group",style:{width:n},children:[(0,i.jsxs)("p",{className:"text-white mb-1 text-sm",children:[t,o&&!p&&(0,i.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",a]})]}),(0,i.jsx)("input",{...s(u),placeholder:d,type:l,id:u,disabled:c,min:m,max:f,minLength:h,maxLength:x,...r?{onChange:e=>{let{target:t}=e;return r(t.value)}}:{},onBlur:b,className:"h-12 w-full px-4 ".concat(c?"text-zinc-400":"text-white"," rounded-xl ").concat(o?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),...void 0!==g?{value:g}:{},...v?{onChange:v}:{}}),o&&(0,i.jsx)("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:a})]})}r(9514)},3336:function(e,t,r){"use strict";r.d(t,{Z:function(){return SelectSearch}});var i=r(7437),o=r(4164),a=r(2307),n=r(2265),s=r(8689);function SelectSearch(e){let{items:t,setValue:r,label:u,selectable:d=!1,disabled:l=!1,handleChange:c,loading:m=!1}=e,[h,x]=(0,n.useState)(!1),[f,p]=(0,n.useState)(""),[b,g]=(0,n.useState)([]),v=(0,n.useRef)(null),C=(0,n.useRef)(null);return(0,n.useEffect)(()=>{p("")},[m]),(0,n.useEffect)(()=>{if(""===f)return g(t);let e=null==t?void 0:t.filter(e=>e.name.toLowerCase().includes(f.toLowerCase()));g(e)},[f]),(0,n.useEffect)(()=>{g(t)},[t]),(0,n.useEffect)(()=>{let handleFocus=()=>{x(!0)},e=v.current;return e&&e.addEventListener("focus",handleFocus),()=>{e&&e.removeEventListener("focus",handleFocus)}},[]),(0,n.useEffect)(()=>{let handleClickOutside=e=>{C.current&&!C.current.contains(e.target)&&v.current&&!v.current.contains(e.target)&&x(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]),(0,i.jsx)("div",{className:"w-full",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("p",{className:"text-white mb-1",children:u}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("input",{disabled:l||m,ref:v,type:"text",value:m?"Carregando...":f,onChange:e=>{let{target:t}=e;return p(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 cursor-pointer"}),(0,i.jsx)("div",{className:"absolute right-2 top-[50%] translate-y-[-50%]",children:h?(0,i.jsx)(a.Z,{width:20,color:"#fff"}):(0,i.jsx)(o.Z,{width:20,color:"#fff"})})]}),h&&(0,i.jsx)("div",{ref:C,className:"text-white absolute w-full border-b border-x px-2 top-[98%] z-40 bg-black max-h-52 overflow-y-auto scroll-",children:m?(0,i.jsxs)("div",{className:"py-1",children:[(0,i.jsx)(s.j,{height:"25px",className:"my-1"}),(0,i.jsx)(s.j,{height:"25px",className:"my-1"})]}):(null==b?void 0:b.length)>0?null==b?void 0:b.map(e=>(0,i.jsx)("div",{onClick:()=>{x(!1),c&&c(e),d?(p(""),r("".concat(e.name," - ").concat(e.id))):(r(e.id),p(e.name))},className:"cursor-pointer my-1 hover:bg-zinc-900",children:e.name},e.id)):(0,i.jsx)("div",{className:"py-3",children:(0,i.jsx)("p",{className:"text-zinc-400",children:"Nenhum dado encontrado!"})})})]})})}},919:function(e,t,r){"use strict";r.d(t,{H:function(){return getFinalDataWithMount},Z:function(){return getDataFilter}});var i=r(2067),o=r.n(i);function getDataFilter(e){switch(e){case"TODAY":{let e=o()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}case"WEEK":{let e=o()().startOf("isoWeek").format("YYYY-MM-DD"),t=o()().endOf("isoWeek").format("YYYY-MM-DD");return{startDate:e,endDate:t}}case"MONTH":{let e=o()().startOf("month").format("YYYY-MM-DD"),t=o()().endOf("month").format("YYYY-MM-DD");return{startDate:e,endDate:t}}default:{let e=o()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}}}function getFinalDataWithMount(e){let{investDate:t,startDate:r}=e,i=o()(r).format("DD/MM/YYYY"),a=Number(t),n=o()(i,"DD/MM/YYYY").add(a,"months").format("DD/MM/YYYY");return n}},7412:function(e,t,r){"use strict";r.d(t,{x:function(){return getZipCode}});var i=r(4829);async function getZipCode(e){let t=e.replace(/\D/g,"");if(8!==t.length)return null;try{let e=await i.Z.get("https://viacep.com.br/ws/".concat(t,"/json/")),r=e.data;if(r&&!r.erro)return{neighborhood:r.bairro||"",street:r.logradouro||"",city:r.localidade||"",state:r.uf||""};return null}catch(e){return console.error("Erro ao buscar o CEP:",e),null}}},6654:function(e,t,r){"use strict";r.d(t,{Z:function(){return returnError}});var i=r(3014);function returnError(e,t){var r,o,a,n;let s=(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(r=o.data)||void 0===r?void 0:r.message)||(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.error);if(Array.isArray(s))return s.forEach(e=>{i.Am.error(e,{toastId:e})}),s.join("\n");if("string"==typeof s)return i.Am.error(s,{toastId:s}),s;if("object"==typeof s&&null!==s){let e=Object.values(s).flat().join("\n");return i.Am.error(e,{toastId:e}),e}return i.Am.error(t,{toastId:t}),t}},7014:function(e,t){"use strict";t.Z=function(e){return e.trim().replace(/\s+/g," ").toLowerCase().split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}},3277:function(e,t,r){"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}r.d(t,{Z:function(){return formatNumberValue}})},7157:function(e,t,r){"use strict";r.d(t,{m:function(){return isUnderage}});var i=r(2067),o=r.n(i);function isUnderage(e){let t=o()(e),r=o()().diff(t,"years");return r<18}},601:function(e,t){"use strict";t.Z=function(e){return["AC","AL","AP","AM","BA","CE","DF","ES","GO","MA","MT","MS","MG","PA","PB","PR","PE","PI","RJ","RN","RS","RO","RR","SC","SP","SE","TO"].includes(e.toUpperCase())}},977:function(e,t,r){"use strict";r.d(t,{$r:function(){return d},_R:function(){return l}});var i=r(5691),o=r(5968),a=r(7848),n=r(4425),s=r(601),u=r(7157);let d=i.Ry().shape({isSCP:i.O7(),name:i.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:i.Z_().test("valid-document","CPF inv\xe1lido",e=>!!e&&(0,a.p)((0,o.p4)(e||""))).required("Campo obrigat\xf3rio"),email:i.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),rg:i.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),phoneNumber:i.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").test("test-phone","N\xfamero de telefone inv\xe1lido",e=>!!e&&(0,n.Z)((0,o.p4)(e))).required("Campo obrigat\xf3rio"),dtBirth:i.Z_().test("dtBirth-error","O investidor n\xe3o pode ser menor de idade.",e=>!!e&&!(0,u.m)(e)).required("Campo obrigat\xf3rio"),motherName:i.Z_().min(3,"Nome da m\xe3e deve conter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),zipCode:i.Z_().required("Obrigat\xf3rio"),neighborhood:i.Z_().required("Obrigat\xf3rio"),state:i.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").test("state-error","Estado inv\xe1lido!",e=>!!e&&(0,s.Z)(e)).required("Obrigat\xf3rio"),city:i.Z_().required("Obrigat\xf3rio"),complement:i.Z_().default("").notRequired(),number:i.Z_().required("Obrigat\xf3rio"),street:i.Z_().required("Obrigat\xf3rio"),value:i.Z_().required("Obrigat\xf3rio"),term:i.Z_().required("Obrigat\xf3rio"),yield:i.Z_().test("yield-error","Taxa Remunera\xe7\xe3o Mensal inv\xe1lida",e=>{if(!e)return!1;let t=Number(e.replace(",","."));return t>0&&t<=5}).required("Obrigat\xf3rio"),purchaseWith:i.Z_().required("Obrigat\xf3rio"),initDate:i.Z_().required("Obrigat\xf3rio"),endDate:i.Z_().required("Obrigat\xf3rio"),profile:i.Z_().required("Obrigat\xf3rio"),isDebenture:i.Z_().required("Obrigat\xf3rio"),bank:i.Z_().min(2,"Nome do banco muito curto").required("Obrigat\xf3rio"),accountNumber:i.Z_().min(3,"Conta inv\xe1lida").required("Obrigat\xf3rio"),agency:i.Z_().min(2,"Ag\xeancia inv\xe1lida").required("Obrigat\xf3rio"),pix:i.Z_().required("Obrigat\xf3rio"),observations:i.Z_().notRequired(),issuer:i.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:i.Z_().required("Campo obrigat\xf3rio"),occupation:i.Z_().required("Campo obrigat\xf3rio"),amountQuotes:i.Z_().when("isSCP",(e,t)=>!0===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired())}).required(),l=i.Ry().shape({isSCP:i.O7(),email:i.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"),phoneNumber:i.Z_().test("test-phone","N\xfamero de telefone inv\xe1lido",e=>!!e&&(0,n.Z)((0,o.p4)(e))).max(15,"N\xfamero de telefone inv\xe1lido!").required("Campo obrigat\xf3rio"),dtBirth:i.Z_().test("dtBirth-error","O representante n\xe3o pode ser menor de idade.",e=>!!e&&!(0,u.m)(e)).required("Campo obrigat\xf3rio"),rg:i.Z_().min(3,"RG deve ter no m\xednimo 3 caracteres").required("Obrigat\xf3rio"),ownerName:i.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),ownerDocument:i.Z_().test("valid-document","CPF inv\xe1lido",e=>!!e&&(0,a.p)((0,o.p4)(e||""))).min(3,"O nome da m\xe3e deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),motherName:i.Z_().required("Campo obrigat\xf3rio"),issuer:i.Z_().required("Campo obrigat\xf3rio"),placeOfBirth:i.Z_().required("Campo obrigat\xf3rio"),occupation:i.Z_().required("Campo obrigat\xf3rio"),name:i.Z_().min(3,"O nome deve ter no m\xednimo 3 caracteres").required("Campo obrigat\xf3rio"),document:i.Z_().test("valid-document-business","CNPJ inv\xe1lido",e=>!!e&&(0,a.w)((0,o.p4)(e||""))).required("Campo obrigat\xf3rio"),companyType:i.Z_().required("Obrigat\xf3rio"),zipCode:i.Z_().required("Obrigat\xf3rio"),neighborhood:i.Z_().required("Obrigat\xf3rio"),state:i.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").test("state-error","Estado inv\xe1lido!",e=>!!e&&(0,s.Z)(e)).required("Obrigat\xf3rio"),city:i.Z_().required("Obrigat\xf3rio"),complement:i.Z_().default("").notRequired(),number:i.Z_().required("Obrigat\xf3rio"),street:i.Z_().required("Obrigat\xf3rio"),value:i.Z_().required("Obrigat\xf3rio"),term:i.Z_().required("Obrigat\xf3rio"),yield:i.Z_().test("yield-error","Taxa Remunera\xe7\xe3o Mensal inv\xe1lida",e=>{if(!e)return!1;let t=Number(e.replace(",","."));return t>0&&t<=5}).required("Obrigat\xf3rio"),purchaseWith:i.Z_().required("Obrigat\xf3rio"),initDate:i.Z_().required("Obrigat\xf3rio"),endDate:i.Z_().required("Obrigat\xf3rio"),profile:i.Z_().required("Obrigat\xf3rio"),isDebenture:i.Z_().required("Obrigat\xf3rio"),amountQuotes:i.Z_().when("isSCP",(e,t)=>!0===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),bank:i.Z_().min(2,"Nome do banco muito curto").required("Obrigat\xf3rio"),accountNumber:i.Z_().min(3,"Conta inv\xe1lida").required("Obrigat\xf3rio"),agency:i.Z_().min(2,"Ag\xeancia inv\xe1lida").required("Obrigat\xf3rio"),pix:i.Z_().required("Obrigat\xf3rio"),observations:i.Z_().notRequired(),companyNumber:i.Z_().required("Campo obrigat\xf3rio"),companyComplement:i.Z_().default("").notRequired(),companyCity:i.Z_().required("Campo obrigat\xf3rio"),companyState:i.Z_().min(2,"Estado inv\xe1lido!").max(2,"Estado inv\xe1lido!").test("company-state-error","Estado inv\xe1lido!",e=>!!e&&(0,s.Z)(e)).required("Campo obrigat\xf3rio"),companyStreet:i.Z_().required("Campo obrigat\xf3rio"),companyZipCode:i.Z_().required("Campo obrigat\xf3rio"),companyNeighborhood:i.Z_().required("Campo obrigat\xf3rio")}).required();i.Ry().shape({tipoContrato:i.Z_().required("Tipo de Contrato \xe9 obrigat\xf3rio"),nomeCompleto:i.Z_().required("Nome Completo \xe9 obrigat\xf3rio"),identidade:i.Z_().required("Identidade \xe9 obrigat\xf3ria"),celular:i.Z_().required("Celular \xe9 obrigat\xf3rio").matches(/(\(\d{2}\)\s?\d{8,9})/,"Celular inv\xe1lido"),cpf:i.Z_().required("CPF \xe9 obrigat\xf3rio").matches(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/,"CPF inv\xe1lido"),dataNascimento:i.Z_().required("Data de Nascimento \xe9 obrigat\xf3ria").matches(/^\d{2}\/\d{2}\/\d{4}$/,"Data inv\xe1lida"),nomeMae:i.Z_().required("Nome da M\xe3e \xe9 obrigat\xf3rio"),email:i.Z_().email("E-mail inv\xe1lido").required("E-mail \xe9 obrigat\xf3rio"),cep:i.Z_().required("CEP \xe9 obrigat\xf3rio").matches(/^\d{8}$/,"CEP inv\xe1lido"),cidade:i.Z_().required("Cidade \xe9 obrigat\xf3ria"),endereco:i.Z_().required("Endere\xe7o \xe9 obrigat\xf3rio"),numero:i.Z_().required("N\xfamero \xe9 obrigat\xf3rio"),complemento:i.Z_().nullable(),banco:i.Z_().required("Nome do Banco \xe9 obrigat\xf3rio"),conta:i.Z_().required("Conta \xe9 obrigat\xf3ria"),agencia:i.Z_().required("Ag\xeancia \xe9 obrigat\xf3ria"),chavePix:i.Z_().required("Chave PIX \xe9 obrigat\xf3ria"),observacoes:i.Z_().nullable(),modalidade:i.Z_().required("Modalidade \xe9 obrigat\xf3ria"),valorInvestimento:i.Z_().required("Valor do Investimento \xe9 obrigat\xf3rio").matches(/^R?\$[\d.,]+$/,"Valor inv\xe1lido"),prazoInvestimento:i.Rx().typeError("Prazo deve ser um n\xfamero").integer("Prazo deve ser inteiro").required("Prazo do Investimento \xe9 obrigat\xf3rio"),taxaRemuneracao:i.Rx().typeError("Taxa deve ser um n\xfamero").required("Taxa de Remunera\xe7\xe3o Mensal \xe9 obrigat\xf3ria"),comprarCom:i.Z_().required("Forma de compra \xe9 obrigat\xf3ria"),inicioContrato:i.Z_().required("In\xedcio do Contrato \xe9 obrigat\xf3rio").matches(/^\d{2}\/\d{2}\/\d{4}$/,"Data inv\xe1lida"),fimContrato:i.Z_().required("Fim do Contrato \xe9 obrigat\xf3rio").matches(/^\d{2}\/\d{2}\/\d{4}$/,"Data inv\xe1lida"),perfil:i.Z_().required("Perfil \xe9 obrigat\xf3rio"),debenture:i.Z_().required("Deb\xeanture \xe9 obrigat\xf3ria")})},7848:function(e,t,r){"use strict";function isValidateCPF(e){if(11!==(e=e.replace(/[^\d]+/g,"")).length||/^(\d)\1{10}$/.test(e))return!1;let calcularDigitoVerificador=e=>{let t=0,r=e.length+1;for(let i=0;i<e.length;i++)t+=parseInt(e.charAt(i),10)*r--;let i=11-t%11;return i>9?0:i},t=e.substring(0,9),r=calcularDigitoVerificador(t),i=calcularDigitoVerificador(t+r);return e===t+r+i}function isValidateCNPJ(e){if(14!==(e=e.replace(/[^\d]+/g,"")).length||/^(\d)\1{13}$/.test(e))return!1;let calcularDigitoVerificador=(e,t)=>{let r=0;for(let i=0;i<e.length;i++)r+=parseInt(e.charAt(i),10)*t[i];let i=11-r%11;return i>9?0:i},t=e.substring(0,12),r=calcularDigitoVerificador(t,[5,4,3,2,9,8,7,6,5,4,3,2]),i=calcularDigitoVerificador(t+r,[6,5,4,3,2,9,8,7,6,5,4,3,2]);return e===t+r+i}r.d(t,{p:function(){return isValidateCPF},w:function(){return isValidateCNPJ}})},4425:function(e,t,r){"use strict";var i=r(5968);t.Z=function(e){return/^(\d{2})(9\d{8}|[2-8]\d{7})$/.test((0,i.p4)(e))}},9514:function(){},9367:function(e,t,r){"use strict";var i=r(2265);let o=i.forwardRef(function({title:e,titleId:t,...r},o){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=o},1543:function(e,t,r){"use strict";var i=r(2265);let o=i.forwardRef(function({title:e,titleId:t,...r},o){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});t.Z=o},5255:function(e,t,r){"use strict";var i=r(2265);let o=i.forwardRef(function({title:e,titleId:t,...r},o){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))});t.Z=o},7470:function(e,t,r){"use strict";r.d(t,{R:function(){return getDefaultState},m:function(){return n}});var i=r(7987),o=r(9024),a=r(1640),n=class extends o.F{#e;#t;#r;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||getDefaultState(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(t=>t!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#t.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(e){let onContinue=()=>{this.#i({type:"continue"})};this.#r=(0,a.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#i({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#i({type:"pause"})},onContinue,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});let t="pending"===this.state.status,r=!this.#r.canStart();try{if(t)onContinue();else{this.#i({type:"pending",variables:e,isPaused:r}),await this.#t.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#i({type:"pending",context:t,variables:e,isPaused:r})}let i=await this.#r.start();return await this.#t.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#t.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#i({type:"success",data:i}),i}catch(t){try{throw await this.#t.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#t.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#i({type:"error",error:t})}}finally{this.#t.runNext(this)}}#i(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),i.Vr.batch(()=>{this.#e.forEach(t=>{t.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},3588:function(e,t,r){"use strict";r.d(t,{D:function(){return useMutation}});var i=r(2265),o=r(7470),a=r(7987),n=r(2996),s=r(300),u=class extends n.l{#o;#a=void 0;#n;#s;constructor(e,t){super(),this.#o=e,this.setOptions(t),this.bindMethods(),this.#u()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#o.defaultMutationOptions(e),(0,s.VS)(this.options,t)||this.#o.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#n,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,s.Ym)(t.mutationKey)!==(0,s.Ym)(this.options.mutationKey)?this.reset():this.#n?.state.status==="pending"&&this.#n.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#n?.removeObserver(this)}onMutationUpdate(e){this.#u(),this.#d(e)}getCurrentResult(){return this.#a}reset(){this.#n?.removeObserver(this),this.#n=void 0,this.#u(),this.#d()}mutate(e,t){return this.#s=t,this.#n?.removeObserver(this),this.#n=this.#o.getMutationCache().build(this.#o,this.options),this.#n.addObserver(this),this.#n.execute(e)}#u(){let e=this.#n?.state??(0,o.R)();this.#a={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#d(e){a.Vr.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#a.variables,r=this.#a.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#a)})})}},d=r(8038);function useMutation(e,t){let r=(0,d.NL)(t),[o]=i.useState(()=>new u(r,e));i.useEffect(()=>{o.setOptions(e)},[o,e]);let n=i.useSyncExternalStore(i.useCallback(e=>o.subscribe(a.Vr.batchCalls(e)),[o]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),l=i.useCallback((e,t)=>{o.mutate(e,t).catch(s.ZT)},[o]);if(n.error&&(0,s.L3)(o.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:l,mutateAsync:n.mutate}}}}]);