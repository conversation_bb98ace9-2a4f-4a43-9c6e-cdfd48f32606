(()=>{var e={};e.id=2864,e.ids=[2864],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},9782:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>x,routeModule:()=>u,tree:()=>d});var s=a(73137),r=a(54647),n=a(4183),l=a.n(n),c=a(71775),o={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);a.d(t,o);let i=s.AppPageRouteModule,d=["",{children:["financeiro",{children:["pagamento",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,38021)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamento\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\pagamento\\[id]\\page.tsx"],m="/financeiro/pagamento/[id]/page",p={require:a,loadChunk:()=>Promise.resolve()},u=new i({definition:{kind:r.x.APP_PAGE,page:"/financeiro/pagamento/[id]/page",pathname:"/financeiro/pagamento/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},37653:(e,t,a)=>{Promise.resolve().then(a.bind(a,37882))},37882:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Payment});var s=a(60080),r=a(97669),n=a(47956),l=a(52451),c=a.n(l),o=a(68012),i=a(9885),d=a(57114),x=a(52274),m=a(64731),p=a.n(m),u=a(96413),h=a(92170),j=a(34751),b=a(85814),v=a(74644),f=a(24577),g=a(30475),N=a(20161),y=a(69888),w=a(33050),P=a(26050),C=a(5193),D=a(90682),F=a(69957);function Payment({params:e}){let[t,a]=(0,i.useState)(""),[l,m]=(0,i.useState)("e"),[V,_]=(0,i.useState)(!1),[q,A]=(0,i.useState)(!1),[R,L]=(0,i.useState)(!0),[Z,k]=(0,i.useState)(),[S,B]=(0,i.useState)(),I=(0,d.useRouter)(),E=(0,D.e)(),DataValues=({label:e,value:t})=>(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-bold text-sm",children:e}),s.jsx(v.Z,{maxWidth:"200px",minWidth:"80px",loading:R,height:"25px",children:s.jsx("p",{className:" text-md",children:t})})]}),getPaymentData=()=>{j.Am.info("Buscando dados do pagamento!",{toastId:"search",autoClose:!1}),b.Z.get(`/income-payment-scheduled/${e.id}`).then(e=>{B(e.data),e.data?.status==="PAID"?m("p"):m("e"),j.Am.dismiss("search")}).catch(e=>{j.Am.error("Tivemos um erro ao buscar os dados do agendamento!",{toastId:"agendamento"})}).finally(()=>L(!1))};(0,i.useEffect)(()=>{getPaymentData()},[]);let z=(0,i.useCallback)(e=>{k(e);let t=URL.createObjectURL(e[0]);a(t)},[]),{getRootProps:M,getInputProps:U}=(0,h.uI)({onDrop:z,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&j.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{a("")}}),$=S?.addendumData?.reduce((e,t)=>e+(Number(t.investmentValue)||0),0)||0,W=(Number(S?.investmentValue)||0)+$;return(0,s.jsxs)("div",{className:`${V?"fixed w-full":"relative"}`,children:[s.jsx(r.Z,{}),s.jsx(n.Z,{children:(0,s.jsxs)("div",{className:"w-full text-white",children:[(0,s.jsxs)("div",{className:"w-full text-white flex flex-col md:flex-row flex-wrap gap-6 justify-start",children:[(0,s.jsxs)("div",{className:"w-full sm:w-48 md:w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[s.jsx("p",{className:"my-4 font-bold text-xl",children:"Valor a ser Pago"}),s.jsx("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:s.jsx("div",{className:"flex w-full items-center justify-center py-3",children:s.jsx(v.Z,{loading:R,height:"25px",width:"100px",children:s.jsx("p",{className:"font-bold text-xl text-[#FF9900]",children:Number(S?.paymentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})})})]}),(0,s.jsxs)("div",{className:"w-full sm:w-48 md:w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[s.jsx("p",{className:"my-4 font-bold text-xl",children:"Pagamento"}),s.jsx("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:s.jsx("div",{className:"flex w-full items-center justify-center py-3",children:s.jsx(v.Z,{loading:R,height:"25px",width:"100px",children:(0,s.jsxs)("p",{className:"font-bold text-xl text-[#FF9900]",children:["Dia ",S?.paymentDate?p()(S?.paymentDate).format("DD"):"00"]})})})})]}),"p"===l?(0,s.jsxs)("div",{onClick:()=>{S?.investorPayment.payment&&window.open(S?.investorPayment.payment.file,"_blank")},className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center flex flex-col items-center justify-center cursor-pointer",children:[s.jsx("p",{className:"mb-5 font-bold text-lg select-none",children:"Ultimo Comprovante"}),s.jsx(g.Z,{width:40,color:"#fff"})]}):s.jsx("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl flex items-center justify-center cursor-pointer px-4 py-6 text-center",...M(),children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center gap-3 w-full",children:[s.jsx("p",{className:"font-bold text-lg select-none text-white",children:t?"Comprovante anexado":"Anexar Comprovante"}),s.jsx("input",{type:"text",...U()}),t?s.jsx(N.Z,{width:40,color:"#fff"}):s.jsx(c(),{className:"select-none",src:o.Z,width:30,alt:""})]})})]}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"my-5",children:"p"===l?s.jsx("div",{className:"bg-[#1ef97d31] w-28 border border-[#1EF97C] rounded-lg",children:s.jsx("p",{className:"text-center text-[#1EF97C] p-1",children:"Pago"})}):s.jsx("div",{className:"bg-[#ffb3382a] w-28 border border-[#FFB238] rounded-lg",children:s.jsx("p",{className:"text-center text-[#FFB238] p-1",children:"Pendente"})})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("p",{className:"text-xl font-bold",children:"Dados Pessoais"}),s.jsx("div",{className:"mb-3 mt-2",children:s.jsx(DataValues,{label:"Nome",value:S?.investorPayment.name||"N\xe3o encontrado"})}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3",children:[s.jsx(DataValues,{label:"E-mail",value:S?.investorPayment.email||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"CPF",value:(0,u.VL)(S?.investorPayment.document||"N\xe3o encontrado")}),S?.advisors.map((e,t)=>s.jsx(DataValues,{label:`Consultor ${t+1}`,value:e.name||"N\xe3o encontrado"},t))]}),s.jsx("div",{children:s.jsx(DataValues,{label:"Broker",value:S?.broker||"N\xe3o encontrado"})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es sobre o Investimento"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2 items-center",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-bold text-sm",children:"Valor Investido"}),s.jsx(v.Z,{maxWidth:"200px",minWidth:"80px",loading:R,height:"25px",children:(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[s.jsx("p",{className:" text-md",children:W.toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),s.jsx("div",{className:"cursor-pointer",children:s.jsx(c(),{alt:"",src:P.Z,width:15})})]})})]}),s.jsx(F.z,{className:"ml-[-15px]",onClick:()=>{_(!0)},children:"Ver mais"}),S?.addendsProRata&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(DataValues,{label:"Pro Rata",value:Number(S?.addendsProRataValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-bold text-sm",children:"Valor Total"}),s.jsx(v.Z,{maxWidth:"200px",minWidth:"80px",loading:R,height:"25px",children:s.jsx("p",{className:"text-md text-[#FF9900]",children:(Number(S?.investmentValue||0)+(S?.addendsProRataValue||0)).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]}),s.jsx(DataValues,{label:"Porcentagem",value:`${S?.investmentYield||"N\xe3o encontrado"}%`}),s.jsx(DataValues,{label:"Rentabilidade",value:Number(S?.paymentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es Banc\xe1rias"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2",children:[s.jsx(DataValues,{label:"Banco",value:S?.investorPayment.account.bank||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"Ag\xeancia",value:S?.investorPayment.account.agency||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"Conta",value:S?.investorPayment.account.account||"N\xe3o encontrado"}),s.jsx(DataValues,{label:"Chave PIX",value:S?.investorPayment.account.pixKey||"N\xe3o encontrado"})]})]}),(0,s.jsxs)("div",{className:"mb-6 w-[30%]",children:[s.jsx("div",{children:s.jsx("p",{className:"text-xl font-bold mb-3",children:"Anexos"})}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0 mb-20",children:[S?.attachments.contract&&s.jsx("div",{className:"w-full md:w-auto",children:s.jsx(C.Z,{file:S.attachments.contract,title:"Contrato"})}),S?.attachments.residenceProof&&s.jsx("div",{className:"w-full md:w-auto",children:s.jsx(C.Z,{file:S.attachments.residenceProof,title:"Comprovante de resid\xeancia"})}),S?.attachments.rg&&s.jsx("div",{className:"w-full md:w-auto",children:s.jsx(C.Z,{file:S.attachments.rg,title:"RG"})})]})]})]}),(0,s.jsxs)("div",{className:"w-full flex flex-wrap gap-4 justify-end mb-4",children:["p"!==l&&s.jsx("div",{children:s.jsx(F.z,{loading:q,onClick:()=>{if(A(!0),!t)return j.Am.warning("Para aprovar o pagamento \xe9 preciso anexar o comprovante!");let e=new FormData;Z&&e.append("proof",Z[0]),e.append("paymentId",S?.investorPayment.id||""),j.Am.info("Aprovando o pagamento...",{toastId:"aprovando-pagamento"}),b.Z.post("/income-payment",e,{headers:{RoleId:E.roleId}}).then(e=>{j.Am.success("Pagamento aprovado!"),j.Am.dismiss("aprovando-pagamento"),I.back()}).catch(e=>{(0,f.Z)(e,"N\xe3o foi poss\xedvel aprovar o pagamento!")}).finally(()=>A(!1))},size:"lg",className:"w-full sm:w-auto",children:"Aprovar pagamento"})}),s.jsx("div",{children:s.jsx(F.z,{onClick:()=>{I.back()},size:"lg",variant:"secondary",children:"Voltar"})})]})]})]})}),s.jsx("div",{children:s.jsx(x.Z,{width:"7/12",openModal:V,setOpenModal:_,children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-5",children:[s.jsx("p",{className:"mb-2",children:"Dados do contrato principal."}),(0,s.jsxs)("table",{className:"border border-[#FF9900] w-full",children:[(0,s.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[s.jsx("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Rendimento"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),(0,s.jsxs)("tr",{className:"text-xs",children:[s.jsx("td",{className:"text-center px-4 py-2",children:Number(S?.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,s.jsxs)("td",{className:"text-center px-4 py-2",children:[S?.investmentYield,"%"]}),s.jsx("td",{className:"text-center px-4 py-2",children:(0,w.Z)(S?.contractStartDate||"")}),s.jsx("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>{S?.attachments.contract?window.open(S?.attachments.contract,"_blanck"):j.Am.warning("N\xe3o conseguimos buscar os dados do contrato.")},children:"ver mais"})]})]})]}),S?.addendumData&&S?.addendumData.length>0&&(0,s.jsxs)("div",{children:[s.jsx("div",{className:"mb-2",children:s.jsx("p",{children:"Contratos aditivos adicionados no c\xe1lculo"})}),(0,s.jsxs)("table",{className:"border border-[#FF9900] w-full min-w-[600px]",children:[(0,s.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[s.jsx("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Valor rentabilizado"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Dias rentabilizados"}),s.jsx("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),S?.addendumData.map(e=>s.jsxs("tr",{className:"text-xs",children:[s.jsx("td",{className:"text-center px-4 py-2",children:Number(e?.investmentValue||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),s.jsx("td",{className:"text-center px-4 py-2",children:w.Z(e.date)}),s.jsx("td",{className:"text-center px-4 py-2",children:e.totalAmount?Number(e?.totalAmount||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"}):"-"}),s.jsx("td",{className:"text-center px-4 py-2",children:`${e.validDays} dias`}),e?.contractUrl?s.jsx("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>window.open(e.contractUrl,"_blanck"),children:"ver mais"}):s.jsx("td",{className:"flex justify-center",children:s.jsx(y.Z,{className:"mt-1",color:"#FF9900",width:20})})]},e.id))]})]})]})})})]})}},38021:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>l,__esModule:()=>n,default:()=>o});var s=a(17536);let r=(0,s.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\financeiro\pagamento\[id]\page.tsx`),{__esModule:n,$$typeof:l}=r,c=r.default,o=c}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,5081,7207,278,7669,658],()=>__webpack_exec__(9782));module.exports=a})();