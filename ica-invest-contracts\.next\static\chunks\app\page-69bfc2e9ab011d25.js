(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{3750:function(e,t,r){Promise.resolve().then(r.bind(r,1295))},1295:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return Home}});var n=r(7437),a=r(8700);r(1577);var s=r(3220),l=r(4734),i=r(5968),o=r(2265),c=r(4033);function Home(){let{handleSignInUser:e}=(0,o.useContext)(l.Z),t=(0,c.useRouter)(),[r,d]=(0,o.useState)(""),[u,p]=(0,o.useState)(""),[f,m]=(0,o.useState)("admin"),[h,g]=(0,o.useState)(!1),v=(0,o.useRef)(null),x=(0,o.useRef)(null);(0,o.useEffect)(()=>{let e="true"===sessionStorage.getItem("isAuthenticated"),r=sessionStorage.getItem("token");e&&r&&t.push("/home")},[t]),(0,o.useEffect)(()=>{let e=setTimeout(()=>{var e,t;let r=(null===(e=v.current)||void 0===e?void 0:e.value)||"",n=(null===(t=x.current)||void 0===t?void 0:t.value)||"";if(r){let e=r.length<=14?(0,i.VL)(r):(0,i.PK)(r);d(e)}n&&p(n)},300);return()=>clearTimeout(e)},[]);let handleSubmit=()=>{let t=(0,i.p4)(r);if(!t||!u){console.warn("Campos obrigat\xf3rios n\xe3o preenchidos");return}e({document:t,password:u,setLoading:g,type:f})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"absolute w-full h-screen",children:(0,n.jsx)("div",{className:"absolute inset-0 bg-half-half w-full"})}),(0,n.jsxs)("div",{className:"flex min-h-full h-screen flex-1 flex-col items-center justify-center px-6 py-12 lg:px-8 relative z-10",children:[(0,n.jsx)("img",{className:"mx-auto h-10 w-auto",src:"/logo.svg",alt:"Your Company"}),(0,n.jsx)("div",{className:"md:w-4/12 p-14 m-auto bg-opacity-30 backdrop-blur-sm border border-[#FF9900] rounded-lg shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:(0,n.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-sm",children:[(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"mb-2",children:(0,n.jsx)(s.Z,{id:"document",label:"",name:"document",placeholder:"Documento",type:"text",value:r,onInput:e=>{let{target:t}=e,r=t.value;r.length<=14?d((0,i.VL)(r)):d((0,i.PK)(r))},onChange:e=>{let{target:t}=e,{value:r}=t;r.length<=14?d((0,i.VL)(r)):d((0,i.PK)(r))},ref:v})})}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"mb-5",children:(0,n.jsx)(s.Z,{id:"password",label:"",name:"password",placeholder:"Senha",type:"password",onKeyDown:e=>{"Enter"===e.key&&handleSubmit()},ref:x,value:u,onInput:e=>{let{target:t}=e;p(t.value)},onChange:e=>{let{target:t}=e;p(t.value)}})})}),(0,n.jsx)("div",{children:(0,n.jsx)(a.z,{onClick:handleSubmit,loading:h,disabled:h,size:"lg",className:"w-full",children:"Entrar"})})]})})]})]})}},3220:function(e,t,r){"use strict";var n=r(7437),a=r(2265),s=r(1543),l=r(9367);let i=(0,a.forwardRef)((e,t)=>{let{label:r,bg:i,type:o,...c}=e,[d,u]=(0,a.useState)(!1);return(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-white mb-1",children:r}),(0,n.jsxs)("div",{className:"custom-input-wrapper h-12 w-full text-white rounded-xl ring-[#FF9900] ring-1 ".concat("month"===o?"":"ring-inset"," ").concat("transparent"===i?"bg-black":"bg-[#1C1C1C]"," flex-1 flex relative"),children:[(0,n.jsx)("input",{ref:t,type:d&&"password"===o?"text":o,...c,className:"w-full h-12 flex-1 px-4 bg-transparent rounded-xl"}),"password"===o&&(0,n.jsx)("div",{className:"mr-2 cursor-pointer absolute right-0 top-[50%] translate-y-[-50%]",onClick:()=>u(!d),children:d?(0,n.jsx)(s.Z,{width:20}):(0,n.jsx)(l.Z,{width:20})})]})]})});i.displayName="Input",t.Z=i},8700:function(e,t,r){"use strict";r.d(t,{d:function(){return c},z:function(){return d}});var n=r(7437),a=r(2265),s=r(7256),l=r(6061),i=r(3715),o=r(992);let c=(0,l.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-orange-linear text-white shadow hover:bg-orange-black-linear",destructive:"bg-[#BC4C4C] text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"bg-black text-white rounded-xl ring-[#FF9900] ring-1 ring-inset",secondary:"bg-primary text-primary-foreground shadow hover:bg-primary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:d=!1,loading:u=!1,...p}=e,f=d?s.g7:"button";return(0,n.jsxs)(f,{className:(0,o.cn)(c({variant:a,size:l,className:r}),"flex items-center gap-2 select-none"),ref:t,disabled:u||p.disabled,...p,children:[u&&(0,n.jsx)(i.Z,{className:"h-4 w-4 animate-spin"}),p.children]})});d.displayName="Button"},992:function(e,t,r){"use strict";r.d(t,{cn:function(){return cn}});var n=r(7042),a=r(4769);function cn(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}},4734:function(e,t,r){"use strict";var n=r(2265);let a=n.createContext({});t.Z=a},5968:function(e,t,r){"use strict";r.d(t,{Ht:function(){return valueMask},PK:function(){return cnpjMask},Tc:function(){return cepMask},VL:function(){return cpfMask},gP:function(){return phoneMask},p4:function(){return clearLetters}});let cpfMask=e=>e.replace(/\D/g,"").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d{1,2})/,"$1-$2").replace(/(-\d{2})\d+?$/,"$1"),cnpjMask=e=>e.replace(/\D/g,"").replace(/^(\d{2})(\d)/,"$1.$2").replace(/^(\d{2})\.(\d{3})(\d)/,"$1.$2.$3").replace(/\.(\d{3})(\d)/,".$1/$2").replace(/(\d{4})(\d)/,"$1-$2").replace(/(-\d{2})\d+?$/,"$1"),valueMask=e=>e.replace(/\D/g,"").replace(/(\d{1})(\d{14})$/,"$1.$2").replace(/(\d{1})(\d{11})$/,"$1.$2").replace(/(\d{1})(\d{8})$/,"$1.$2").replace(/(\d{1})(\d{5})$/,"$1.$2").replace(/(\d{1})(\d{1,2})$/,"$1,$2"),phoneMask=e=>e.replace(/\D/g,"").replace(/^55/,"").replace(/^(\d{2})(\d)/g,"($1) $2").replace(/(\d)(\d{4})$/,"$1-$2"),clearLetters=e=>e.replace(/\D/g,""),cepMask=e=>e.replace(/\D/g,"").replace(/(\d{5})(\d)/,"$1-$2").replace(/(-\d{3})\d+?$/,"$1")},1577:function(){},622:function(e,t,r){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(2265),a=Symbol.for("react.element"),s=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,r){var n,s={},c=null,d=null;for(n in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)l.call(t,n)&&!o.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===s[n]&&(s[n]=t[n]);return{$$typeof:a,type:e,key:c,ref:d,props:s,_owner:i.current}}t.Fragment=s,t.jsx=q,t.jsxs=q},7437:function(e,t,r){"use strict";e.exports=r(622)},4033:function(e,t,r){e.exports=r(290)},9367:function(e,t,r){"use strict";var n=r(2265);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=a},1543:function(e,t,r){"use strict";var n=r(2265);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});t.Z=a}},function(e){e.O(0,[5371,2971,7864,1744],function(){return e(e.s=3750)}),_N_E=e.O()}]);