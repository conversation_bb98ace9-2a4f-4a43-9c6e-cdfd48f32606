"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-multiple\", \"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue > 0 && numericValue % 5000 === 0;\n        }\n        return true;\n    }).test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (mutuamente exclusivos)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                setValue(\"cpf\", contractData.document || \"\");\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                setValue(\"celular\", contractData.phone || \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                setValue(\"cep\", contractData.zipCode || \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue,\n        watch\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        const modalidade = watch(\"modalidade\");\n        const valorInvestimento = watch(\"valorInvestimento\");\n        if (modalidade === \"SCP\" && valorInvestimento) {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const cotas = Math.floor(valorNumerico / 5000);\n            setValue(\"quotaQuantity\", cotas.toString());\n        }\n    }, [\n        watch(\"modalidade\"),\n        watch(\"valorInvestimento\"),\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n    }\n    const onSubmit = async (data)=>{\n        var _contractData_contracts_, _contractData_contracts;\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        // Usar o contractId do primeiro contrato retornado pela API\n        const contractId = contractData === null || contractData === void 0 ? void 0 : (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.id;\n        console.log(\"Contract ID:\", contractId);\n        console.log(\"Investor ID:\", investorId);\n        if (!contractId) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"ID do contrato n\\xe3o encontrado nos dados carregados\");\n            return;\n        }\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto JSON em vez de FormData\n            const requestData = {\n                role: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"\",\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                bankAccount: {\n                    bank: data.banco,\n                    agency: data.agencia,\n                    account: data.conta,\n                    pix: data.chavePix,\n                    type: \"CORRENTE\"\n                },\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")),\n                    monthlyRate: parseFloat(data.taxaRemuneracao),\n                    durationInMonths: parseInt(data.prazoInvestimento),\n                    paymentMethod: data.comprarCom,\n                    startDate: data.inicioContrato,\n                    endDate: data.fimContrato,\n                    profile: data.perfil,\n                    isDebenture: data.debenture === \"s\",\n                    ...data.modalidade === \"SCP\" && {\n                        quotaQuantity: parseInt(data.quotaQuantity || \"0\")\n                    }\n                }\n            };\n            if (isPJ) {\n                requestData.company = {\n                    corporateName: data.nomeCompleto,\n                    cnpj: documento\n                };\n            } else {\n                requestData.individual = {\n                    fullName: data.nomeCompleto,\n                    cpf: documento,\n                    rg: data.identidade,\n                    birthDate: data.dataNascimento,\n                    email: data.email,\n                    phone: data.celular.replace(/\\D/g, \"\"),\n                    motherName: data.nomeMae,\n                    nationality: \"brasileira\",\n                    occupation: \"Investidor\",\n                    issuingAgency: \"SSP\",\n                    address: {\n                        street: data.endereco,\n                        city: data.cidade,\n                        state: data.estado || \"\",\n                        postalCode: data.cep.replace(/\\D/g, \"\"),\n                        number: data.numero,\n                        neighborhood: \"Centro\",\n                        complement: data.complemento || \"\"\n                    }\n                };\n            }\n            console.log(\"Enviando dados para API...\", requestData);\n            console.log(\"Usando Contract ID para upgrade:\", contractId);\n            upgradeContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const upgradeContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            var _contractData_contracts_, _contractData_contracts;\n            // Usar o contractId do primeiro contrato retornado pela API\n            const contractId = contractData === null || contractData === void 0 ? void 0 : (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.id;\n            console.log(\"Mutation - Contract Data:\", contractData);\n            console.log(\"Mutation - Contract ID:\", contractId);\n            if (!contractId) {\n                throw new Error(\"ID do contrato n\\xe3o encontrado nos dados carregados\");\n            }\n            console.log(\"Fazendo PUT para:\", \"/contract/\".concat(contractId, \"/upgrade\"));\n            return _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].put(\"/contract/\" + contractId + \"/upgrade\", data);\n        },\n        onSuccess: (response)=>{\n            var _response_data;\n            console.log(\"Upgrade realizado com sucesso:\", response.data);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Upgrade realizado com sucesso! Novo contrato: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Upgrade realizado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        const modalidade = watch(\"modalidade\");\n        const valorInvestimento = watch(\"valorInvestimento\");\n        const irDesconto = watch(\"irDesconto\");\n        if (modalidade === \"SCP\" && valorInvestimento && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                const valorComplementarNecessario = 5000 - resto;\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        watch(\"modalidade\"),\n        watch(\"valorInvestimento\"),\n        watch(\"irDesconto\"),\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Remove tudo que não for número ou vírgula\n        const limpo = valor.replace(/[^0-9,]/g, \"\").replace(\",\", \".\");\n        return parseFloat(limpo) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 719,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 15,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 770,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 796,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 795,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 806,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 805,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 854,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 853,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 720,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 888,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 891,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 890,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 889,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 936,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 944,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 937,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 948,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 718,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 979,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 967,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 986,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message,\n                                        label: \"Valor do Investimento\",\n                                        placeholder: \"ex: R$ 50.000,00\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 996,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: calcularAliquotaIR,\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1011,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1010,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 964,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1018,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1017,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1028,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1033,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1034,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1035,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1036,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1037,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1038,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1039,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1032,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1044,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1062,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1063,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1064,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1065,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1071,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1076,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1082,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1083,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1092,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1029,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1027,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1106,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1119,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1118,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1117,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDeposito\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDeposito\", true);\n                                                    setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDeposito\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1129,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDesconto\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDesconto\", true);\n                                                    setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDesconto\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: \"Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1173,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 963,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1180,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1187,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                register: register,\n                                                name: \"quotaQuantity\",\n                                                width: \"100%\",\n                                                error: !!errors.quotaQuantity,\n                                                errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                                label: \"Quantidade de cotas (calculado automaticamente)\",\n                                                placeholder: \"Calculado automaticamente\",\n                                                disabled: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs mt-1\",\n                                                children: \"R$ 5.000,00 equivale a 1 cota. Valor m\\xednimo: R$ 30.000,00 (6 cotas). As cotas s\\xe3o calculadas automaticamente baseadas no valor do investimento.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1245,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1239,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1267,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1268,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1269,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1263,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1282,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1287,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1288,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1283,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1182,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1181,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1295,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || upgradeContractMutation.isPending || isRedirecting,\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || upgradeContractMutation.isPending ? \"Enviando...\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1304,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1294,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 959,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1332,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1337,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1338,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1336,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1334,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1333,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1357,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1354,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1353,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1370,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1368,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1352,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1351,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1350,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"8sABsyhbeHRUSYIp6zi4Ia5ysl0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});