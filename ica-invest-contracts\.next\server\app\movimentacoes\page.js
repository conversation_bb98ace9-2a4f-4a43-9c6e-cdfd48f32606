(()=>{var e={};e.id=4268,e.ids=[4268],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},22196:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>x,routeModule:()=>u,tree:()=>o});var r=t(73137),a=t(54647),l=t(4183),i=t.n(l),c=t(71775),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=r.AppPageRouteModule,o=["",{children:["movimentacoes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7630)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\movimentacoes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\movimentacoes\\page.tsx"],m="/movimentacoes/page",p={require:t,loadChunk:()=>Promise.resolve()},u=new d({definition:{kind:a.x.APP_PAGE,page:"/movimentacoes/page",pathname:"/movimentacoes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},42343:(e,s,t)=>{Promise.resolve().then(t.bind(t,99059))},99059:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Movimentacoes});var r=t(60080),a=t(97669),l=t(47956),i=t(28168),c=t(9317),n=t(7537),d=t(69195),o=t(9885),x=t(85814),m=t(30170),p=t(96413),u=t(33050),h=t(90682);function Movimentacoes(){let[e,s]=(0,o.useState)([]),[t,f]=(0,o.useState)(),[j,v]=(0,o.useState)(),[N,b]=(0,o.useState)(!1),[g,w]=(0,o.useState)(!1),[y,C]=(0,o.useState)("Todos"),_=(0,h.e)(),[P,q]=(0,o.useState)(1),[M,D]=(0,o.useState)(),getMoviments=()=>{x.Z.get("/investor/movements",{params:{roleId:_.roleId,limit:"10",page:P}}).then(e=>{s(e.data.data),v(e.data.summary),D({total:e.data.totalItems,perPage:e.data.itemsPerPage,page:e.data.currentPage,lastPage:e.data.totalPages})}).catch(e=>{})};return(0,o.useEffect)(()=>{getMoviments()},[P]),(0,r.jsxs)("div",{className:g?"fixed":"relative",children:[r.jsx(a.Z,{}),r.jsx(l.Z,{children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex justify-between w-full gap-4",children:[r.jsx("div",{className:"bg-[#1C1C1C] md:w-[32%] p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"m-auto text-center flex flex-col items-center",children:[r.jsx("p",{className:"text-white mb-5 md:text-base text-sm",children:"Saldo de Rendimento"}),r.jsx("div",{className:"md:w-36 md:h-36 w-28 h-28 bg-orange-linear rounded-full flex items-center justify-center",children:r.jsx("div",{className:"md:w-28 md:h-28 w-20 h-20 bg-black rounded-full flex items-center justify-center",children:(0,r.jsxs)("p",{className:"text-white md:text-xs text-[10px] font-bold",children:["R$ ",(0,p.Ht)(String(j?.totalAmountThisMonth||"0"))]})})})]})}),r.jsx("div",{className:"bg-[#1C1C1C] md:w-[32%] p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"m-auto text-center flex flex-col items-center",children:[(0,r.jsxs)("p",{className:"text-white mb-5 md:text-base text-sm",children:["Movimenta\xe7\xf5es de ",r.jsx("b",{className:"capitalize",children:new Date().toLocaleDateString("pt-BR",{month:"long"})})]}),r.jsx("div",{className:"md:w-36 md:h-36 w-28 h-28 bg-orange-linear rounded-full flex items-center justify-center",children:r.jsx("div",{className:"md:w-28 md:h-28 w-20 h-20 bg-black rounded-full flex items-center justify-center",children:r.jsx("p",{className:"text-white font-bold",children:j?.totalMovementsThisMonth||"0"})})})]})})]}),r.jsx("div",{className:"md:min-w-[1140px]",children:(0,r.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] text-white mt-10 overflow-x-auto w-full rounded-b-md border border-[#FF9900]",children:[r.jsx("div",{className:"flex w-full justify-end p-2",children:r.jsx("div",{className:"w-80",children:r.jsx(m.Z,{handleSearch:()=>{},setValue:()=>{},value:""})})}),(0,r.jsxs)("div",{className:"bg-[#313131] flex items-center justify-center border-y border-y-[#FF9900] ",children:[r.jsx("div",{className:"p-2 w-[60px] text-center"}),r.jsx("div",{className:"p-2 w-3/12",children:r.jsx("p",{className:"font-bold text-sm",children:"Opera\xe7\xe3o"})}),r.jsx("div",{className:"p-2 w-2/12",children:r.jsx("p",{className:"font-bold text-sm",children:"Valor"})}),r.jsx("div",{className:"p-2 w-6/12",children:r.jsx("p",{className:"font-bold text-sm",children:"Descri\xe7\xe3o"})}),r.jsx("div",{className:"p-2 w-2/12",children:r.jsx("p",{className:"font-bold text-sm",children:"Data"})})]}),e.map((e,s)=>(0,r.jsxs)("div",{className:"bg-[#1C1C1C] flex border-b-[1px] border-black relative",children:[r.jsx("div",{className:"w-[60px] flex items-center justify-center cursor-pointer",onClick:()=>{f(e),w(!0)},children:r.jsx(i.Z,{color:"#fff",width:20})}),r.jsx("div",{className:"p-2 w-3/12 flex items-center ",children:r.jsx("p",{className:"text-xs",children:e?.operationType})}),r.jsx("div",{className:"p-2 w-2/12 flex items-center ",children:(0,r.jsxs)("p",{className:"text-xs",children:["R$ ",(0,p.Ht)(e?.amount)]})}),r.jsx("div",{className:"p-2 w-6/12 flex items-center ",children:r.jsx("p",{className:"text-xs",children:e?.description})}),r.jsx("div",{className:"p-2 w-2/12 flex items-center",children:r.jsx("p",{className:"text-xs",children:(0,u.Z)(e?.createdAt)})})]},s)),r.jsx("div",{className:"w-full flex justify-end items-center pr-5 ",children:(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[r.jsx("p",{className:`p-1 bg-[#262626] rounded-md ${Number(P)>1?"cursor-pointer":""} flex items-center`,onClick:({})=>{Number(P)>1&&q(P-1)},children:r.jsx(c.Z,{color:Number(P)>1?"#fff":"#424242",width:20})}),r.jsx("p",{className:"font-bold bg-[#262626] rounded-md py-1 px-2",children:P}),r.jsx("p",{className:`p-1 bg-[#262626] rounded-md ${P<Number(M?.lastPage)?"cursor-pointer":""} flex items-center`,onClick:({})=>{P<Number(M?.lastPage)&&q(P+1)},children:r.jsx(n.Z,{color:P<Number(M?.lastPage)?"#fff":"#424242",width:20})})]})})]})}),g&&r.jsx("div",{className:"z-10 fixed top-0 left-0 md:w-screen w-full min-h-screen bg-[#1c1c1c71]",children:(0,r.jsxs)("div",{className:"z-20 md:w-5/12 w-full bg-[#1C1C1C] min-h-screen fixed top-0 right-0 border-l border-t border-b border-[#FF9900] p-10 text-white overflow-auto",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center",children:r.jsx(d.Z,{color:"#000",width:20})}),(0,r.jsxs)("div",{className:"gap-y-1 flex flex-col",children:[r.jsx("p",{className:"font-bold text-xs",children:"Detalhes da Opera\xe7\xe3o"}),(0,r.jsxs)("p",{className:"text-xs",children:["N\xb0 ",t?.contract.externalId]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10",children:[(0,r.jsxs)("div",{className:"flex md:w-9/12",children:[r.jsx("p",{className:"flex-1 font-bold text-xs",children:"ID"}),r.jsx("p",{className:"text-xs",children:t?.id})]}),(0,r.jsxs)("div",{className:"flex md:w-9/12",children:[r.jsx("p",{className:"flex-1 font-bold text-xs",children:"Tipo de Opera\xe7\xe3o"}),r.jsx("p",{className:"text-xs",children:t?.operationType})]}),(0,r.jsxs)("div",{className:"flex md:w-9/12",children:[r.jsx("p",{className:"flex-1 font-bold text-xs",children:"N˚ do Contrato"}),r.jsx("p",{className:"text-xs",children:t?.contract.contractNumber})]}),(0,r.jsxs)("div",{className:"flex md:w-9/12",children:[r.jsx("p",{className:"flex-1 font-bold text-xs",children:"Valor"}),(0,r.jsxs)("p",{className:"text-xs",children:["R$ ",(0,p.Ht)(t?.amount||"")]})]}),(0,r.jsxs)("div",{className:"flex md:w-9/12",children:[r.jsx("p",{className:"flex-1 font-bold text-xs",children:"Data da Opera\xe7\xe3o"}),r.jsx("p",{className:"text-xs",children:(0,u.Z)(t?.createdAt||"")})]}),(0,r.jsxs)("div",{className:"flex md:w-9/12",children:[r.jsx("p",{className:"flex-1 font-bold text-xs",children:"Descri\xe7\xe3o"}),r.jsx("p",{className:"text-xs text-end md:text-start",children:t?.description})]})]}),r.jsx("div",{className:"md:w-9/12 flex justify-between mt-10",children:r.jsx("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer",onClick:()=>w(!1),children:r.jsx("p",{className:"text-sm",children:"Fechar"})})})]})})]})})]})}},33050:(e,s,t)=>{"use strict";t.d(s,{Z:()=>formatDate,l:()=>formatDateToEnglishType});var r=t(95081),a=t.n(r);function formatDate(e){return a().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?a().utc(e).format("YYYY-MM-DD"):a().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},7630:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var r=t(17536);let a=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\movimentacoes\page.tsx`),{__esModule:l,$$typeof:i}=a,c=a.default,n=c},28168:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});var r=t(9885);let a=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"}))}),l=a}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[4103,6426,4731,8813,5081,8394,7207,278,7669],()=>__webpack_exec__(22196));module.exports=t})();