exports.id=2307,exports.ids=[2307],exports.modules={32307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>InputText});var r=s(60080);function InputText({label:e,setValue:t,error:s,errorMessage:n,width:x="auto",register:i,name:l,placeholder:a="",type:d="text",disabled:o=!1,minDate:c,minLength:u,maxLength:p,maxDate:h,disableErrorMessage:m=!1,onBlur:g,value:b,onChange:v}){return(0,r.jsxs)("div",{className:"input relative group",style:{width:x},children:[(0,r.jsxs)("p",{className:"text-white mb-1 text-sm",children:[e,s&&!m&&(0,r.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",n]})]}),r.jsx("input",{...i(l),placeholder:a,type:d,id:l,disabled:o,min:c,max:h,minLength:u,maxLength:p,...t?{onChange:({target:e})=>t(e.value)}:{},onBlur:g,className:`h-12 w-full px-4 ${o?"text-zinc-400":"text-white"} rounded-xl ${s?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`,...void 0!==b?{value:b}:{},...v?{onChange:v}:{}}),s&&r.jsx("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:n})]})}s(3510)},3510:()=>{}};