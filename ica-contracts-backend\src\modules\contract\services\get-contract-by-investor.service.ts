import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';

@Injectable()
export class GetContractsByInvestorService {
  constructor(
    @InjectRepository(OwnerRoleRelationEntity)
    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
  ) {}

  async perform(investorId: string) {
    const userProfile = await this.ownerRoleRelationRepository.findOne({
      where: { id: investorId },
      relations: {
        owner: { address: true, account: true },
        business: { address: true, account: true },
        role: true,
      },
    });

    if (!userProfile) {
      throw new NotFoundException(`Usuario nāo encontrado.`);
    }

    const contracts = await this.contractRepository.find({
      where: { investor: { id: userProfile.id } },
      relations: {
        ownerRoleRelation: { business: true, owner: true },
        signataries: true,
        addendum: { addendumFiles: { file: true } },
        contractAdvisors: {
          advisor: { owner: true, business: true },
        },
      },
    });

    const result = contracts.map((contract) => {
      const signatary = contract.signataries[0];

      return {
        id: contract.id,
        investmentValue: signatary?.investmentValue,
        investmentTerm: signatary?.investmentTerm,
        investmentYield: signatary?.investmentYield,
        investorName: signatary?.name,
        investorDocument: signatary?.document,
        tags: signatary?.investmentModality,
        purchasedWith: signatary?.purchaseWith,
        quotesAmount: signatary?.amountQuotes,
        gracePeriod: signatary?.gracePeriod,
        responsibleConsultant:
          contract.ownerRoleRelation?.owner?.name ||
          contract.ownerRoleRelation?.business?.fantasyName ||
          'Nāo possui',
        contractStatus: contract.status,
        contractStart: contract.startContract,
        contractEnd: contract.endContract,
        finalizedAt: contract.updatedAt,
        pdfDocument: contract.contractPdf,
        proofPaymentPdf: contract.proofPayment,
        advisors: contract.contractAdvisors.map(({ advisor, rate }) => ({
          id: advisor.id,
          name: advisor.owner?.name ?? advisor.business?.fantasyName,
          document: advisor.owner?.cpf ?? advisor.business?.cnpj,
          rate,
        })),
        addendum: contract.addendum.map((addendum) => ({
          id: addendum.id,
          investmentValue: addendum.value,
          investmentYield: addendum.yieldRate,
          contractStart: addendum.applicationDate,
          contractEnd: addendum.expiresIn,
          contractStatus: addendum.status,
          responsibleConsultant:
            contract.ownerRoleRelation?.owner?.name ||
            contract.ownerRoleRelation?.business?.fantasyName,
          files: addendum.addendumFiles.map((addendumFile) => ({
            type: addendumFile.type,
            url: addendumFile.file.url,
          })),
        })),
      };
    });

    return {
      document: userProfile.owner?.cpf || userProfile.business?.cnpj,
      email: userProfile.owner?.email || userProfile.business?.email,
      phone: userProfile.owner?.phone,
      profile: userProfile.role?.name,
      advisor:
        contracts[0]?.ownerRoleRelation?.owner?.name ||
        contracts[0]?.ownerRoleRelation?.business?.fantasyName,
      rg: contracts[0]?.signataries[0]?.rg,
      // Adicionar campos motherName e birthDate para pré-preenchimento no upgrade
      motherName: userProfile.owner?.motherName,
      birthDate: userProfile.owner?.dtBirth ? (() => {
        try {
          // Verificar se já é uma instância de Date
          if (userProfile.owner.dtBirth instanceof Date) {
            return userProfile.owner.dtBirth.toISOString().split('T')[0];
          }
          // Se for string, tentar converter
          const birthDate = new Date(userProfile.owner.dtBirth);
          if (!isNaN(birthDate.getTime())) {
            return birthDate.toISOString().split('T')[0];
          }
          return undefined;
        } catch (e) {
          console.error('Error parsing dtBirth', e);
          return undefined;
        }
      })() : undefined,
      zipCode:
        userProfile?.owner?.address[0]?.cep ||
        userProfile?.business?.address[0]?.cep,
      city:
        userProfile.owner?.address[0]?.city ||
        userProfile.business?.address[0]?.city,
      address:
        userProfile.owner?.address[0]?.street ||
        userProfile.business?.address[0]?.street,
      addressNumber:
        userProfile.owner?.address[0]?.number ||
        userProfile.business?.address[0]?.number,
      complement:
        userProfile.owner?.address[0]?.complement ||
        userProfile.business?.address[0]?.complement,
      neighborhood:
        userProfile.owner?.address[0]?.neighborhood ||
        userProfile.business?.address[0]?.neighborhood,
      state:
        userProfile.owner?.address[0]?.state ||
        userProfile.business?.address[0]?.state,
      bank:
        userProfile.owner?.account[0]?.bank ||
        userProfile.business?.account[0]?.bank ||
        contracts[0]?.signataries[0]?.bank,
      accountNumber:
        userProfile.owner?.account[0]?.number ||
        userProfile.business?.account[0]?.number ||
        contracts[0]?.signataries[0]?.account,
      branch:
        userProfile.owner?.account[0]?.branch ||
        userProfile.business?.account[0]?.branch ||
        contracts[0]?.signataries[0]?.agency,
      contracts: result,
    };
  }
}
