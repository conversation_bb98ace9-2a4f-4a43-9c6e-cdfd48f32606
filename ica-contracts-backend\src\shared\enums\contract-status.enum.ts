export enum ContractStatusEnum {
  ABERTO = 'aberto',
  DRAFT = 'DRAFT',
  GENERATED = 'GENERATED',
  SIGNATURE_SENT = 'SIGNATURE_SENT',
  AWAITING_INVESTOR_SIGNATURE = 'AWAITING_INVESTOR_SIGNATURE',
  AWAITING_DEPOSIT = 'AWAITING_DEPOSIT',
  AWAITING_AUDIT = 'AWAITING_AUDIT',
  AWAITING_AUDIT_SIGNATURE = 'AWAITING_AUDIT_SIGNATURE',
  ACTIVE = 'ACTIVE',
  SIGNATURE_FAILED = 'SIGNATURE_FAILED',
  EXPIRED_BY_INVESTOR = 'EXPIRED_BY_INVESTOR',
  EXPIRED_BY_AUDIT = 'EXPIRED_BY_AUDIT',
  EXPIRED_FAILURE_PROOF_PAYMENT = 'EXPIRED_FAILURE_PROOF_PAYMENT',
  REJECTED = 'REJECTED',
  REJECTED_BY_AUDIT = 'REJECTED_BY_AUDIT', // REJEITADO PELA AUDITORIA
  GENERATE_CONTRACT_FAILED = 'GENERATE_CONTRACT_FAILED',
  EXPIRED = 'EXPIRED',
  DELETED = 'DELETED',
}
