import { CityEntity } from 'src/shared/database/typeorm/entities/city.entity';
import { StateEntity } from 'src/shared/database/typeorm/entities/state.entity';
import { Repository } from 'typeorm';
import { StateDto } from '../dto/create-state-city.dto';
export declare class CreateStateCityService {
    private stateRepository;
    private cityRepository;
    constructor(stateRepository: Repository<StateEntity>, cityRepository: Repository<CityEntity>);
    perform(stateDto: StateDto): Promise<void>;
}
