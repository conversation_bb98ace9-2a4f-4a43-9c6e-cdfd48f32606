{"version": 3, "file": "create-state-city.service.js", "sourceRoot": "/", "sources": ["modules/location/services/create-state-city.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,uFAA8E;AAC9E,yFAAgF;AAChF,qCAAqC;AAK9B,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACjC,YAEU,eAAwC,EAExC,cAAsC;QAFtC,oBAAe,GAAf,eAAe,CAAyB;QAExC,mBAAc,GAAd,cAAc,CAAwB;IAC7C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,QAAkB;QAC9B,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE;SAC/C,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,KAAK,GAAG,IAAI,0BAAW,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;YAC3C,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACrB,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;QACD,MAAM,OAAO,CAAC,GAAG,CACf,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACpC,MAAM,IAAI,GAAG,IAAI,wBAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AAhCY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,wBAAU,CAAC,CAAA;qCADJ,oBAAU;QAEX,oBAAU;GALzB,sBAAsB,CAgClC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { CityEntity } from 'src/shared/database/typeorm/entities/city.entity';\r\nimport { StateEntity } from 'src/shared/database/typeorm/entities/state.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { StateDto } from '../dto/create-state-city.dto';\r\n\r\n@Injectable()\r\nexport class CreateStateCityService {\r\n  constructor(\r\n    @InjectRepository(StateEntity)\r\n    private stateRepository: Repository<StateEntity>,\r\n    @InjectRepository(CityEntity)\r\n    private cityRepository: Repository<CityEntity>,\r\n  ) {}\r\n\r\n  async perform(stateDto: StateDto): Promise<void> {\r\n    let state = await this.stateRepository.findOne({\r\n      where: { abbreviation: stateDto.abbreviation },\r\n    });\r\n\r\n    if (!state) {\r\n      state = new StateEntity();\r\n      state.name = stateDto.name;\r\n      state.abbreviation = stateDto.abbreviation;\r\n      state = await this.stateRepository.save(state);\r\n    }\r\n\r\n    if (!stateDto.cities) {\r\n      throw new BadRequestException('Estado já registrado.');\r\n    }\r\n    await Promise.all(\r\n      stateDto.cities.map(async (cityDto) => {\r\n        const city = new CityEntity();\r\n        city.name = cityDto.name;\r\n        city.state = state;\r\n        await this.cityRepository.save(city);\r\n      }),\r\n    );\r\n  }\r\n}\r\n"]}