(()=>{var e={};e.id=9356,e.ids=[9356],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},93190:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>o,pages:()=>m,routeModule:()=>p,tree:()=>x});var i=s(73137),r=s(54647),a=s(4183),n=s.n(a),l=s(71775),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c=i.AppPageRouteModule,x=["",{children:["registro",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,92949)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\registro\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51918,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\registro\\[id]\\page.tsx"],o="/registro/[id]/page",h={require:s,loadChunk:()=>Promise.resolve()},p=new c({definition:{kind:r.x.APP_PAGE,page:"/registro/[id]/page",pathname:"/registro/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:x}})},64721:(e,t,s)=>{Promise.resolve().then(s.bind(s,58672))},58672:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Registro});var i=s(60080),r=s(85814),a=s(9885),n=s(34751),l=s(99986),d=s(66558),c=s(57086),x=s(21145),m=s(88879),o=s(96413),h=s(49714),p=s(54986);function Registro({params:e}){let[t,s]=(0,a.useState)(!1),[g,u]=(0,a.useState)(!1),[f,b]=(0,a.useState)(),[N,j]=(0,a.useState)(void 0),{register:w,handleSubmit:v,watch:y,setValue:F,reset:C,formState:{errors:$}}=(0,d.cI)({resolver:(0,c.X)(m._n)}),P=y("zipCode");(0,a.useEffect)(()=>{(function(e){let t=e.replace(/[^0-9]/g,"");8===t.length&&handleGetByCep(t)})(P||"")},[P]),(0,a.useEffect)(()=>{validateToken()},[]);let validateToken=()=>{r.Z.get("/pre-register/token-validate",{headers:{token:e.id}}).then(e=>{b(e.data.investment),F("email",e.data.email),F("document",e.data.document),j(e.data.owner)}).catch(e=>{n.Am.error("Tivemos um erro ao buscar os dados")})};async function handleGetByCep(e){let t=e.replace(/[^0-9]/g,"");8!==t.length||await x.Z.get(`https://viacep.com.br/ws/${t}/json/`).then(e=>{e&&e.data&&(e.data.erro||(F("neighborhood",""!==e.data.bairro?`${e.data.bairro} - ${e.data.logradouro}`:""),F("city",e.data.localidade),F("state",e.data.uf)))}).catch(()=>{}).finally(()=>{})}return(0,i.jsxs)("div",{className:"px-10",children:[(0,i.jsxs)("div",{className:"mt-10",children:[i.jsx("img",{className:"mx-auto h-10 w-auto",src:"/logo.svg",alt:"Your Company"}),i.jsx("p",{className:"text-2xl text-white text-center my-10",children:"Preencha os Dados Necess\xe1rios"})]}),i.jsx("form",{action:"",onSubmit:v(t=>{if(u(!0),(0,p.m)(t.dtBirth))return n.Am.warn("O investidor n\xe3o pode ser menor de idade.");let s={name:t.name,rg:t.rg,document:(0,o.p4)(t.document||""),phoneNumber:`+55${(0,o.p4)(t.phoneNumber||"")}`,dtBirth:t.dtBirth,email:t.email,signIca:h.l,occupation:t.occupation,placeOfBirth:t.placeOfBirth,issuer:t.issuer,motherName:t.motherName,address:{zipCode:t.zipCode,neighborhood:t.neighborhood,city:t.city,complement:t.complement,number:t.number,state:t.state},investment:f,observations:"",accountBank:{bank:t.bank,accountNumber:t.accountNumber,agency:t.agency,pix:t.pix},accountType:N?"BUSINESS":"PHYSICAL",owner:N||void 0,testify:[{name:t?.testifyPrimaryName||"",cpf:(0,o.p4)(t.testifyPrimaryCpf||""),sign:t?.testifyPrimaryName||""},{name:t.testifySecondaryName||"",cpf:(0,o.p4)(t.testifySecondaryCpf||""),sign:t.testifySecondaryName||""}]};r.Z.post("/contract",s,{headers:{token:e.id}}).then(e=>{n.Am.success("Conta pr\xe9 cadastrada com sucesso!"),C(),window.close()}).catch(e=>{n.Am.error(e.response.data.message||"Tivemos um erro ao cadastrar a conta")}).finally(()=>u(!1))}),children:(0,i.jsxs)("div",{className:"md:w-9/12 m-auto",children:[i.jsx("p",{className:"text-xl text-white",children:"Dados Pessoais"}),(0,i.jsxs)("div",{className:"mb-10 p-7 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.name&&`- ${$.name.message}`})]}),i.jsx("input",{...w("name"),className:`h-12 w-full px-4 text-white rounded-xl ${$.name?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Identidade ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.rg&&`- ${$.rg.message}`})]}),i.jsx("input",{...w("rg"),type:"number",className:`h-12 w-full px-4 text-white rounded-xl ${$.rg?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),f?.modality==="SCP"&&i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Org\xe3o emissor",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.issuer&&`- ${$.issuer.message}`})]}),i.jsx("input",{...w("issuer"),type:"text",className:`h-12 w-full px-4 text-white rounded-xl ${$.issuer?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Celular ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.phoneNumber&&`- ${$.phoneNumber.message}`})]}),i.jsx("input",{...w("phoneNumber"),onChange:({target:e})=>F("phoneNumber",(0,o.gP)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${$.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),f?.modality==="SCP"&&i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Ocupa\xe7\xe3o ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.occupation&&`- ${$.occupation.message}`})]}),i.jsx("input",{...w("occupation"),type:"text",className:`h-12 w-full px-4 text-white rounded-xl ${$.occupation?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CPF/CNPJ ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.document&&`- ${$.document.message}`})]}),i.jsx("input",{...w("document"),disabled:!0,className:`h-12 w-full px-4 text-white rounded-xl ${$.document?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.dtBirth&&`- ${$.dtBirth.message}`})]}),i.jsx("input",{...w("dtBirth"),type:"date",className:`h-12 w-full px-4 text-white rounded-xl ${$.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),f?.modality==="SCP"&&i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nacionalidade",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.placeOfBirth&&`- ${$.placeOfBirth.message}`})]}),i.jsx("input",{...w("placeOfBirth"),type:"text",className:`h-12 w-full px-4 text-white rounded-xl ${$.placeOfBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),N&&(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[i.jsx("p",{className:"text-white mb-1",children:"Nome do representante"}),i.jsx("input",{disabled:!0,value:N?.name,className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1"})]})}),i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[i.jsx("p",{className:"text-white mb-1",children:"CPF do representante"}),i.jsx("input",{type:"text",disabled:!0,value:(0,o.VL)(N?.cpf||""),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1"})]})})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["E-mail ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.email&&`- ${$.email.message}`})]}),i.jsx("input",{...w("email"),disabled:!0,className:`h-12 w-full px-4 text-white rounded-xl ${$.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.motherName&&`- ${$.motherName.message}`})]}),i.jsx("input",{...w("motherName"),className:`h-12 w-full px-4 text-white rounded-xl ${$.motherName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]}),i.jsx("div",{className:"flex items-center",children:i.jsx("p",{className:"text-xl text-white mr-1",children:"Informa\xe7\xf5es de Endere\xe7o"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CEP ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.zipCode&&`- ${$.zipCode.message}`})]}),i.jsx("input",{...w("zipCode"),className:`h-12 w-full px-4 text-white rounded-xl ${$.zipCode?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Endere\xe7o ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.neighborhood&&`- ${$.neighborhood.message}`})]}),i.jsx("input",{...w("neighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${$.neighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.number&&`- ${$.number.message}`})]}),i.jsx("input",{...w("number"),className:`h-12 w-full px-4 text-white rounded-xl ${$.number?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Cidade ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.city&&`- ${$.city.message}`})]}),i.jsx("input",{...w("city"),className:`h-12 w-full px-4 text-white rounded-xl ${$.city?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Estado ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.state&&`- ${$.state.message}`})]}),i.jsx("input",{...w("state"),className:`h-12 w-full px-4 text-white rounded-xl ${$.state?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Complemento ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.complement&&`- ${$.complement.message}`})]}),i.jsx("input",{...w("complement"),className:`h-12 w-full px-4 text-white rounded-xl ${$.complement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]}),i.jsx("div",{className:"flex items-center",children:i.jsx("p",{className:"text-xl text-white mr-1",children:"Informa\xe7\xf5es Bancarias"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[(0,i.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Banco ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.bank&&`- ${$.bank.message}`})]}),i.jsx("input",{...w("bank"),className:`h-12 w-full px-4 text-white rounded-xl ${$.bank?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Ag\xeancia ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.agency&&`- ${$.agency.message}`})]}),i.jsx("input",{...w("agency"),className:`h-12 w-full px-4 text-white rounded-xl ${$.agency?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"md:w-1/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Conta ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.accountNumber&&`- ${$.accountNumber.message}`})]}),i.jsx("input",{...w("accountNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${$.accountNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),i.jsx("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:i.jsx("div",{className:"md:w-2/4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Chave PIX ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.pix&&`- ${$.pix.message}`})]}),i.jsx("input",{...w("pix"),className:`h-12 w-full px-4 text-white rounded-xl ${$.pix?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})})]}),f?.modality==="SCP"&&(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsxs)("div",{className:"w-2/4",children:[i.jsx("div",{className:"flex items-center",children:i.jsx("p",{className:"text-xl text-white mr-1",children:"Testemunha 1"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[i.jsx("div",{children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.testifyPrimaryName&&`- ${$.testifyPrimaryName.message}`})]}),i.jsx("input",{...w("testifyPrimaryName"),className:`h-12 w-full px-4 text-white rounded-xl ${$.testifyPrimaryName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"mt-4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CPF ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.testifyPrimaryCpf&&`- ${$.testifyPrimaryCpf.message}`})]}),i.jsx("input",{...w("testifyPrimaryCpf"),onChange:({target:e})=>{F("testifyPrimaryCpf",(0,o.VL)(e.value))},className:`h-12 w-full px-4 text-white rounded-xl ${$.testifyPrimaryCpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]}),(0,i.jsxs)("div",{className:"w-2/4",children:[i.jsx("div",{className:"flex items-center",children:i.jsx("p",{className:"text-xl text-white mr-1",children:"Testemunha 2"})}),(0,i.jsxs)("div",{className:"mb-10 px-7 py-5 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[i.jsx("div",{children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["Nome ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.testifySecondaryName&&`- ${$.testifySecondaryName.message}`})]}),i.jsx("input",{...w("testifySecondaryName"),className:`h-12 w-full px-4 text-white rounded-xl ${$.testifySecondaryName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),i.jsx("div",{className:"mt-4",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white mb-1",children:["CPF ",i.jsx("b",{className:"text-red-500 font-light text-sm",children:$.testifySecondaryCpf&&`- ${$.testifySecondaryCpf.message}`})]}),i.jsx("input",{...w("testifySecondaryCpf"),onChange:({target:e})=>{F("testifySecondaryCpf",(0,o.VL)(e.value))},className:`h-12 w-full px-4 text-white rounded-xl ${$.testifySecondaryCpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),i.jsx("p",{className:"text-xl text-white",children:"Dados para Dep\xf3sito"}),(0,i.jsxs)("div",{className:"mb-10 p-7 m-auto bg-opacity-90 border border-[#FF9900] rounded-[30px] shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:[i.jsx("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),i.jsx("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),i.jsx("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),i.jsx("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),i.jsx("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),i.jsx("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]}),(0,i.jsxs)("div",{className:"mb-5",children:[i.jsx("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),i.jsx("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,i.jsxs)("div",{className:"flex mt-5",children:[i.jsx("input",{type:"checkbox",checked:t,className:"cursor-pointer",onChange:()=>s(!t)}),i.jsx("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),i.jsx("div",{className:"md:w-52 mb-10",children:i.jsx(l.Z,{label:"ENVIAR",loading:g,disabled:!t||g})})]})})]})}},99986:(e,t,s)=>{"use strict";s.d(t,{Z:()=>Button});var i=s(60080),r=s(69957);function Button({handleSubmit:e,loading:t,label:s,disabled:a,className:n,...l}){return i.jsx(r.z,{...l,onClick:e,loading:t,disabled:a,className:n,children:s})}},49714:(e,t,s)=>{"use strict";s.d(t,{l:()=>i});let i="Shayra Madalena Lyra de Pinho"},92949:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>d});var i=s(17536);let r=(0,i.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\registro\[id]\page.tsx`),{__esModule:a,$$typeof:n}=r,l=r.default,d=l}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[4103,6426,4731,6558,3356,7207,278,8879],()=>__webpack_exec__(93190));module.exports=s})();