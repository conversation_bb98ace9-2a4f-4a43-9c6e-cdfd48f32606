"use strict";exports.id=7017,exports.ids=[7017],exports.modules={27017:(e,t,l)=>{l.d(t,{Z:()=>InputSelect});var s=l(60080);function InputSelect({optionSelected:e,options:t,setOptionSelected:l,label:r,placeHolder:a="",width:i="100%",register:n=()=>{},error:d,errorMessage:c,name:x="",disableErrorMessage:o=!1,disabled:u=!1}){return(0,s.jsxs)("div",{className:"inputSelect relative group",style:{width:i},children:[s.jsx("p",{className:"text-white mb-1 text-sm",children:r}),(0,s.jsxs)("select",{disabled:o&&!c,...n(x),value:e,onChange:({target:e})=>{l&&l(e.value)},id:x,className:`h-12 w-full px-4 ${u?"text-zinc-400":"text-white"} rounded-xl ${d?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`,children:[a&&s.jsx("option",{selected:!0,disabled:!0,value:"",children:a}),t.map((e,t)=>s.jsx("option",{className:"cursor-pointer",value:e.value,children:e.label},t))]}),d&&s.jsx("div",{className:" absolute gr max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[90%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:c})]})}}};