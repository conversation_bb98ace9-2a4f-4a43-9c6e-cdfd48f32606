(()=>{var e={};e.id=9879,e.ids=[9879],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},8284:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>u,routeModule:()=>m,tree:()=>d});var r=a(73137),s=a(54647),o=a(4183),i=a.n(o),n=a(71775),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let c=r.AppPageRouteModule,d=["",{children:["financeiro",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,19040)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\financeiro\\page.tsx"],p="/financeiro/page",x={require:a,loadChunk:()=>Promise.resolve()},m=new c({definition:{kind:s.x.APP_PAGE,page:"/financeiro/page",pathname:"/financeiro",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},46525:(e,t,a)=>{Promise.resolve().then(a.bind(a,59673))},59673:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Financeiro});var r=a(60080),s=a(52451),o=a.n(s),i=a(51344),n=a(63960),l=a(73589),c=a(9885),d=a(27612),u=a(97669),p=a(47956),x=a(85814),m=a(34751),h=a(64731),f=a.n(h);a(73079);var v=a(74644),g=a(24577),y=a(7537),j=a(57114),b=a(90682),C=a(69957);function Financeiro(){let e=(0,b.e)(),t="broker"===e.name,[a,s]=(0,c.useState)(!0),h=(0,j.useRouter)();f().locale("pt-br");let[w,N]=(0,c.useState)({background:{visible:!1},theme:d.Z,data:[{month:"Jan",value:0},{month:"Fev",value:0},{month:"Mar",value:0},{month:"Abr",value:0},{month:"Mai",value:0},{month:"Jun",value:0},{month:"Jul",value:0},{month:"Ago",value:0},{month:"Set",value:0},{month:"Out",value:0},{month:"Nov",value:0},{month:"Dez",value:0}],series:[{type:"line",xKey:"month",xName:"Month",yKey:"value",interpolation:{type:"linear"},tooltip:{enabled:!1}}],axes:[{type:"category",position:"bottom"},{type:"number",label:{fontSize:9,formatter:({value:e})=>new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e)}}]}),capitalizeFirstLetter=e=>e.charAt(0).toUpperCase()+e.slice(1),formatData=e=>t?e.map(e=>({month:capitalizeFirstLetter(`${e.month[0]}${e.month[1]}${e.month[2]}`),value:Number(e.totalContracts)})):e.map(e=>({month:capitalizeFirstLetter(f()(e.date).utc().format("MMM")),value:Number(e.totalValue)}));(0,c.useEffect)(()=>{t?getQuotes("daily"):(getQuotes("daily"),getQuotes("weekly"),getQuotes("monthly")),getChartData()},[]);let[q,_]=(0,c.useState)({daily:{details:[],totalContracts:0,totalValue:0},weekly:{details:[],totalContracts:0,totalValue:0},monthly:{details:[],totalContracts:0,totalValue:0}}),getQuotes=a=>{x.Z.get(t?`/broker/contracts-capture/${e.roleId}`:"/acquisition",{params:{period:a}}).then(e=>{t?_({daily:{totalValue:e.data.investments.today,totalContracts:e.data.contracts.today,details:[]},monthly:{totalValue:e.data.investments.thisMonth,totalContracts:e.data.contracts.thisMonth,details:[]},weekly:{totalValue:e.data.investments.thisWeek,totalContracts:e.data.contracts.thisWeek,details:[]}}):_(t=>({...t,[a]:e.data}))}).catch(e=>{(0,g.Z)(e,"N\xe3o foi possivel pegar os dados de capta\xe7\xe3o!")}).finally(()=>s(!1))},getChartData=()=>{x.Z.get(t?`/broker/contracts-monthly-capture/${e.roleId}`:"/acquisition/chart",{params:{period:"monthly"}}).then(e=>{let t=formatData(e.data);N({...w,data:[...t]})}).catch(e=>{m.Am.error(e.response.data.message||"N\xe3o foi possivel buscar os valores de capta\xe7\xe3o")})},AcquisitinCard=({title:s,values:i,typePdf:l})=>{let[d,u]=(0,c.useState)(!1);return(0,r.jsxs)("div",{className:"md:w-1/3 mb-5 md:mb-0 bg-orange-linear flex flex-col justify-between rounded-t-xl rounded-b-2xl",children:[(0,r.jsxs)("div",{className:"w-full flex flex-col items-center justify-center p-3",children:[r.jsx(o(),{src:n.Z,alt:"",width:35,color:"#fff"}),r.jsx("p",{className:"text-xl",children:s})]}),r.jsx("div",{className:"w-full bg-[#1C1C1C] p-5 rounded-xl",children:(0,r.jsxs)("div",{className:"flex w-full justify-between items-end pb-3",children:[(0,r.jsxs)("div",{className:"",children:[r.jsx("p",{className:"text-sm",children:"Novos Contratos"}),r.jsx(v.Z,{loading:a,height:"25px",children:r.jsx("p",{className:"text-5xl font-bold",children:i.totalContracts})}),r.jsx("p",{className:"text-sm mt-2",children:"Valor total de Investimento"}),r.jsx(v.Z,{loading:a,height:"25px",children:r.jsx("p",{className:"text-xl font-bold",children:i.totalValue.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),i.totalContracts>0&&r.jsx(C.z,{loading:d,className:"ml-2",size:"sm",onClick:()=>{u(!0),m.Am.info("Gerando relat\xf3rio!",{autoClose:!1,toastId:"generatePdf"}),x.Z.post(t?`/broker/${e.roleId}/contracts-report`:"/reports",{},{params:{period:l,type:"acquisition"},headers:{roleId:e.roleId}}).then(e=>{if(""===e.data.url)return m.Am.warning("N\xe3o foram encontrados dados dispon\xedveis para a gera\xe7\xe3o do relat\xf3rio.");window.open(e.data.url,"_blanck")}).catch(e=>{m.Am.error(e?.response?.data?.message||"N\xe3o foi possivel exportar o relat\xf3rio!")}).finally(()=>{m.Am.dismiss("generatePdf"),u(!1)})},children:"Gerar relat\xf3rio"})]})})]})};return(0,r.jsxs)("div",{children:[r.jsx(u.Z,{}),r.jsx(p.Z,{children:(0,r.jsxs)("div",{className:"w-full flex flex-col gap-8 text-white",children:[(0,r.jsxs)("div",{className:"md:flex w-full gap-8",children:[r.jsx(i.Z,{type:"TODAY",push:"/financeiro/pagamento"}),r.jsx(i.Z,{type:"WEEK",push:"/financeiro/pagamento"}),r.jsx(i.Z,{type:"MONTH",push:"/financeiro/pagamento"})]}),!t&&(0,r.jsxs)("div",{className:"flex justify-between items-center py-3 px-7 bg-orange-linear rounded-xl cursor-pointer",onClick:()=>h.push("/financeiro/pagamentos"),children:[r.jsx("div",{children:r.jsx("p",{children:"Visualizar lista completa de pagamentos"})}),r.jsx(y.Z,{width:30})]}),(0,r.jsxs)("div",{className:"md:flex w-full gap-8",children:[r.jsx(AcquisitinCard,{typePdf:"daily",title:"Capta\xe7\xe3o Di\xe1ria",values:q.daily}),r.jsx(AcquisitinCard,{typePdf:"weekly",title:"Capta\xe7\xe3o Semanal",values:q.weekly}),r.jsx(AcquisitinCard,{typePdf:"monthly",title:"Capta\xe7\xe3o Mensal",values:q.monthly})]}),r.jsx("div",{className:"md:w-5/12",children:(0,r.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 rounded-xl bg-orange-linear border border-[#FF9900]",children:[(0,r.jsxs)("div",{className:"flex items-start justify-center flex-col p-3",children:[r.jsx(o(),{src:n.Z,alt:"",width:35,color:"#fff"}),r.jsx("p",{className:"text-xl",children:"Capta\xe7\xe3o"})]}),r.jsx("div",{className:"w-full bg-[#1C1C1C] p-1 rounded-xl",children:r.jsx(l.bY,{options:w})})]})})]})})]})}},19040:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>o,default:()=>l});var r=a(17536);let s=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\financeiro\page.tsx`),{__esModule:o,$$typeof:i}=s,n=s.default,l=n},7537:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var r=a(9885);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}),o=s}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,3079,130,7207,278,7669,2686,3130],()=>__webpack_exec__(8284));module.exports=a})();