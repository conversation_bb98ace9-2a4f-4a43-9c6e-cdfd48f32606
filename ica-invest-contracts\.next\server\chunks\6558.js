"use strict";exports.id=6558,exports.ids=[6558],exports.modules={66558:(e,t,i)=>{i.d(t,{KN:()=>appendErrors,U2:()=>get,cI:()=>useForm,t8:()=>set});var r=i(9885),isCheckBoxInput=e=>"checkbox"===e.type,isDateObject=e=>e instanceof Date,isNullOrUndefined=e=>null==e;let isObjectType=e=>"object"==typeof e;var isObject=e=>!isNullOrUndefined(e)&&!Array.isArray(e)&&isObjectType(e)&&!isDateObject(e),getEventValue=e=>isObject(e)&&e.target?isCheckBoxInput(e.target)?e.target.checked:e.target.value:e,getNodeParentName=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,isNameInFieldArray=(e,t)=>e.has(getNodeParentName(t)),isPlainObject=e=>{let t=e.constructor&&e.constructor.prototype;return isObject(t)&&t.hasOwnProperty("isPrototypeOf")},s="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function cloneObject(e){let t;let i=Array.isArray(e),r="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(s&&(e instanceof Blob||r))&&(i||isObject(e))))return e;else if(t=i?[]:{},i||isPlainObject(e))for(let i in e)e.hasOwnProperty(i)&&(t[i]=cloneObject(e[i]));else t=e;return t}var isKey=e=>/^\w*$/.test(e),isUndefined=e=>void 0===e,compact=e=>Array.isArray(e)?e.filter(Boolean):[],stringToPath=e=>compact(e.replace(/["|']|\]/g,"").split(/\.|\[/)),get=(e,t,i)=>{if(!t||!isObject(e))return i;let r=(isKey(t)?[t]:stringToPath(t)).reduce((e,t)=>isNullOrUndefined(e)?e:e[t],e);return isUndefined(r)||r===e?isUndefined(e[t])?i:e[t]:r},isBoolean=e=>"boolean"==typeof e,set=(e,t,i)=>{let r=-1,s=isKey(t)?[t]:stringToPath(t),a=s.length,l=a-1;for(;++r<a;){let t=s[r],a=i;if(r!==l){let i=e[t];a=isObject(i)||Array.isArray(i)?i:isNaN(+s[r+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=a,e=e[t]}};let a={BLUR:"blur",FOCUS_OUT:"focusout"},l={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},n={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},u=r.createContext(null);u.displayName="HookFormContext";var getProxyFormState=(e,t,i,r=!0)=>{let s={defaultValues:t._defaultValues};for(let a in e)Object.defineProperty(s,a,{get:()=>(t._proxyFormState[a]!==l.all&&(t._proxyFormState[a]=!r||l.all),i&&(i[a]=!0),e[a])});return s};let d="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;var isString=e=>"string"==typeof e,generateWatchOutput=(e,t,i,r,s)=>isString(e)?(r&&t.watch.add(e),get(i,e,s)):Array.isArray(e)?e.map(e=>(r&&t.watch.add(e),get(i,e))):(r&&(t.watchAll=!0),i),appendErrors=(e,t,i,r,s)=>t?{...i[e],types:{...i[e]&&i[e].types?i[e].types:{},[r]:s||!0}}:{},convertToArrayPayload=e=>Array.isArray(e)?e:[e],createSubject=()=>{let e=[];return{get observers(){return e},next:t=>{for(let i of e)i.next&&i.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},isPrimitive=e=>isNullOrUndefined(e)||!isObjectType(e);function deepEqual(e,t,i=new WeakSet){if(isPrimitive(e)||isPrimitive(t))return e===t;if(isDateObject(e)&&isDateObject(t))return e.getTime()===t.getTime();let r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;if(i.has(e)||i.has(t))return!0;for(let a of(i.add(e),i.add(t),r)){let r=e[a];if(!s.includes(a))return!1;if("ref"!==a){let e=t[a];if(isDateObject(r)&&isDateObject(e)||isObject(r)&&isObject(e)||Array.isArray(r)&&Array.isArray(e)?!deepEqual(r,e,i):r!==e)return!1}}return!0}var isEmptyObject=e=>isObject(e)&&!Object.keys(e).length,isFileInput=e=>"file"===e.type,isFunction=e=>"function"==typeof e,isHTMLElement=e=>{if(!s)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},isMultipleSelect=e=>"select-multiple"===e.type,isRadioInput=e=>"radio"===e.type,isRadioOrCheckbox=e=>isRadioInput(e)||isCheckBoxInput(e),live=e=>isHTMLElement(e)&&e.isConnected;function unset(e,t){let i=Array.isArray(t)?t:isKey(t)?[t]:stringToPath(t),r=1===i.length?e:function(e,t){let i=t.slice(0,-1).length,r=0;for(;r<i;)e=isUndefined(e)?r++:e[t[r++]];return e}(e,i),s=i.length-1,a=i[s];return r&&delete r[a],0!==s&&(isObject(r)&&isEmptyObject(r)||Array.isArray(r)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!isUndefined(e[t]))return!1;return!0}(r))&&unset(e,i.slice(0,-1)),e}var objectHasFunction=e=>{for(let t in e)if(isFunction(e[t]))return!0;return!1};function markFieldsDirty(e,t={}){let i=Array.isArray(e);if(isObject(e)||i)for(let i in e)Array.isArray(e[i])||isObject(e[i])&&!objectHasFunction(e[i])?(t[i]=Array.isArray(e[i])?[]:{},markFieldsDirty(e[i],t[i])):isNullOrUndefined(e[i])||(t[i]=!0);return t}var getDirtyFields=(e,t)=>(function getDirtyFieldsFromDefaultValues(e,t,i){let r=Array.isArray(e);if(isObject(e)||r)for(let r in e)Array.isArray(e[r])||isObject(e[r])&&!objectHasFunction(e[r])?isUndefined(t)||isPrimitive(i[r])?i[r]=Array.isArray(e[r])?markFieldsDirty(e[r],[]):{...markFieldsDirty(e[r])}:getDirtyFieldsFromDefaultValues(e[r],isNullOrUndefined(t)?{}:t[r],i[r]):i[r]=!deepEqual(e[r],t[r]);return i})(e,t,markFieldsDirty(t));let o={value:!1,isValid:!1},c={value:!0,isValid:!0};var getCheckboxValue=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!isUndefined(e[0].attributes.value)?isUndefined(e[0].value)||""===e[0].value?c:{value:e[0].value,isValid:!0}:c:o}return o},getFieldValueAs=(e,{valueAsNumber:t,valueAsDate:i,setValueAs:r})=>isUndefined(e)?e:t?""===e?NaN:e?+e:e:i&&isString(e)?new Date(e):r?r(e):e;let f={isValid:!1,value:null};var getRadioValue=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,f):f;function getFieldValue(e){let t=e.ref;return isFileInput(t)?t.files:isRadioInput(t)?getRadioValue(e.refs).value:isMultipleSelect(t)?[...t.selectedOptions].map(({value:e})=>e):isCheckBoxInput(t)?getCheckboxValue(e.refs).value:getFieldValueAs(isUndefined(t.value)?e.ref.value:t.value,e)}var getResolverOptions=(e,t,i,r)=>{let s={};for(let i of e){let e=get(t,i);e&&set(s,i,e._f)}return{criteriaMode:i,names:[...e],fields:s,shouldUseNativeValidation:r}},isRegex=e=>e instanceof RegExp,getRuleValue=e=>isUndefined(e)?e:isRegex(e)?e.source:isObject(e)?isRegex(e.value)?e.value.source:e.value:e,getValidationModes=e=>({isOnSubmit:!e||e===l.onSubmit,isOnBlur:e===l.onBlur,isOnChange:e===l.onChange,isOnAll:e===l.all,isOnTouch:e===l.onTouched});let g="AsyncFunction";var hasPromiseValidation=e=>!!e&&!!e.validate&&!!(isFunction(e.validate)&&e.validate.constructor.name===g||isObject(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===g)),hasValidation=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),isWatched=(e,t,i)=>!i&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let iterateFieldsByAction=(e,t,i,r)=>{for(let s of i||Object.keys(e)){let i=get(e,s);if(i){let{_f:e,...a}=i;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],s)&&!r||e.ref&&t(e.ref,e.name)&&!r)return!0;if(iterateFieldsByAction(a,t))break}else if(isObject(a)&&iterateFieldsByAction(a,t))break}}};function schemaErrorLookup(e,t,i){let r=get(e,i);if(r||isKey(i))return{error:r,name:i};let s=i.split(".");for(;s.length;){let r=s.join("."),a=get(t,r),l=get(e,r);if(a&&!Array.isArray(a)&&i!==r)break;if(l&&l.type)return{name:r,error:l};if(l&&l.root&&l.root.type)return{name:`${r}.root`,error:l.root};s.pop()}return{name:i}}var shouldRenderFormState=(e,t,i,r)=>{i(e);let{name:s,...a}=e;return isEmptyObject(a)||Object.keys(a).length>=Object.keys(t).length||Object.keys(a).find(e=>t[e]===(!r||l.all))},shouldSubscribeByName=(e,t,i)=>!e||!t||e===t||convertToArrayPayload(e).some(e=>e&&(i?e===t:e.startsWith(t)||t.startsWith(e))),skipValidation=(e,t,i,r,s)=>!s.isOnAll&&(!i&&s.isOnTouch?!(t||e):(i?r.isOnBlur:s.isOnBlur)?!e:(i?!r.isOnChange:!s.isOnChange)||e),unsetEmptyArray=(e,t)=>!compact(get(e,t)).length&&unset(e,t),updateFieldArrayRootError=(e,t,i)=>{let r=convertToArrayPayload(get(e,i));return set(r,"root",t[i]),set(e,i,r),e},isMessage=e=>isString(e);function getValidateError(e,t,i="validate"){if(isMessage(e)||Array.isArray(e)&&e.every(isMessage)||isBoolean(e)&&!e)return{type:i,message:isMessage(e)?e:"",ref:t}}var getValueAndMessage=e=>isObject(e)&&!isRegex(e)?e:{value:e,message:""},validateField=async(e,t,i,r,s,a)=>{let{ref:l,refs:u,required:d,maxLength:o,minLength:c,min:f,max:g,pattern:y,validate:m,name:b,valueAsNumber:p,mount:h}=e._f,V=get(i,b);if(!h||t.has(b))return{};let v=u?u[0]:l,setCustomValidity=e=>{s&&v.reportValidity&&(v.setCustomValidity(isBoolean(e)?"":e||""),v.reportValidity())},F={},_=isRadioInput(l),O=isCheckBoxInput(l),A=(p||isFileInput(l))&&isUndefined(l.value)&&isUndefined(V)||isHTMLElement(l)&&""===l.value||""===V||Array.isArray(V)&&!V.length,S=appendErrors.bind(null,b,r,F),getMinMaxMessage=(e,t,i,r=n.maxLength,s=n.minLength)=>{let a=e?t:i;F[b]={type:e?r:s,message:a,ref:l,...S(e?r:s,a)}};if(a?!Array.isArray(V)||!V.length:d&&(!(_||O)&&(A||isNullOrUndefined(V))||isBoolean(V)&&!V||O&&!getCheckboxValue(u).isValid||_&&!getRadioValue(u).isValid)){let{value:e,message:t}=isMessage(d)?{value:!!d,message:d}:getValueAndMessage(d);if(e&&(F[b]={type:n.required,message:t,ref:v,...S(n.required,t)},!r))return setCustomValidity(t),F}if(!A&&(!isNullOrUndefined(f)||!isNullOrUndefined(g))){let e,t;let i=getValueAndMessage(g),s=getValueAndMessage(f);if(isNullOrUndefined(V)||isNaN(V)){let r=l.valueAsDate||new Date(V),convertTimeToDate=e=>new Date(new Date().toDateString()+" "+e),a="time"==l.type,n="week"==l.type;isString(i.value)&&V&&(e=a?convertTimeToDate(V)>convertTimeToDate(i.value):n?V>i.value:r>new Date(i.value)),isString(s.value)&&V&&(t=a?convertTimeToDate(V)<convertTimeToDate(s.value):n?V<s.value:r<new Date(s.value))}else{let r=l.valueAsNumber||(V?+V:V);isNullOrUndefined(i.value)||(e=r>i.value),isNullOrUndefined(s.value)||(t=r<s.value)}if((e||t)&&(getMinMaxMessage(!!e,i.message,s.message,n.max,n.min),!r))return setCustomValidity(F[b].message),F}if((o||c)&&!A&&(isString(V)||a&&Array.isArray(V))){let e=getValueAndMessage(o),t=getValueAndMessage(c),i=!isNullOrUndefined(e.value)&&V.length>+e.value,s=!isNullOrUndefined(t.value)&&V.length<+t.value;if((i||s)&&(getMinMaxMessage(i,e.message,t.message),!r))return setCustomValidity(F[b].message),F}if(y&&!A&&isString(V)){let{value:e,message:t}=getValueAndMessage(y);if(isRegex(e)&&!V.match(e)&&(F[b]={type:n.pattern,message:t,ref:l,...S(n.pattern,t)},!r))return setCustomValidity(t),F}if(m){if(isFunction(m)){let e=await m(V,i),t=getValidateError(e,v);if(t&&(F[b]={...t,...S(n.validate,t.message)},!r))return setCustomValidity(t.message),F}else if(isObject(m)){let e={};for(let t in m){if(!isEmptyObject(e)&&!r)break;let s=getValidateError(await m[t](V,i),v,t);s&&(e={...s,...S(t,s.message)},setCustomValidity(s.message),r&&(F[b]=e))}if(!isEmptyObject(e)&&(F[b]={ref:v,...e},!r))return F}}return setCustomValidity(!0),F};let y={mode:l.onSubmit,reValidateMode:l.onChange,shouldFocusError:!0};function useForm(e={}){let t=r.useRef(void 0),i=r.useRef(void 0),[n,u]=r.useState({isDirty:!1,isValidating:!1,isLoading:isFunction(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:isFunction(e.defaultValues)?void 0:e.defaultValues});if(!t.current){if(e.formControl)t.current={...e.formControl,formState:n},e.defaultValues&&!isFunction(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:i,...r}=function(e={}){let t,i={...y,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:isFunction(i.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:i.errors||{},disabled:i.disabled||!1},n={},u=(isObject(i.defaultValues)||isObject(i.values))&&cloneObject(i.defaultValues||i.values)||{},d=i.shouldUnregister?{}:cloneObject(u),o={action:!1,mount:!1,watch:!1},c={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},f=0,g={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},m={...g},b={array:createSubject(),state:createSubject()},p=i.criteriaMode===l.all,debounce=e=>t=>{clearTimeout(f),f=setTimeout(e,t)},_setValid=async e=>{if(!i.disabled&&(g.isValid||m.isValid||e)){let e=i.resolver?isEmptyObject((await _runSchema()).errors):await executeBuiltInValidation(n,!0);e!==r.isValid&&b.state.next({isValid:e})}},_updateIsValidating=(e,t)=>{!i.disabled&&(g.isValidating||g.validatingFields||m.isValidating||m.validatingFields)&&((e||Array.from(c.mount)).forEach(e=>{e&&(t?set(r.validatingFields,e,t):unset(r.validatingFields,e))}),b.state.next({validatingFields:r.validatingFields,isValidating:!isEmptyObject(r.validatingFields)}))},updateErrors=(e,t)=>{set(r.errors,e,t),b.state.next({errors:r.errors})},updateValidAndValue=(e,t,i,r)=>{let s=get(n,e);if(s){let a=get(d,e,isUndefined(i)?get(u,e):i);isUndefined(a)||r&&r.defaultChecked||t?set(d,e,t?a:getFieldValue(s._f)):setFieldValue(e,a),o.mount&&_setValid()}},updateTouchAndDirty=(e,t,s,a,l)=>{let n=!1,d=!1,o={name:e};if(!i.disabled){if(!s||a){(g.isDirty||m.isDirty)&&(d=r.isDirty,r.isDirty=o.isDirty=_getDirty(),n=d!==o.isDirty);let i=deepEqual(get(u,e),t);d=!!get(r.dirtyFields,e),i?unset(r.dirtyFields,e):set(r.dirtyFields,e,!0),o.dirtyFields=r.dirtyFields,n=n||(g.dirtyFields||m.dirtyFields)&&!i!==d}if(s){let t=get(r.touchedFields,e);t||(set(r.touchedFields,e,s),o.touchedFields=r.touchedFields,n=n||(g.touchedFields||m.touchedFields)&&t!==s)}n&&l&&b.state.next(o)}return n?o:{}},shouldRenderByError=(e,s,a,l)=>{let n=get(r.errors,e),u=(g.isValid||m.isValid)&&isBoolean(s)&&r.isValid!==s;if(i.delayError&&a?(t=debounce(()=>updateErrors(e,a)))(i.delayError):(clearTimeout(f),t=null,a?set(r.errors,e,a):unset(r.errors,e)),(a?!deepEqual(n,a):n)||!isEmptyObject(l)||u){let t={...l,...u&&isBoolean(s)?{isValid:s}:{},errors:r.errors,name:e};r={...r,...t},b.state.next(t)}},_runSchema=async e=>{_updateIsValidating(e,!0);let t=await i.resolver(d,i.context,getResolverOptions(e||c.mount,n,i.criteriaMode,i.shouldUseNativeValidation));return _updateIsValidating(e),t},executeSchemaAndUpdateState=async e=>{let{errors:t}=await _runSchema(e);if(e)for(let i of e){let e=get(t,i);e?set(r.errors,i,e):unset(r.errors,i)}else r.errors=t;return t},executeBuiltInValidation=async(e,t,s={valid:!0})=>{for(let a in e){let l=e[a];if(l){let{_f:e,...n}=l;if(e){let n=c.array.has(e.name),u=l._f&&hasPromiseValidation(l._f);u&&g.validatingFields&&_updateIsValidating([a],!0);let o=await validateField(l,c.disabled,d,p,i.shouldUseNativeValidation&&!t,n);if(u&&g.validatingFields&&_updateIsValidating([a]),o[e.name]&&(s.valid=!1,t))break;t||(get(o,e.name)?n?updateFieldArrayRootError(r.errors,o,e.name):set(r.errors,e.name,o[e.name]):unset(r.errors,e.name))}isEmptyObject(n)||await executeBuiltInValidation(n,t,s)}}return s.valid},_getDirty=(e,t)=>!i.disabled&&(e&&t&&set(d,e,t),!deepEqual(getValues(),u)),_getWatch=(e,t,i)=>generateWatchOutput(e,c,{...o.mount?d:isUndefined(t)?u:isString(e)?{[e]:t}:t},i,t),setFieldValue=(e,t,i={})=>{let r=get(n,e),s=t;if(r){let i=r._f;i&&(i.disabled||set(d,e,getFieldValueAs(t,i)),s=isHTMLElement(i.ref)&&isNullOrUndefined(t)?"":t,isMultipleSelect(i.ref)?[...i.ref.options].forEach(e=>e.selected=s.includes(e.value)):i.refs?isCheckBoxInput(i.ref)?i.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(s)?e.checked=!!s.find(t=>t===e.value):e.checked=s===e.value||!!s)}):i.refs.forEach(e=>e.checked=e.value===s):isFileInput(i.ref)?i.ref.value="":(i.ref.value=s,i.ref.type||b.state.next({name:e,values:cloneObject(d)})))}(i.shouldDirty||i.shouldTouch)&&updateTouchAndDirty(e,s,i.shouldTouch,i.shouldDirty,!0),i.shouldValidate&&trigger(e)},setValues=(e,t,i)=>{for(let r in t){if(!t.hasOwnProperty(r))return;let s=t[r],a=e+"."+r,l=get(n,a);(c.array.has(e)||isObject(s)||l&&!l._f)&&!isDateObject(s)?setValues(a,s,i):setFieldValue(a,s,i)}},setValue=(e,t,i={})=>{let s=get(n,e),a=c.array.has(e),l=cloneObject(t);set(d,e,l),a?(b.array.next({name:e,values:cloneObject(d)}),(g.isDirty||g.dirtyFields||m.isDirty||m.dirtyFields)&&i.shouldDirty&&b.state.next({name:e,dirtyFields:getDirtyFields(u,d),isDirty:_getDirty(e,l)})):!s||s._f||isNullOrUndefined(l)?setFieldValue(e,l,i):setValues(e,l,i),isWatched(e,c)&&b.state.next({...r}),b.state.next({name:o.mount?e:void 0,values:cloneObject(d)})},onChange=async e=>{o.mount=!0;let s=e.target,l=s.name,u=!0,f=get(n,l),_updateIsFieldValueUpdated=e=>{u=Number.isNaN(e)||isDateObject(e)&&isNaN(e.getTime())||deepEqual(e,get(d,l,e))},y=getValidationModes(i.mode),h=getValidationModes(i.reValidateMode);if(f){let o,V;let v=s.type?getFieldValue(f._f):getEventValue(e),F=e.type===a.BLUR||e.type===a.FOCUS_OUT,_=!hasValidation(f._f)&&!i.resolver&&!get(r.errors,l)&&!f._f.deps||skipValidation(F,get(r.touchedFields,l),r.isSubmitted,h,y),O=isWatched(l,c,F);set(d,l,v),F?(f._f.onBlur&&f._f.onBlur(e),t&&t(0)):f._f.onChange&&f._f.onChange(e);let A=updateTouchAndDirty(l,v,F),S=!isEmptyObject(A)||O;if(F||b.state.next({name:l,type:e.type,values:cloneObject(d)}),_)return(g.isValid||m.isValid)&&("onBlur"===i.mode?F&&_setValid():F||_setValid()),S&&b.state.next({name:l,...O?{}:A});if(!F&&O&&b.state.next({...r}),i.resolver){let{errors:e}=await _runSchema([l]);if(_updateIsFieldValueUpdated(v),u){let t=schemaErrorLookup(r.errors,n,l),i=schemaErrorLookup(e,n,t.name||l);o=i.error,l=i.name,V=isEmptyObject(e)}}else _updateIsValidating([l],!0),o=(await validateField(f,c.disabled,d,p,i.shouldUseNativeValidation))[l],_updateIsValidating([l]),_updateIsFieldValueUpdated(v),u&&(o?V=!1:(g.isValid||m.isValid)&&(V=await executeBuiltInValidation(n,!0)));u&&(f._f.deps&&trigger(f._f.deps),shouldRenderByError(l,V,o,A))}},_focusInput=(e,t)=>{if(get(r.errors,t)&&e.focus)return e.focus(),1},trigger=async(e,t={})=>{let s,a;let l=convertToArrayPayload(e);if(i.resolver){let t=await executeSchemaAndUpdateState(isUndefined(e)?e:l);s=isEmptyObject(t),a=e?!l.some(e=>get(t,e)):s}else e?((a=(await Promise.all(l.map(async e=>{let t=get(n,e);return await executeBuiltInValidation(t&&t._f?{[e]:t}:t)}))).every(Boolean))||r.isValid)&&_setValid():a=s=await executeBuiltInValidation(n);return b.state.next({...!isString(e)||(g.isValid||m.isValid)&&s!==r.isValid?{}:{name:e},...i.resolver||!e?{isValid:s}:{},errors:r.errors}),t.shouldFocus&&!a&&iterateFieldsByAction(n,_focusInput,e?l:c.mount),a},getValues=e=>{let t={...o.mount?d:u};return isUndefined(e)?t:isString(e)?get(t,e):e.map(e=>get(t,e))},getFieldState=(e,t)=>({invalid:!!get((t||r).errors,e),isDirty:!!get((t||r).dirtyFields,e),error:get((t||r).errors,e),isValidating:!!get(r.validatingFields,e),isTouched:!!get((t||r).touchedFields,e)}),setError=(e,t,i)=>{let s=(get(n,e,{_f:{}})._f||{}).ref,a=get(r.errors,e)||{},{ref:l,message:u,type:d,...o}=a;set(r.errors,e,{...o,...t,ref:s}),b.state.next({name:e,errors:r.errors,isValid:!1}),i&&i.shouldFocus&&s&&s.focus&&s.focus()},_subscribe=e=>b.state.subscribe({next:t=>{shouldSubscribeByName(e.name,t.name,e.exact)&&shouldRenderFormState(t,e.formState||g,_setFormState,e.reRenderRoot)&&e.callback({values:{...d},...r,...t})}}).unsubscribe,unregister=(e,t={})=>{for(let s of e?convertToArrayPayload(e):c.mount)c.mount.delete(s),c.array.delete(s),t.keepValue||(unset(n,s),unset(d,s)),t.keepError||unset(r.errors,s),t.keepDirty||unset(r.dirtyFields,s),t.keepTouched||unset(r.touchedFields,s),t.keepIsValidating||unset(r.validatingFields,s),i.shouldUnregister||t.keepDefaultValue||unset(u,s);b.state.next({values:cloneObject(d)}),b.state.next({...r,...t.keepDirty?{isDirty:_getDirty()}:{}}),t.keepIsValid||_setValid()},_setDisabledField=({disabled:e,name:t})=>{(isBoolean(e)&&o.mount||e||c.disabled.has(t))&&(e?c.disabled.add(t):c.disabled.delete(t))},register=(e,t={})=>{let r=get(n,e),s=isBoolean(t.disabled)||isBoolean(i.disabled);return set(n,e,{...r||{},_f:{...r&&r._f?r._f:{ref:{name:e}},name:e,mount:!0,...t}}),c.mount.add(e),r?_setDisabledField({disabled:isBoolean(t.disabled)?t.disabled:i.disabled,name:e}):updateValidAndValue(e,!0,t.value),{...s?{disabled:t.disabled||i.disabled}:{},...i.progressive?{required:!!t.required,min:getRuleValue(t.min),max:getRuleValue(t.max),minLength:getRuleValue(t.minLength),maxLength:getRuleValue(t.maxLength),pattern:getRuleValue(t.pattern)}:{},name:e,onChange,onBlur:onChange,ref:s=>{if(s){register(e,t),r=get(n,e);let i=isUndefined(s.value)&&s.querySelectorAll&&s.querySelectorAll("input,select,textarea")[0]||s,a=isRadioOrCheckbox(i),l=r._f.refs||[];(a?l.find(e=>e===i):i===r._f.ref)||(set(n,e,{_f:{...r._f,...a?{refs:[...l.filter(live),i,...Array.isArray(get(u,e))?[{}]:[]],ref:{type:i.type,name:e}}:{ref:i}}}),updateValidAndValue(e,!1,void 0,i))}else(r=get(n,e,{}))._f&&(r._f.mount=!1),(i.shouldUnregister||t.shouldUnregister)&&!(isNameInFieldArray(c.array,e)&&o.action)&&c.unMount.add(e)}}},_focusError=()=>i.shouldFocusError&&iterateFieldsByAction(n,_focusInput,c.mount),handleSubmit=(e,t)=>async s=>{let a;s&&(s.preventDefault&&s.preventDefault(),s.persist&&s.persist());let l=cloneObject(d);if(b.state.next({isSubmitting:!0}),i.resolver){let{errors:e,values:t}=await _runSchema();r.errors=e,l=cloneObject(t)}else await executeBuiltInValidation(n);if(c.disabled.size)for(let e of c.disabled)unset(l,e);if(unset(r.errors,"root"),isEmptyObject(r.errors)){b.state.next({errors:{}});try{await e(l,s)}catch(e){a=e}}else t&&await t({...r.errors},s),_focusError(),setTimeout(_focusError);if(b.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:isEmptyObject(r.errors)&&!a,submitCount:r.submitCount+1,errors:r.errors}),a)throw a},_reset=(e,t={})=>{let a=e?cloneObject(e):u,l=cloneObject(a),f=isEmptyObject(e),y=f?u:l;if(t.keepDefaultValues||(u=a),!t.keepValues){if(t.keepDirtyValues){let e=new Set([...c.mount,...Object.keys(getDirtyFields(u,d))]);for(let t of Array.from(e))get(r.dirtyFields,t)?set(y,t,get(d,t)):setValue(t,get(y,t))}else{if(s&&isUndefined(e))for(let e of c.mount){let t=get(n,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(isHTMLElement(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of c.mount){let t=get(y,e,get(u,e));isUndefined(t)||(set(y,e,t),setValue(e,get(y,e)))}}d=cloneObject(y),b.array.next({values:{...y}}),b.state.next({values:{...y}})}c={mount:t.keepDirtyValues?c.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!g.isValid||!!t.keepIsValid||!!t.keepDirtyValues,o.watch=!!i.shouldUnregister,b.state.next({submitCount:t.keepSubmitCount?r.submitCount:0,isDirty:!f&&(t.keepDirty?r.isDirty:!!(t.keepDefaultValues&&!deepEqual(e,u))),isSubmitted:!!t.keepIsSubmitted&&r.isSubmitted,dirtyFields:f?{}:t.keepDirtyValues?t.keepDefaultValues&&d?getDirtyFields(u,d):r.dirtyFields:t.keepDefaultValues&&e?getDirtyFields(u,e):t.keepDirty?r.dirtyFields:{},touchedFields:t.keepTouched?r.touchedFields:{},errors:t.keepErrors?r.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},reset=(e,t)=>_reset(isFunction(e)?e(d):e,t),_setFormState=e=>{r={...r,...e}},h={control:{register,unregister,getFieldState,handleSubmit,setError,_subscribe,_runSchema,_focusError,_getWatch,_getDirty,_setValid,_setFieldArray:(e,t=[],s,a,l=!0,c=!0)=>{if(a&&s&&!i.disabled){if(o.action=!0,c&&Array.isArray(get(n,e))){let t=s(get(n,e),a.argA,a.argB);l&&set(n,e,t)}if(c&&Array.isArray(get(r.errors,e))){let t=s(get(r.errors,e),a.argA,a.argB);l&&set(r.errors,e,t),unsetEmptyArray(r.errors,e)}if((g.touchedFields||m.touchedFields)&&c&&Array.isArray(get(r.touchedFields,e))){let t=s(get(r.touchedFields,e),a.argA,a.argB);l&&set(r.touchedFields,e,t)}(g.dirtyFields||m.dirtyFields)&&(r.dirtyFields=getDirtyFields(u,d)),b.state.next({name:e,isDirty:_getDirty(e,t),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else set(d,e,t)},_setDisabledField,_setErrors:e=>{r.errors=e,b.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>compact(get(o.mount?d:u,e,i.shouldUnregister?get(u,e,[]):[])),_reset,_resetDefaultValues:()=>isFunction(i.defaultValues)&&i.defaultValues().then(e=>{reset(e,i.resetOptions),b.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of c.unMount){let t=get(n,e);t&&(t._f.refs?t._f.refs.every(e=>!live(e)):!live(t._f.ref))&&unregister(e)}c.unMount=new Set},_disableForm:e=>{isBoolean(e)&&(b.state.next({disabled:e}),iterateFieldsByAction(n,(t,i)=>{let r=get(n,i);r&&(t.disabled=r._f.disabled||e,Array.isArray(r._f.refs)&&r._f.refs.forEach(t=>{t.disabled=r._f.disabled||e}))},0,!1))},_subjects:b,_proxyFormState:g,get _fields(){return n},get _formValues(){return d},get _state(){return o},set _state(value){o=value},get _defaultValues(){return u},get _names(){return c},set _names(value){c=value},get _formState(){return r},get _options(){return i},set _options(value){i={...i,...value}}},subscribe:e=>(o.mount=!0,m={...m,...e.formState},_subscribe({...e,formState:m})),trigger,register,handleSubmit,watch:(e,t)=>isFunction(e)?b.state.subscribe({next:i=>e(_getWatch(void 0,t),i)}):_getWatch(e,t,!0),setValue,getValues,reset,resetField:(e,t={})=>{get(n,e)&&(isUndefined(t.defaultValue)?setValue(e,cloneObject(get(u,e))):(setValue(e,t.defaultValue),set(u,e,cloneObject(t.defaultValue))),t.keepTouched||unset(r.touchedFields,e),t.keepDirty||(unset(r.dirtyFields,e),r.isDirty=t.defaultValue?_getDirty(e,cloneObject(get(u,e))):_getDirty()),!t.keepError&&(unset(r.errors,e),g.isValid&&_setValid()),b.state.next({...r}))},clearErrors:e=>{e&&convertToArrayPayload(e).forEach(e=>unset(r.errors,e)),b.state.next({errors:e?r.errors:{}})},unregister,setError,setFocus:(e,t={})=>{let i=get(n,e),r=i&&i._f;if(r){let e=r.refs?r.refs[0]:r.ref;e.focus&&(e.focus(),t.shouldSelect&&isFunction(e.select)&&e.select())}},getFieldState};return{...h,formControl:h}}(e);t.current={...r,formState:n}}}let o=t.current.control;return o._options=e,d(()=>{let e=o._subscribe({formState:o._proxyFormState,callback:()=>u({...o._formState}),reRenderRoot:!0});return u(e=>({...e,isReady:!0})),o._formState.isReady=!0,e},[o]),r.useEffect(()=>o._disableForm(e.disabled),[o,e.disabled]),r.useEffect(()=>{e.mode&&(o._options.mode=e.mode),e.reValidateMode&&(o._options.reValidateMode=e.reValidateMode)},[o,e.mode,e.reValidateMode]),r.useEffect(()=>{e.errors&&(o._setErrors(e.errors),o._focusError())},[o,e.errors]),r.useEffect(()=>{e.shouldUnregister&&o._subjects.state.next({values:o._getWatch()})},[o,e.shouldUnregister]),r.useEffect(()=>{if(o._proxyFormState.isDirty){let e=o._getDirty();e!==n.isDirty&&o._subjects.state.next({isDirty:e})}},[o,n.isDirty]),r.useEffect(()=>{e.values&&!deepEqual(e.values,i.current)?(o._reset(e.values,o._options.resetOptions),i.current=e.values,u(e=>({...e}))):o._resetDefaultValues()},[o,e.values]),r.useEffect(()=>{o._state.mount||(o._setValid(),o._state.mount=!0),o._state.watch&&(o._state.watch=!1,o._subjects.state.next({...o._formState})),o._removeUnmounted()}),t.current.formState=getProxyFormState(n,o),t.current}}};