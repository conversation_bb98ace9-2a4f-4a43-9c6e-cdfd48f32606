(()=>{var e={};e.id=1389,e.ids=[1389],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},79720:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>x,routeModule:()=>h,tree:()=>d});var r=s(73137),a=s(54647),i=s(4183),n=s.n(i),o=s(71775),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=r.AppPageRouteModule,d=["",{children:["home",{children:["ranking",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62786)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\ranking\\[id]\\page.tsx"],p="/home/<USER>/[id]/page",u={require:s,loadChunk:()=>Promise.resolve()},h=new c({definition:{kind:a.x.APP_PAGE,page:"/home/<USER>/[id]/page",pathname:"/home/<USER>/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73258:(e,t,s)=>{Promise.resolve().then(s.bind(s,49448))},49448:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>RankingSearch});var r=s(60080),a=s(74352),i=s(97669),n=s(18109),o=s(47956),l=s(74644),c=s(85814),d=s(24577),x=s(45682),p=s(73294),u=s(96413),h=s(95081),m=s.n(h),j=s(9885);function RankingSearch({params:e}){let t=localStorage.getItem("searchRanking"),{notificationModal:s}=(0,j.useContext)(x.Z),[h,g]=(0,j.useState)(!1),[f,v]=(0,j.useState)(1),[b,N]=(0,j.useState)(!1),[y,P]=(0,j.useState)({startData:"",endData:"",input:"",type:"",filterOptions:[{label:"Todos",value:""},{label:"Contratos SCP",value:"SCP"},{label:"Contratos M\xfatuo",value:"P2P"}]}),[_,k]=(0,j.useState)({total:0,lastPage:1,perPage:0}),[q,C]=(0,j.useState)(),getBroker=()=>{g(!0),c.Z.get(`/super-admin/active-brokers/${e.id}`,{params:{page:f,limit:10,dateFrom:""!==y.startData?y.startData:void 0,dateTo:""!==y.endData?y.endData:void 0,contractType:""!==y.type?y.type:void 0}}).then(e=>{e?.data?.data?.length>0&&(k(e.data.meta),C(e.data.data))}).catch(e=>{(0,d.Z)(e,"N\xe3o foi possivel buscar os investidores do broker")}).finally(()=>{g(!1)})},getAdvisor=()=>{g(!0),c.Z.get(`/super-admin/active-advisors/${e.id}`,{params:{page:f,limit:10,dateFrom:""!==y.startData?y.startData:void 0,dateTo:""!==y.endData?y.endData:void 0,contractType:""!==y.type?y.type:void 0}}).then(e=>{e?.data?.data?.length>0&&(k(e.data.meta),C(e.data.data))}).catch(e=>{(0,d.Z)(e,"N\xe3o foi possivel buscar os investidores do assessor")}).finally(()=>{g(!1)})};return(0,j.useEffect)(()=>{"broker"===t?getBroker():getAdvisor()},[f]),(0,r.jsxs)("div",{className:`${s?"fixed w-full":"relative"}`,children:[r.jsx(i.Z,{}),r.jsx(o.Z,{children:(0,r.jsxs)("div",{children:[r.jsx("div",{children:(0,r.jsxs)("p",{className:"capitalize text-white text-center text-xl",children:["broker"===t?"Broker":"Assessor"," - Contratos Captados"]})}),(0,r.jsxs)("div",{className:"min-h-[400px] rounded-t-md bg-[#1C1C1C] w-full text-white mt-10 overflow-x-auto rounded-b-md border border-[#FF9900] flex flex-col",children:[r.jsx("div",{className:"flex w-full justify-end p-2",children:r.jsx(a.Z,{inputPlaceholder:"Pesquisar por nome",handleSearch:"broker"===t?getBroker:getAdvisor,filterData:y,setFilterData:P,activeModal:b,setActiveModal:N})}),r.jsx("div",{className:"flex-1",children:(0,r.jsxs)("table",{className:"w-full relative min-h-20",children:[r.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,r.jsxs)("tr",{className:"w-full",children:[r.jsx("th",{className:"py-2 max-w-[350px] text-center",children:r.jsx("p",{className:"font-bold text-sm ",children:"Nome"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Valor"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Data e hor\xe1rio"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Valor Captado"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Perfil"})}),r.jsx("th",{className:""})]})}),r.jsx(r.Fragment,{children:r.jsx("tbody",{className:"w-full",children:h?(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(l.j,{height:"25px"})})]}):q?.map((e,t)=>r.jsxs("tr",{children:[r.jsx("td",{className:"max-w-[350px]",children:r.jsx("p",{className:"text-sm text-center py-1",children:e.name})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:p.Z(e.totalContractAmount||0)})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:u.p4(e.document).length<=11?u.VL(e.document):u.PK(e.document)})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:m().utc(e.createdAt).format("DD/MM/YYYY [\xe0s] HH:mm")})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:p.Z(e.totalCaptured||0)})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:"Investidor"})})]},t))})})]})}),r.jsx(n.Z,{lastPage:_.lastPage,page:f,perPage:_.perPage,setPage:v,totalItems:String(_.total)})]})]})})]})}},73294:(e,t,s)=>{"use strict";function formatValue(e){return("number"==typeof e?e:0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}function cleanValue(e){let t=e.toLocaleString("pt-BR",{style:"currency",currency:"BRL"});return t.replace(/R\$\s?/,"")}s.d(t,{A:()=>cleanValue,Z:()=>formatValue})},62786:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=s(17536);let a=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\home\ranking\[id]\page.tsx`),{__esModule:i,$$typeof:n}=a,o=a.default,l=o}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[4103,6426,4731,8813,5081,8394,7207,278,7669,8109],()=>__webpack_exec__(79720));module.exports=s})();