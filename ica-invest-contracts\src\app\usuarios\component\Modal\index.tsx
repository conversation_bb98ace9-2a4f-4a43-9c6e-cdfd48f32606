import Contact from "@/models/contract";
import { cepMask, clearLetters, cpfMask, phoneMask } from "@/utils/masks";
import UserData from "./compose/UserData";
import { useState } from "react";
import UserContracts from "./compose/UserContracts";
import UserDataBank from "./compose/UserDataBank";
import api from "@/core/api";
import { UserProfile } from "@/models/user";
import { toast } from "react-toastify";
import { Investor } from "../../types";
import { getUserProfile } from "@/functions/getUserData";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import CardUpgradeOpt from "./compose/CardUpgradeOpt";

interface IProps {
  setModal: (d: any) => void;
  investor: Investor | any;
}

export default function Modal({ setModal, investor }: IProps) {
  const router = useRouter();
  const [newInvestor, setNewInvestor] = useState<Investor>(investor);
  const [step, setStep] = useState(0);
  const [edit, setEdit] = useState(false);
  const [loadingEdit, setLoadingEdit] = useState(false);
  const [openAlterarContrato, setOpenAlterarContrato] = useState(false);

  const userProfile = getUserProfile();

  const returnData = () => {
    switch (step) {
      case 0:
        return (
          <UserData
            investor={newInvestor}
            setInvestor={setNewInvestor}
            edit={edit}
          />
        );
      case 1:
        return (
          <UserDataBank
            investor={newInvestor}
            edit={edit && userProfile.name === "superadmin"}
            setInvestor={setNewInvestor}
          />
        );
      case 2:
        return openAlterarContrato ? (
          upgradeContractOptions()
        ) : (
          <UserContracts investor={newInvestor} />
        );
    }
  };

  const canEditContract = ["superadmin", "admin", "broker"];
  const canUpgradeContract = ["superadmin", "admin", "broker", "advisor"];

  const editData = () => {
    if (JSON.stringify(newInvestor) === JSON.stringify(investor)) {
      return "owner";
    }
    if (
      newInvestor.zipCode === investor.zipCode &&
      newInvestor.address === investor.address &&
      newInvestor.neighborhood === investor.neighborhood &&
      newInvestor.addressNumber === investor.addressNumber &&
      newInvestor.city === investor.city &&
      newInvestor.state === investor.state &&
      newInvestor.complement === investor.complement
    ) {
      return "address";
    }
  };

  const editBank = () => {
    if (
      newInvestor.bank === investor.bank &&
      newInvestor.accountNumber === investor.accountNumber &&
      newInvestor.branch === investor.branch
    ) {
      return "bank";
    }
  };

  const editUser = () => {
    if (editData() === "owner") {
      return setEdit(false);
    }

    setLoadingEdit(true);
    const payload = {
      owner:
        editData() !== "owner"
          ? {
              name:
                newInvestor.name !== investor.name
                  ? newInvestor.name
                  : undefined,
              document:
                newInvestor.document !== investor.document
                  ? cpfMask(newInvestor.document)
                  : undefined,
              email:
                newInvestor.email !== investor.email
                  ? newInvestor.email
                  : undefined,
              phone:
                newInvestor.phone !== investor.phone
                  ? `+55${clearLetters(newInvestor.phone)}`
                  : undefined,
              rg: newInvestor.rg !== investor.rg ? newInvestor.rg : undefined,
            }
          : undefined,
      bank:
        editBank() !== "bank" && userProfile.name === "superadmin"
          ? {
              bank: newInvestor.bank,
              accountNumber: newInvestor.accountNumber,
              branch: newInvestor.branch,
            }
          : undefined,
      address:
        editData() !== "address"
          ? {
              cep: cepMask(newInvestor.zipCode),
              street: newInvestor.address,
              neighborhood: newInvestor.neighborhood,
              number: newInvestor.addressNumber,
              city: newInvestor.city,
              state: newInvestor.state,
              complement: newInvestor.complement,
            }
          : undefined,
      ownerRoleRelationId: newInvestor.id,
    };
    api
      .put(`/${userProfile.name}/investor`, payload)
      .then((resp) => {
        toast.success("Dados alterados com sucesso!");
        setEdit(false);
        setModal(false);
        window.location.reload();
      })
      .catch((error) => {
        toast.error(
          error.response.data.message ||
            "Não foi possivel alterar os dados do investidor"
        );
      })
      .finally(() => setLoadingEdit(false));
  };

  const handleRentabilityClick = () => {
    router.push(
      `/contratos/alterar?tipo=rentabilidade&investorId=${investor.id}`
    );
  };

  const handleModalityClick = () => {
    router.push(`/contratos/alterar?tipo=modalidade&investorId=${investor.id}`);
  };

  // Verificar se há contratos ativos
  const hasActiveContracts = () => {
    if (!newInvestor?.contracts || newInvestor.contracts.length === 0) {
      return false;
    }

    return newInvestor.contracts.some(contract => {
      const status = contract.contractStatus?.toUpperCase();
      return status === 'ACTIVE' || status === 'ATIVO';
    });
  };

  const upgradeContractOptions = () => {
    return (
      <>
        <CardUpgradeOpt
          title="Mudança de Rentabilidade"
          value="Clique aqui para mudar a rentabilidade do contrato do investidor"
          onClick={handleRentabilityClick}
        />
        <CardUpgradeOpt
          title="Mudança de Modalidade"
          value="Clique aqui para mudar a modalidade do contrato do investidor"
          onClick={handleModalityClick}
        />
      </>
    );
  };
  return (
    <div className="z-10 fixed top-0 left-0 w-screen h-screen bg-[#1c1c1c71]">
      <div
        className={`z-20 w-full ${
          step === 2 && !openAlterarContrato ? "md:w-6/12" : "md:w-5/12"
        } bg-[#1C1C1C] min-h-screen fixed top-0 right-0 border-l border-t border-[#FF9900] p-10 text-white overflow-auto h-full flex flex-col`}
      >
        <div className="flex items-center">
          <div className="w-10 h-10 bg-orange-linear rounded-full mr-5"></div>
          <div className="gap-y-1 flex flex-col">
            <p className="font-bold text-xs">{newInvestor?.name}</p>
            <p className="text-xs">Investidor</p>
          </div>
        </div>
        <div className="flex gap-x-2 mt-5">
          <div
            className={`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${
              step === 0 ? "bg-zinc-800 text-[#FF9900]" : ""
            }`}
            onClick={() => setStep(0)}
          >
            <p className="md:text-sm text-xs">Dados Pessoais</p>
          </div>
          <div
            className={`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${
              step === 1 ? "bg-zinc-800 text-[#FF9900]" : ""
            }`}
            onClick={() => setStep(1)}
          >
            <p className="md:text-sm text-xs">Dados Bancários</p>
          </div>
          <div
            className={`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${
              step === 2 ? "bg-zinc-800 text-[#FF9900]" : ""
            }`}
            onClick={() => setStep(2)}
          >
            <p className="md:text-sm text-xs">Contratos</p>
          </div>
        </div>
        <div className="w-full flex-1">{returnData()}</div>
        <div className="md:w-9/12 w-full flex flex-col md:flex-row justify-start gap-4 mt-5">
          {(step === 0 || (step === 1 && userProfile.name === "superadmin")) &&
            canEditContract.includes(userProfile.name) && (
              <Button
                onClick={() => {
                  if (edit) {
                    editUser();
                  } else {
                    setEdit(true);
                  }
                }}
              >
                {edit
                  ? editData() === "owner"
                    ? "Cancelar edição"
                    : "Confirmar edição"
                  : "Editar dados"}
              </Button>
            )}
          {step === 2 && canUpgradeContract.includes(userProfile.name) && hasActiveContracts() ? (
            <Button
              variant="default"
              onClick={() => setOpenAlterarContrato(true)}
            >
              Alterar Contrato
            </Button>
          ) : null}
          <Button
            variant="secondary"
            onClick={(_) =>
              openAlterarContrato
                ? setOpenAlterarContrato(false)
                : setModal(false)
            }
          >
            Fechar
          </Button>
        </div>
      </div>
    </div>
  );
}
