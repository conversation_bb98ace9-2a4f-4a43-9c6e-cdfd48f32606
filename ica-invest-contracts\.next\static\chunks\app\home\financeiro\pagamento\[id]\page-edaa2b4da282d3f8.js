(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7048],{8364:function(e,a,t){Promise.resolve().then(t.bind(t,8688))},8688:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return Payment}});var l=t(7437),n=t(3877),s=t(8637),r=t(6691),d=t.n(r),o=t(7120),c=t(2265),i=t(2875),x=t(4033),m=t(3862),u=t(2067),p=t.n(u),h=t(5968),v=t(8647),j=t(3014),b=t(4568),N=t(8689),f=t(6654),y=t(5044),g=t(365),w=t(5838),D=t(6121),F=t(4346),P=t(6041),C=t(3256);function Payment(e){let{params:a}=e,[t,r]=(0,c.useState)(""),[u,V]=(0,c.useState)("e"),[Z,L]=(0,c.useState)(!1),[R,k]=(0,c.useState)(!1),[A,S]=(0,c.useState)(!0),[B,I]=(0,c.useState)(),[_,E]=(0,c.useState)(),U=(0,x.useRouter)(),O=(0,C.e)(),DataValues=e=>{let{label:a,value:t}=e;return(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-bold text-sm",children:a}),(0,l.jsx)(N.Z,{maxWidth:"200px",minWidth:"80px",loading:A,height:"25px",children:(0,l.jsx)("p",{className:" text-md",children:t})})]})},getPaymentData=()=>{j.Am.info("Buscando dados do pagamento!",{toastId:"search",autoClose:!1}),b.Z.get("/income-payment-scheduled/".concat(a.id)).then(e=>{var a;E(e.data),(null===(a=e.data)||void 0===a?void 0:a.status)==="PAID"?V("p"):V("e"),j.Am.dismiss("search")}).catch(e=>{j.Am.error("Tivemos um erro ao buscar os dados do agendamento!",{toastId:"agendamento"})}).finally(()=>S(!1))};(0,c.useEffect)(()=>{getPaymentData()},[]);let W=(0,c.useCallback)(e=>{I(e);let a=URL.createObjectURL(e[0]);r(a)},[]),{getRootProps:q,getInputProps:z}=(0,v.uI)({onDrop:W,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&j.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{r("")}});return(0,l.jsxs)("div",{className:"".concat(Z?"fixed w-full":"relative"),children:[(0,l.jsx)(n.Z,{}),(0,l.jsx)(s.Z,{children:(0,l.jsxs)("div",{className:"w-full text-white",children:[(0,l.jsxs)("div",{className:"w-full text-white flex md:flex-row flex-col flex-wrap gap-8 justify-start",children:[(0,l.jsxs)("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[(0,l.jsx)("p",{className:"my-4 font-bold text-xl",children:"Valor a ser Pago"}),(0,l.jsx)("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:(0,l.jsx)("div",{className:"flex w-full items-center justify-center py-3",children:(0,l.jsx)(N.Z,{loading:A,height:"25px",width:"100px",children:(0,l.jsx)("p",{className:"font-bold text-xl text-[#FF9900]",children:Number((null==_?void 0:_.paymentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})})})]}),(0,l.jsxs)("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center",children:[(0,l.jsx)("p",{className:"my-4 font-bold text-xl",children:"Pagamento"}),(0,l.jsx)("div",{className:"w-full bg-[#1C1C1C] p-4 rounded-xl border border-[#FF9900]",children:(0,l.jsx)("div",{className:"flex w-full items-center justify-center py-3",children:(0,l.jsx)(N.Z,{loading:A,height:"25px",width:"100px",children:(0,l.jsxs)("p",{className:"font-bold text-xl text-[#FF9900]",children:["Dia ",(null==_?void 0:_.paymentDate)?p()(null==_?void 0:_.paymentDate).format("DD"):"00"]})})})})]}),"p"===u?(0,l.jsxs)("div",{onClick:()=>{(null==_?void 0:_.investorPayment.payment)&&window.open(null==_?void 0:_.investorPayment.payment.file,"_blank")},className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center flex flex-col items-center justify-center cursor-pointer",children:[(0,l.jsx)("p",{className:"mb-5 font-bold text-lg select-none",children:"Ultimo Comprovante"}),(0,l.jsx)(y.Z,{width:40,color:"#fff"})]}):(0,l.jsxs)("div",{className:"min-w-52 mb-5 md:mb-0 bg-orange-linear rounded-t-xl rounded-b-2xl text-center flex flex-col items-center justify-center cursor-pointer",...q(),children:[(0,l.jsx)("p",{className:"mb-5 font-bold text-lg mx-3 select-none",children:t?"Comprovante anexado":"Anexar Comprovante"}),(0,l.jsx)("input",{type:"text",...z()}),t?(0,l.jsx)(g.Z,{width:40,color:"#fff"}):(0,l.jsx)(d(),{className:"select-none",src:o.Z,width:30,alt:""})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"my-5",children:"p"===u?(0,l.jsx)("div",{className:"bg-[#1ef97d31] w-28 border border-[#1EF97C] rounded-lg",children:(0,l.jsx)("p",{className:"text-center text-[#1EF97C] p-1",children:"Pago"})}):(0,l.jsx)("div",{className:"bg-[#ffb3382a] w-28 border border-[#FFB238] rounded-lg",children:(0,l.jsx)("p",{className:"text-center text-[#FFB238] p-1",children:"Pendente"})})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:"Dados Pessoais"}),(0,l.jsx)("div",{className:"mb-3 mt-2",children:(0,l.jsx)(DataValues,{label:"Nome",value:(null==_?void 0:_.investorPayment.name)||"N\xe3o encontrado"})}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3",children:[(0,l.jsx)(DataValues,{label:"E-mail",value:(null==_?void 0:_.investorPayment.email)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"CPF",value:(0,h.VL)((null==_?void 0:_.investorPayment.document)||"N\xe3o encontrado")}),null==_?void 0:_.advisors.map((e,a)=>(0,l.jsx)(DataValues,{label:"Consultor ".concat(a+1),value:e.name||"N\xe3o encontrado"},a))]}),(0,l.jsx)("div",{children:(0,l.jsx)(DataValues,{label:"Broker",value:(null==_?void 0:_.broker)||"N\xe3o encontrado"})})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es sobre o Investimento"}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2 items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-bold text-sm",children:"Valor Investido"}),(0,l.jsx)(N.Z,{maxWidth:"200px",minWidth:"80px",loading:A,height:"25px",children:(0,l.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,l.jsx)("p",{className:" text-md",children:Number((null==_?void 0:_.investmentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsx)("div",{className:"cursor-pointer",children:(0,l.jsx)(d(),{alt:"",src:F.Z,width:15})})]})})]}),(0,l.jsx)("div",{className:"flex bg-orange-linear px-3 py-2 rounded-lg items-center cursor-pointer ml-[-15px]",onClick:()=>{L(!0)},children:(0,l.jsx)("p",{className:"font-bold text-sm",children:"Ver mais"})}),(null==_?void 0:_.addendsProRata)&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(DataValues,{label:"Pro Rata",value:Number((null==_?void 0:_.addendsProRataValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-bold text-sm",children:"Valor Total"}),(0,l.jsx)(N.Z,{maxWidth:"200px",minWidth:"80px",loading:A,height:"25px",children:(0,l.jsx)("p",{className:"text-md text-[#FF9900]",children:(Number((null==_?void 0:_.investmentValue)||0)+((null==_?void 0:_.addendsProRataValue)||0)).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]}),(0,l.jsx)(DataValues,{label:"Porcentagem",value:"".concat((null==_?void 0:_.investmentYield)||"N\xe3o encontrado","%")}),(0,l.jsx)(DataValues,{label:"Rentabilidade",value:Number((null==_?void 0:_.paymentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:"Informa\xe7\xf5es Banc\xe1rias"}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-8 mb-3 mt-2",children:[(0,l.jsx)(DataValues,{label:"Banco",value:(null==_?void 0:_.investorPayment.account.bank)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"Ag\xeancia",value:(null==_?void 0:_.investorPayment.account.agency)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"Conta",value:(null==_?void 0:_.investorPayment.account.account)||"N\xe3o encontrado"}),(0,l.jsx)(DataValues,{label:"Chave PIX",value:(null==_?void 0:_.investorPayment.account.pixKey)||"N\xe3o encontrado"})]})]}),(0,l.jsxs)("div",{className:"mb-6 w-[30%]",children:[(0,l.jsx)("div",{children:(0,l.jsx)("p",{className:"text-xl font-bold mb-3",children:"Anexos"})}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-end mb-20",children:[(null==_?void 0:_.attachments.contract)&&(0,l.jsx)("div",{children:(0,l.jsx)(P.Z,{file:null==_?void 0:_.attachments.contract,title:"Contrato"})}),(null==_?void 0:_.attachments.residenceProof)&&(0,l.jsx)("div",{children:(0,l.jsx)(P.Z,{file:null==_?void 0:_.attachments.residenceProof,title:"Comprovante de resid\xeancia"})}),(null==_?void 0:_.attachments.rg)&&(0,l.jsx)("div",{children:(0,l.jsx)(P.Z,{file:null==_?void 0:_.attachments.rg,title:"RG"})})]})]})]}),(0,l.jsxs)("div",{className:"w-full flex flex-wrap gap-8 justify-end mb-4",children:["p"!==u&&(0,l.jsx)("div",{children:(0,l.jsx)(i.Z,{label:"Aprovar pagamento",className:"bg-orange-linear",loading:R,handleSubmit:()=>{if(k(!0),!t)return j.Am.warning("Para aprovar o pagamento \xe9 preciso anexar o comprovante!");let e=new FormData;B&&e.append("proof",B[0]),e.append("paymentId",(null==_?void 0:_.investorPayment.id)||""),j.Am.info("Aprovando o pagamento...",{toastId:"aprovando-pagamento"}),b.Z.post("/income-payment",e,{headers:{RoleId:O.roleId}}).then(e=>{j.Am.success("Pagamento aprovado!"),j.Am.dismiss("aprovando-pagamento"),U.back()}).catch(e=>{(0,f.Z)(e,"N\xe3o foi poss\xedvel aprovar o pagamento!")}).finally(()=>k(!1))}})}),(0,l.jsx)("div",{children:(0,l.jsx)(i.Z,{label:"Voltar",handleSubmit:()=>{U.back()},loading:!1})})]})]})]})}),(0,l.jsx)("div",{children:(0,l.jsx)(m.Z,{width:"7/12",openModal:Z,setOpenModal:L,children:(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"mb-5",children:[(0,l.jsx)("p",{className:"mb-2",children:"Dados do contrato principal."}),(0,l.jsxs)("table",{className:"border border-[#FF9900] w-full",children:[(0,l.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Rendimento"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),(0,l.jsxs)("tr",{className:"text-xs",children:[(0,l.jsx)("td",{className:"text-center px-4 py-2",children:Number((null==_?void 0:_.investmentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsxs)("td",{className:"text-center px-4 py-2",children:[null==_?void 0:_.investmentYield,"%"]}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:(0,D.Z)((null==_?void 0:_.contractStartDate)||"")}),(0,l.jsx)("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>window.open(null==_?void 0:_.attachments.contract,"_blanck"),children:"ver mais"})]})]})]}),(null==_?void 0:_.addendumData)&&(null==_?void 0:_.addendumData.length)>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)("p",{children:"Contratos aditivos adicionados no c\xe1lculo"})}),(0,l.jsxs)("table",{className:" border border-[#FF9900] ",children:[(0,l.jsxs)("tr",{className:"text-sm border-b bg-[#313131] border-b-[#FF9900]",children:[(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Valor total"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Data de Aporte"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Valor rentabilizado"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Dias rentabilizados"}),(0,l.jsx)("th",{className:"px-2 py-2 text-xs",children:"Contrato"})]}),null==_?void 0:_.addendumData.map(e=>(0,l.jsxs)("tr",{className:"text-xs",children:[(0,l.jsx)("td",{className:"text-center px-4 py-2",children:Number((null==e?void 0:e.investmentValue)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:(0,D.Z)(e.date)}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:e.totalAmount?Number((null==e?void 0:e.totalAmount)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"}):"-"}),(0,l.jsx)("td",{className:"text-center px-4 py-2",children:"".concat(e.validDays," dias")}),(null==e?void 0:e.contractUrl)?(0,l.jsx)("td",{className:"text-center px-4 py-2 text-blue-400 cursor-pointer",onClick:()=>window.open(e.contractUrl,"_blanck"),children:"ver mais"}):(0,l.jsx)("td",{className:"flex justify-center",children:(0,l.jsx)(w.Z,{className:"mt-1",color:"#FF9900",width:20})})]},e.id))]})]})]})})})]})}},2875:function(e,a,t){"use strict";t.d(a,{Z:function(){return Button}});var l=t(7437),n=t(8700);function Button(e){let{handleSubmit:a,loading:t,label:s,disabled:r,className:d,...o}=e;return(0,l.jsx)(n.z,{...o,onClick:a,loading:t,disabled:r,className:d,children:s})}}},function(e){e.O(0,[6990,7326,8276,5371,6946,3151,1327,2971,7864,1744],function(){return e(e.s=8364)}),_N_E=e.O()}]);