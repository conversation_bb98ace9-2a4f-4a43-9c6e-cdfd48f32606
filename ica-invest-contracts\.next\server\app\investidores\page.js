(()=>{var e={};e.id=8092,e.ids=[8092],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},16980:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>x,routeModule:()=>p,tree:()=>c});var r=t(73137),a=t(54647),i=t(4183),o=t.n(i),n=t(71775),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(s,l);let d=r.AppPageRouteModule,c=["",{children:["investidores",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69478)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\investidores\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\investidores\\page.tsx"],u="/investidores/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new d({definition:{kind:a.x.APP_PAGE,page:"/investidores/page",pathname:"/investidores",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},58187:(e,s,t)=>{Promise.resolve().then(t.bind(t,79410))},79410:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Investidores});var r=t(60080),a=t(97669),i=t(47956),o=t(9885),n=t(85814),l=t(34751),d=t(28168),c=t(1808),x=t(9317),u=t(7537),m=t(57048),p=t(96413),h=t(34692),j=t(74644),v=t(58470),f=t(24577),b=t(90682),g=t(24774);function Investidores(){let[e,s]=(0,o.useState)(!1),[t,N]=(0,o.useState)(),[w,y]=(0,o.useState)(),[_,P]=(0,o.useState)(),[q,C]=(0,o.useState)(),[k,I]=(0,o.useState)(!1),[S,A]=(0,o.useState)(!1),[Z,F]=(0,o.useState)("Todos"),[$,E]=(0,o.useState)(1),[M,K]=(0,o.useState)(0),[L,V]=(0,o.useState)(),[D,G]=(0,o.useState)(),[B,O]=(0,o.useState)(),[T,z]=(0,o.useState)("broker"),[J,R]=(0,o.useState)(),U=(0,b.e)();async function getAcessorsInvestors(){s(!0);try{let e=await n.Z.get("/wallets/broker/investors",{params:{adviserId:U.roleId}}),t=await n.Z.get("/wallets/broker/advisors",{params:{adviserId:U.roleId}}),r=e.data,a=t.data,i=[],o=[];r.map(e=>{i.push({...e,document:e.document.length<=11?(0,p.VL)(e.document):(0,p.PK)(e.document),type:"investidor"})}),a.map(e=>{o.push({...e,document:e.document.length<=11?(0,p.VL)(e.document):(0,p.PK)(e.document),type:"assessor"})}),N([...o,...i]),s(!1)}catch(e){l.Am.error("Erro ao buscar os assessores")}}let getInvestorData=e=>{l.Am.info("Buscando dados do investidor..."),n.Z.get(`/contract/${e.id}`).then(s=>{G({...s.data,...e}),I(!0)}).catch(e=>{l.Am.error("N\xe3o foi possivel buscar o investidor")})};(0,o.useEffect)(()=>{"advisor"===U.name?(s(!0),n.Z.get("/wallets/advisor/investors",{params:{adviserId:U.roleId}}).then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e.document.length<=11?(0,p.VL)(e.document):(0,p.PK)(e.document),type:"investidor"})}),N(s)}).catch(e=>{l.Am.error("Erro ao buscar os investidores")}).finally(()=>s(!1))):0===M?getAcessorsInvestors():1===M&&(s(!0),n.Z.get("/wallets/broker/investors-advisor",{params:{adviserId:U.roleId,advisorId:q?.id}}).then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e.document.length<=11?(0,p.VL)(e.document):(0,p.PK)(e.document),type:"investidor"})}),N(s)}).catch(e=>{l.Am.error("Erro ao buscar os investidores")}).finally(()=>s(!1)))},[M]);let getAdvisorData=e=>{l.Am.info("Buscando dados do assessor...",{toastId:"advisor"}),n.Z.get(`/wallets/advisor/one?advisorId=${e.id}`).then(e=>{l.Am.dismiss("advisor"),I(!0),R(e.data)}).catch(e=>{(0,f.Z)(e,"N\xe3o foi possivel buscar o investidor"),l.Am.dismiss("broadvisorker")})};return r.jsx("div",{children:(0,r.jsxs)(g.yo,{open:k,onOpenChange:I,children:[r.jsx(a.Z,{}),r.jsx(i.Z,{children:(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:r.jsx("h1",{className:"m-auto font-bold text-2xl",children:"Usu\xe1rios"})}),q&&r.jsx("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border text-white",children:(0,r.jsxs)("div",{className:"flex w-full",children:[r.jsx("div",{className:"w-[25px] h-[25px] bg-white rounded-full flex items-center justify-center",children:r.jsx("p",{className:"text-[#FF9900] text-xs font-bold",children:(e=>{let s=e.split(" ");return s.length>1?`${q?.name.split(" ")[0][0]}${q?.name.split(" ")[1][0]}`:`${q?.name.split(" ")[0][0]}${q?.name.split(" ")[0][1]}`})(q.name)})}),r.jsx("div",{className:"w-full",children:(0,r.jsxs)("div",{children:[r.jsx("p",{className:"ml-3 text-base",children:q?.name}),r.jsx("p",{className:"ml-3 text-sm",children:q?.type})]})}),r.jsx("div",{className:"cursor-pointer",onClick:()=>{K(0),C(void 0)},children:r.jsx(m.Z,{width:20})})]})}),(0,r.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] w-full text-white mt-10 overflow-x-auto rounded-b-md border border-[#FF9900]",children:[r.jsx("div",{className:"flex w-full justify-end p-2"}),(0,r.jsxs)("table",{className:"w-full relative min-h-20",children:[r.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,r.jsxs)("tr",{className:"w-full",children:[r.jsx("th",{className:"w-10",children:r.jsx("p",{className:"font-bold text-sm"})}),r.jsx("th",{className:"w-10",children:r.jsx("p",{className:"font-bold text-sm"})}),r.jsx("th",{className:"py-2",children:r.jsx("p",{className:"font-bold text-sm",children:"Nome"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"E-mail"})}),r.jsx("th",{children:r.jsx("p",{className:"font-bold text-sm",children:"Perfil"})})]})}),r.jsx(r.Fragment,{children:!1===e?r.jsx(r.Fragment,{children:t?.length>=1?r.jsx("tbody",{className:"w-full",children:t.map((e,s)=>(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[r.jsx("td",{className:"w-10",children:"assessor"===e.type&&r.jsx("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{K(1),C(e)},children:r.jsx(d.Z,{color:"#fff",width:20})})}),r.jsx("td",{className:"w-10",children:("investidor"===e.type||"assessor"===e.type)&&r.jsx("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{"investidor"===e.type?(z("investor"),getInvestorData(e)):"assessor"===e.type&&(z("advisor"),getAdvisorData(e))},children:r.jsx(c.Z,{color:"#fff",width:20})})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center py-1",children:e.name||"N\xe3o encontrado"})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:(0,p.p4)(e.document).length<=11?(0,p.VL)(e.document):(0,p.PK)(e.document)})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:e.email})}),r.jsx("td",{children:r.jsx("p",{className:"text-sm text-center",children:e.type})})]},s))}):r.jsx("tbody",{className:"w-full",children:r.jsx("tr",{children:r.jsx("td",{colSpan:6,className:"text-center py-4",children:"Nenhum dado encontrado"})})})}):(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[r.jsx("td",{className:"px-1",children:r.jsx(j.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(j.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(j.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(j.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(j.j,{height:"25px"})}),r.jsx("td",{className:"px-1",children:r.jsx(j.j,{height:"25px"})})]})})]}),r.jsx("div",{className:"w-full flex justify-end items-center pr-5 ",children:(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[r.jsx("p",{className:`p-1 bg-[#262626] rounded-md ${Number($)>1?"cursor-pointer":""} flex items-center`,onClick:({})=>{Number($)>1&&E($-1)},children:r.jsx(x.Z,{color:Number($)>1?"#fff":"#424242",width:20})}),r.jsx("p",{className:"font-bold bg-[#262626] rounded-md py-1 px-2",children:$}),r.jsx("p",{className:`p-1 bg-[#262626] rounded-md ${$<Number(L?.lastPage)?"cursor-pointer":""} flex items-center`,onClick:({})=>{$<Number(L?.lastPage)&&E($+1)},children:r.jsx(u.Z,{color:$<Number(L?.lastPage)?"#fff":"#424242",width:20})})]})})]}),r.jsx(g.ue,{className:"w-full md:w-[500px]",children:k?"investor"===T?r.jsx(h.Z,{investor:D,setModal:I}):J&&r.jsx(v.Z,{typeModal:T,broker:J,setModal:I}):r.jsx(r.Fragment,{})})]})})]})})}},69478:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>o,__esModule:()=>i,default:()=>l});var r=t(17536);let a=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\investidores\page.tsx`),{__esModule:i,$$typeof:o}=a,n=a.default,l=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[4103,6426,4731,8813,5081,8394,6558,1808,5459,7207,278,7669,6774,6440],()=>__webpack_exec__(16980));module.exports=t})();