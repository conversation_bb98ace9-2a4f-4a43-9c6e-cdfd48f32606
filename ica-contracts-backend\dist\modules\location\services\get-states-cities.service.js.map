{"version": 3, "file": "get-states-cities.service.js", "sourceRoot": "/", "sources": ["modules/location/services/get-states-cities.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,yFAAgF;AAChF,qCAAqC;AAK9B,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEU,eAAwC;QAAxC,oBAAe,GAAf,eAAe,CAAyB;IAC/C,CAAC;IAEJ,KAAK,CAAC,OAAO;QACX,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC7C,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzB,SAAS,EAAE,CAAC,QAAQ,CAAC;SACtB,CAAC,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5C,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAC5C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,CACrC,CAAC;YACF,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,MAAM,CAAC;YACpD,WAAW,IAAI,iBAAiB,CAAC;YACjC,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;IAChE,CAAC;CACF,CAAA;AAvBY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;qCACL,oBAAU;GAH1B,mBAAmB,CAuB/B", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { StateEntity } from 'src/shared/database/typeorm/entities/state.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IStatesCitiesResponse } from '../response/states-cities.response';\r\n\r\n@Injectable()\r\nexport class StatesCitiesService {\r\n  constructor(\r\n    @InjectRepository(StateEntity)\r\n    private stateRepository: Repository<StateEntity>,\r\n  ) {}\r\n\r\n  async perform(): Promise<IStatesCitiesResponse> {\r\n    const states = await this.stateRepository.find({\r\n      where: { isActive: true },\r\n      relations: ['cities'],\r\n    });\r\n    const totalStates = states.length;\r\n    let totalCities = 0;\r\n    const statesWithCities = states.map((state) => {\r\n      const totalCitiesInState = state.cities.filter(\r\n        (cities) => cities.isActive === true,\r\n      );\r\n      const totalCitiesActive = totalCitiesInState.length;\r\n      totalCities += totalCitiesActive;\r\n      return { state, totalCities };\r\n    });\r\n    return { states: statesWithCities, totalStates, totalCities };\r\n  }\r\n}\r\n"]}