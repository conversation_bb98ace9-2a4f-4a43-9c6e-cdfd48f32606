"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9891],{9198:function(e,t,r){r.d(t,{j:function(){return n}});var s=r(2996),i=r(300),n=new class extends s.l{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!i.sk&&window.addEventListener){let listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){let t=this.#e!==e;t&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}}},7987:function(e,t,r){r.d(t,{Vr:function(){return s}});var defaultScheduler=e=>setTimeout(e,0),s=function(){let e=[],t=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()},r=defaultScheduler,schedule=s=>{t?e.push(s):r(()=>{notifyFn(s)})},flush=()=>{let t=e;e=[],t.length&&r(()=>{batchNotifyFn(()=>{t.forEach(e=>{notifyFn(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||flush()}return r},batchCalls:e=>(...t)=>{schedule(()=>{e(...t)})},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e},setScheduler:e=>{r=e}}}()},436:function(e,t,r){r.d(t,{N:function(){return n}});var s=r(2996),i=r(300),n=new class extends s.l{#s=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!i.sk&&window.addEventListener){let onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){let t=this.#s!==e;t&&(this.#s=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#s}}},3002:function(e,t,r){r.d(t,{A:function(){return o},z:function(){return fetchState}});var s=r(300),i=r(7987),n=r(1640),a=r(9024),o=class extends a.F{#i;#n;#a;#o;#u;#c;#h;constructor(e){super(),this.#h=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#o=e.client,this.#a=this.#o.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#i=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#i,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#a.remove(this)}setData(e,t){let r=(0,s.oE)(this.state.data,e,this.options);return this.#l({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#l({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#u?.promise;return this.#u?.cancel(e),t?t.then(s.ZT).catch(s.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#i)}isActive(){return this.observers.some(e=>!1!==(0,s.Nc)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===s.CN||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===(0,s.KC)(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!(0,s.Kp)(this.state.dataUpdatedAt,e))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#a.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#u&&(this.#h?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#a.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#l({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#h=!0,r.signal)})},fetchFn=()=>{let e=(0,s.cG)(this.options,t),r=(()=>{let e={client:this.#o,queryKey:this.queryKey,meta:this.meta};return addSignalProperty(e),e})();return(this.#h=!1,this.options.persister)?this.options.persister(e,r,this):e(r)},i=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#o,state:this.state,fetchFn};return addSignalProperty(e),e})();this.options.behavior?.onFetch(i,this),this.#n=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#l({type:"fetch",meta:i.fetchOptions?.meta});let onError=e=>{(0,n.DV)(e)&&e.silent||this.#l({type:"error",error:e}),(0,n.DV)(e)||(this.#a.config.onError?.(e,this),this.#a.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#u=(0,n.Mz)({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){onError(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){onError(e);return}this.#a.config.onSuccess?.(e,this),this.#a.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError,onFail:(e,t)=>{this.#l({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#l({type:"pause"})},onContinue:()=>{this.#l({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#u.start()}#l(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...fetchState(t.data,this.options),fetchMeta:e.meta??null};case"success":return this.#n=void 0,{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,n.DV)(r)&&r.revert&&this.#n)return{...this.#n,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.Vr.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#a.notify({query:this,type:"updated",action:e})})}};function fetchState(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,n.Kw)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},9024:function(e,t,r){r.d(t,{F:function(){return i}});var s=r(300),i=class{#d;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.PN)(this.gcTime)&&(this.#d=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(s.sk?1/0:3e5))}clearGcTimeout(){this.#d&&(clearTimeout(this.#d),this.#d=void 0)}}},1640:function(e,t,r){r.d(t,{DV:function(){return isCancelledError},Kw:function(){return canFetch},Mz:function(){return createRetryer}});var s=r(9198),i=r(436),n=r(8684),a=r(300);function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return(e??"online")!=="online"||i.N.isOnline()}var o=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof o}function createRetryer(e){let t,r=!1,u=0,c=!1,h=(0,n.O)(),canContinue=()=>s.j.isFocused()&&("always"===e.networkMode||i.N.isOnline())&&e.canRun(),canStart=()=>canFetch(e.networkMode)&&e.canRun(),resolve=r=>{c||(c=!0,e.onSuccess?.(r),t?.(),h.resolve(r))},reject=r=>{c||(c=!0,e.onError?.(r),t?.(),h.reject(r))},pause=()=>new Promise(r=>{t=e=>{(c||canContinue())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,c||e.onContinue?.()}),run=()=>{let t;if(c)return;let s=0===u?e.initialPromise:void 0;try{t=s??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(resolve).catch(t=>{if(c)return;let s=e.retry??(a.sk?0:3),i=e.retryDelay??defaultRetryDelay,n="function"==typeof i?i(u,t):i,o=!0===s||"number"==typeof s&&u<s||"function"==typeof s&&s(u,t);if(r||!o){reject(t);return}u++,e.onFail?.(u,t),(0,a._v)(n).then(()=>canContinue()?void 0:pause()).then(()=>{r?reject(t):run()})})};return{promise:h,cancel:t=>{c||(reject(new o(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart,start:()=>(canStart()?run():pause().then(run),h)}}},2996:function(e,t,r){r.d(t,{l:function(){return s}});var s=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},8684:function(e,t,r){r.d(t,{O:function(){return pendingThenable}});function pendingThenable(){let e,t;let r=new Promise((r,s)=>{e=r,t=s});function finalize(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{finalize({status:"fulfilled",value:t}),e(t)},r.reject=e=>{finalize({status:"rejected",reason:e}),t(e)},r}},300:function(e,t,r){r.d(t,{CN:function(){return i},Ht:function(){return addToStart},KC:function(){return resolveStaleTime},Kp:function(){return timeUntilStale},L3:function(){return shouldThrowError},Nc:function(){return resolveEnabled},PN:function(){return isValidTimeout},Rm:function(){return hashQueryKeyByOptions},SE:function(){return functionalUpdate},VS:function(){return shallowEqualObjects},VX:function(){return addToEnd},X7:function(){return matchMutation},Ym:function(){return hashKey},ZT:function(){return noop},_v:function(){return sleep},_x:function(){return matchQuery},cG:function(){return ensureQueryFn},oE:function(){return replaceData},sk:function(){return s},to:function(){return partialMatchKey}});var s="undefined"==typeof window||"Deno"in globalThis;function noop(){}function functionalUpdate(e,t){return"function"==typeof e?e(t):e}function isValidTimeout(e){return"number"==typeof e&&e>=0&&e!==1/0}function timeUntilStale(e,t){return Math.max(e+(t||0)-Date.now(),0)}function resolveStaleTime(e,t){return"function"==typeof e?e(t):e}function resolveEnabled(e,t){return"function"==typeof e?e(t):e}function matchQuery(e,t){let{type:r="all",exact:s,fetchStatus:i,predicate:n,queryKey:a,stale:o}=e;if(a){if(s){if(t.queryHash!==hashQueryKeyByOptions(a,t.options))return!1}else if(!partialMatchKey(t.queryKey,a))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!i||i===t.state.fetchStatus)&&(!n||!!n(t))}function matchMutation(e,t){let{exact:r,status:s,predicate:i,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(r){if(hashKey(t.options.mutationKey)!==hashKey(n))return!1}else if(!partialMatchKey(t.options.mutationKey,n))return!1}return(!s||t.state.status===s)&&(!i||!!i(t))}function hashQueryKeyByOptions(e,t){let r=t?.queryKeyHashFn||hashKey;return r(e)}function hashKey(e){return JSON.stringify(e,(e,t)=>isPlainObject(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function partialMatchKey(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>partialMatchKey(e[r],t[r]))}function shallowEqualObjects(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!(hasObjectPrototype(r)&&r.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function sleep(e){return new Promise(t=>{setTimeout(t,e)})}function replaceData(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?function replaceEqualDeep(e,t){if(e===t)return e;let r=isPlainArray(e)&&isPlainArray(t);if(r||isPlainObject(e)&&isPlainObject(t)){let s=r?e:Object.keys(e),i=s.length,n=r?t:Object.keys(t),a=n.length,o=r?[]:{},u=new Set(s),c=0;for(let s=0;s<a;s++){let i=r?s:n[s];(!r&&u.has(i)||r)&&void 0===e[i]&&void 0===t[i]?(o[i]=void 0,c++):(o[i]=replaceEqualDeep(e[i],t[i]),o[i]===e[i]&&void 0!==e[i]&&c++)}return i===a&&c===i?e:o}return t}(e,t):t}function addToEnd(e,t,r=0){let s=[...e,t];return r&&s.length>r?s.slice(1):s}function addToStart(e,t,r=0){let s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var i=Symbol();function ensureQueryFn(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==i?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}function shouldThrowError(e,t){return"function"==typeof e?e(...t):!!e}},8038:function(e,t,r){r.d(t,{NL:function(){return useQueryClient},aH:function(){return QueryClientProvider}});var s=r(2265),i=r(7437),n=s.createContext(void 0),useQueryClient=e=>{let t=s.useContext(n);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},QueryClientProvider=({client:e,children:t})=>(s.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(n.Provider,{value:e,children:t}))},9891:function(e,t,r){let s;r.d(t,{a:function(){return useQuery}});var i=r(9198),n=r(7987),a=r(3002),o=r(2996),u=r(8684),c=r(300),h=class extends o.l{constructor(e,t){super(),this.options=t,this.#o=e,this.#f=null,this.#p=(0,u.O)(),this.options.experimental_prefetchInRender||this.#p.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#o;#y=void 0;#v=void 0;#b=void 0;#m;#R;#p;#f;#g;#S;#O;#C;#F;#T;#E=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#y.addObserver(this),shouldFetchOnMount(this.#y,this.options)?this.#Q():this.updateResult(),this.#w())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#y,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#y,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#P(),this.#I(),this.#y.removeObserver(this)}setOptions(e){let t=this.options,r=this.#y;if(this.options=this.#o.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,c.Nc)(this.options.enabled,this.#y))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#j(),this.#y.setOptions(this.options),t._defaulted&&!(0,c.VS)(this.options,t)&&this.#o.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#y,observer:this});let s=this.hasListeners();s&&shouldFetchOptionally(this.#y,r,this.options,t)&&this.#Q(),this.updateResult(),s&&(this.#y!==r||(0,c.Nc)(this.options.enabled,this.#y)!==(0,c.Nc)(t.enabled,this.#y)||(0,c.KC)(this.options.staleTime,this.#y)!==(0,c.KC)(t.staleTime,this.#y))&&this.#D();let i=this.#U();s&&(this.#y!==r||(0,c.Nc)(this.options.enabled,this.#y)!==(0,c.Nc)(t.enabled,this.#y)||i!==this.#T)&&this.#q(i)}getOptimisticResult(e){let t=this.#o.getQueryCache().build(this.#o,e),r=this.createResult(t,e);return(0,c.VS)(this.getCurrentResult(),r)||(this.#b=r,this.#R=this.options,this.#m=this.#y.state),r}getCurrentResult(){return this.#b}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#E.add(e)}getCurrentQuery(){return this.#y}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#o.defaultQueryOptions(e),r=this.#o.getQueryCache().build(this.#o,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#Q({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#b))}#Q(e){this.#j();let t=this.#y.fetch(this.options,e);return e?.throwOnError||(t=t.catch(c.ZT)),t}#D(){this.#P();let e=(0,c.KC)(this.options.staleTime,this.#y);if(c.sk||this.#b.isStale||!(0,c.PN)(e))return;let t=(0,c.Kp)(this.#b.dataUpdatedAt,e);this.#C=setTimeout(()=>{this.#b.isStale||this.updateResult()},t+1)}#U(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#y):this.options.refetchInterval)??!1}#q(e){this.#I(),this.#T=e,!c.sk&&!1!==(0,c.Nc)(this.options.enabled,this.#y)&&(0,c.PN)(this.#T)&&0!==this.#T&&(this.#F=setInterval(()=>{(this.options.refetchIntervalInBackground||i.j.isFocused())&&this.#Q()},this.#T))}#w(){this.#D(),this.#q(this.#U())}#P(){this.#C&&(clearTimeout(this.#C),this.#C=void 0)}#I(){this.#F&&(clearInterval(this.#F),this.#F=void 0)}createResult(e,t){let r;let s=this.#y,i=this.options,n=this.#b,o=this.#m,h=this.#R,l=e!==s,d=l?e.state:this.#v,{state:f}=e,p={...f},y=!1;if(t._optimisticResults){let r=this.hasListeners(),n=!r&&shouldFetchOnMount(e,t),o=r&&shouldFetchOptionally(e,s,t,i);(n||o)&&(p={...p,...(0,a.z)(f.data,e.options)}),"isRestoring"===t._optimisticResults&&(p.fetchStatus="idle")}let{error:v,errorUpdatedAt:b,status:m}=p;r=p.data;let R=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===m){let e;n?.isPlaceholderData&&t.placeholderData===h?.placeholderData?(e=n.data,R=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#O?.state.data,this.#O):t.placeholderData,void 0!==e&&(m="success",r=(0,c.oE)(n?.data,e,t),y=!0)}if(t.select&&void 0!==r&&!R){if(n&&r===o?.data&&t.select===this.#g)r=this.#S;else try{this.#g=t.select,r=t.select(r),r=(0,c.oE)(n?.data,r,t),this.#S=r,this.#f=null}catch(e){this.#f=e}}this.#f&&(v=this.#f,r=this.#S,b=Date.now(),m="error");let g="fetching"===p.fetchStatus,S="pending"===m,O="error"===m,C=S&&g,F=void 0!==r,T={status:m,fetchStatus:p.fetchStatus,isPending:S,isSuccess:"success"===m,isError:O,isInitialLoading:C,isLoading:C,data:r,dataUpdatedAt:p.dataUpdatedAt,error:v,errorUpdatedAt:b,failureCount:p.fetchFailureCount,failureReason:p.fetchFailureReason,errorUpdateCount:p.errorUpdateCount,isFetched:p.dataUpdateCount>0||p.errorUpdateCount>0,isFetchedAfterMount:p.dataUpdateCount>d.dataUpdateCount||p.errorUpdateCount>d.errorUpdateCount,isFetching:g,isRefetching:g&&!S,isLoadingError:O&&!F,isPaused:"paused"===p.fetchStatus,isPlaceholderData:y,isRefetchError:O&&F,isStale:isStale(e,t),refetch:this.refetch,promise:this.#p};if(this.options.experimental_prefetchInRender){let finalizeThenableIfPossible=e=>{"error"===T.status?e.reject(T.error):void 0!==T.data&&e.resolve(T.data)},recreateThenable=()=>{let e=this.#p=T.promise=(0,u.O)();finalizeThenableIfPossible(e)},t=this.#p;switch(t.status){case"pending":e.queryHash===s.queryHash&&finalizeThenableIfPossible(t);break;case"fulfilled":("error"===T.status||T.data!==t.value)&&recreateThenable();break;case"rejected":("error"!==T.status||T.error!==t.reason)&&recreateThenable()}}return T}updateResult(){let e=this.#b,t=this.createResult(this.#y,this.options);this.#m=this.#y.state,this.#R=this.options,void 0!==this.#m.data&&(this.#O=this.#y),(0,c.VS)(t,e)||(this.#b=t,this.#k({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#E.size)return!0;let s=new Set(r??this.#E);return this.options.throwOnError&&s.add("error"),Object.keys(this.#b).some(t=>{let r=this.#b[t]!==e[t];return r&&s.has(t)})})()}))}#j(){let e=this.#o.getQueryCache().build(this.#o,this.options);if(e===this.#y)return;let t=this.#y;this.#y=e,this.#v=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#w()}#k(e){n.Vr.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#b)}),this.#o.getQueryCache().notify({query:this.#y,type:"observerResultsUpdated"})})}};function shouldFetchOnMount(e,t){return!1!==(0,c.Nc)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)||void 0!==e.state.data&&shouldFetchOn(e,t,t.refetchOnMount)}function shouldFetchOn(e,t,r){if(!1!==(0,c.Nc)(t.enabled,e)&&"static"!==(0,c.KC)(t.staleTime,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&isStale(e,t)}return!1}function shouldFetchOptionally(e,t,r,s){return(e!==t||!1===(0,c.Nc)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&isStale(e,r)}function isStale(e,t){return!1!==(0,c.Nc)(t.enabled,e)&&e.isStaleByTime((0,c.KC)(t.staleTime,e))}var l=r(2265),d=r(8038);r(7437);var f=l.createContext((s=!1,{clearReset:()=>{s=!1},reset:()=>{s=!0},isReset:()=>s})),useQueryErrorResetBoundary=()=>l.useContext(f),ensurePreventErrorBoundaryRetry=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},useClearResetErrorBoundary=e=>{l.useEffect(()=>{e.clearReset()},[e])},getHasError=({result:e,errorResetBoundary:t,throwOnError:r,query:s,suspense:i})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(i&&void 0===e.data||(0,c.L3)(r,[e.error,s])),p=l.createContext(!1),useIsRestoring=()=>l.useContext(p);p.Provider;var ensureSuspenseTimers=e=>{if(e.suspense){let clamp=e=>"static"===e?e:Math.max(e??1e3,1e3),t=e.staleTime;e.staleTime="function"==typeof t?(...e)=>clamp(t(...e)):clamp(t),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3))}},willFetch=(e,t)=>e.isLoading&&e.isFetching&&!t,shouldSuspend=(e,t)=>e?.suspense&&t.isPending,fetchOptimistic=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function useQuery(e,t){return function(e,t,r){let s=useIsRestoring(),i=useQueryErrorResetBoundary(),a=(0,d.NL)(r),o=a.defaultQueryOptions(e);a.getDefaultOptions().queries?._experimental_beforeQuery?.(o),o._optimisticResults=s?"isRestoring":"optimistic",ensureSuspenseTimers(o),ensurePreventErrorBoundaryRetry(o,i),useClearResetErrorBoundary(i);let u=!a.getQueryCache().get(o.queryHash),[h]=l.useState(()=>new t(a,o)),f=h.getOptimisticResult(o),p=!s&&!1!==e.subscribed;if(l.useSyncExternalStore(l.useCallback(e=>{let t=p?h.subscribe(n.Vr.batchCalls(e)):c.ZT;return h.updateResult(),t},[h,p]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),l.useEffect(()=>{h.setOptions(o)},[o,h]),shouldSuspend(o,f))throw fetchOptimistic(o,h,i);if(getHasError({result:f,errorResetBoundary:i,throwOnError:o.throwOnError,query:a.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw f.error;if(a.getDefaultOptions().queries?._experimental_afterQuery?.(o,f),o.experimental_prefetchInRender&&!c.sk&&willFetch(f,s)){let e=u?fetchOptimistic(o,h,i):a.getQueryCache().get(o.queryHash)?.promise;e?.catch(c.ZT).finally(()=>{h.updateResult()})}return o.notifyOnChangeProps?f:h.trackResult(f)}(e,h,t)}}}]);