(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3069],{5306:function(e,s,t){Promise.resolve().then(t.bind(t,8404))},8404:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return Usuarios}});var r=t(7437),a=t(3877),l=t(8637),n=t(2265),d=t(4568),i=t(3014),o=t(3042),c=t(9367),m=t(6689),u=t(5968),x=t(1481),h=t(8689),v=t(6654),p=t(8089),j=t(9715),f=t(3256),b=t(2359);function Usuarios(){var e,s,t;let[N,w]=(0,n.useState)(!1),[g,y]=(0,n.useState)(),[k,Z]=(0,n.useState)(),[I,F]=(0,n.useState)(),[C,P]=(0,n.useState)(),[S,A]=(0,n.useState)(),[E,D]=(0,n.useState)(!1),[K,L]=(0,n.useState)("broker"),[V,_]=(0,n.useState)(!1),[B,M]=(0,n.useState)("Todos"),[T,U]=(0,n.useState)(1),[O,z]=(0,n.useState)(),[J,q]=(0,n.useState)(),[G,H]=(0,n.useState)(),[Q,R]=(0,n.useState)(),[W,X]=(0,n.useState)(),Y=(0,f.e)();async function getAcessorsInvestors(){w(!0);try{let e=await d.Z.get("superadmin"===Y.name?"/wallets/list-investors-broker":"/wallets/admin/investors-broker",{params:{adviserId:"superadmin"!==Y.name?Y.roleId:void 0,brokerId:null==C?void 0:C.id}}),s=await d.Z.get("superadmin"===Y.name?"/wallets/list-advisors-broker":"/wallets/admin/advisors-broker",{params:{adviserId:"superadmin"!==Y.name?Y.roleId:void 0,brokerId:null==C?void 0:C.id}}),t=null==e?void 0:e.data,r=null==s?void 0:s.data,a=[],l=[];t.map(e=>{a.push({...e,document:e.document.length<=11?(0,u.VL)(e.document||""):(0,u.PK)(e.document||""),type:"investidor"})}),r.map(e=>{l.push({...e,document:e.document.length<=11?(0,u.VL)(e.document||""):(0,u.PK)(e.document||""),type:"assessor"})}),y([...l,...a]),w(!1)}catch(e){w(!1),(0,v.Z)(e,"Erro ao buscar os assessores")}}let getInvestorData=e=>{i.Am.info("Buscando dados do investidor..."),d.Z.get("/contract/".concat(e.id)).then(s=>{H({...s.data,...e}),D(!0)}).catch(e=>{(0,v.Z)(e,"N\xe3o foi possivel buscar o investidor")})},getBrokerData=e=>{i.Am.info("Buscando dados do broker...",{toastId:"broker"}),d.Z.get("/wallets/broker/one?brokerId=".concat(e.id)).then(e=>{i.Am.dismiss("broker"),D(!0),R(e.data)}).catch(e=>{(0,v.Z)(e,"N\xe3o foi possivel buscar o investidor"),i.Am.dismiss("broker")})},getAdvisorData=e=>{i.Am.info("Buscando dados do assessor...",{toastId:"advisor"}),d.Z.get("/wallets/advisor/one?advisorId=".concat(e.id)).then(e=>{i.Am.dismiss("advisor"),D(!0),R(e.data)}).catch(e=>{(0,v.Z)(e,"N\xe3o foi possivel buscar o investidor"),i.Am.dismiss("broadvisorker")})};(0,n.useEffect)(()=>{"superadmin"===Y.name?z(0):z(1)},[]),(0,n.useEffect)(()=>{0===O?(w(!0),d.Z.get("/wallets/list-admin").then(e=>{let s=[];e.data.map(e=>{var t;s.push({...e,document:(null==e?void 0:null===(t=e.document)||void 0===t?void 0:t.length)<=11?(0,u.VL)((null==e?void 0:e.document)||""):(0,u.PK)((null==e?void 0:e.document)||""),type:"gestor de carteira"})}),y(s)}).catch(e=>{i.Am.error("Erro ao buscar os brokers")}).finally(()=>w(!1))):1===O?(w(!0),d.Z.get("superadmin"!==Y.name?"/wallets/admin/brokers":"/wallets/list-brokers-admin",{params:{adminId:"superadmin"===Y.name?null==I?void 0:I.id:void 0,adviserId:"superadmin"!==Y.name?Y.roleId:void 0}}).then(e=>{let s=[];e.data.map(e=>{var t;s.push({...e,document:(null==e?void 0:null===(t=e.document)||void 0===t?void 0:t.length)<=11?(0,u.VL)(e.document||""):(0,u.PK)(e.document||""),type:"broker"})}),y(s)}).catch(e=>{(0,v.Z)(e,"Erro ao buscar os brokers")}).finally(()=>w(!1))):2===O?getAcessorsInvestors():3===O&&(w(!0),d.Z.get("superadmin"===Y.name?"/wallets/list-investors-advisor":"/wallets/admin/investors-advisor-broker",{params:"superadmin"===Y.name?{advisorId:null==S?void 0:S.id}:{adviserId:Y.roleId,brokerId:null==C?void 0:C.id,advisorId:null==S?void 0:S.id}}).then(e=>{let s=[];e.data.map(e=>{s.push({...e,document:e.document.length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document),type:"investidor"})}),y(s)}).catch(e=>{(0,v.Z)(e,"Erro ao buscar os investidores")}).finally(()=>w(!1)))},[O]);let renderTable=(e,s)=>(0,r.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] w-full text-white mt-10 overflow-x-auto rounded-b-md border border-[#FF9900]",children:[(0,r.jsx)("div",{className:"flex w-full justify-end p-2",children:(0,r.jsx)("div",{className:"w-80"})}),(0,r.jsxs)("table",{className:"w-full relative min-h-20",children:[(0,r.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,r.jsxs)("tr",{className:"w-full",children:[(0,r.jsx)("th",{className:"w-10",children:(0,r.jsx)("p",{className:"font-bold text-sm"})}),(0,r.jsx)("th",{className:"w-10",children:(0,r.jsx)("p",{className:"font-bold text-sm"})}),(0,r.jsx)("th",{className:"py-2 max-w-[350px] text-center",children:(0,r.jsx)("p",{className:"font-bold text-sm text-center",children:"Nome"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"E-mail"})}),(0,r.jsx)("th",{children:(0,r.jsx)("p",{className:"font-bold text-sm",children:"Perfil"})})]})}),(0,r.jsx)(r.Fragment,{children:N?(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[(0,r.jsx)("td",{className:"w-10 px-1",children:(0,r.jsx)(h.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(h.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(h.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(h.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(h.j,{height:"25px"})}),(0,r.jsx)("td",{className:"px-1",children:(0,r.jsx)(h.j,{height:"25px"})})]}):(0,r.jsx)(r.Fragment,{children:(null==g?void 0:g.length)>0?(0,r.jsx)("tbody",{className:"w-full",children:g.map((e,s)=>(0,r.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[(0,r.jsx)("td",{className:"w-10",children:"investidor"!==e.type&&(0,r.jsx)("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{"investidor"!==e.type&&(0===O?(F(e),z(1)):1===O?(P(e),z(2)):2===O&&(A(e),z(3)))},children:(0,r.jsx)(o.Z,{color:"#fff",width:20})})}),(0,r.jsx)("td",{className:"w-10",children:("investidor"===e.type||"broker"===e.type||"assessor"===e.type)&&("superadmin"===Y.name||"admin"===Y.name)&&(0,r.jsx)("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{"investidor"===e.type?(L("investor"),getInvestorData(e)):"broker"===e.type?(L("broker"),getBrokerData(e)):"assessor"===e.type&&(L("advisor"),getAdvisorData(e))},children:(0,r.jsx)(c.Z,{color:"#fff",width:20})})}),(0,r.jsx)("td",{className:"max-w-[350px]",children:(0,r.jsx)("p",{className:"text-sm text-center py-1 ",children:e.name||"N\xe3o encontrado"})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:e.document?(0,u.p4)(e.document).length<=11?(0,u.VL)(e.document):(0,u.PK)(e.document):"N\xe3o encontrado"})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:e.email||"N\xe3o encontrado"})}),(0,r.jsx)("td",{children:(0,r.jsx)("p",{className:"text-sm text-center",children:e.type})})]},s))}):(0,r.jsx)("div",{className:"text-center mt-5 absolute w-full pb-5",children:(0,r.jsx)("p",{children:"Nenhum dado encontrado"})})})})]}),(0,r.jsx)(j.Z,{page:T,setPage:U,lastPage:Number((null==J?void 0:J.lastPage)||1),perPage:Number((null==J?void 0:J.perPage)||10),totalItems:(null==J?void 0:J.total)||"0"})]});function returnName(){if(1===O)return{name:null==I?void 0:I.name,letters:"".concat(null==I?void 0:I.name.split(" ")[0][0]).concat((null==I?void 0:I.name.split(" ")[1])?null==I?void 0:I.name.split(" ")[1][0]:""),type:null==I?void 0:I.type};if(2===O){var e,s;return{name:null==C?void 0:C.name,letters:"".concat(null==C?void 0:C.name.split(" ")[0][0]).concat((null==C?void 0:null===(e=C.name)||void 0===e?void 0:e.split(" ")[1])?null==C?void 0:null===(s=C.name)||void 0===s?void 0:s.split(" ")[1][0]:""),type:null==C?void 0:C.type}}if(3===O)return{name:null==S?void 0:S.name,letters:"".concat(null==S?void 0:S.name.split(" ")[0][0]).concat((null==S?void 0:S.name.split(" ")[1])?null==S?void 0:S.name.split(" ")[1][0]:""),type:null==S?void 0:S.type}}return(0,r.jsx)("div",{children:(0,r.jsxs)(b.yo,{open:E,onOpenChange:D,children:[(0,r.jsx)(a.Z,{}),(0,r.jsx)(l.Z,{children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:(0,r.jsx)("h1",{className:"m-auto font-bold text-2xl",children:"Usu\xe1rios"})}),(I||C)&&(0,r.jsx)("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border text-white",children:(0,r.jsxs)("div",{className:"flex w-full",children:[(0,r.jsx)("div",{className:"w-[25px] h-[25px] bg-white rounded-full flex items-center justify-center",children:(0,r.jsx)("p",{className:"text-[#FF9900] text-xs font-bold",children:null===(e=returnName())||void 0===e?void 0:e.letters})}),(0,r.jsx)("div",{className:"w-full",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"ml-3 text-base",children:null===(s=returnName())||void 0===s?void 0:s.name}),(0,r.jsx)("p",{className:"ml-3 text-sm",children:null===(t=returnName())||void 0===t?void 0:t.type})]})}),(0,r.jsx)("div",{className:"cursor-pointer",onClick:()=>{"superadmin"===Y.name?z(0):z(1),P(void 0),F(void 0)},children:(0,r.jsx)(m.Z,{width:20})})]})}),renderTable(g,!1),(0,r.jsx)(b.ue,{className:"w-full md:w-[500px]",children:E?"investor"===K?(0,r.jsx)(x.Z,{investor:G,setModal:D}):Q&&(0,r.jsx)(p.Z,{typeModal:K,broker:Q,setModal:D}):(0,r.jsx)(r.Fragment,{})}),V&&(0,r.jsx)("div",{className:"z-30 fixed top-0 left-0 w-screen min-h-screen bg-[#1c1c1c71]",children:(0,r.jsx)("div",{className:"z-40 w-8/12 bg-[#181818] fixed top-1/2 right-1/2 translate-x-1/2 translate-y-[-50%] border border-[#FF9900] p-10 text-white overflow-auto",children:(0,r.jsxs)("div",{className:"flex flex-col gap-y-3 relative",children:[(0,r.jsx)("p",{className:"text-2xl",children:"Usu\xe1rios"}),(0,r.jsx)("p",{className:"absolute right-0 cursor-pointer",onClick:()=>_(!1),children:"x"}),renderTable([],!0)]})})})]})})]})})}},9715:function(e,s,t){"use strict";t.d(s,{Z:function(){return Pagination}});var r=t(7437),a=t(13),l=t(3217);function Pagination(e){let{lastPage:s,page:t,setPage:n,totalItems:d,perPage:i,loading:o=!1}=e;return o?(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:"Carregando..."})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(a.Z,{color:"#555",width:15})}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818]",children:"..."}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm bg-[#181818] cursor-not-allowed",children:(0,r.jsx)(l.Z,{color:"#555",width:15})})]})]}):(isNaN(s),(0,r.jsxs)("div",{className:"w-full flex justify-between items-center pr-5 border-t border-[#FF9900]",children:[(0,r.jsx)("div",{}),(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm",children:(()=>{let e=parseInt(d,10);if(0===e)return"Nenhum resultado";let s=(t-1)*i+1,r=Math.min(t*i,e);return"Exibindo ".concat(s," a ").concat(r," de ").concat(e," resultados")})()})}),(0,r.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(t>1?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>t>1&&n(t-1),children:(0,r.jsx)(a.Z,{color:"#fff",width:15})}),(0,r.jsx)("div",{className:"flex gap-2 select-none",children:(()=>{let e=new Set;e.add(1),e.add(s),t>1&&e.add(t-1),e.add(t),t<s&&e.add(t+1);let a=Array.from(e).sort((e,s)=>e-s);return a.map((e,s,a)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[s>0&&a[s-1]!==e-1&&(0,r.jsx)("div",{className:"flex items-center justify-center",children:"..."}),Number.isFinite(e)&&(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer bg-[#262626] ".concat(t===e?"text-[#FF9900]":"text-white"),onClick:()=>n(e),children:e})]},e))})()}),(0,r.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-sm cursor-pointer ".concat(t<s?"bg-[#262626]":"bg-[#181818] cursor-not-allowed"),onClick:()=>t<s&&n(t+1),children:(0,r.jsx)(l.Z,{color:"#fff",width:15})})]})]}))}}},function(e){e.O(0,[6990,7326,8276,5371,6946,1865,1306,3151,7050,2971,7864,1744],function(){return e(e.s=5306)}),_N_E=e.O()}]);