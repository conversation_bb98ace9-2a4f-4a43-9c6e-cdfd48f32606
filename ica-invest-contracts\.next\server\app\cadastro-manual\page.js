(()=>{var e={};e.id=6930,e.ids=[6930],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},98899:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>m});var a=t(73137),i=t(54647),n=t(4183),r=t.n(n),l=t(71775),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=a.AppPageRouteModule,m=["",{children:["cadastro-manual",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,31608)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,51918,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\cadastro-manual\\page.tsx"],x="/cadastro-manual/page",h={require:t,loadChunk:()=>Promise.resolve()},p=new o({definition:{kind:i.x.APP_PAGE,page:"/cadastro-manual/page",pathname:"/cadastro-manual",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},65764:(e,s,t)=>{Promise.resolve().then(t.bind(t,67439))},67439:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>CadastroManual});var a=t(60080),i=t(9885),n=t(97669),r=t(47956),l=t(32307),d=t(57086),o=t(66558),m=t(99986),c=t(96413),x=t(12107),h=t(85814),p=t(24577),u=t(34751),g=t(22796),b=t(32411),j=t(34635),f=t(40990),w=t(9993),N=t(42686),v=t(64731),y=t.n(v),C=t(17871),S=t(90682),F=t(15455),$=t(40509),k=t(80223),D=t(34944),Z=t(51778),E=t(28303);function PhysicalRegister({modalityContract:e}){let[s,t]=(0,i.useState)(""),[n,r]=(0,i.useState)(""),[v,P]=(0,i.useState)(!1),[M,T]=(0,i.useState)([]),[A,I]=(0,i.useState)(),[V,R]=(0,i.useState)(),[q,L]=(0,i.useState)(),[Y,U]=(0,i.useState)(),O=(0,S.e)(),{register:B,handleSubmit:z,watch:_,setValue:K,reset:G,formState:{errors:W,isValid:Q}}=(0,o.cI)({resolver:(0,d.X)(x.$r),mode:"all",defaultValues:{initDate:y()().format("DD/MM/YYYY"),isSCP:"SCP"===e,purchaseWith:"pix",profile:"moderate",isDebenture:"s"}}),X=_("term"),J=(0,D.D)({mutationFn:async e=>h.Z.post("/account/create/existing-contract",e),onSuccess:()=>{u.Am.success("Investidor cadastrado com sucesso!"),G(),T([]),I(void 0),R(void 0),L(void 0),U(void 0)},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o contrato")}}),{data:H=[]}=(0,Z.a)({queryKey:["brokers"],queryFn:async()=>{let e=await h.Z.get("superadmin"===O.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data}}),{data:ee=[],isLoading:es}=(0,Z.a)({queryKey:["acessors",n],queryFn:async()=>{let e=await h.Z.get(function(){switch(O.name){case"superadmin":return"/wallets/list-advisors-broker";case"admin":return"/wallets/admin/advisors-broker";case"broker":return"/wallets/broker/advisors";default:return""}}(),{params:{brokerId:"broker"!==O.name?n:void 0}});return e.data},enabled:"broker"===O.name||!n}),et=(0,i.useMemo)(()=>{if(_("initDate")&&X){let e=(0,N.H)({investDate:X,startDate:_("initDate")});return y()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[_("initDate"),X]);(0,i.useEffect)(()=>{et&&K("endDate",et,{shouldValidate:!0})},[et,K]),(0,i.useEffect)(()=>{"broker"===O.name&&O.roleId&&r(O.roleId)},[O]),(0,Z.a)({queryKey:["address",_("zipCode")],queryFn:async()=>{let e=await (0,$.x)(_("zipCode"));e&&(K("neighborhood",e.neighborhood,{shouldValidate:!0}),K("city",e.city,{shouldValidate:!0}),K("state",e.state,{shouldValidate:!0}),K("street",e.street,{shouldValidate:!0}))},enabled:_("zipCode")?.length===9});let ea=(0,i.useCallback)(()=>{T(e=>[...e,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},[]),ei=(0,i.useCallback)(async s=>{let t=Number(s.yield.replace(",",".")),a=new FormData;a.append("role",O.name),"broker"!==O.name&&a.append("brokerId",n),a.append("personType","PF"),a.append("contractType",e),a.append("individual[fullName]",(0,E.Z)(String(s.name).trim())),a.append("individual[cpf]",(0,c.p4)(s.document).trim()),a.append("individual[rg]",s.rg),a.append("individual[issuingAgency]",s.issuer),a.append("individual[nationality]",s.placeOfBirth),a.append("individual[occupation]",(0,E.Z)(s.occupation)),a.append("individual[birthDate]",s.dtBirth),a.append("individual[email]",s.email),a.append("individual[phone]",`55${(0,c.p4)(s.phoneNumber)}`),a.append("individual[motherName]",(0,E.Z)(s.motherName)),a.append("individual[address][street]",s.street),a.append("individual[address][neighborhood]",s.neighborhood),a.append("individual[address][city]",s.city),a.append("individual[address][state]",s.state),a.append("individual[address][postalCode]",(0,c.p4)(s.zipCode)),a.append("individual[address][number]",s.number),a.append("individual[address][complement]",s.complement||""),a.append("bankAccount[bank]",s.bank),a.append("bankAccount[agency]",s.agency),a.append("bankAccount[account]",s.accountNumber),a.append("bankAccount[pix]",s.pix),a.append("bankAccount[accountType]","CORRENTE"),a.append("investment[amount]",String((0,C.Z)(s.value))),a.append("investment[monthlyRate]",String(t)),a.append("investment[durationInMonths]",s.term),a.append("investment[paymentMethod]",s.purchaseWith),a.append("investment[startDate]",`${s.initDate}T00:00:00.000Z`),a.append("investment[endDate]",`${et?y()(et,"DD/MM/YYYY").format("YYYY-MM-DD"):""}T00:00:00.000Z`),a.append("investment[profile]",s.profile),a.append("investment[isDebenture]","s"===s.isDebenture?"true":"false"),"SCP"===e&&a.append("investment[quotaQuantity]",s.amountQuotes||""),1===M.length?M.map((e,s)=>{a.append(`advisors[${s}][advisorId]`,e.id),a.append(`advisors[${s}][rate]`,String(e.taxValue))}):M.length>1&&""!==M[0].id&&M.map((e,s)=>{a.append(`advisors[${s}][advisorId]`,e.id),a.append(`advisors[${s}][rate]`,String(e.taxValue))}),A&&a.append("contract",A[0]),V&&a.append("proofOfPayment",V[0]),q&&a.append("personalDocument",q[0]),Y&&a.append("proofOfResidence",Y[0]),await J.mutateAsync(a,{onSuccess:()=>{T([]),G()}})},[M,n,A,q,Y,e,V,O.name,J]),en=(0,i.useCallback)((e,s)=>{T(t=>t.map(t=>t.generatedId===e?{...t,taxValue:s}:t))},[]);return a.jsx("div",{children:(0,a.jsxs)("form",{action:"",onSubmit:z(ei),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[a.jsx(w.Z,{color:"black",title:"Dados Pessoais",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[a.jsx(l.Z,{register:B,name:"name",width:"300px",error:!!W.name,errorMessage:W?.name?.message,label:"Nome completo"}),a.jsx(l.Z,{register:B,name:"document",width:"200px",error:!!W.document,errorMessage:W?.document?.message,label:"CPF",setValue:e=>K("document",(0,c.VL)(e||""),{shouldValidate:!0})}),a.jsx(l.Z,{register:B,name:"rg",width:"200px",error:!!W.rg,errorMessage:W?.rg?.message,label:"Identidade"}),a.jsx(l.Z,{register:B,name:"issuer",width:"200px",error:!!W.issuer,errorMessage:W?.issuer?.message,label:"Org\xe3o emissor"}),a.jsx(l.Z,{register:B,name:"placeOfBirth",width:"200px",error:!!W.placeOfBirth,errorMessage:W?.placeOfBirth?.message,label:"Nacionalidade"}),a.jsx(l.Z,{register:B,name:"occupation",width:"200px",error:!!W.occupation,errorMessage:W?.occupation?.message,label:"Ocupa\xe7\xe3o"}),a.jsx(l.Z,{width:"200px",register:B,name:"phoneNumber",maxLength:15,error:!!W.phoneNumber,errorMessage:W?.phoneNumber?.message,label:"Celular",setValue:e=>K("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})}),(0,a.jsxs)("div",{style:{width:"200px"},children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:W.dtBirth&&`- ${W.dtBirth.message}`})]}),a.jsx("input",{...B("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value=s.value.replace(/[^0-9\-]/g,""),s.value.length>10&&(s.value=s.value.slice(0,10));let[t,a,i]=s.value.split("-");t&&t.length>4&&(s.value=t.slice(0,4)+(a?"-"+a:"")+(i?"-"+i:""))},className:`h-12 w-full px-4 text-white rounded-xl ${W.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx(l.Z,{register:B,name:"email",width:"300px",error:!!W.email,errorMessage:W?.email?.message,label:"E-mail"}),a.jsx(l.Z,{register:B,name:"motherName",width:"300px",error:!!W.motherName,errorMessage:W?.motherName?.message,label:"Nome da m\xe3e"}),a.jsx(l.Z,{register:B,name:"zipCode",width:"200px",error:!!W.zipCode,errorMessage:W?.zipCode?.message,label:"CEP",setValue:e=>{K("zipCode",(0,c.Tc)(e),{shouldValidate:!0})}}),a.jsx(l.Z,{register:B,name:"neighborhood",width:"200px",error:!!W.neighborhood,errorMessage:W?.neighborhood?.message,label:"Bairro"}),a.jsx(l.Z,{register:B,name:"street",width:"300px",error:!!W.street,errorMessage:W?.street?.message,label:"Rua"}),a.jsx(l.Z,{register:B,name:"city",width:"200px",error:!!W.city,errorMessage:W?.city?.message,label:"Cidade"}),a.jsx(l.Z,{register:B,maxLength:2,setValue:e=>K("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!W.state,errorMessage:W?.state?.message,label:"Estado"}),a.jsx(l.Z,{register:B,name:"number",width:"200px",error:!!W.number,errorMessage:W?.number?.message,label:"N\xfamero"}),a.jsx(l.Z,{register:B,name:"complement",width:"200px",error:!!W.complement,errorMessage:W?.complement?.message,label:"Complemento"})]})}),a.jsx(w.Z,{color:"black",title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[a.jsx(l.Z,{register:B,name:"value",width:"200px",error:!!W.value,errorMessage:W?.value?.message,label:"Valor",setValue:e=>K("value",(0,c.Ht)(e||""),{shouldValidate:!0})}),a.jsx(l.Z,{register:B,type:"number",name:"term",width:"250px",error:!!W.term,errorMessage:W?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),a.jsx(l.Z,{register:B,type:"text",name:"yield",width:"250px",error:!!W.yield,errorMessage:W?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),(0,a.jsxs)("div",{className:"w-[200px]",children:[a.jsx("p",{className:"text-white mb-1",children:"Comprar com"}),(0,a.jsxs)(k.Z,{value:_("purchaseWith"),onChange:e=>K("purchaseWith",e.target.value,{shouldValidate:!0}),children:[a.jsx("option",{value:"pix",children:"PIX"}),a.jsx("option",{value:"boleto",children:"Boleto"}),a.jsx("option",{value:"bank_transfer",children:"Transfer\xeancia"})]})]}),"SCP"===e&&a.jsx(a.Fragment,{children:a.jsx(l.Z,{register:B,type:"number",name:"amountQuotes",width:"150px",error:!!W.amountQuotes,errorMessage:W?.amountQuotes?.message,label:"Quantidade de cotas"})}),a.jsx(l.Z,{type:"date",register:B,maxDate:y()().format("YYYY-MM-DD"),name:"initDate",width:"200px",setValue:e=>K("initDate",e,{shouldValidate:!0}),error:!!W.initDate,errorMessage:W?.initDate?.message,label:"Inicio do contrato"}),a.jsx(l.Z,{type:"date",register:B,name:"endDate",value:et?y()(et,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"200px",disabled:!0,label:"Final do contrato"}),(0,a.jsxs)("div",{className:"w-[200px]",children:[a.jsx("p",{className:"text-white mb-1",children:"Perfil"}),(0,a.jsxs)(k.Z,{value:_("profile"),onChange:e=>K("profile",e.target.value,{shouldValidate:!0}),children:[a.jsx("option",{value:"conservative",children:"Conservador"}),a.jsx("option",{value:"moderate",children:"Moderado"}),a.jsx("option",{value:"aggressive",children:"Agressivo"})]})]}),(0,a.jsxs)("div",{className:"w-[100px]",children:[a.jsx("p",{className:"text-white mb-1",children:"Deb\xeanture"}),(0,a.jsxs)(k.Z,{value:_("isDebenture"),onChange:e=>K("isDebenture",e.target.value,{shouldValidate:!0}),children:[a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})]})}),"broker"!==O.name&&a.jsx(w.Z,{color:"black",title:"Selecione o broker",children:a.jsx("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-start",children:a.jsx("div",{className:"md:w-2/4",children:a.jsx(g.Z,{label:"Broker",items:H,value:n,setValue:r})})})}),("broker"!==O.name&&""!==n||"broker"===O.name)&&a.jsx(w.Z,{color:"black",title:"Adicionar Assessor",children:(0,a.jsxs)("div",{children:[M.length>0?M?.map((e,s)=>a.jsxs("div",{className:"flex justify-between items-end gap-4 mb-4",children:[a.jsx("div",{className:"flex-1",children:a.jsx(g.Z,{label:"Selecione um Assessor",items:ee,value:"",setValue:()=>{},loading:v,handleChange:t=>{let a=M.filter(s=>s.generatedId===e.generatedId)[0],i={generatedId:a.generatedId,id:t.id,taxValue:Number(t.rate)};M[s]=i,T([...M])}})}),a.jsx("div",{className:"flex-1",children:a.jsx(b.Z,{label:"Adicione a Taxa - em %",id:"",name:"",value:String(e.taxValue),type:"text",onChange:s=>en(e.generatedId,s.target.value)})}),a.jsx("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=M.filter((e,t)=>t!==s);T(e)},children:a.jsx(j.Z,{width:20})})]},s)):a.jsx("div",{children:a.jsx("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),a.jsx("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:ea,children:a.jsx(f.Z,{width:25,color:"#fff"})})]})}),a.jsx(w.Z,{color:"black",title:"Dados bancarios",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[a.jsx(l.Z,{register:B,name:"bank",width:"300px",error:!!W.bank,errorMessage:W?.bank?.message,label:"Banco"}),a.jsx(l.Z,{register:B,name:"agency",width:"200px",error:!!W.agency,errorMessage:W?.agency?.message,label:"Ag\xeancia"}),a.jsx(l.Z,{register:B,name:"accountNumber",width:"200px",error:!!W.accountNumber,errorMessage:W?.accountNumber?.message,label:"Conta"}),a.jsx(l.Z,{register:B,name:"pix",width:"250px",error:!!W.pix,errorMessage:W?.pix?.message,label:"Pix"})]})}),a.jsx(w.Z,{color:"black",title:"Anexo de documentos",children:a.jsx("div",{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato"}),a.jsx(F.Z,{onFileUploaded:I})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante"}),a.jsx("div",{children:a.jsx(F.Z,{onFileUploaded:R})})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx("div",{children:a.jsx(F.Z,{onFileUploaded:L})})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx("div",{children:a.jsx(F.Z,{onFileUploaded:U})})]})]})})}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",size:"lg",loading:J.isPending,disabled:J.isPending||!et||!Q||!A||!V||!q||!Y})})]})})}var P=t(27017);let M=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}];function BusinessRegister({modalityContract:e}){let[s,t]=(0,i.useState)(""),[n,r]=(0,i.useState)(""),[v,k]=(0,i.useState)(),[T,A]=(0,i.useState)(),[I,V]=(0,i.useState)(),[R,q]=(0,i.useState)(),[L,Y]=(0,i.useState)(""),[U,O]=(0,i.useState)([]),B=(0,S.e)(),{register:z,handleSubmit:_,watch:K,setValue:G,reset:W,formState:{errors:Q,isValid:X}}=(0,o.cI)({resolver:(0,d.X)(x._R),mode:"all",defaultValues:{initDate:y()().format("DD/MM/YYYY"),isSCP:"SCP"===e,purchaseWith:"pix",profile:"moderate",isDebenture:"s"}}),J=K("term"),{data:H=[]}=(0,Z.a)({queryKey:["brokers"],queryFn:async()=>{let e=await h.Z.get("superadmin"===B.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data},enabled:"broker"!==B.name&&"advisor"!==B.name}),{data:ee=[],isLoading:es}=(0,Z.a)({queryKey:["acessors",L],queryFn:async()=>{let e=await h.Z.get(ea(),{params:{brokerId:"broker"!==B.name?L:void 0}});return e.data},enabled:"broker"!==B.name&&""!==L||"broker"===B.name}),et=(0,i.useMemo)(()=>{if(K("initDate")&&J){let e=(0,N.H)({investDate:J,startDate:K("initDate")});return y()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[K("initDate"),J]);(0,i.useEffect)(()=>{et&&G("endDate",et,{shouldValidate:!0})},[et,G]);let ea=(0,i.useCallback)(()=>{switch(B.name){case"superadmin":return"/wallets/list-advisors-broker";case"admin":return"/wallets/admin/advisors-broker";case"broker":return"/wallets/broker/advisors";default:return""}},[B.name]);(0,i.useEffect)(()=>{G("isSCP","SCP"===e,{shouldValidate:!0})},[e]),(0,i.useEffect)(()=>{"broker"===B.name&&B.roleId&&Y(B.roleId)},[B]);let ei=(0,D.D)({mutationFn:async e=>{await h.Z.post("/account/create/existing-contract",e)},onSuccess:()=>{u.Am.success("Investidor cadastrado com sucesso!"),W(),O([]),V(void 0),q(void 0),k(void 0),A(void 0)},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o contrato!")}}),en=(0,i.useCallback)(async s=>{let t=Number(s.yield.replace(",",".")),a=new FormData;a.append("personType","PJ"),a.append("contractType",e),a.append("role",B.name),"broker"!==B.name&&a.append("brokerId",L),a.append("bankAccount[bank]",s.bank),a.append("bankAccount[agency]",s.agency),a.append("bankAccount[account]",s.accountNumber),a.append("bankAccount[pix]",s.pix),a.append("bankAccount[accountType]","CORRENTE"),a.append("investment[amount]",String((0,C.Z)(s.value))),a.append("investment[monthlyRate]",String(t)),a.append("investment[durationInMonths]",s.term),a.append("investment[paymentMethod]",s.purchaseWith),a.append("investment[startDate]",`${s.initDate}T00:00:00.000Z`),a.append("investment[endDate]",`${et?y()(et,"DD/MM/YYYY").format("YYYY-MM-DD"):""}T00:00:00.000Z`),a.append("investment[profile]",s.profile),a.append("investment[isDebenture]","s"===s.isDebenture?"true":"false"),"SCP"===e&&a.append("investment[quotaQuantity]",s.amountQuotes||""),a.append("company[corporateName]",s.name),a.append("company[cnpj]",(0,c.p4)(s.document)),a.append("company[type]",s.companyType),a.append("company[address][street]",s.companyStreet),a.append("company[address][city]",s.companyCity),a.append("company[address][state]",s.companyState),a.append("company[address][neighborhood]",s.companyNeighborhood),a.append("company[address][postalCode]",(0,c.p4)(s.companyZipCode)),a.append("company[address][number]",s.companyNumber),a.append("company[address][complement]",s.companyComplement||""),a.append("company[representative][fullName]",(0,E.Z)(s.ownerName)),a.append("company[representative][cpf]",(0,c.p4)(s.ownerDocument)),a.append("company[representative][rg]",s.rg),a.append("company[representative][issuingAgency]",s.issuer||""),a.append("company[representative][nationality]",s.placeOfBirth),a.append("company[representative][occupation]",(0,E.Z)(s.occupation)),a.append("company[representative][birthDate]",s.dtBirth),a.append("company[representative][email]",s.email),a.append("company[representative][phone]",`55${(0,c.p4)(s.phoneNumber)}`),a.append("company[representative][motherName]",(0,E.Z)(s.motherName)),a.append("company[representative][address][street]",s.street),a.append("company[representative][address][city]",s.city),a.append("company[representative][address][state]",s.state),a.append("company[representative][address][neighborhood]",s.neighborhood),a.append("company[representative][address][postalCode]",(0,c.p4)(s.zipCode)),a.append("company[representative][address][number]",s.number),a.append("company[representative][address][complement]",s.complement||""),(1===U.length||U.length>1&&""!==U[0].id)&&U.forEach((e,s)=>{a.append(`advisors[${s}][advisorId]`,e.id),a.append(`advisors[${s}][rate]`,String(e.taxValue))}),v&&a.append("contract",v[0]),T&&a.append("proofOfPayment",T[0]),I&&a.append("personalDocument",I[0]),R&&a.append("proofOfResidence",R[0]),ei.mutate(a)},[ei,U,v,T,I,R,e,L,B.name]);(0,Z.a)({queryKey:["address",K("zipCode")],queryFn:async()=>{let e=await (0,$.x)(K("zipCode"));e&&(G("neighborhood",e.neighborhood,{shouldValidate:!0}),G("city",e.city,{shouldValidate:!0}),G("state",e.state,{shouldValidate:!0}),G("street",e.street,{shouldValidate:!0}))},enabled:K("zipCode")?.length===9}),(0,Z.a)({queryKey:["address",K("companyZipCode")],queryFn:async()=>{let e=await (0,$.x)(K("companyZipCode"));e&&(G("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),G("companyCity",e.city,{shouldValidate:!0}),G("companyState",e.state,{shouldValidate:!0}),G("companyStreet",e.street,{shouldValidate:!0}))},enabled:K("companyZipCode")?.length===9});let er=(0,i.useCallback)((e,s)=>{O(t=>t.map(t=>t.generatedId===e?{...t,taxValue:s}:t))},[]),el=(0,i.useCallback)(()=>{O(e=>[...e,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},[]);return a.jsx("div",{children:(0,a.jsxs)("form",{action:"",onSubmit:_(en),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[a.jsx(w.Z,{color:"black",title:"Dados Pessoais - Representante",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[a.jsx(l.Z,{register:z,name:"ownerName",width:"300px",error:!!Q.ownerName,errorMessage:Q?.ownerName?.message,label:"Nome"}),a.jsx(l.Z,{register:z,name:"ownerDocument",width:"200px",error:!!Q.ownerDocument,errorMessage:Q?.ownerDocument?.message,label:"CPF",setValue:e=>G("ownerDocument",(0,c.VL)(e||""),{shouldValidate:!0})}),a.jsx(l.Z,{register:z,name:"rg",width:"200px",error:!!Q.rg,errorMessage:Q?.rg?.message,label:"RG"}),a.jsx(l.Z,{register:z,name:"issuer",width:"200px",error:!!Q.issuer,errorMessage:Q?.issuer?.message,label:"Org\xe3o emissor"}),a.jsx(l.Z,{register:z,name:"placeOfBirth",width:"200px",error:!!Q.placeOfBirth,errorMessage:Q?.placeOfBirth?.message,label:"Nacionalidade"}),a.jsx(l.Z,{register:z,name:"occupation",width:"200px",error:!!Q.occupation,errorMessage:Q?.occupation?.message,label:"Ocupa\xe7\xe3o"}),a.jsx(l.Z,{register:z,name:"motherName",width:"250px",error:!!Q.motherName,errorMessage:Q?.motherName?.message,label:"Nome da m\xe3e"}),(0,a.jsxs)("div",{style:{width:"200px"},children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:Q.dtBirth&&`- ${Q.dtBirth.message}`})]}),a.jsx("input",{...z("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${Q.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx(l.Z,{width:"200px",register:z,name:"phoneNumber",error:!!Q.phoneNumber,errorMessage:Q?.phoneNumber?.message,label:"Celular",maxLength:15,setValue:e=>G("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})}),a.jsx(l.Z,{register:z,name:"email",width:"300px",error:!!Q.email,errorMessage:Q?.email?.message,label:"E-mail"}),a.jsx(l.Z,{register:z,name:"zipCode",width:"200px",error:!!Q.zipCode,errorMessage:Q?.zipCode?.message,label:"CEP",setValue:e=>{t(e),G("zipCode",(0,c.Tc)(e),{shouldValidate:!0})}}),a.jsx(l.Z,{register:z,name:"neighborhood",width:"300px",error:!!Q.neighborhood,errorMessage:Q?.neighborhood?.message,label:"Bairro"}),a.jsx(l.Z,{register:z,name:"street",width:"300px",error:!!Q.street,errorMessage:Q?.street?.message,label:"Rua"}),a.jsx(l.Z,{register:z,name:"city",width:"200px",error:!!Q.city,errorMessage:Q?.city?.message,label:"Cidade"}),a.jsx(l.Z,{register:z,maxLength:2,setValue:e=>G("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!Q.state,errorMessage:Q?.state?.message,label:"Estado"}),a.jsx(l.Z,{register:z,name:"number",width:"200px",error:!!Q.number,errorMessage:Q?.number?.message,label:"N\xfamero"}),a.jsx(l.Z,{register:z,name:"complement",width:"200px",error:!!Q.complement,errorMessage:Q?.complement?.message,label:"Complemento"})]})}),a.jsx(w.Z,{color:"black",title:"Dados da empresa",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[a.jsx(l.Z,{register:z,name:"name",width:"400px",error:!!Q.name,errorMessage:Q?.name?.message,label:"Raz\xe3o Social"}),a.jsx(l.Z,{register:z,name:"document",width:"200px",error:!!Q.document,errorMessage:Q?.document?.message,label:"CNPJ",setValue:e=>G("document",(0,c.PK)(e||""),{shouldValidate:!0})}),a.jsx(P.Z,{width:"200px",name:"companyType",register:z,options:M,error:!!Q.companyType,errorMessage:Q?.companyType?.message,label:"Tipo"}),a.jsx(l.Z,{register:z,name:"companyZipCode",width:"200px",error:!!Q.companyZipCode,errorMessage:Q?.companyZipCode?.message,label:"CEP",setValue:e=>{r(e),G("companyZipCode",(0,c.Tc)(e),{shouldValidate:!0})}}),a.jsx(l.Z,{register:z,name:"companyNeighborhood",width:"300px",error:!!Q.companyNeighborhood,errorMessage:Q?.companyNeighborhood?.message,label:"Bairro"}),a.jsx(l.Z,{register:z,name:"companyStreet",width:"300px",error:!!Q.companyStreet,errorMessage:Q?.companyStreet?.message,label:"Rua"}),a.jsx(l.Z,{register:z,name:"companyCity",width:"200px",error:!!Q.companyCity,errorMessage:Q?.companyCity?.message,label:"Cidade"}),a.jsx(l.Z,{register:z,maxLength:2,setValue:e=>G("companyState",String(e).toUpperCase(),{shouldValidate:!0}),name:"companyState",width:"150px",error:!!Q.companyState,errorMessage:Q?.companyState?.message,label:"Estado"}),a.jsx(l.Z,{register:z,name:"companyNumber",width:"200px",error:!!Q.companyNumber,errorMessage:Q?.companyNumber?.message,label:"N\xfamero"}),a.jsx(l.Z,{register:z,name:"companyComplement",width:"200px",error:!!Q.companyComplement,errorMessage:Q?.companyComplement?.message,label:"Complemento"})]})}),a.jsx(w.Z,{color:"black",title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[a.jsx(l.Z,{register:z,name:"value",width:"200px",error:!!Q.value,errorMessage:Q?.value?.message,label:"Valor",setValue:e=>G("value",(0,c.Ht)(e||""),{shouldValidate:!0})}),a.jsx(l.Z,{register:z,type:"number",name:"term",width:"250px",error:!!Q.term,errorMessage:Q?.term?.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),a.jsx(l.Z,{register:z,type:"text",name:"yield",width:"250px",error:!!Q.yield,errorMessage:Q?.yield?.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),a.jsx(P.Z,{width:"200px",name:"purchaseWith",register:z,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!Q?.purchaseWith,errorMessage:Q?.purchaseWith?.message,label:"Comprar com"}),"SCP"===e&&a.jsx(a.Fragment,{children:a.jsx(l.Z,{register:z,type:"number",name:"amountQuotes",width:"150px",error:!!Q.amountQuotes,errorMessage:Q?.amountQuotes?.message,label:"Quantidade de cotas"})}),a.jsx(l.Z,{type:"date",register:z,maxDate:y()().format("YYYY-MM-DD"),name:"initDate",width:"200px",setValue:e=>G("initDate",e,{shouldValidate:!0}),error:!!Q.initDate,errorMessage:Q?.initDate?.message,label:"Inicio do contrato"}),a.jsx(l.Z,{type:"date",register:z,name:"endDate",value:et?y()(et,"DD/MM/YYYY").format("YYYY-MM-DD"):"",width:"200px",disabled:!0,label:"Final do contrato"}),a.jsx(P.Z,{width:"200px",name:"profile",register:z,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!Q.profile,errorMessage:Q?.profile?.message,label:"Perfil"}),a.jsx(P.Z,{width:"100px",name:"isDebenture",register:z,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!Q.isDebenture,errorMessage:Q?.isDebenture?.message,label:"Deb\xeanture"})]})}),"broker"!==B.name&&a.jsx(w.Z,{color:"black",title:"Selecione o broker",children:a.jsx("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-start",children:a.jsx("div",{className:"md:w-2/4",children:a.jsx(g.Z,{label:"Broker",items:H,value:L,setValue:Y})})})}),("broker"!==B.name&&""!==L||"broker"===B.name)&&a.jsx(w.Z,{color:"black",title:"Adicionar Assessor",children:(0,a.jsxs)("div",{children:[U.length>0?U?.map((e,s)=>a.jsxs("div",{className:"flex justify-between items-end gap-4 mb-4",children:[a.jsx("div",{className:"flex-1",children:a.jsx(g.Z,{label:"Selecione um Assessor",items:ee,value:"",setValue:()=>{},loading:es,handleChange:t=>{let a=U.filter(s=>s.generatedId===e.generatedId)[0],i={generatedId:a.generatedId,id:t.id,taxValue:Number(t.rate)};U[s]=i,O([...U])}})}),a.jsx("div",{className:"flex-1",children:a.jsx(b.Z,{label:"Adicione a Taxa - em %",id:"",name:"",value:String(e.taxValue),type:"text",onChange:s=>er(e.generatedId,s.target.value)})}),a.jsx("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=U.filter((e,t)=>t!==s);O(e)},children:a.jsx(j.Z,{width:20})})]},s)):a.jsx("div",{children:a.jsx("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),a.jsx("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:el,children:a.jsx(f.Z,{width:25,color:"#fff"})})]})}),a.jsx(w.Z,{color:"black",title:"Dados bancarios",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[a.jsx(l.Z,{register:z,name:"bank",width:"300px",error:!!Q.bank,errorMessage:Q?.bank?.message,label:"Banco"}),a.jsx(l.Z,{register:z,name:"agency",width:"200px",error:!!Q.agency,errorMessage:Q?.agency?.message,label:"Ag\xeancia"}),a.jsx(l.Z,{register:z,name:"accountNumber",width:"200px",error:!!Q.accountNumber,errorMessage:Q?.accountNumber?.message,label:"Conta"}),a.jsx(l.Z,{register:z,name:"pix",width:"250px",error:!!Q.pix,errorMessage:Q?.pix?.message,label:"Pix"})]})}),a.jsx(w.Z,{color:"black",title:"Anexo de documentos",children:a.jsx("div",{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato"}),a.jsx(F.Z,{onFileUploaded:k})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante"}),a.jsx("div",{children:a.jsx(F.Z,{onFileUploaded:A})})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx("div",{children:a.jsx(F.Z,{onFileUploaded:V})})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx("div",{children:a.jsx(F.Z,{onFileUploaded:q})})]})]})})}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",size:"lg",loading:ei.isPending,disabled:ei.isPending||!X||!v||!T||!I||!R||!et})})]})})}function CreateInvestor(){let[e,s]=(0,i.useState)("pf"),[t,n]=(0,i.useState)("MUTUO");return(0,a.jsxs)("div",{className:"m-3",children:[a.jsx("p",{className:"text-xl text-white mb-3",children:"Cadastro de contrato"}),(0,a.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"mb-5",children:[a.jsx("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:({target:e})=>s(e.value),children:[a.jsx("option",{value:"pf",children:"Pessoa F\xedsica"}),a.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),(0,a.jsxs)("div",{className:"mb-5",children:[a.jsx("p",{className:"text-white mb-1",children:"Tipo de contrato"}),(0,a.jsxs)(k.Z,{value:t,onChange:({target:e})=>n("MUTUO"===e.value?"MUTUO":"SCP"),children:[a.jsx("option",{value:"MUTUO",children:"M\xfatuo"}),a.jsx("option",{value:"SCP",children:"SCP"})]})]})]}),"pj"===e?a.jsx(BusinessRegister,{modalityContract:t}):a.jsx(PhysicalRegister,{modalityContract:t})]})}var T=t(88879),A=t(70322),I=t(22434),V=t(93543);function BrokerRegisterPf({typeCreate:e,hide:s,adminId:t}){let[n,r]=(0,i.useState)("");(0,S.e)();let[l,x]=(0,i.useState)(!1),[g,j]=(0,i.useState)(""),[f,w]=(0,i.useState)(""),[N,v]=(0,i.useState)(),[y,C]=(0,i.useState)(""),[P,M]=(0,i.useState)(),[R,q]=(0,i.useState)(),[L,Y]=(0,i.useState)(""),[U,O]=(0,i.useState)(""),[B,z]=(0,i.useState)(""),[_,K]=(0,i.useState)({document:!1,residence:!1,parcer:!1}),{register:G,handleSubmit:W,watch:Q,setValue:X,reset:J,formState:{errors:H,isValid:ee},getValues:es}=(0,o.cI)({resolver:(0,d.X)(T.WF),mode:"all"}),et=(0,D.D)({mutationFn:async s=>{let t=await h.Z.post(`/create-wallets/${e}`,s);return t.data},onSuccess:e=>{u.Am.success("Acesso cadastrado com sucesso!"),x(!0),j(e.id),er(e.id),w(""),J(),v(void 0),M(void 0),q(void 0)},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o Broker")}}),ea=(0,D.D)({mutationFn:async({id:e,type:s,file:t})=>{let a=new FormData;return a.append("id",e),a.append("type",s),a.append("file",t),await h.Z.post("/uploads/documents",a)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer"}[s.type.toLowerCase()];t&&ei(t)},onError:e=>{(0,p.Z)(e,"Erro ao enviar o documento")}}),ei=(0,i.useCallback)(e=>{K(s=>({...s,[e]:!0}))},[]),en=(0,i.useMemo)(()=>{if("broker"===e&&"pf"===y)return{document:!0,card:!1,mei:!1,residence:!0,social:!1,parcer:!s}},[e,y,s]),er=(0,i.useCallback)(e=>{let s=[{file:N?.[0],type:"RG",title:"Documento de identidade",required:en?.document},{file:P?.[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:en?.residence},{file:R?.[0],type:"CONTRACT",title:"Contrato de parceria",required:en?.parcer}].filter(e=>e.required&&e.file);u.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"}),s.forEach((t,a)=>{ea.mutateAsync({id:e,type:t.type,file:t.file}).then(()=>{a===s.length-1&&u.Am.dismiss("sendDocuments")}).catch(()=>{u.Am.dismiss("sendDocuments")})})},[N,P,R,en,ea]);(0,Z.a)({queryKey:["address",Q("ownerCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(Q("ownerCep")));e&&(X("ownerCity",e.city,{shouldValidate:!0}),X("ownerState",e.state,{shouldValidate:!0}),X("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),X("ownerStreet",e.street,{shouldValidate:!0}))},enabled:Q("ownerCep")?.length===9});let el=(0,i.useCallback)(async e=>{if(l)return er(g);if(!(0,I.p)((0,c.p4)(String(e.cpf||""))))return u.Am.warn("CPF do investidor inv\xe1lido!");if(!(0,V.Z)(e.phoneNumber))return u.Am.warn("N\xfamero de telefone inv\xe1lido");if(!(0,A.Z)(e.ownerState))return u.Am.warn("Estado inv\xe1lido");let s={birthDate:e.birthDate,socialName:(0,E.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,c.p4)(String(e.cpf||"")),email:e.email,phoneNumber:`55${(0,c.p4)(e.phoneNumber||"")}`,motherName:(0,E.Z)(e.motherName),pep:"s"===e.pep,password:""!==f?f:void 0,address:{cep:(0,c.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},a={adminId:t,create:{accountType:"PHYSICAL",owner:s},partPercent:e.participationPercentage};await et.mutateAsync(a)},[l,g,f,er,et]);return(0,i.useEffect)(()=>{X("isPf",!0)},[X]),(0,i.useEffect)(()=>{console.log("document",N),console.log("residence",P),console.log("parcer",R),console.log("isValid",ee),console.log("errors",H),console.log("form values",Q())},[N,P,R,ee,H,Q]),a.jsx("div",{className:"md:px-5",children:a.jsx("form",{onSubmit:W(el),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.fullName&&`- ${H.fullName.message}`})]}),a.jsx("input",{...G("fullName"),className:`h-12 w-full px-4 text-white rounded-xl ${H.fullName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.cpf&&`- ${H.cpf.message}`})]}),a.jsx("input",{...G("cpf"),onChange:({target:e})=>{let s=e.value;X("cpf",(0,c.VL)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${H.cpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.isTaxable&&`- ${H.isTaxable.message}`})]}),(0,a.jsxs)(k.Z,{value:Q("isTaxable"),onChange:e=>X("isTaxable",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.birthDate&&`- ${H.birthDate.message}`})]}),a.jsx("input",{...G("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${H.birthDate?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.phoneNumber&&`- ${H.phoneNumber.message}`})]}),a.jsx("input",{...G("phoneNumber"),maxLength:15,onChange:({target:e})=>X("phoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${H.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.email&&`- ${H.email.message}`})]}),a.jsx("input",{...G("email"),className:`h-12 w-full px-4 text-white rounded-xl ${H.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.participationPercentage&&`- ${H.participationPercentage.message}`})]}),a.jsx("input",{...G("participationPercentage"),className:`h-12 w-full px-4 text-white rounded-xl ${H.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.motherName&&`- ${H.motherName.message}`})]}),a.jsx("input",{...G("motherName"),className:`h-12 w-full px-4 text-white rounded-xl ${H.motherName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:a.jsx("div",{children:a.jsx(b.Z,{id:"",label:"Senha",type:"password",value:f,onChange:e=>w(e.target.value),name:"password"})})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.pep&&`- ${H.pep.message}`})]}),(0,a.jsxs)(k.Z,{value:Q("pep"),onChange:e=>X("pep",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.ownerCep&&`- ${H.ownerCep.message}`})]}),a.jsx("input",{...G("ownerCep"),onChange:({target:e})=>X("ownerCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${H.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.ownerCity&&`- ${H.ownerCity.message}`})]}),a.jsx("input",{...G("ownerCity"),className:`h-12 w-full px-4 text-white rounded-xl ${H.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.ownerState&&`- ${H.ownerState.message}`})]}),a.jsx("input",{...G("ownerState"),maxLength:2,onChange:({target:e})=>X("ownerState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${H.ownerState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.ownerNeighborhood&&`- ${H.ownerNeighborhood.message}`})]}),a.jsx("input",{...G("ownerNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${H.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.ownerNumber&&`- ${H.ownerNumber.message}`})]}),a.jsx("input",{...G("ownerNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${H.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.ownerStreet&&`- ${H.ownerStreet.message}`})]}),a.jsx("input",{...G("ownerStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${H.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:H.ownerComplement&&`- ${H.ownerComplement.message}`})]}),a.jsx("input",{...G("ownerComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${H.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx(F.Z,{onFileUploaded:v,fileName:L,onRemoveFile:()=>{v(void 0),Y("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx(F.Z,{onFileUploaded:M,fileName:U,onRemoveFile:()=>{M(void 0),O("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato de parceria"}),a.jsx(F.Z,{onFileUploaded:q,fileName:B,onRemoveFile:()=>{q(void 0),z("")}})]})]}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",loading:et.isPending,disabled:et.isPending||!ee||!N||!P||!R})})]})})})}function BrokerRegisterPj({typeCreate:e,hide:s,adminId:t}){let[n,r]=(0,i.useState)(""),[l,x]=(0,i.useState)(),[g,j]=(0,i.useState)(),[f,w]=(0,i.useState)(),[N,v]=(0,i.useState)(),[C,P]=(0,i.useState)(),[M,A]=(0,i.useState)(),[I,V]=(0,i.useState)(!1),[R,q]=(0,i.useState)(""),[L,Y]=(0,i.useState)(""),[U,O]=(0,i.useState)(""),[B,z]=(0,i.useState)(""),[_,K]=(0,i.useState)(""),[G,W]=(0,i.useState)(""),[Q,X]=(0,i.useState)(""),[J,H]=(0,i.useState)({document:!1,card:!1,mei:!1,residence:!1,social:!1,parcer:!1}),[ee,es]=(0,i.useState)("");(0,S.e)();let{register:et,handleSubmit:ea,watch:ei,setValue:en,reset:er,formState:{errors:el,isValid:ed}}=(0,o.cI)({resolver:(0,d.X)(T.WF),mode:"all"}),eo=ei("type"),em=(0,D.D)({mutationFn:async s=>{let t=await h.Z.post(`/create-wallets/${e}`,s);return t.data},onSuccess:e=>{u.Am.success("Acesso cadastrado com sucesso!"),V(!0),q(e.id),ep(e.id),r(""),er()},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o Broker")}}),ec=(0,D.D)({mutationFn:async({id:e,type:s,file:t})=>{let a=new FormData;return a.append("id",e),a.append("type",s),a.append("file",t),await h.Z.post("/uploads/documents",a)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer",card_cnpj:"card",social_contract:"social",mei:"mei"}[s.type.toLowerCase()];t&&ex(t)},onError:e=>{(0,p.Z)(e,"Erro ao enviar o documento")}}),ex=(0,i.useCallback)(e=>{H(s=>({...s,[e]:!0}))},[]),eh=(0,i.useMemo)(()=>{if("pj"===ee)return{document:!0,card:!0,mei:"MEI"===eo,residence:!0,social:!0,parcer:!s}},[e,ee,eo,s]),ep=(0,i.useCallback)(e=>{let s=[{file:l?.[0],type:"RG",title:"Documento de identidade",required:eh?.document},{file:N?.[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:eh?.residence},{file:M?.[0],type:"CONTRACT",title:"Contrato de parceria",required:eh?.parcer}].filter(e=>e.required&&e.file);u.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{u.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{ec.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),u.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),u.Am.dismiss("sendDocuments")})})},[l,N,M,eh,ec]),eu=(0,i.useCallback)(async e=>{if(I)return ep(R);if(!t)return u.Am.warn("Selecione o gestor de carteira para vincular o broker.");let s={birthDate:e.birthDate,socialName:(0,E.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,c.p4)(String(e.cpf||"")),email:e.email,phoneNumber:`55${(0,c.p4)(e.phoneNumber||"")}`,motherName:(0,E.Z)(e.motherName),pep:"s"===e.pep,password:""!==n?n:void 0,address:{cep:(0,c.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},a={fantasyName:(0,E.Z)(e.fantasyName||""),cnpj:(0,c.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:`55${(0,c.p4)(e.businessPhoneNumber||"")}`,isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,c.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}},i={adminId:t,create:{accountType:"BUSINESS",owner:s,business:a},partPercent:e.participationPercentage};await em.mutateAsync(i)},[I,R,t,n,ep,em]);return(0,Z.a)({queryKey:["address",ei("ownerCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(ei("ownerCep")));e&&(en("ownerCity",e.city,{shouldValidate:!0}),en("ownerState",e.state,{shouldValidate:!0}),en("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),en("ownerStreet",e.street,{shouldValidate:!0}))},enabled:ei("ownerCep")?.length===9}),(0,Z.a)({queryKey:["address",ei("businessCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(ei("businessCep")??""));e&&(en("businessCity",e.city,{shouldValidate:!0}),en("businessState",e.state,{shouldValidate:!0}),en("businessNeighborhood",e.neighborhood,{shouldValidate:!0}),en("businessStreet",e.street,{shouldValidate:!0}))}}),a.jsx("div",{className:"md:px-5",children:a.jsx("form",{onSubmit:ea(eu),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.fullName&&`- ${el.fullName.message}`})]}),a.jsx("input",{...et("fullName"),className:`h-12 w-full px-4 text-white rounded-xl ${el.fullName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.cpf&&`- ${el.cpf.message}`})]}),a.jsx("input",{...et("cpf"),onChange:({target:e})=>{let s=e.value;en("cpf",(0,c.VL)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${el.cpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.isTaxable&&`- ${el.isTaxable.message}`})]}),(0,a.jsxs)(k.Z,{value:ei("isTaxable"),onChange:e=>en("isTaxable",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.birthDate&&`- ${el.birthDate.message}`})]}),a.jsx("input",{...et("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${el.birthDate?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.phoneNumber&&`- ${el.phoneNumber.message}`})]}),a.jsx("input",{...et("phoneNumber"),maxLength:15,onChange:({target:e})=>en("phoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${el.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.email&&`- ${el.email.message}`})]}),a.jsx("input",{...et("email"),className:`h-12 w-full px-4 text-white rounded-xl ${el.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),!s&&a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.participationPercentage&&`- ${el.participationPercentage.message}`})]}),a.jsx("input",{...et("participationPercentage"),className:`h-12 w-full px-4 text-white rounded-xl ${el.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.motherName&&`- ${el.motherName.message}`})]}),a.jsx("input",{...et("motherName"),className:`h-12 w-full px-4 text-white rounded-xl ${el.motherName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:a.jsx("div",{children:a.jsx(b.Z,{id:"",label:"Senha",type:"password",value:n,onChange:e=>r(e.target.value),name:"password"})})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.pep&&`- ${el.pep.message}`})]}),(0,a.jsxs)(k.Z,{value:ei("pep"),onChange:e=>en("pep",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.ownerCep&&`- ${el.ownerCep.message}`})]}),a.jsx("input",{...et("ownerCep"),onChange:({target:e})=>en("ownerCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${el.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.ownerCity&&`- ${el.ownerCity.message}`})]}),a.jsx("input",{...et("ownerCity"),className:`h-12 w-full px-4 text-white rounded-xl ${el.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.ownerState&&`- ${el.ownerState.message}`})]}),a.jsx("input",{...et("ownerState"),maxLength:2,onChange:({target:e})=>en("ownerState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${el.ownerState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.ownerNeighborhood&&`- ${el.ownerNeighborhood.message}`})]}),a.jsx("input",{...et("ownerNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${el.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.ownerNumber&&`- ${el.ownerNumber.message}`})]}),a.jsx("input",{...et("ownerNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${el.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.ownerStreet&&`- ${el.ownerStreet.message}`})]}),a.jsx("input",{...et("ownerStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${el.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.ownerComplement&&`- ${el.ownerComplement.message}`})]}),a.jsx("input",{...et("ownerComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${el.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados da Empresa"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Fantasia"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.fantasyName&&`- ${el.fantasyName.message}`})]}),a.jsx("input",{...et("fantasyName"),className:`h-12 w-full px-4 text-white rounded-xl ${el.fantasyName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CNPJ"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.cnpj&&`- ${el.cnpj.message}`})]}),a.jsx("input",{...et("cnpj"),onChange:({target:e})=>{let s=e.value;en("cnpj",(0,c.PK)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${el.cnpj?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Abertura"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.dtOpening&&`- ${el.dtOpening.message}`})]}),a.jsx("input",{...et("dtOpening"),type:"date",max:y().utc().format("YYYY-MM-DD"),className:`h-12 w-full px-4 text-white rounded-xl ${el.dtOpening?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessPhoneNumber&&`- ${el.businessPhoneNumber.message}`})]}),a.jsx("input",{...et("businessPhoneNumber"),maxLength:15,onChange:({target:e})=>en("businessPhoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessPhoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessEmail&&`- ${el.businessEmail.message}`})]}),a.jsx("input",{...et("businessEmail"),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessEmail?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da Companhia"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.companyName&&`- ${el.companyName.message}`})]}),a.jsx("input",{...et("companyName"),className:`h-12 w-full px-4 text-white rounded-xl ${el.companyName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tipo",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.type&&`- ${el.type.message}`})]}),(0,a.jsxs)(k.Z,{value:ei("type"),onChange:e=>en("type",e.target.value),children:[a.jsx("option",{value:"",children:"Selecione o tipo"}),a.jsx("option",{value:"MEI",children:"MEI"}),a.jsx("option",{value:"EI",children:"EI"}),a.jsx("option",{value:"EIRELI",children:"EIRELI"}),a.jsx("option",{value:"SLU",children:"SLU"}),a.jsx("option",{value:"LTDA",children:"LTDA"}),a.jsx("option",{value:"SA",children:"SA"}),a.jsx("option",{value:"TS",children:"TS"})]})]}),(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tamanho",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.size&&`- ${el.size.message}`})]}),(0,a.jsxs)(k.Z,{value:ei("size"),onChange:e=>en("size",e.target.value),children:[a.jsx("option",{value:"",children:"Selecione o tamanho"}),a.jsx("option",{value:"MEI",children:"MEI"}),a.jsx("option",{value:"ME",children:"ME"}),a.jsx("option",{value:"EPP",children:"EPP"}),a.jsx("option",{value:"SMALL",children:"SMALL"}),a.jsx("option",{value:"MEDIUM",children:"MEDIUM"}),a.jsx("option",{value:"LARGE",children:"LARGE"})]})]})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessCep&&`- ${el.businessCep.message}`})]}),a.jsx("input",{...et("businessCep"),onChange:({target:e})=>en("businessCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessState&&`- ${el.businessState.message}`})]}),a.jsx("input",{...et("businessState"),maxLength:2,onChange:({target:e})=>en("businessState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessCity&&`- ${el.businessCity.message}`})]}),a.jsx("input",{...et("businessCity"),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessNeighborhood&&`- ${el.businessNeighborhood.message}`})]}),a.jsx("input",{...et("businessNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessNumber&&`- ${el.businessNumber.message}`})]}),a.jsx("input",{...et("businessNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessStreet&&`- ${el.businessStreet.message}`})]}),a.jsx("input",{...et("businessStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:el.businessComplement&&`- ${el.businessComplement.message}`})]}),a.jsx("input",{...et("businessComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${el.businessComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx(F.Z,{onFileUploaded:x,fileName:L,onRemoveFile:()=>{x(void 0),Y("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx(F.Z,{onFileUploaded:v,fileName:U,onRemoveFile:()=>{v(void 0),O("")}})]}),!s&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato de parceria"}),a.jsx(F.Z,{onFileUploaded:A,fileName:B,onRemoveFile:()=>{A(void 0),z("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Cart\xe3o cnpj"}),a.jsx(F.Z,{onFileUploaded:j,fileName:_,onRemoveFile:()=>{j(void 0),K("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato social"}),a.jsx(F.Z,{onFileUploaded:P,fileName:G,onRemoveFile:()=>{P(void 0),W("")}})]}),"MEI"===eo&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Certificado de mei"}),a.jsx(F.Z,{onFileUploaded:w,fileName:Q,onRemoveFile:()=>{w(void 0),X("")}})]})]}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",loading:em.isPending,disabled:em.isPending||!ed||!l||!N||!M||!g||!C||"MEI"===eo&&!f})})]})})})}function BrokerCreate(){let[e,s]=(0,i.useState)("pf"),[t,n]=(0,i.useState)(""),r=(0,S.e)(),{data:l=[]}=(0,Z.a)({queryKey:["admins"],queryFn:async()=>{let e=await h.Z.get("/wallets/list-admin");return e.data.map(e=>({...e,document:(0,c.PK)(e?.document||""),type:"admin"}))},enabled:"superadmin"===r.name});return(0,a.jsxs)("div",{children:[a.jsx("div",{className:"m-3",children:(0,a.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"mb-5",children:[a.jsx("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:({target:e})=>s(e.value),children:[a.jsx("option",{value:"pf",children:"Pessoa F\xedsica"}),a.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),a.jsx("div",{className:"md:w-3/4 mb-5",children:a.jsx(g.Z,{label:"Vincular ao gestor de carteiras",items:l,value:t,setValue:n})})]})}),"pj"===e?a.jsx(BrokerRegisterPj,{typeCreate:"broker",adminId:t}):a.jsx(BrokerRegisterPf,{typeCreate:"broker",adminId:t})]})}function AssessorRegisterPf({typeCreate:e,hide:s,brokerId:t}){let n=(0,S.e)(),[r,l]=(0,i.useState)(!1),[x,g]=(0,i.useState)(""),[j,f]=(0,i.useState)(""),[w,N]=(0,i.useState)(),[v,y]=(0,i.useState)(""),[C,P]=(0,i.useState)(),[M,R]=(0,i.useState)(),[q,L]=(0,i.useState)(""),[Y,U]=(0,i.useState)(""),[O,B]=(0,i.useState)(""),[z,_]=(0,i.useState)(""),[K,G]=(0,i.useState)({document:!1,residence:!1,parcer:!1}),{register:W,handleSubmit:Q,watch:X,setValue:J,reset:H,formState:{errors:ee,isValid:es},getValues:et}=(0,o.cI)({resolver:(0,d.X)(T.WF),mode:"all"}),{data:ea=[]}=(0,Z.a)({queryKey:["brokers",n.name],queryFn:async()=>{let e=await h.Z.get("superadmin"===n.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data},enabled:"advisor"!==n.name&&"broker"!==n.name}),ei=(0,D.D)({mutationFn:async e=>{let s=await h.Z.post("/create-wallets/advisor",e);return s.data},onSuccess:e=>{u.Am.success("Acesso cadastrado com sucesso!"),l(!0),g(e.id),ed(e.id),f(""),H(),N(void 0),P(void 0),R(void 0)},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o Broker")}}),en=(0,D.D)({mutationFn:async({id:e,type:s,file:t})=>{let a=new FormData;return a.append("id",e),a.append("type",s),a.append("file",t),await h.Z.post("/uploads/documents",a)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer"}[s.type.toLowerCase()];t&&er(t)},onError:e=>{(0,p.Z)(e,"Erro ao enviar o documento")}}),er=(0,i.useCallback)(e=>{G(s=>({...s,[e]:!0}))},[]),el=(0,i.useMemo)(()=>{if("advisor"===e&&"pf"===v)return{document:!0,card:!1,mei:!1,residence:!0,social:!1,parcer:!s}},[e,v,s]),ed=(0,i.useCallback)(e=>{let s=[{file:w?.[0],type:"RG",title:"Documento de identidade",required:el?.document},{file:C?.[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:el?.residence},{file:M?.[0],type:"CONTRACT",title:"Contrato de parceria",required:el?.parcer}].filter(e=>e.required&&e.file);u.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{u.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{en.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),u.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),u.Am.dismiss("sendDocuments")})})},[w,C,M,el,en]);(0,Z.a)({queryKey:["address",X("ownerCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(X("ownerCep")));e&&(J("ownerCity",e.city,{shouldValidate:!0}),J("ownerState",e.state,{shouldValidate:!0}),J("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),J("ownerStreet",e.street,{shouldValidate:!0}))},enabled:X("ownerCep")?.length===9});let eo=(0,i.useCallback)(async e=>{if(r)return ed(x);if(!(0,I.p)((0,c.p4)(String(e.cpf||""))))return u.Am.warn("CPF do investidor inv\xe1lido!");if(!(0,V.Z)(e.phoneNumber))return u.Am.warn("N\xfamero de telefone inv\xe1lido");if(!(0,A.Z)(e.ownerState))return u.Am.warn("Estado inv\xe1lido");let s={birthDate:e.birthDate,socialName:(0,E.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,c.p4)(String(e.cpf||"")),email:e.email,phoneNumber:`55${(0,c.p4)(e.phoneNumber||"")}`,motherName:(0,E.Z)(e.motherName),pep:"s"===e.pep,password:""!==j?j:void 0,address:{cep:(0,c.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},a={brokerId:t,create:{accountType:"PHYSICAL",owner:s},partPercent:e.participationPercentage};await ei.mutateAsync(a)},[r,x,j,ed,ei]);return(0,i.useEffect)(()=>{J("isPf",!0)},[J]),a.jsx("div",{className:"md:px-5",children:a.jsx("form",{onSubmit:Q(eo),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.fullName&&`- ${ee.fullName.message}`})]}),a.jsx("input",{...W("fullName"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.fullName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.cpf&&`- ${ee.cpf.message}`})]}),a.jsx("input",{...W("cpf"),onChange:({target:e})=>{let s=e.value;J("cpf",(0,c.VL)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${ee.cpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.isTaxable&&`- ${ee.isTaxable.message}`})]}),(0,a.jsxs)(k.Z,{value:X("isTaxable"),onChange:e=>J("isTaxable",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.birthDate&&`- ${ee.birthDate.message}`})]}),a.jsx("input",{...W("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${ee.birthDate?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.phoneNumber&&`- ${ee.phoneNumber.message}`})]}),a.jsx("input",{...W("phoneNumber"),maxLength:15,onChange:({target:e})=>J("phoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${ee.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.email&&`- ${ee.email.message}`})]}),a.jsx("input",{...W("email"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.participationPercentage&&`- ${ee.participationPercentage.message}`})]}),a.jsx("input",{...W("participationPercentage"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.motherName&&`- ${ee.motherName.message}`})]}),a.jsx("input",{...W("motherName"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.motherName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:a.jsx("div",{children:a.jsx(b.Z,{id:"",label:"Senha",type:"password",value:j,onChange:e=>f(e.target.value),name:"password"})})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.pep&&`- ${ee.pep.message}`})]}),(0,a.jsxs)(k.Z,{value:X("pep"),onChange:e=>J("pep",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerCep&&`- ${ee.ownerCep.message}`})]}),a.jsx("input",{...W("ownerCep"),onChange:({target:e})=>J("ownerCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerCity&&`- ${ee.ownerCity.message}`})]}),a.jsx("input",{...W("ownerCity"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerState&&`- ${ee.ownerState.message}`})]}),a.jsx("input",{...W("ownerState"),maxLength:2,onChange:({target:e})=>J("ownerState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerNeighborhood&&`- ${ee.ownerNeighborhood.message}`})]}),a.jsx("input",{...W("ownerNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerNumber&&`- ${ee.ownerNumber.message}`})]}),a.jsx("input",{...W("ownerNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerStreet&&`- ${ee.ownerStreet.message}`})]}),a.jsx("input",{...W("ownerStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerComplement&&`- ${ee.ownerComplement.message}`})]}),a.jsx("input",{...W("ownerComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx(F.Z,{onFileUploaded:N,fileName:q,onRemoveFile:()=>{N(void 0),L("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx(F.Z,{onFileUploaded:P,fileName:Y,onRemoveFile:()=>{P(void 0),U("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato de parceria"}),a.jsx(F.Z,{onFileUploaded:R,fileName:O,onRemoveFile:()=>{R(void 0),B("")}})]})]}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",loading:ei.isPending,disabled:ei.isPending||!es||!w||!C||!M})})]})})})}function AssessorRegisterPj({typeCreate:e,hide:s,brokerId:t}){let[n,r]=(0,i.useState)(""),[l,x]=(0,i.useState)(),[g,j]=(0,i.useState)(),[f,w]=(0,i.useState)(),[N,v]=(0,i.useState)(),[C,P]=(0,i.useState)(),[M,A]=(0,i.useState)(),[I,V]=(0,i.useState)(!1),[R,q]=(0,i.useState)(""),[L,Y]=(0,i.useState)(""),[U,O]=(0,i.useState)(""),[B,z]=(0,i.useState)(""),[_,K]=(0,i.useState)(""),[G,W]=(0,i.useState)(""),[Q,X]=(0,i.useState)(""),[J,H]=(0,i.useState)({document:!1,card:!1,mei:!1,residence:!1,social:!1,parcer:!1}),[ee,es]=(0,i.useState)(""),[et,ea]=(0,i.useState)(""),[ei,en]=(0,i.useState)("");(0,S.e)();let{register:er,handleSubmit:el,watch:ed,setValue:eo,reset:em,formState:{errors:ec,isValid:ex}}=(0,o.cI)({resolver:(0,d.X)(T.WF),mode:"all"}),eh=ed("type"),ep=(0,D.D)({mutationFn:async s=>{let t=await h.Z.post(`/create-wallets/${e}`,s);return t.data},onSuccess:e=>{u.Am.success("Acesso cadastrado com sucesso!"),V(!0),q(e.id),ej(e.id),r(""),em()},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o Broker")}}),eu=(0,D.D)({mutationFn:async({id:e,type:s,file:t})=>{let a=new FormData;return a.append("id",e),a.append("type",s),a.append("file",t),await h.Z.post("/uploads/documents",a)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer",card_cnpj:"card",social_contract:"social",mei:"mei"}[s.type.toLowerCase()];t&&eg(t)},onError:e=>{(0,p.Z)(e,"Erro ao enviar o documento")}}),eg=(0,i.useCallback)(e=>{H(s=>({...s,[e]:!0}))},[]),eb=(0,i.useMemo)(()=>{if("pj"===et)return{document:!0,card:!0,mei:"MEI"===eh,residence:!0,social:!0,parcer:!s}},[e,et,eh,s]),ej=(0,i.useCallback)(e=>{let s=[{file:l?.[0],type:"RG",title:"Documento de identidade",required:eb?.document},{file:N?.[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:eb?.residence},{file:M?.[0],type:"CONTRACT",title:"Contrato de parceria",required:eb?.parcer}].filter(e=>e.required&&e.file);u.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{u.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{eu.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),u.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),u.Am.dismiss("sendDocuments")})})},[l,N,M,eb,eu]),ef=(0,i.useCallback)(async e=>{if(I)return ej(R);if(!t)return u.Am.warn("Selecione o broker para vincular o assessor.");let s={birthDate:e.birthDate,socialName:(0,E.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,c.p4)(String(e.cpf||"")),email:e.email,phoneNumber:`55${(0,c.p4)(e.phoneNumber||"")}`,motherName:(0,E.Z)(e.motherName),pep:"s"===e.pep,password:""!==n?n:void 0,address:{cep:(0,c.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},a={fantasyName:(0,E.Z)(e.fantasyName||""),cnpj:(0,c.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:`55${(0,c.p4)(e.businessPhoneNumber||"")}`,isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,c.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}},i={brokerId:t,create:{accountType:"BUSINESS",owner:s,business:a},partPercent:e.participationPercentage};await ep.mutateAsync(i)},[I,R,t,n,ej,ep]);return(0,Z.a)({queryKey:["address",ed("ownerCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(ed("ownerCep")));e&&(eo("ownerCity",e.city,{shouldValidate:!0}),eo("ownerState",e.state,{shouldValidate:!0}),eo("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),eo("ownerStreet",e.street,{shouldValidate:!0}))},enabled:ed("ownerCep")?.length===9}),(0,Z.a)({queryKey:["address",ed("businessCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(ed("businessCep")??""));e&&(eo("businessCity",e.city,{shouldValidate:!0}),eo("businessState",e.state,{shouldValidate:!0}),eo("businessNeighborhood",e.neighborhood,{shouldValidate:!0}),eo("businessStreet",e.street,{shouldValidate:!0}))}}),a.jsx("div",{className:"md:px-5",children:a.jsx("form",{onSubmit:el(ef),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.fullName&&`- ${ec.fullName.message}`})]}),a.jsx("input",{...er("fullName"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.fullName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.cpf&&`- ${ec.cpf.message}`})]}),a.jsx("input",{...er("cpf"),onChange:({target:e})=>{let s=e.value;eo("cpf",(0,c.VL)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${ec.cpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.isTaxable&&`- ${ec.isTaxable.message}`})]}),(0,a.jsxs)(k.Z,{value:ed("isTaxable"),onChange:e=>eo("isTaxable",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.birthDate&&`- ${ec.birthDate.message}`})]}),a.jsx("input",{...er("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${ec.birthDate?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.phoneNumber&&`- ${ec.phoneNumber.message}`})]}),a.jsx("input",{...er("phoneNumber"),maxLength:15,onChange:({target:e})=>eo("phoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${ec.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.email&&`- ${ec.email.message}`})]}),a.jsx("input",{...er("email"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),!s&&a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.participationPercentage&&`- ${ec.participationPercentage.message}`})]}),a.jsx("input",{...er("participationPercentage"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.motherName&&`- ${ec.motherName.message}`})]}),a.jsx("input",{...er("motherName"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.motherName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:a.jsx("div",{children:a.jsx(b.Z,{id:"",label:"Senha",type:"password",value:n,onChange:e=>r(e.target.value),name:"password"})})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.pep&&`- ${ec.pep.message}`})]}),(0,a.jsxs)(k.Z,{value:ed("pep"),onChange:e=>eo("pep",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.ownerCep&&`- ${ec.ownerCep.message}`})]}),a.jsx("input",{...er("ownerCep"),onChange:({target:e})=>eo("ownerCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${ec.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.ownerCity&&`- ${ec.ownerCity.message}`})]}),a.jsx("input",{...er("ownerCity"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.ownerState&&`- ${ec.ownerState.message}`})]}),a.jsx("input",{...er("ownerState"),maxLength:2,onChange:({target:e})=>eo("ownerState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${ec.ownerState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.ownerNeighborhood&&`- ${ec.ownerNeighborhood.message}`})]}),a.jsx("input",{...er("ownerNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.ownerNumber&&`- ${ec.ownerNumber.message}`})]}),a.jsx("input",{...er("ownerNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.ownerStreet&&`- ${ec.ownerStreet.message}`})]}),a.jsx("input",{...er("ownerStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.ownerComplement&&`- ${ec.ownerComplement.message}`})]}),a.jsx("input",{...er("ownerComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados da Empresa"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Fantasia"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.fantasyName&&`- ${ec.fantasyName.message}`})]}),a.jsx("input",{...er("fantasyName"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.fantasyName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CNPJ"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.cnpj&&`- ${ec.cnpj.message}`})]}),a.jsx("input",{...er("cnpj"),onChange:({target:e})=>{let s=e.value;eo("cnpj",(0,c.PK)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${ec.cnpj?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Abertura"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.dtOpening&&`- ${ec.dtOpening.message}`})]}),a.jsx("input",{...er("dtOpening"),type:"date",max:y().utc().format("YYYY-MM-DD"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.dtOpening?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessPhoneNumber&&`- ${ec.businessPhoneNumber.message}`})]}),a.jsx("input",{...er("businessPhoneNumber"),maxLength:15,onChange:({target:e})=>eo("businessPhoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessPhoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessEmail&&`- ${ec.businessEmail.message}`})]}),a.jsx("input",{...er("businessEmail"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessEmail?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da Companhia"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.companyName&&`- ${ec.companyName.message}`})]}),a.jsx("input",{...er("companyName"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.companyName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tipo",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.type&&`- ${ec.type.message}`})]}),(0,a.jsxs)(k.Z,{value:ed("type"),onChange:e=>eo("type",e.target.value),children:[a.jsx("option",{value:"",children:"Selecione o tipo"}),a.jsx("option",{value:"MEI",children:"MEI"}),a.jsx("option",{value:"EI",children:"EI"}),a.jsx("option",{value:"EIRELI",children:"EIRELI"}),a.jsx("option",{value:"SLU",children:"SLU"}),a.jsx("option",{value:"LTDA",children:"LTDA"}),a.jsx("option",{value:"SA",children:"SA"}),a.jsx("option",{value:"TS",children:"TS"})]})]}),(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tamanho",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.size&&`- ${ec.size.message}`})]}),(0,a.jsxs)(k.Z,{value:ed("size"),onChange:e=>eo("size",e.target.value),children:[a.jsx("option",{value:"",children:"Selecione o tamanho"}),a.jsx("option",{value:"MEI",children:"MEI"}),a.jsx("option",{value:"ME",children:"ME"}),a.jsx("option",{value:"EPP",children:"EPP"}),a.jsx("option",{value:"SMALL",children:"SMALL"}),a.jsx("option",{value:"MEDIUM",children:"MEDIUM"}),a.jsx("option",{value:"LARGE",children:"LARGE"})]})]})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessCep&&`- ${ec.businessCep.message}`})]}),a.jsx("input",{...er("businessCep"),onChange:({target:e})=>eo("businessCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessState&&`- ${ec.businessState.message}`})]}),a.jsx("input",{...er("businessState"),maxLength:2,onChange:({target:e})=>eo("businessState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessCity&&`- ${ec.businessCity.message}`})]}),a.jsx("input",{...er("businessCity"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessNeighborhood&&`- ${ec.businessNeighborhood.message}`})]}),a.jsx("input",{...er("businessNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessNumber&&`- ${ec.businessNumber.message}`})]}),a.jsx("input",{...er("businessNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessStreet&&`- ${ec.businessStreet.message}`})]}),a.jsx("input",{...er("businessStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ec.businessComplement&&`- ${ec.businessComplement.message}`})]}),a.jsx("input",{...er("businessComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${ec.businessComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx(F.Z,{onFileUploaded:x,fileName:L,onRemoveFile:()=>{x(void 0),Y("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx(F.Z,{onFileUploaded:v,fileName:U,onRemoveFile:()=>{v(void 0),O("")}})]}),!s&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato de parceria"}),a.jsx(F.Z,{onFileUploaded:A,fileName:B,onRemoveFile:()=>{A(void 0),z("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Cart\xe3o cnpj"}),a.jsx(F.Z,{onFileUploaded:j,fileName:_,onRemoveFile:()=>{j(void 0),K("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato social"}),a.jsx(F.Z,{onFileUploaded:P,fileName:G,onRemoveFile:()=>{P(void 0),W("")}})]}),"MEI"===eh&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Certificado de mei"}),a.jsx(F.Z,{onFileUploaded:w,fileName:Q,onRemoveFile:()=>{w(void 0),X("")}})]})]}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",loading:ep.isPending,disabled:ep.isPending||!ex||!l||!N||!M||!g||!C||"MEI"===eh&&!f})})]})})})}let AssessorCreate=()=>{let[e,s]=(0,i.useState)("pf"),t=(0,S.e)();(0,S.P)();let[n,r]=(0,i.useState)("broker"===t.name?t.roleId:""),{data:l=[]}=(0,Z.a)({queryKey:["brokers",t.name],queryFn:async()=>{let e=await h.Z.get("superadmin"===t.name?"/wallets/list-brokers":"/wallets/admin/brokers");return e.data},enabled:"advisor"!==t.name&&"broker"!==t.name});return(0,a.jsxs)("div",{children:[a.jsx("div",{className:"m-3",children:(0,a.jsxs)("div",{className:" mb-5 flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"mb-5",children:[a.jsx("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:({target:e})=>s(e.value),children:[a.jsx("option",{value:"pf",children:"Pessoa F\xedsica"}),a.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]}),"superadmin"===t.name&&a.jsx("div",{className:"md:w-3/4 mb-5",children:a.jsx(g.Z,{label:"Vincular ao Broker",items:l,value:n,setValue:r})})]})}),"pj"===e?a.jsx(AssessorRegisterPj,{typeCreate:"advisor",brokerId:n}):a.jsx(AssessorRegisterPf,{typeCreate:"advisor",brokerId:n})]})};function AdminRegisterPj({typeCreate:e,hide:s}){let[t,n]=(0,i.useState)(""),[r,l]=(0,i.useState)(),[x,g]=(0,i.useState)(),[j,f]=(0,i.useState)(),[w,N]=(0,i.useState)(),[v,C]=(0,i.useState)(),[P,M]=(0,i.useState)(),[A,I]=(0,i.useState)(!1),[V,R]=(0,i.useState)(""),[q,L]=(0,i.useState)(""),[Y,U]=(0,i.useState)(""),[O,B]=(0,i.useState)(""),[z,_]=(0,i.useState)(""),[K,G]=(0,i.useState)(""),[W,Q]=(0,i.useState)(""),[X,J]=(0,i.useState)({document:!1,card:!1,mei:!1,residence:!1,social:!1,parcer:!1}),[H,ee]=(0,i.useState)(""),[es,et]=(0,i.useState)("");(0,S.e)();let{register:ea,handleSubmit:ei,watch:en,setValue:er,reset:el,formState:{errors:ed,isValid:eo}}=(0,o.cI)({resolver:(0,d.X)(T.WF),mode:"all"}),em=en("type"),ec=(0,D.D)({mutationFn:async s=>{let t=await h.Z.post(`/create-wallets/${e}`,s);return t.data},onSuccess:e=>{u.Am.success("Acesso cadastrado com sucesso!"),I(!0),R(e.id),eu(e.id),n(""),el()},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o Broker")}}),ex=(0,D.D)({mutationFn:async({id:e,type:s,file:t})=>{let a=new FormData;return a.append("id",e),a.append("type",s),a.append("file",t),await h.Z.post("/uploads/documents",a)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer",card_cnpj:"card",social_contract:"social",mei:"mei"}[s.type.toLowerCase()];t&&eh(t)},onError:e=>{(0,p.Z)(e,"Erro ao enviar o documento")}}),eh=(0,i.useCallback)(e=>{J(s=>({...s,[e]:!0}))},[]),ep=(0,i.useMemo)(()=>{if("pj"===H)return{document:!0,card:!0,mei:"MEI"===em,residence:!0,social:!0,parcer:!s}},[e,H,em,s]),eu=(0,i.useCallback)(e=>{let s=[{file:r?.[0],type:"RG",title:"Documento de identidade",required:ep?.document},{file:w?.[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:ep?.residence},{file:P?.[0],type:"CONTRACT",title:"Contrato de parceria",required:ep?.parcer}].filter(e=>e.required&&e.file);u.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{u.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{ex.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),u.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),u.Am.dismiss("sendDocuments")})})},[r,w,P,ep,ex]),eg=(0,i.useCallback)(async e=>{if(A)return eu(V);let s={birthDate:e.birthDate,socialName:(0,E.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,c.p4)(String(e.cpf||"")),email:e.email,phoneNumber:`55${(0,c.p4)(e.phoneNumber||"")}`,motherName:(0,E.Z)(e.motherName),pep:"s"===e.pep,password:""!==t?t:void 0,address:{cep:(0,c.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},a={fantasyName:(0,E.Z)(e.fantasyName||""),cnpj:(0,c.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:`55${(0,c.p4)(e.businessPhoneNumber||"")}`,isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,c.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}};await ec.mutateAsync({create:{accountType:"BUSINESS",owner:s,business:a}})},[A,V,t,eu,ec]);return(0,Z.a)({queryKey:["address",en("ownerCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(en("ownerCep")));e&&(er("ownerCity",e.city,{shouldValidate:!0}),er("ownerState",e.state,{shouldValidate:!0}),er("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),er("ownerStreet",e.street,{shouldValidate:!0}))},enabled:en("ownerCep")?.length===9}),(0,Z.a)({queryKey:["address",en("businessCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(en("businessCep")??""));e&&(er("businessCity",e.city,{shouldValidate:!0}),er("businessState",e.state,{shouldValidate:!0}),er("businessNeighborhood",e.neighborhood,{shouldValidate:!0}),er("businessStreet",e.street,{shouldValidate:!0}))}}),a.jsx("div",{className:"md:px-5",children:a.jsx("form",{onSubmit:ei(eg),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.fullName&&`- ${ed.fullName.message}`})]}),a.jsx("input",{...ea("fullName"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.fullName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.cpf&&`- ${ed.cpf.message}`})]}),a.jsx("input",{...ea("cpf"),onChange:({target:e})=>{let s=e.value;er("cpf",(0,c.VL)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${ed.cpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.isTaxable&&`- ${ed.isTaxable.message}`})]}),(0,a.jsxs)(k.Z,{value:en("isTaxable"),onChange:e=>er("isTaxable",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.birthDate&&`- ${ed.birthDate.message}`})]}),a.jsx("input",{...ea("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${ed.birthDate?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.phoneNumber&&`- ${ed.phoneNumber.message}`})]}),a.jsx("input",{...ea("phoneNumber"),maxLength:15,onChange:({target:e})=>er("phoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${ed.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.email&&`- ${ed.email.message}`})]}),a.jsx("input",{...ea("email"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),!s&&a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.participationPercentage&&`- ${ed.participationPercentage.message}`})]}),a.jsx("input",{...ea("participationPercentage"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.motherName&&`- ${ed.motherName.message}`})]}),a.jsx("input",{...ea("motherName"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.motherName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:a.jsx("div",{children:a.jsx(b.Z,{id:"",label:"Senha",type:"password",value:t,onChange:e=>n(e.target.value),name:"password"})})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.pep&&`- ${ed.pep.message}`})]}),(0,a.jsxs)(k.Z,{value:en("pep"),onChange:e=>er("pep",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.ownerCep&&`- ${ed.ownerCep.message}`})]}),a.jsx("input",{...ea("ownerCep"),onChange:({target:e})=>er("ownerCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${ed.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.ownerCity&&`- ${ed.ownerCity.message}`})]}),a.jsx("input",{...ea("ownerCity"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.ownerState&&`- ${ed.ownerState.message}`})]}),a.jsx("input",{...ea("ownerState"),maxLength:2,onChange:({target:e})=>er("ownerState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${ed.ownerState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.ownerNeighborhood&&`- ${ed.ownerNeighborhood.message}`})]}),a.jsx("input",{...ea("ownerNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.ownerNumber&&`- ${ed.ownerNumber.message}`})]}),a.jsx("input",{...ea("ownerNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.ownerStreet&&`- ${ed.ownerStreet.message}`})]}),a.jsx("input",{...ea("ownerStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.ownerComplement&&`- ${ed.ownerComplement.message}`})]}),a.jsx("input",{...ea("ownerComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados da Empresa"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Fantasia"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.fantasyName&&`- ${ed.fantasyName.message}`})]}),a.jsx("input",{...ea("fantasyName"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.fantasyName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CNPJ"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.cnpj&&`- ${ed.cnpj.message}`})]}),a.jsx("input",{...ea("cnpj"),onChange:({target:e})=>{let s=e.value;er("cnpj",(0,c.PK)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${ed.cnpj?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Abertura"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.dtOpening&&`- ${ed.dtOpening.message}`})]}),a.jsx("input",{...ea("dtOpening"),type:"date",max:y().utc().format("YYYY-MM-DD"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.dtOpening?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessPhoneNumber&&`- ${ed.businessPhoneNumber.message}`})]}),a.jsx("input",{...ea("businessPhoneNumber"),maxLength:15,onChange:({target:e})=>er("businessPhoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessPhoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessEmail&&`- ${ed.businessEmail.message}`})]}),a.jsx("input",{...ea("businessEmail"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessEmail?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da Companhia"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.companyName&&`- ${ed.companyName.message}`})]}),a.jsx("input",{...ea("companyName"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.companyName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tipo",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.type&&`- ${ed.type.message}`})]}),(0,a.jsxs)(k.Z,{value:en("type"),onChange:e=>er("type",e.target.value),children:[a.jsx("option",{value:"",children:"Selecione o tipo"}),a.jsx("option",{value:"MEI",children:"MEI"}),a.jsx("option",{value:"EI",children:"EI"}),a.jsx("option",{value:"EIRELI",children:"EIRELI"}),a.jsx("option",{value:"SLU",children:"SLU"}),a.jsx("option",{value:"LTDA",children:"LTDA"}),a.jsx("option",{value:"SA",children:"SA"}),a.jsx("option",{value:"TS",children:"TS"})]})]}),(0,a.jsxs)("div",{className:"md:w-1/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tamanho",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.size&&`- ${ed.size.message}`})]}),(0,a.jsxs)(k.Z,{value:en("size"),onChange:e=>er("size",e.target.value),children:[a.jsx("option",{value:"",children:"Selecione o tamanho"}),a.jsx("option",{value:"MEI",children:"MEI"}),a.jsx("option",{value:"ME",children:"ME"}),a.jsx("option",{value:"EPP",children:"EPP"}),a.jsx("option",{value:"SMALL",children:"SMALL"}),a.jsx("option",{value:"MEDIUM",children:"MEDIUM"}),a.jsx("option",{value:"LARGE",children:"LARGE"})]})]})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessCep&&`- ${ed.businessCep.message}`})]}),a.jsx("input",{...ea("businessCep"),onChange:({target:e})=>er("businessCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessState&&`- ${ed.businessState.message}`})]}),a.jsx("input",{...ea("businessState"),maxLength:2,onChange:({target:e})=>er("businessState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessCity&&`- ${ed.businessCity.message}`})]}),a.jsx("input",{...ea("businessCity"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessNeighborhood&&`- ${ed.businessNeighborhood.message}`})]}),a.jsx("input",{...ea("businessNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessNumber&&`- ${ed.businessNumber.message}`})]}),a.jsx("input",{...ea("businessNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessStreet&&`- ${ed.businessStreet.message}`})]}),a.jsx("input",{...ea("businessStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ed.businessComplement&&`- ${ed.businessComplement.message}`})]}),a.jsx("input",{...ea("businessComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${ed.businessComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx(F.Z,{onFileUploaded:l,fileName:q,onRemoveFile:()=>{l(void 0),L("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx(F.Z,{onFileUploaded:N,fileName:Y,onRemoveFile:()=>{N(void 0),U("")}})]}),!s&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato de parceria"}),a.jsx(F.Z,{onFileUploaded:M,fileName:O,onRemoveFile:()=>{M(void 0),B("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Cart\xe3o cnpj"}),a.jsx(F.Z,{onFileUploaded:g,fileName:z,onRemoveFile:()=>{g(void 0),_("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato social"}),a.jsx(F.Z,{onFileUploaded:C,fileName:K,onRemoveFile:()=>{C(void 0),G("")}})]}),"MEI"===em&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Certificado de mei"}),a.jsx(F.Z,{onFileUploaded:f,fileName:W,onRemoveFile:()=>{f(void 0),Q("")}})]})]}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",loading:ec.isPending,disabled:ec.isPending||!eo||!r||!w||!P||!x||!v||"MEI"===em&&!j})})]})})})}function AdminRegisterPf({typeCreate:e,hide:s}){let[t,n]=(0,i.useState)("");(0,S.e)();let[r,l]=(0,i.useState)(!1),[x,g]=(0,i.useState)(""),[j,f]=(0,i.useState)(""),[w,N]=(0,i.useState)(),[v,y]=(0,i.useState)(""),[C,P]=(0,i.useState)(),[M,R]=(0,i.useState)(),[q,L]=(0,i.useState)(""),[Y,U]=(0,i.useState)(""),[O,B]=(0,i.useState)(""),[z,_]=(0,i.useState)(""),[K,G]=(0,i.useState)({document:!1,residence:!1,parcer:!1}),{register:W,handleSubmit:Q,watch:X,setValue:J,reset:H,formState:{errors:ee,isValid:es},getValues:et}=(0,o.cI)({resolver:(0,d.X)(T.WF),mode:"all"}),ea=(0,D.D)({mutationFn:async s=>{let t=await h.Z.post(`/create-wallets/${e}`,s);return t.data},onSuccess:e=>{u.Am.success("Broker cadastrado com sucesso!"),l(!0),g(e.id),el(e.id),f(""),H(),N(void 0),P(void 0),R(void 0)},onError:e=>{(0,p.Z)(e,"Erro ao cadastrar o Broker")}}),ei=(0,D.D)({mutationFn:async({id:e,type:s,file:t})=>{let a=new FormData;return a.append("id",e),a.append("type",s),a.append("file",t),await h.Z.post("/uploads/documents",a)},onSuccess:(e,s)=>{let t={rg:"document",proof_residence:"residence",contract:"parcer"}[s.type.toLowerCase()];t&&en(t)},onError:e=>{(0,p.Z)(e,"Erro ao enviar o documento")}}),en=(0,i.useCallback)(e=>{G(s=>({...s,[e]:!0}))},[]),er=(0,i.useMemo)(()=>{if("broker"===e&&"pf"===v)return{document:!0,card:!1,mei:!1,residence:!0,social:!1,parcer:!s}},[e,v,s]),el=(0,i.useCallback)(e=>{let s=[{file:w?.[0],type:"RG",title:"Documento de identidade",required:er?.document},{file:C?.[0],type:"PROOF_RESIDENCE",title:"Comprovante de resid\xeancia",required:er?.residence},{file:M?.[0],type:"CONTRACT",title:"Contrato de parceria",required:er?.parcer}].filter(e=>e.required&&e.file);u.Am.info("Enviando documentos anexados...",{autoClose:!1,toastId:"sendDocuments"});let t=setTimeout(()=>{u.Am.dismiss("sendDocuments")},3e4);s.forEach((a,i)=>{ei.mutateAsync({id:e,type:a.type,file:a.file}).then(()=>{i===s.length-1&&(clearTimeout(t),u.Am.dismiss("sendDocuments"))}).catch(()=>{clearTimeout(t),u.Am.dismiss("sendDocuments")})})},[w,C,M,er,ei]);(0,Z.a)({queryKey:["address",X("ownerCep")],queryFn:async()=>{let e=await (0,$.x)((0,c.p4)(X("ownerCep")));e&&(J("ownerCity",e.city,{shouldValidate:!0}),J("ownerState",e.state,{shouldValidate:!0}),J("ownerNeighborhood",e.neighborhood,{shouldValidate:!0}),J("ownerStreet",e.street,{shouldValidate:!0}))},enabled:X("ownerCep")?.length===9});let ed=(0,i.useCallback)(async e=>{if(r)return el(x);if(!(0,I.p)((0,c.p4)(String(e.cpf||""))))return u.Am.warn("CPF do investidor inv\xe1lido!");if(!(0,V.Z)(e.phoneNumber))return u.Am.warn("N\xfamero de telefone inv\xe1lido");if(!(0,A.Z)(e.ownerState))return u.Am.warn("Estado inv\xe1lido");let s={birthDate:e.birthDate,socialName:(0,E.Z)(e.fullName),isTaxable:"s"===e.isTaxable,fullName:e.fullName,cpf:(0,c.p4)(String(e.cpf||"")),email:e.email,phoneNumber:`55${(0,c.p4)(e.phoneNumber||"")}`,motherName:(0,E.Z)(e.motherName),pep:"s"===e.pep,password:""!==j?j:void 0,address:{cep:(0,c.p4)(e.ownerCep||""),city:e.ownerCity,state:e.ownerState,neighborhood:e.ownerNeighborhood,street:e.ownerStreet,complement:e.ownerComplement,number:e.ownerNumber}},t={fantasyName:(0,E.Z)(e.fantasyName||""),cnpj:(0,c.p4)(String(e.cnpj||"")),companyName:e.companyName,phoneNumber:`55${(0,c.p4)(e.businessPhoneNumber||"")}`,isTaxable:"s"===e.isTaxable,dtOpening:e.dtOpening,email:e.businessEmail,type:e.type,size:e.size,address:{cep:(0,c.p4)(e.businessCep||""),city:e.businessCity,state:e.businessState,neighborhood:e.businessNeighborhood,street:e.businessStreet,complement:e.businessComplement,number:e.businessNumber}};await ea.mutateAsync({create:{accountType:"BUSINESS",owner:s,business:t}})},[r,x,j,el,ea]);return(0,i.useEffect)(()=>{J("isPf",!0)},[J]),a.jsx("div",{className:"md:px-5",children:a.jsx("form",{onSubmit:Q(ed),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:(0,a.jsxs)("div",{className:"m-auto",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-lg text-white font-bold",children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"mb-10 m-auto",children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-2",children:[(0,a.jsxs)("div",{className:"md:w-2/4",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome Completo"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.fullName&&`- ${ee.fullName.message}`})]}),a.jsx("input",{...W("fullName"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.fullName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.cpf&&`- ${ee.cpf.message}`})]}),a.jsx("input",{...W("cpf"),onChange:({target:e})=>{let s=e.value;J("cpf",(0,c.VL)(s))},className:`h-12 w-full px-4 text-white rounded-xl ${ee.cpf?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Emitir Taxa",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.isTaxable&&`- ${ee.isTaxable.message}`})]}),(0,a.jsxs)(k.Z,{value:X("isTaxable"),onChange:e=>J("isTaxable",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.birthDate&&`- ${ee.birthDate.message}`})]}),a.jsx("input",{...W("birthDate"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let s=e.target;s.value.length>10&&(s.value=s.value.slice(0,10))},className:`h-12 w-full px-4 text-white rounded-xl ${ee.birthDate?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Telefone"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.phoneNumber&&`- ${ee.phoneNumber.message}`})]}),a.jsx("input",{...W("phoneNumber"),maxLength:15,onChange:({target:e})=>J("phoneNumber",String((0,c.gP)(e.value))),className:`h-12 w-full px-4 text-white rounded-xl ${ee.phoneNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.email&&`- ${ee.email.message}`})]}),a.jsx("input",{...W("email"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de participa\xe7\xe3o em %",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.participationPercentage&&`- ${ee.participationPercentage.message}`})]}),a.jsx("input",{...W("participationPercentage"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.participationPercentage?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Nome da m\xe3e"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.motherName&&`- ${ee.motherName.message}`})]}),a.jsx("input",{...W("motherName"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.motherName?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:a.jsx("div",{children:a.jsx(b.Z,{id:"",label:"Senha",type:"password",value:j,onChange:e=>f(e.target.value),name:"password"})})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Pessoa politicamente exposta?"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.pep&&`- ${ee.pep.message}`})]}),(0,a.jsxs)(k.Z,{value:X("pep"),onChange:e=>J("pep",e.target.value),children:[a.jsx("option",{disabled:!0,selected:!0,children:"Selecione"}),a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CEP"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerCep&&`- ${ee.ownerCep.message}`})]}),a.jsx("input",{...W("ownerCep"),onChange:({target:e})=>J("ownerCep",(0,c.Tc)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerCep?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Cidade"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerCity&&`- ${ee.ownerCity.message}`})]}),a.jsx("input",{...W("ownerCity"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerCity?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Estado"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerState&&`- ${ee.ownerState.message}`})]}),a.jsx("input",{...W("ownerState"),maxLength:2,onChange:({target:e})=>J("ownerState",e.value.toUpperCase()),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerState?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Bairro"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerNeighborhood&&`- ${ee.ownerNeighborhood.message}`})]}),a.jsx("input",{...W("ownerNeighborhood"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerNeighborhood?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["N\xfamero"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerNumber&&`- ${ee.ownerNumber.message}`})]}),a.jsx("input",{...W("ownerNumber"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerNumber?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Rua"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerStreet&&`- ${ee.ownerStreet.message}`})]}),a.jsx("input",{...W("ownerStreet"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerStreet?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Complemento"," ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:ee.ownerComplement&&`- ${ee.ownerComplement.message}`})]}),a.jsx("input",{...W("ownerComplement"),className:`h-12 w-full px-4 text-white rounded-xl ${ee.ownerComplement?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Documento de identidade"}),a.jsx(F.Z,{onFileUploaded:N,fileName:q,onRemoveFile:()=>{N(void 0),L("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Comprovante de resid\xeancia"}),a.jsx(F.Z,{onFileUploaded:P,fileName:Y,onRemoveFile:()=>{P(void 0),U("")}})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1",children:"Contrato de parceria"}),a.jsx(F.Z,{onFileUploaded:R,fileName:O,onRemoveFile:()=>{R(void 0),B("")}})]})]}),a.jsx("div",{className:"md:w-52 mb-10",children:a.jsx(m.Z,{label:"Enviar",loading:ea.isPending,disabled:ea.isPending||!es||!w||!C||!M})})]})})})}let AdminCreate=()=>{let[e,s]=(0,i.useState)("pf");return(0,S.e)(),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"m-3",children:[a.jsx("p",{className:"text-xl text-white mb-5",children:"Cadastro de Admin / Gestor de carteira"}),a.jsx("div",{className:" mb-5 flex items-center gap-4",children:(0,a.jsxs)("div",{className:"mb-5",children:[a.jsx("p",{className:"text-white mb-1",children:"Tipo de conta"}),(0,a.jsxs)(k.Z,{value:e,onChange:({target:e})=>s(e.value),children:[a.jsx("option",{value:"pf",children:"Pessoa F\xedsica"}),a.jsx("option",{value:"pj",children:"Pessoa Jur\xeddica"})]})]})})]}),"pj"===e?a.jsx(AdminRegisterPj,{typeCreate:"admin"}):a.jsx(AdminRegisterPf,{typeCreate:"admin"})]})};function CadastroManual(){let[e,s]=(0,i.useState)(),t=(0,S.e)();return(0,i.useEffect)(()=>{"admin"===t.name?s("broker"):s("investor")},[]),(0,a.jsxs)(a.Fragment,{children:[a.jsx(n.Z,{}),a.jsx(r.Z,{children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"text-white flex",children:["admin"!==t.name&&a.jsx("div",{onClick:()=>s("investor"),className:`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${"investor"===e?"bg-zinc-700":""}`,children:a.jsx("p",{children:"Investidor"})}),"broker"!==t.name&&a.jsx("div",{onClick:()=>s("broker"),className:`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${"broker"===e?"bg-zinc-700":""}`,children:a.jsx("p",{children:"Broker"})}),a.jsx("div",{onClick:()=>s("acessor"),className:`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${"acessor"===e?"bg-zinc-700":""}`,children:a.jsx("p",{children:"Assessor"})}),"superadmin"===t.name&&a.jsx("div",{onClick:()=>s("admin"),className:`px-3 cursor-pointer rounded-t-lg mr-1 border border-b-0 border-zinc-700 ${"admin"===e?"bg-zinc-700":""}`,children:a.jsx("p",{children:"Admin / Gestor de carteiras"})})]}),a.jsx("div",{className:"border rounded-tr-2xl rounded-b-2xl p-2 border-zinc-700",children:(()=>{switch(e){case"investor":return a.jsx(CreateInvestor,{});case"broker":return a.jsx(BrokerCreate,{});case"acessor":return a.jsx(AssessorCreate,{});case"admin":return a.jsx(AdminCreate,{});default:return a.jsx("div",{})}})()})]})})]})}},31608:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>n,default:()=>d});var a=t(17536);let i=(0,a.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\cadastro-manual\page.tsx`),{__esModule:n,$$typeof:r}=i,l=i.default,d=l}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[4103,6426,4731,8813,6558,3356,1808,7878,4944,7207,278,7669,7913,2411,2686,87,7017,8879,2796,2434,8427],()=>__webpack_exec__(98899));module.exports=t})();