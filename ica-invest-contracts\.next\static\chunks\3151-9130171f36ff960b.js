(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3151],{2480:function(){},4984:function(e,t,a){"use strict";var r=a(7437),n=a(5044),o=a(365),s=a(2361),i=a(7227),l=a(2265),d=a(8647),c=a(3014),u=a(992);t.Z=e=>{let{onFileUploaded:t,isPdf:a=!1,disable:x=!1,errorMessage:m,onRemoveFile:f,fileName:p}=e,[h,v]=(0,l.useState)(""),[g,j]=(0,l.useState)(null),[N,b]=(0,l.useState)(),E=(0,l.useMemo)(()=>a?["application/pdf"]:["image/png","image/jpeg","application/pdf"],[a]),w=(0,l.useCallback)((e,a)=>{if(j(null),!e||0===e.length)return;let r=e[0];if(!E.includes(r.type)){c.Am.error("Tipo de arquivo n\xe3o suportado."),v("");return}t([r]);let n=URL.createObjectURL(r);v(n),b(r.name)},[t,E]),{getRootProps:A,getInputProps:C,isDragActive:y,isDragReject:I}=(0,d.uI)({onDrop:w,maxFiles:1,multiple:!1,onDragEnter:e=>j(null),onDragLeave:e=>j(null),onError:e=>{console.log(e),"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&c.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:a?{"application/pdf":[".pdf"]}:{"image/png":[".png"],"image/jpeg":[".jpeg",".jpg"],"application/pdf":[".pdf"]},onFileDialogCancel:()=>{v("")},disabled:x}),T=(0,l.useMemo)(()=>y&&(I||!!g),[y,I,g]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{...A(),className:(0,u.cn)("text-white input relative group cursor-pointer rounded-sm bg-[#D9D9D9] px-16 py-10 w-full flex items-center justify-center border-2",{"border-green-500":y&&!T,"border-red-500":T,"border-2 border-red-500":m}),children:[(0,r.jsx)("input",{...C(),accept:".png,.jpg,.jpeg,.pdf"}),x?(0,r.jsx)(n.Z,{width:40,color:"#0da34e"}):h?(0,r.jsx)(o.Z,{width:40,color:"#0da34e"}):(0,r.jsx)(s.Z,{width:40,color:"#515151"}),m&&(0,r.jsx)("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[120%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:m})]}),h&&(0,r.jsxs)("div",{className:"z-50 flex justify-between items-center mt-1 px-2 py-1 text-white text-xs bg-[#1a1a1a] rounded",children:[(0,r.jsx)("span",{className:"truncate max-w-[150px]",children:N}),(0,r.jsx)("button",{type:"button",onClick:()=>{b(""),v(""),null==f||f(),t(void 0)},className:"ml-2 flex-shrink-0",children:(0,r.jsx)(i.Z,{className:"w-5 h-5 text-red-500 hover:text-red-700"})})]})]})}},7059:function(e,t,a){"use strict";a.d(t,{Z:function(){return FilterModal}});var r=a(7437),n=a(5969),o=a(2307),s=a(4164),i=a(2893),l=a(1980),d=a(8700),c=a(2265),u=a(2067),x=a.n(u),m=a(8203),f=a(992),p=a(1291),h=a(7158),v=a(3170);function Calendar(e){let{className:t,classNames:a,showOutsideDays:n=!0,...o}=e;return(0,r.jsx)(v._W,{showOutsideDays:n,className:(0,f.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,f.cn)((0,d.d)({variant:"secondary"}),"h-7 w-7 p-0"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,f.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===o.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,f.cn)((0,d.d)({variant:"ghost"}),"h-8 w-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start",day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:e=>{let{className:t,...a}=e;return(0,r.jsx)(p.Z,{className:(0,f.cn)("h-4 w-4",t),...a})},IconRight:e=>{let{className:t,...a}=e;return(0,r.jsx)(h.Z,{className:(0,f.cn)("h-4 w-4",t),...a})}},...o})}Calendar.displayName="Calendar";var g=a(5050);let j=g.fC,N=g.xz;g.ee;let b=c.forwardRef((e,t)=>{let{className:a,align:n="center",sideOffset:o=4,...s}=e;return(0,r.jsx)(g.h_,{children:(0,r.jsx)(g.VY,{ref:t,align:n,sideOffset:o,className:(0,f.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",a),...s})})});function DatePicker(e){let{setValue:t,value:a}=e,[n,o]=c.useState();return(0,r.jsxs)(j,{children:[(0,r.jsx)(N,{asChild:!0,children:(0,r.jsxs)(d.z,{variant:"outline",size:"lg",className:(0,f.cn)("font-normal w-full"),children:[(0,r.jsx)(m.Z,{}),(0,r.jsx)("span",{className:"text-xs",children:n?x()(n).format("DD/MM/YYYY"):"DD/MM/YYYY"})]})}),(0,r.jsx)(g.h_,{children:(0,r.jsx)(b,{className:"w-auto p-0 z-[1000000]",children:(0,r.jsx)(Calendar,{mode:"single",selected:n,className:"z-[10000]",onSelect:e=>{t(x()(e).format("YYYY-MM-DD")),o(e)},initialFocus:!0})})})]})}b.displayName=g.VY.displayName;var E=a(4765),w=a(3495),A=a(5781);let C=[{label:"Todos",value:"all"},{label:"Contratos SCP",value:"SCP"},{label:"Contratos M\xfatuo",value:"MUTUO"}];function FilterModal(e){let{activeModal:t,setActiveModal:a,filterData:c,setFilterData:u,handleSearch:x,inputPlaceholder:m="Pesquisar",children:f,hidenButton:p,isNew:h=!1}=e;return h?(0,r.jsxs)(A.h_,{open:t,onOpenChange:a,children:[(0,r.jsxs)(A.$F,{onClick:()=>a(!t),className:"flex w-24 text-sm justify-around p-2 rounded-lg bg-[#3A3A3A] cursor-pointer",children:[(0,r.jsx)(n.Z,{width:15,color:"#fff"}),(0,r.jsx)("p",{children:"Filtros"})]}),(0,r.jsxs)(A.AW,{className:"mr-1 max-w-[370px] px-3",children:[(0,r.jsx)(A.Ju,{className:"text-md",children:"Filtros"}),(0,r.jsx)(A.VD,{}),(0,r.jsxs)("div",{className:"flex flex-col justify-between mt-2",children:[f,!p&&(0,r.jsx)("div",{className:"m-auto mt-5 w-full mb-2",children:(0,r.jsx)(d.z,{onClick:()=>{x&&x(),a(!1)},className:"w-full",children:"Aplicar"})})]})]})]}):(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center items-end relative gap-5 justify-end",children:[(null==c?void 0:c.input)!==void 0&&(0,r.jsx)("div",{className:"md:w-80 mb-2 md:mb-0",children:(0,r.jsx)(l.Z,{handleSearch:()=>{x&&x()},placeholder:m,setValue:e=>{u&&u({...c,input:e})},value:c.input})}),(0,r.jsxs)("div",{className:"flex w-24 md:items-center p-2 rounded-lg bg-[#3A3A3A] cursor-pointer",onClick:()=>a(!t),children:[(0,r.jsx)(n.Z,{width:15,color:"#fff"}),(0,r.jsx)("p",{className:"px-2 text-sm",children:"Filtros"}),t?(0,r.jsx)(o.Z,{width:15,color:"#fff"}):(0,r.jsx)(s.Z,{width:15,color:"#fff"})]}),t&&(0,r.jsxs)("div",{className:"absolute md:w-[300px]  bg-[#3A3A3A] p-5 top-10 rounded-tl-lg rounded-b-lg z-10",children:[(0,r.jsxs)("div",{className:"flex w-full justify-between items-center",children:[(0,r.jsx)("p",{className:"text-base",children:"Filtros"}),(0,r.jsx)("div",{className:"cursor-pointer",onClick:()=>a(!1),children:(0,r.jsx)(i.Z,{width:20})})]}),f?(0,r.jsx)("div",{className:"mt-5",children:f}):c&&u?(0,r.jsxs)("div",{className:"flex flex-col justify-between mt-2",children:[(0,r.jsxs)("div",{className:"mb-4 flex flex-row justify-between gap-2",children:[void 0!==c.startData&&(0,r.jsxs)("div",{className:"mb-2 md:mb-0 w-[48%]",children:[(0,r.jsx)("p",{className:"text-xs",children:"In\xedcio"}),(0,r.jsx)(DatePicker,{value:c.startData,setValue:e=>{u({...c,startData:e})}})]}),void 0!==c.endData&&(0,r.jsxs)("div",{className:"mb-2 md:mb-0 w-[48%]",children:[(0,r.jsx)("p",{className:"text-xs",children:"Fim"}),(0,r.jsx)(DatePicker,{value:c.endData,setValue:e=>{u({...c,endData:e})}})]})]}),void 0!==c.type&&(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("p",{className:"text-xs",children:"Tipo de contrato"}),(0,r.jsx)(E.Z,{value:c.type,onChange:e=>{u({...c,type:e.target.value})},children:C.map((e,t)=>(0,r.jsx)("option",{value:e.value,children:e.label},t))})]}),void 0!==c.filterOptionSelected&&(0,r.jsxs)("div",{className:"w-full mt-4",children:[(0,r.jsx)("p",{className:"text-xs",children:"Status do contrato"}),(0,r.jsx)(E.Z,{value:c.filterOptionSelected,onChange:e=>{console.log(e.target.value),u({...c,filterOptionSelected:e.target.value})},children:w.Z.map((e,t)=>(0,r.jsx)("option",{value:e.value,children:e.label},t))})]})]}):void 0,!p&&(0,r.jsx)("div",{className:"m-auto mt-5",children:(0,r.jsx)(d.z,{onClick:()=>{x&&x(),a(!1)},className:"w-full",children:"Aplicar"})})]})]})}},3877:function(e,t,a){"use strict";a.d(t,{Z:function(){return Header}});var r=a(7437),n=a(2265),o=a(3482),s=a(7965),i=a(6689),l=a(9874),d=a(1809),c=a(4033),u=a(3014),x=a(8637);function formatUserType(e){return({retention:"Reten\xe7\xe3o",superadmin:"Super Administrador",advisor:"Assessor",broker:"Broker",admin:"Gestor de carteira",investor:"Investidor",financial:"Financeiro"})[e]||""}var m=a(4734),f=a(8689),p=a(4568),h=a(7059),v=a(5215),g=a(4984),j=a(3256),N=a(2549);function Notifications(e){let{open:t,notifications:a,filter:o,setFilter:i,handleNotifications:l}=e,d=(0,j.e)(),{setNotificationModal:c}=(0,n.useContext)(m.Z),[u,x]=(0,n.useState)(),[b,E]=(0,n.useState)(!1),[w,A]=(0,n.useState)(!1),[C,y]=(0,n.useState)(!1),[I,T]=(0,n.useState)(null),Notification=e=>{let{notification:t}=e;return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("div",{className:"w-[95%] flex justify-end mt-2 mb-[-5px] ml-[-2.5px] cursor-pointer",onClick:()=>x(void 0),children:(0,r.jsx)(N.Z,{className:"w-5 h-5",onClick:()=>handleDeleteNotification(t.id)})}),(0,r.jsxs)("div",{className:"mt-2 p-5 bg-[#282828] rounded-lg cursor-pointer flex justify-between items-center",onClick:()=>openNotification(t.id,t),children:[(0,r.jsxs)("div",{className:"w-[95%]",children:[(0,r.jsxs)("div",{className:"w-[50%] flex items-start flex-col",children:[(0,r.jsx)("p",{className:"text-base font-bold flex",children:t.title}),(0,r.jsx)("div",{className:"w-full bg-[#FF9900] h-[1px] my-2"})]}),(0,r.jsx)("p",{className:"text-xs w-[80%] font-extralight text-[#afafaf]",children:t.description}),(0,r.jsxs)("div",{className:"flex justify-start flex-wrap w-[90%] gap-2 mt-1 items-center",children:[t.brokerName&&(0,r.jsxs)("p",{className:"text-xs",children:["Broker: ",t.brokerName]}),(0,r.jsx)("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),(0,r.jsxs)("p",{className:"text-xs",children:["Data: ",t.date]}),(0,r.jsx)("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),(0,r.jsxs)("p",{className:"text-xs",children:["Hor\xe1rio: ",t.hour]})]})]}),(0,r.jsx)("div",{className:"w-[5%]",children:!1===t.viewed&&(0,r.jsx)("div",{className:"w-[10px] h-[10px] bg-[#FF9900] rounded-full"})})]})]})},openNotification=(e,t)=>{p.Z.put("/notifications/mark-viewed",{},{params:{notificationId:e},headers:{roleId:d.roleId}}).finally(()=>{x(t)})},handleCloseNotification=()=>{x(void 0),l()},handleClose=()=>{u?x(void 0):(i(""),y(!1),c(!1))},handleDeleteNotification=e=>{window.confirm("Tem certeza que deseja deletar esta notifica\xe7\xe3o?")&&(E(!0),p.Z.delete("/notifications/".concat(e),{headers:{roleId:d.roleId}}).finally(()=>{l(),E(!1)}))};return(0,n.useEffect)(()=>{void 0!==a&&C&&setTimeout(()=>{y(!1)},300)},[a,C]),(0,n.useEffect)(()=>{let handleClickOutside=e=>{let a=e.target;t&&!a.closest(".notifications-modal")&&handleClose()};return t&&document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[t,u]),(0,n.useEffect)(()=>()=>{I&&clearTimeout(I)},[I]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"".concat(t?"fixed opacity-50":"fixed opacity-0 pointer-events-none"," inset-0 bg-black z-40 transition-opacity duration-300 ease-in-out"),onClick:handleClose}),(0,r.jsxs)("div",{className:"".concat(t?"fixed opacity-100 translate-x-0":"fixed opacity-0 translate-x-full pointer-events-none"," w-full md:w-5/12 2xl:w-4/12 h-full bg-[#1C1C1C] z-50 right-0 top-0 text-white p-5 overflow-auto border-t border-l border-[#FF9900] notifications-modal transition-all duration-300 ease-in-out"),children:[(0,r.jsx)("div",{className:"flex w-full justify-between items-center gap-4 pb-5",children:(0,r.jsxs)("div",{className:"flex flex-1 justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(s.Z,{color:"#FF9900 ",width:40}),(0,r.jsx)("h1",{className:"font-bold text-2xl mt-1",children:(null==u?void 0:u.id)?"Notifica\xe7\xe3o":"Notifica\xe7\xf5es"})]}),!u&&(0,r.jsx)(h.Z,{activeModal:w,handleSearch:()=>{y(!0),I&&clearTimeout(I);let e=setTimeout(()=>{l(o),A(!1)},300);T(e)},setActiveModal:A,hidenButton:!0,children:(0,r.jsx)(v.Z,{options:[{label:"Todos",value:""},{label:"Tentativa de Duplicidade",value:"DUPLICATED_DOCUMENT"},{label:"Contratos Gerados",value:"NEW_CONTRACT"},{label:"Aditivo Criado",value:"INCLUDE_ADDITIVE"}],selected:o,setSelected:e=>{y(!0),i(e),I&&clearTimeout(I);let t=setTimeout(()=>{l(e),A(!1)},300);T(t)}})})]})}),u?(0,r.jsxs)("div",{className:"mt-2 w-full",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center gap-4 mb-2",children:[(0,r.jsxs)("div",{className:"flex flex-col w-[80%]",children:[(0,r.jsx)("p",{className:"text-xl font-bold",children:u.title}),(0,r.jsx)("div",{className:"w-[60%] bg-[#FF9900] h-[1px] my-2"})]}),(0,r.jsx)("div",{className:"flex justify-end w-[5%] cursor-pointer",children:(0,r.jsx)(N.Z,{className:"w-5 h-5 text-[#FF9900]",onClick:()=>handleCloseNotification()})})]}),(0,r.jsx)("p",{className:"text-sm w-[60%] mb-2 font-light text-[#afafaf]",children:u.description}),(0,r.jsxs)("p",{className:"text-xs mt-4 text-[#afafaf]",children:["Broker Respons\xe1vel: ",u.brokerName,(0,r.jsx)("div",{className:"w-[55%] bg-[#FF9900] h-[1px] my-2"})]}),(0,r.jsxs)("div",{className:"flex flex-col justify-between flex-wrap w-[50%] gap-2 mt-1",children:[(0,r.jsxs)("p",{className:"text-xs font-extralight text-[#afafaf]",children:["Data: ",u.date]}),(0,r.jsx)("div",{className:"w-[40%] bg-[#FF9900] h-[1px]"}),(0,r.jsxs)("p",{className:"text-xs font-extralight text-[#afafaf]",children:["Hor\xe1rio: ",u.hour]})]}),("NEW_CONTRACT"===u.type||"INCLUDE_ADDITIVE"===u.type)&&(0,r.jsxs)("div",{className:"flex flex-col mt-8",children:[(0,r.jsx)("p",{className:"text-base font-medium",children:"Anexos"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 mt-3",children:[(null==u?void 0:u.contractPdf)&&(0,r.jsxs)("div",{className:"w-56",children:[(0,r.jsx)("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Contrato Principal"}),(0,r.jsx)("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),(0,r.jsx)("div",{onClick:()=>{window.open(u.contractPdf,"_blank")},children:(0,r.jsx)(g.Z,{disable:!0,onFileUploaded:()=>{}})})]}),(null==u?void 0:u.proofPayment)&&(0,r.jsxs)("div",{className:"w-56",children:[(0,r.jsx)("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Comprovante de Pagamento"}),(0,r.jsx)("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),(0,r.jsx)("div",{onClick:()=>{window.open(u.proofPayment,"_blank")},children:(0,r.jsx)(g.Z,{disable:!0,onFileUploaded:()=>{}})})]}),"INCLUDE_ADDITIVE"===u.type&&(0,r.jsxs)("div",{className:"flex gap-4",children:[(null==u?void 0:u.addendumPdf)&&(0,r.jsxs)("div",{className:"w-56",children:[(0,r.jsx)("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Contrato de Aditivo"}),(0,r.jsx)("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),(0,r.jsx)("div",{onClick:()=>{window.open(null==u?void 0:u.addendumPdf,"_blank")},children:(0,r.jsx)(g.Z,{disable:!0,onFileUploaded:()=>{}})})]}),(null==u?void 0:u.proofPaymentAddendum)&&(0,r.jsxs)("div",{className:"w-56",children:[(0,r.jsx)("p",{className:"text-xs mb-1 font-extralight text-[#afafaf]",children:"Comprovante de Pagamento Aditivo"}),(0,r.jsx)("div",{className:"w-full bg-[#FF9900] h-[1px] my-1"}),(0,r.jsx)("div",{onClick:()=>{window.open(null==u?void 0:u.proofPaymentAddendum,"_blank")},children:(0,r.jsx)(g.Z,{disable:!0,onFileUploaded:()=>{}})})]})]})]})]})]}):(0,r.jsx)("div",{children:!0===b||C?(0,r.jsx)("div",{children:(0,r.jsx)(()=>(0,r.jsx)("div",{children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"w-[95%] flex justify-end mt-2 mb-[-5px] ml-[-2.5px]",children:(0,r.jsx)(f.j,{height:"20px",width:"20px"})}),(0,r.jsxs)("div",{className:"mt-2 p-5 bg-[#282828] rounded-lg flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"w-[95%]",children:[(0,r.jsxs)("div",{className:"w-[50%] flex items-start flex-col",children:[(0,r.jsx)(f.j,{height:"20px",width:"60%"}),(0,r.jsx)("div",{className:"w-full bg-[#FF9900] h-[1px] my-2"})]}),(0,r.jsx)(f.j,{height:"16px",width:"80%",className:"my-2"}),(0,r.jsxs)("div",{className:"flex justify-start flex-wrap w-[90%] gap-2 mt-1 items-center",children:[(0,r.jsx)(f.j,{height:"16px",width:"30%"}),(0,r.jsx)("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),(0,r.jsx)(f.j,{height:"16px",width:"25%"}),(0,r.jsx)("div",{className:"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full"}),(0,r.jsx)(f.j,{height:"16px",width:"25%"})]})]}),(0,r.jsx)("div",{className:"w-[5%]",children:(0,r.jsx)(f.j,{height:"10px",width:"10px",className:"rounded-full"})})]})]},e))}),{})}):(0,r.jsx)("div",{children:null==a?void 0:a.map(e=>(0,r.jsx)(Notification,{notification:e},e.id))})})]})]})}var b=a(1617),E=a(9588);function Header(){let e=(0,j.e)(),[t,a]=(0,n.useState)(!1),[f,h]=(0,n.useState)(""),v=(0,c.usePathname)(),g=(0,c.useRouter)(),{push:N}=(0,c.useRouter)(),{notificationModal:w,setNotificationModal:A}=(0,n.useContext)(m.Z),getNotifications=async()=>{let t=await p.Z.get("/notifications",{params:{ownerRoleRelationId:"superadmin"!==e.name?e.roleId:void 0,type:""===f?void 0:f}});if(!t.data)throw Error("Erro");return t.data},{data:C,error:y,isLoading:I,mutate:T}=(0,b.ZP)("superadmin"===e.name?"/api/notifications?filter=".concat(f):null,"superadmin"===e.name?getNotifications:null,{refreshInterval:1e4}),handleNotificationsUpdate=async t=>{"superadmin"===e.name&&(void 0!==t&&h(t),await T())},D=(0,j.P)(),_=[{name:"Sair",href:"/",action:logout}];function logout(){sessionStorage.clear(),localStorage.clear(),N("/")}return(0,r.jsx)(o.p,{as:"header",className:"bg-header",children:n=>{let{open:c}=n;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between w-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between w-full relative",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("img",{className:"h-6 w-auto",src:"/logo.svg",alt:"Your Company"})}),(0,r.jsxs)("div",{onClick:()=>{D.roles.length>1&&a(!t)},className:"cursor-pointer text-white relative",children:[(0,r.jsxs)("p",{className:"select-none text-left",children:["Perfil selecionado:"," ",(0,r.jsx)("b",{children:formatUserType(e.name)})]}),t&&(0,r.jsxs)("div",{className:"z-50 absolute right-0 top-6 bg-zinc-900 w-52 rounded-s-lg rounded-br-lg p-2 text-white shadow-lg border border-[#FF9900]",children:[(0,r.jsx)("p",{className:"text-sm mb-2 select-none",children:"Selecione o perfil:"}),D.roles.map(t=>{if(t.name!==e.name)return(0,r.jsx)("p",{className:"cursor-pointer py-1 bg-orange-linear rounded-sm text-center select-none my-1",onClick:()=>{u.Am.info("Alterando para o perfil: ".concat(formatUserType(t.name)));let e=(0,j.P)();(0,E.Tl)(e),(0,E.jE)(t),(0,E.zO)(),sessionStorage.setItem("role_changed","true"),window.location.reload()},children:formatUserType(t.name)},t.roleId)})]})]}),(0,r.jsx)("div",{children:"superadmin"===e.name&&(0,r.jsxs)("div",{className:"relative",onClick:()=>A(!0),children:[C&&C.filter(e=>!1===e.viewed).length>0&&(0,r.jsx)("p",{className:"absolute select-none top-0 right-[-10px] translate-y-[-8px] text-[10px] bg-black text-white w-[15px] h-[15px] text-center rounded-full",children:C.filter(e=>!1===e.viewed).length}),(0,r.jsx)(s.Z,{className:"cursor-pointer",width:22,color:"#fff"})]})})]}),(0,r.jsx)(Notifications,{notifications:C,open:w,filter:f,setFilter:h,handleNotifications:handleNotificationsUpdate}),(0,r.jsx)("div",{className:"-mr-2 flex md:hidden",children:(0,r.jsxs)(o.p.Button,{className:"relative inline-flex items-center justify-center rounded-md  p-2 text-white  hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800",children:[(0,r.jsx)("span",{className:"absolute -inset-0.5"}),(0,r.jsx)("span",{className:"sr-only",children:"Open main menu"}),c?(0,r.jsx)(i.Z,{className:"block h-6 w-6","aria-hidden":"true"}):(0,r.jsx)(l.Z,{className:"block h-6 w-6","aria-hidden":"true"})]})})]})}),(0,r.jsxs)(o.p.Panel,{className:"md:hidden",children:[(0,r.jsx)("div",{className:"space-y-1 px-2 pb-3 pt-2 sm:px-3",children:x.L.map(t=>{if(t.role.includes(e.name))return(0,r.jsx)("div",{className:"w-full py-1 px-2 text-white rounded-md ".concat(v===t.path?"bg-zinc-800":""),onClick:()=>g.push(t.path),children:(0,r.jsx)("p",{children:t.title})},t.title)})}),(0,r.jsx)("div",{className:"border-t border-gray-700 pb-3",children:(0,r.jsxs)("div",{className:"mt-3 space-y-1 px-2 flex items-center",onClick:logout,children:[(0,r.jsx)("div",{className:"",children:(0,r.jsx)(d.Z,{className:"w-8 h-8 text-red-400 p-1"})}),_.map(e=>(0,r.jsx)(o.p.Button,{as:"a",className:"block rounded-md px-3 py-2 text-base font-medium text-red-400 hover:bg-red-600 hover:text-white",children:e.name},e.name))]})})]})]})}})}},1980:function(e,t,a){"use strict";a.d(t,{Z:function(){return InputSearch}});var r=a(7437),n=a(5968),o=a(4020);function InputSearch(e){let{setValue:t,value:a,disabled:s=!1,handleSearch:i,placeholder:l="Pesquisar",isDocument:d=!1}=e;return(0,r.jsxs)("div",{className:"w-full bg-white flex items-center justify-start p-1 rounded-full md:w-auto min-w-64",children:[(0,r.jsx)("div",{onClick:()=>{i(a)},children:(0,r.jsx)(o.Z,{width:20,color:"#868E96",className:"cursor-pointer"})}),(0,r.jsx)("input",{type:"text",className:"border-none w-full text-[#868E96] ml-2 focus:outline-none",placeholder:l,onKeyDown:e=>{"Enter"===e.key&&i(a)},disabled:s,value:a,onChange:e=>{let{target:a}=e;d?t(a.value.length<=14?(0,n.VL)(a.value):(0,n.PK)(a.value)):t(a.value)}})]})}},4765:function(e,t,a){"use strict";a.d(t,{Z:function(){return SelectCustom}});var r=a(7437);function SelectCustom(e){let{value:t,onChange:a,children:n,className:o="",disabled:s=!1}=e;return(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)("select",{disabled:s,value:t,onChange:a,className:"h-12 w-full pl-6 pr-10 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black cursor-pointer appearance-none ".concat(o),children:n}),(0,r.jsx)("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-[#FFFF]",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}a(2265)},5215:function(e,t,a){"use strict";a.d(t,{Z:function(){return Select}});var r=a(7437),n=a(4164),o=a(2265);function Select(e){let{options:t,selected:a,setSelected:s,width:i="100%",size:l="normal",align:d,zIndex:c=10}=e,[u,x]=(0,o.useState)(),[m,f]=(0,o.useState)(a);(0,o.useEffect)(()=>{var e;f(null===(e=t.filter(e=>e.value===a)[0])||void 0===e?void 0:e.label)},[]),(0,o.useEffect)(()=>{var e;f(null===(e=t.filter(e=>e.value===a)[0])||void 0===e?void 0:e.label)},[a]);let handleOptionClick=e=>{f(e.label),s(e.value),x(!1)},returnTypeBG=e=>{if("normal"===l){if(a===e)return"bg-[#3A3A3A80] mx-2"}else if(a===e)return"bg-[#7c787880] mx-2"};return(0,r.jsxs)("div",{className:"relative w-[".concat(i,"]"),children:[(0,r.jsxs)("button",{style:{zIndex:c+1},className:"w-full justify-between relative text-left ".concat("normal"===l?"bg-[#3A3A3A]":"bg-[#303030]"," ").concat("normal"===l?"border px-2 py-2 text-sm":" py-2 px-4 text-sm"," text-white rounded-md shadow-md focus:outline-none flex items-center"),onClick:()=>x(e=>!e),children:[m,(0,r.jsx)("div",{className:"ml-2 ".concat(!0===u&&"animate-flipIn rotate-180"," ").concat(!1===u&&"animate-flipOut"),children:(0,r.jsx)(n.Z,{width:20})})]}),(0,r.jsx)("div",{style:{zIndex:c,top:"20px"},className:"absolute w-full ".concat("normal"===l?"bg-[#141414]":"bg-[#242424]"," rounded-b-md shadow-lg py-2 ").concat(u?"animate-openY":"hidden"," "),children:(0,r.jsx)("div",{className:"mt-5 max-h-40 overflow-auto scroll-mx-1",children:t.map((e,a)=>(0,r.jsx)("div",{className:"px-3 py-2  ".concat("center"===d&&"text-center"," text-white cursor-pointer text-sm rounded-lg ").concat("normal"===l?"hover:bg-[#3a3a3a50]":"hover:bg-[#52525250]"," hover:mx-2 my-1 transition-transform ").concat(returnTypeBG(e.value)," ").concat(a===t.length?"rounded-b-lg":""),onClick:()=>handleOptionClick(e),children:e.label},e.value))})})]})}},8637:function(e,t,a){"use strict";a.d(t,{Z:function(){return Sidebar},L:function(){return h}});var r=a(7437),n=a(1566),o=a(6610),s=a(1095),i=a(1809),l=a(4033),d=a(3014),c={src:"/_next/static/media/users.eb931a1f.svg",height:20,width:28,blurWidth:0,blurHeight:0},u={src:"/_next/static/media/monitoring.11c7bfee.svg",height:30,width:27,blurWidth:0,blurHeight:0},x=a(1535),m=a(6691),f=a.n(m),p=a(3256);let h=[{title:"Dashboard",path:"/home",icon:(0,r.jsx)(f(),{alt:"",src:{src:"/_next/static/media/dashboard.15d835ed.svg",height:23,width:24,blurWidth:0,blurHeight:0},width:20,color:"#fff"}),role:["admin","broker","investor","advisor","superadmin","retention","financial"]},{title:"Usu\xe1rios",path:"/usuarios",icon:(0,r.jsx)(f(),{src:c,alt:"",width:20,color:"#fff"}),role:["admin","superadmin"]},{title:"Movimenta\xe7\xf5es",path:"/movimentacoes",icon:(0,r.jsx)(n.Z,{width:20,color:"#fff"}),role:["investor"]},{title:"Meus Contratos",path:"/meus-contratos",icon:(0,r.jsx)(o.Z,{width:20,color:"#fff"}),role:["broker","investor","advisor"]},{title:"Contratos",path:"/contratos",icon:(0,r.jsx)(o.Z,{width:20,color:"#fff"}),role:["superadmin","admin"]},{title:"Usu\xe1rios",path:"/investidores",icon:(0,r.jsx)(f(),{src:c,alt:"",width:20,color:"#fff"}),role:["broker","advisor"]},{title:"Cadastros",path:"/cadastro-manual",icon:(0,r.jsx)(f(),{alt:"",src:{src:"/_next/static/media/create_users.3686b7db.svg",height:19,width:21,blurWidth:0,blurHeight:0},width:20,color:"#fff"}),role:["admin","superadmin","broker"]},{title:"Monitoramento",path:"/monitoramento",icon:(0,r.jsx)(f(),{alt:"",src:u,width:20,color:"#fff"}),role:["admin","superadmin"]},{title:"Metas",path:"/metas",icon:(0,r.jsx)(f(),{alt:"",src:x.Z,width:20,color:"#fff"}),role:["admin","broker","advisor","superadmin"]},{title:"Pagamentos",path:"/financeiro",icon:(0,r.jsx)(f(),{alt:"",src:u,width:20,color:"#fff"}),role:["superadmin","broker"]},{title:"Pagamentos Previstos",path:"/pagamentos-previstos",icon:(0,r.jsx)(s.Z,{width:20,color:"#fff"}),role:["financial","superadmin"]},{title:"Informe de Rendimentos",path:"/informe-rendimentos",icon:(0,r.jsx)(f(),{alt:"",src:{src:"/_next/static/media/ir.fa1b5c3e.svg",height:27,width:24,blurWidth:0,blurHeight:0},width:20,color:"#fff"}),role:["superadmin","broker","advisor"]}];function Sidebar(e){let{children:t,pathValid:a}=e,n=(0,l.useRouter)(),o=(0,l.usePathname)(),s=(0,p.e)();return(0,r.jsxs)("div",{className:"flex w-full min-h-screen py-2",children:[(0,r.jsxs)("div",{className:"2xl:w-2/12 hidden md:block rounded-r-lg p-5 bg-[#1C1C1C] relative",children:[h.map((e,t)=>{if(e.role.includes(s.name)){let a=o.includes(e.path)||"/home"===e.path&&"/cadastro"===o;return(0,r.jsxs)("div",{onClick:()=>n.push(e.path),style:{borderColor:a?"#FF9900":"",borderWidth:a?"1px":""},className:"rounded-xl px-3 py-2 flex items-center justify-center flex-col cursor-pointer mb-4 hover:bg-zinc-900",children:[e.icon,(0,r.jsx)("p",{style:{color:"#fff"},className:"mt-1 text-sm text-center w-[94%]",children:e.title})]},t)}}),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{onClick:()=>{d.Am.success("Deslogado com sucesso!"),localStorage.clear(),sessionStorage.clear(),n.push("/")},className:"rounded-xl px-5 py-4 flex items-center justify-center cursor-pointer mb-4 hover:bg-red-100",children:[(0,r.jsx)(i.Z,{width:20,color:"#dc2626 ",className:"mr-3"}),(0,r.jsx)("p",{className:"text-sm text-red-600",children:"Sair"})]})})]}),(0,r.jsx)("div",{className:"md:ml-6 md:mx-5 px-5 md:px-0 md:p-0 mt-6 w-full",children:t})]})}},8689:function(e,t,a){"use strict";a.d(t,{j:function(){return SkeletonItem}});var r=a(7437);a(2265);let SkeletonItem=e=>{let{width:t="100%",height:a="1rem",borderRadius:n="4px",className:o=""}=e;return(0,r.jsx)("div",{className:o,style:{width:t,height:a,borderRadius:n,background:"linear-gradient(90deg, #222222 25%, #363636 50%, #222222 75%)",backgroundSize:"200% 100%",animation:"skeleton-loading 5s infinite"}})};t.Z=e=>{let{width:t="100%",height:a="1rem",borderRadius:n="4px",className:o="",loading:s,children:i,minWidth:l="",maxWidth:d=""}=e;return s?(0,r.jsx)("div",{className:o,style:{width:t,height:a,minWidth:l,maxWidth:d,borderRadius:n,background:"linear-gradient(90deg, #222222 25%, #363636 50%, #222222 75%)",backgroundSize:"200% 100%",animation:"skeleton-loading 5s infinite"}}):(0,r.jsx)("div",{children:i})}},8700:function(e,t,a){"use strict";a.d(t,{d:function(){return d},z:function(){return c}});var r=a(7437),n=a(2265),o=a(7256),s=a(6061),i=a(3715),l=a(992);let d=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-orange-linear text-white shadow hover:bg-orange-black-linear",destructive:"bg-[#BC4C4C] text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"bg-black text-white rounded-xl ring-[#FF9900] ring-1 ring-inset",secondary:"bg-primary text-primary-foreground shadow hover:bg-primary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:a,variant:n,size:s,asChild:c=!1,loading:u=!1,...x}=e,m=c?o.g7:"button";return(0,r.jsxs)(m,{className:(0,l.cn)(d({variant:n,size:s,className:a}),"flex items-center gap-2 select-none"),ref:t,disabled:u||x.disabled,...x,children:[u&&(0,r.jsx)(i.Z,{className:"h-4 w-4 animate-spin"}),x.children]})});c.displayName="Button"},5781:function(e,t,a){"use strict";a.d(t,{$F:function(){return DropdownMenuTrigger},AW:function(){return DropdownMenuContent},Ju:function(){return DropdownMenuLabel},VD:function(){return DropdownMenuSeparator},h_:function(){return DropdownMenu}});var r=a(7437);a(2265);var n=a(9290),o=a(992);function DropdownMenu(e){let{...t}=e;return(0,r.jsx)(n.fC,{"data-slot":"dropdown-menu",...t})}function DropdownMenuTrigger(e){let{...t}=e;return(0,r.jsx)(n.xz,{"data-slot":"dropdown-menu-trigger",...t})}function DropdownMenuContent(e){let{className:t,sideOffset:a=4,...s}=e;return(0,r.jsx)(n.Uv,{children:(0,r.jsx)(n.VY,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-[#3A3A3A] border-none text-white data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-sm p-1 shadow-md",t),...s})})}function DropdownMenuLabel(e){let{className:t,inset:a,...s}=e;return(0,r.jsx)(n.__,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...s})}function DropdownMenuSeparator(e){let{className:t,...a}=e;return(0,r.jsx)(n.Z0,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},3495:function(e,t,a){"use strict";var r=a(8440);let n=[{label:"Todos",value:"Todos"},{label:"Contrato Gerado",value:r.rd.GENERATED},{label:"Contrato Ativo",value:r.rd.ACTIVE},{label:"Envio da assinatura",value:r.rd.SIGNATURE_SENT},{label:"Aguardando assinatura do investidor",value:r.rd.AWAITING_INVESTOR_SIGNATURE},{label:"Aguardando comprovante de pagamento",value:r.rd.AWAITING_DEPOSIT},{label:"Aguardando auditoria",value:r.rd.AWAITING_AUDIT},{label:"Aguardando assinatura da auditoria",value:r.rd.AWAITING_AUDIT_SIGNATURE},{label:"Falha ao enviar contrato para assinatura",value:r.rd.SIGNATURE_FAILED},{label:"Expirado por falta de assinatura do investidor",value:r.rd.EXPIRED_BY_INVESTOR},{label:"Expirado por falta de assinatura da auditoria",value:r.rd.EXPIRED_BY_AUDIT},{label:"Rejeitado",value:r.rd.REJECTED},{label:"Rejeitado pela auditoria",value:r.rd.REJECTED_BY_AUDIT},{label:"Falha ao gerar o contrato",value:r.rd.GENERATE_CONTRACT_FAILED},{label:"Expirado",value:r.rd.EXPIRED},{label:"Exclu\xeddo",value:r.rd.DELETED}];t.Z=n},4568:function(e,t,a){"use strict";a.d(t,{o:function(){return setToken}});var r=a(3256),n=a(4829),o=a(3014);let s=n.Z.create({baseURL:"http://localhost:3001"}),setToken=e=>{s.defaults.headers.common.Authorization="Bearer ".concat(e)},i=!1,l=[],processQueue=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;l.forEach(a=>{t?a.resolve(t):a.reject(e)}),l=[]};s.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null===(t=e.response)||void 0===t?void 0:t.status)===401&&!a._retry){let t=(0,r.P)(),d=sessionStorage.getItem("refreshToken");if(!d)return o.Am.error("Sess\xe3o expirada. Fa\xe7a login novamente!"),sessionStorage.clear(),window.location.href="/",Promise.reject(e);if(a._retry=!0,i)return new Promise(function(e,t){l.push({resolve:e,reject:t})}).then(e=>(a.headers.Authorization="Bearer ".concat(e),(0,n.Z)(a))).catch(e=>Promise.reject(e));i=!0;try{let{data:e}=await s.post("/auth-backoffice/login",{document:t.document,refreshToken:d}),r=e.accessToken;return setToken(r),sessionStorage.setItem("token",r),processQueue(null,r),a.headers.Authorization="Bearer ".concat(r),(0,n.Z)(a)}catch(e){o.Am.error("Erro ao validar token, fa\xe7a login novamente!"),setTimeout(()=>(processQueue(e,null),sessionStorage.clear(),window.location.href="/",Promise.reject(e)),1500)}finally{i=!1}}return Promise.reject(e)}),t.Z=s},8440:function(e,t,a){"use strict";var r,n,o,s;function formatStatusContract(e){let t={DRAFT:{title:"Rascunho",description:"Contrato ainda em fase de edi\xe7\xe3o",textColor:"text-[#FF9900]"},GENERATED:{title:"Gerado",description:"Contrato gerado com sucesso e aguardando a\xe7\xe3o",textColor:"text-[#FF9900]"},SIGNATURE_SENT:{title:"Assinatura",description:"Contrato enviado para assinatura digital",textColor:"text-[#FF9900]"},AWAITING_INVESTOR_SIGNATURE:{title:"Aguardando",description:"Aguardando que o investidor assine o contrato",textColor:"text-[#FF9900]"},AWAITING_DEPOSIT:{title:"Aguardando",description:"Aguardando envio do comprovante de pagamento",textColor:"text-[#FF9900]"},AWAITING_AUDIT:{title:"Aguardando",description:"Contrato aguardando an\xe1lise da auditoria",textColor:"text-[#FF9900]"},AWAITING_AUDIT_SIGNATURE:{title:"Aguardando",description:"Aguardando assinatura final da auditoria",textColor:"text-[#FF9900]"},ACTIVE:{title:"Ativo",description:"Contrato ativo e em vigor",textColor:"text-[#3cff00]"},SIGNATURE_FAILED:{title:"Falha",description:"Erro ao tentar enviar o contrato para assinatura",textColor:"text-red-500"},EXPIRED_BY_INVESTOR:{title:"Expirado",description:"Contrato expirou por falta de assinatura do investidor",textColor:"text-red-500"},EXPIRED_BY_AUDIT:{title:"Expirado",description:"Contrato expirou por n\xe3o ter sido assinado pela auditoria",textColor:"text-red-500"},EXPIRED_FAILURE_PROOF_PAYMENT:{title:"Expirado",description:"Contrato expirado por n\xe3o envio do comprovante de pagamento",textColor:"text-red-500"},REJECTED_BY_AUDIT:{title:"Rejeitado",description:"Contrato foi rejeitado pela auditoria.",textColor:"text-red-500"},REJECTED:{title:"Rejeitado",description:"Contrato foi rejeitado por alguma das partes",textColor:"text-red-500"},GENERATE_CONTRACT_FAILED:{title:"Erro",description:"Ocorreu uma falha ao gerar o contrato",textColor:"text-red-500"},EXPIRED:{title:"Expirado",description:"Contrato expirado por tempo excedido ou inatividade",textColor:"text-red-500"},DELETED:{title:"Exclu\xeddo",description:"Contrato exclu\xeddo do sistema",textColor:"text-red-500"}};return e in t?t[e]:{title:"Desconhecido",description:"Status n\xe3o reconhecido pelo sistema",textColor:"text-gray-300"}}function formatStatusAditive(e){switch(e){case"DRAFT":return{title:"Erro",textColor:"text-[#FF0000]",description:"Ocorreu um erro na confec\xe7\xe3o do contrato"};case"SENT_FOR_SIGNATURE":return{title:"Aguardando",textColor:"text-[#FF9900]",description:"Contrato enviado para assinatura das partes"};case"PENDING_INVESTOR_SIGNATURE":return{title:"Aguardando",textColor:"text-[#FF9900]",description:"Aguardando assinatura do investidor"};case"FULLY_SIGNED":return{title:"Finalizado",textColor:"text-[#3cff00]",description:"Todas as partes assinaram o contrato"};case"CANCELED":return{title:"Cancelado",textColor:"text-[#FF0000]",description:"Contrato cancelado antes de ser conclu\xeddo"};case"EXPIRED":return{title:"Expirado",textColor:"text-[#FF0000]",description:"Contrato expirado sem ser assinado dentro do prazo"};default:return{title:"Processando",textColor:"text-[#FF9900]",description:""}}}function formatStatusIncomePayment(e){switch(e){case"PENDENT":return{title:"Pendente",textColor:"text-[#FFB238]",description:"Informe de rendimentos pendente para enviar",borderColor:"border-[#FFB238]",backgroundColor:"bg-[#FFB238]"};case"DELIVERED":return{title:"Enviando",textColor:"text-[#429AEC]",description:"Envio em andamento ao destinat\xe1rio",backgroundColor:"bg-[#429AEC]",borderColor:"border-[#429AEC]"};case"SEND":return{title:"Enviado",textColor:"text-[#1EF97C]",description:"Informe de rendimentos enviado com sucesso!",backgroundColor:"bg-[#1EF97C]",borderColor:"border-[#1EF97C]"};case"ERROR":return{title:"Erro",textColor:"text-[#F10303]",description:"Erro ao enviar o informe de rendimentos",borderColor:"border-[#F10303]",backgroundColor:"bg-[#F10303]"}}}a.d(t,{Jk:function(){return formatStatusIncomePayment},XW:function(){return formatStatusAditive},mP:function(){return formatStatusContract},rd:function(){return r}}),(o=r||(r={})).EXPIRED_BY_AUDIT="EXPIRED_BY_AUDIT",o.EXPIRED_FAILURE_PROOF_PAYMENT="EXPIRED_FAILURE_PROOF_PAYMENT",o.DRAFT="DRAFT",o.GENERATED="GENERATED",o.SIGNATURE_SENT="SIGNATURE_SENT",o.AWAITING_INVESTOR_SIGNATURE="AWAITING_INVESTOR_SIGNATURE",o.AWAITING_DEPOSIT="AWAITING_DEPOSIT",o.AWAITING_AUDIT="AWAITING_AUDIT",o.AWAITING_AUDIT_SIGNATURE="AWAITING_AUDIT_SIGNATURE",o.ACTIVE="ACTIVE",o.SIGNATURE_FAILED="SIGNATURE_FAILED",o.EXPIRED_BY_INVESTOR="EXPIRED_BY_INVESTOR",o.REJECTED="REJECTED",o.REJECTED_BY_AUDIT="REJECTED_BY_AUDIT",o.GENERATE_CONTRACT_FAILED="GENERATE_CONTRACT_FAILED",o.EXPIRED="EXPIRED",o.DELETED="DELETED",(s=n||(n={})).DRAFT="DRAFT",s.SENT_FOR_SIGNATURE="SENT_FOR_SIGNATURE",s.PENDING_INVESTOR_SIGNATURE="PENDING_INVESTOR_SIGNATURE",s.FULLY_SIGNED="FULLY_SIGNED",s.CANCELED="CANCELED",s.EXPIRED="EXPIRED"},3256:function(e,t,a){"use strict";a.d(t,{P:function(){return getUser},e:function(){return getUserProfile}});var r=a(9588);function getUserProfile(){(0,r.Uh)();let e=(0,r.aA)();return e||(console.warn("Perfil n\xe3o encontrado, usando padr\xe3o"),{name:"investor",roleId:""})}function getUser(){(0,r.Uh)();let e=(0,r.mo)();return e||{name:"investor",id:"",document:"",roles:[]}}},9588:function(e,t,a){"use strict";a.d(t,{mo:function(){return getDecryptedUser},aA:function(){return getDecryptedUserProfile},zO:function(){return persistSessionData},Uh:function(){return restoreSessionData},Tl:function(){return setEncryptedUser},jE:function(){return setEncryptedUserProfile}});var r=a(4155),n=a.n(r);let o="f5beb0e0-8b97-4440-9ef2-cd6b2557345a";if(!o)throw Error("Chave de criptografia n\xe3o definida em NEXT_PUBLIC_CRYPTO_SECRET_KEY");function encryptData(e){let t=JSON.stringify(e);return n().AES.encrypt(t,o).toString()}function decryptData(e){try{let t=n().AES.decrypt(e,o),a=t.toString(n().enc.Utf8);return JSON.parse(a)}catch(e){return console.error("Erro ao descriptografar dados:",e),null}}var s=a(2601);let i=null,l=null,d=s.env.NEXT_PUBLIC_MEMORY_KEY||"secure-memory-key",c="session_token";function setEncryptedUser(e){let t=encryptData(e);i=n().AES.encrypt(t,d).toString()}function getDecryptedUser(){if(!i)return null;try{let e=n().AES.decrypt(i,d).toString(n().enc.Utf8);return decryptData(e)}catch(e){return console.error("Erro ao descriptografar usu\xe1rio:",e),null}}function setEncryptedUserProfile(e){let t=encryptData(e);l=n().AES.encrypt(t,d).toString()}function getDecryptedUserProfile(){if(!l)return null;try{let e=n().AES.decrypt(l,d).toString(n().enc.Utf8);return decryptData(e)}catch(e){return console.error("Erro ao descriptografar perfil de usu\xe1rio:",e),null}}function persistSessionData(){if(!i||!l)return;let e={user:i,profile:l},t=n().AES.encrypt(JSON.stringify(e),d).toString();sessionStorage.setItem(c,t)}function restoreSessionData(){let e=sessionStorage.getItem(c);if(!e)return!1;try{let t=n().AES.decrypt(e,d).toString(n().enc.Utf8),a=JSON.parse(t);return i=a.user,l=a.profile,!0}catch(e){return console.error("Erro ao restaurar dados da sess\xe3o:",e),!1}}},992:function(e,t,a){"use strict";a.d(t,{cn:function(){return cn}});var r=a(7042),n=a(4769);function cn(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.m6)((0,r.W)(t))}},4734:function(e,t,a){"use strict";var r=a(2265);let n=r.createContext({});t.Z=n},5968:function(e,t,a){"use strict";a.d(t,{Ht:function(){return valueMask},PK:function(){return cnpjMask},Tc:function(){return cepMask},VL:function(){return cpfMask},gP:function(){return phoneMask},p4:function(){return clearLetters}});let cpfMask=e=>e.replace(/\D/g,"").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d)/,"$1.$2").replace(/(\d{3})(\d{1,2})/,"$1-$2").replace(/(-\d{2})\d+?$/,"$1"),cnpjMask=e=>e.replace(/\D/g,"").replace(/^(\d{2})(\d)/,"$1.$2").replace(/^(\d{2})\.(\d{3})(\d)/,"$1.$2.$3").replace(/\.(\d{3})(\d)/,".$1/$2").replace(/(\d{4})(\d)/,"$1-$2").replace(/(-\d{2})\d+?$/,"$1"),valueMask=e=>e.replace(/\D/g,"").replace(/(\d{1})(\d{14})$/,"$1.$2").replace(/(\d{1})(\d{11})$/,"$1.$2").replace(/(\d{1})(\d{8})$/,"$1.$2").replace(/(\d{1})(\d{5})$/,"$1.$2").replace(/(\d{1})(\d{1,2})$/,"$1,$2"),phoneMask=e=>e.replace(/\D/g,"").replace(/^55/,"").replace(/^(\d{2})(\d)/g,"($1) $2").replace(/(\d)(\d{4})$/,"$1-$2"),clearLetters=e=>e.replace(/\D/g,""),cepMask=e=>e.replace(/\D/g,"").replace(/(\d{5})(\d)/,"$1-$2").replace(/(-\d{3})\d+?$/,"$1")},1535:function(e,t){"use strict";t.Z={src:"/_next/static/media/target.4ba3f0d8.svg",height:27,width:27,blurWidth:0,blurHeight:0}}}]);