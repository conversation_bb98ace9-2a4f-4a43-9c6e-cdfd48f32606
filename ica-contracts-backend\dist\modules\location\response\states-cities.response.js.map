{"version": 3, "file": "states-cities.response.js", "sourceRoot": "/", "sources": ["modules/location/response/states-cities.response.ts"], "names": [], "mappings": "", "sourcesContent": ["import { StateEntity } from 'src/shared/database/typeorm/entities/state.entity';\r\n\r\nexport interface IStatesCitiesResponse {\r\n  states: { state: StateEntity; totalCities: number }[];\r\n  totalStates: number;\r\n  totalCities: number;\r\n}\r\n"]}