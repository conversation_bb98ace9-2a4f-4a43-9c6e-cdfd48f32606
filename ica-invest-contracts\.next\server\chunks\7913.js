exports.id=7913,exports.ids=[7913],exports.modules={32307:(e,t,r)=>{"use strict";r.d(t,{Z:()=>InputText});var l=r(60080);function InputText({label:e,setValue:t,error:r,errorMessage:s,width:a="auto",register:n,name:i,placeholder:x="",type:d="text",disabled:u=!1,minDate:o,minLength:c,maxLength:m,maxDate:p,disableErrorMessage:h=!1,onBlur:b,value:g,onChange:f}){return(0,l.jsxs)("div",{className:"input relative group",style:{width:a},children:[(0,l.jsxs)("p",{className:"text-white mb-1 text-sm",children:[e,r&&!h&&(0,l.jsxs)("b",{className:"text-red-500 font-light text-sm",children:[" - ",s]})]}),l.jsx("input",{...n(i),placeholder:x,type:d,id:i,disabled:u,min:o,max:p,minLength:c,maxLength:m,...t?{onChange:({target:e})=>t(e.value)}:{},onBlur:b,className:`h-12 w-full px-4 ${u?"text-zinc-400":"text-white"} rounded-xl ${r?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`,...void 0!==g?{value:g}:{},...f?{onChange:f}:{}}),r&&l.jsx("div",{className:"absolute max-w-72 text-center left-1/2 top-0 -translate-x-1/2 -translate-y-[20%] hidden w-max bg-black border-red-600 border text-white text-xs rounded px-2 py-1 group-hover:block",children:s})]})}r(3510)},17871:(e,t,r)=>{"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}r.d(t,{Z:()=>formatNumberValue})},3510:()=>{}};