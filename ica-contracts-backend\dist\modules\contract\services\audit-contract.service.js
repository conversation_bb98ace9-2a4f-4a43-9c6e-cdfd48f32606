"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuditContractService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditContractService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const typeorm_2 = require("typeorm");
const create_notification_service_1 = require("../../notifications/services/create-notification.service");
const notification_entity_1 = require("../../../shared/database/typeorm/entities/notification.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
let AuditContractService = AuditContractService_1 = class AuditContractService {
    constructor(contractRepository, ownerRoleRelationRepository, createNotificationService) {
        this.contractRepository = contractRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.createNotificationService = createNotificationService;
        this.logger = new common_1.Logger(AuditContractService_1.name);
    }
    async approveContract(contractId, userId, observations) {
        this.logger.log(`Iniciando aprovação de contrato ${contractId} pelo auditor ${userId}`);
        await this.validateAuditPermissions(userId);
        const contract = await this.contractRepository.findOne({
            where: { id: contractId },
            relations: ['investor', 'investor.owner', 'investor.business'],
        });
        if (!contract) {
            throw new common_1.NotFoundException('Contrato não encontrado');
        }
        if (contract.status !== contract_status_enum_1.ContractStatusEnum.AWAITING_AUDIT) {
            throw new common_1.BadRequestException('Apenas contratos aguardando auditoria podem ser aprovados');
        }
        try {
            await this.contractRepository.update(contractId, {
                status: contract_status_enum_1.ContractStatusEnum.ACTIVE,
                auditApprovedAt: new Date(),
                auditApprovedBy: userId,
                auditObservations: observations,
                updatedAt: new Date(),
            });
            this.logger.log(`Contrato ${contractId} aprovado e ativado com sucesso`);
            const userProfile = await this.ownerRoleRelationRepository.findOne({
                where: [
                    { ownerId: userId },
                    { businessId: userId },
                ],
                relations: { role: true },
            });
            await this.createNotificationService.create({
                userOwnerRoleRelationId: userProfile.id,
                description: `Contrato ${contractId} foi aprovado pela auditoria e está agora ativo.`,
                title: `Contrato Aprovado e Ativado`,
                type: notification_entity_1.NotificationTypeEnum.CONTRACT_APPROVED,
                contractId: contractId,
                contractValue: contract.investmentValue || 0,
                investorId: contract.investorId,
            });
            return {
                success: true,
                message: 'Contrato aprovado e ativado com sucesso.',
            };
        }
        catch (error) {
            this.logger.error('Erro ao aprovar contrato:', error);
            throw new common_1.BadRequestException(`Erro ao aprovar contrato: ${error.message}`);
        }
    }
    async rejectContract(contractId, userId, observations) {
        this.logger.log(`Iniciando rejeição de contrato ${contractId} pelo auditor ${userId}`);
        await this.validateAuditPermissions(userId);
        const contract = await this.contractRepository.findOne({
            where: { id: contractId },
            relations: ['investor', 'investor.owner', 'investor.business'],
        });
        if (!contract) {
            throw new common_1.NotFoundException('Contrato não encontrado');
        }
        if (contract.status !== contract_status_enum_1.ContractStatusEnum.AWAITING_AUDIT) {
            throw new common_1.BadRequestException('Apenas contratos aguardando auditoria podem ser rejeitados');
        }
        try {
            await this.contractRepository.update(contractId, {
                status: contract_status_enum_1.ContractStatusEnum.REJECTED_BY_AUDIT,
                auditRejectedAt: new Date(),
                auditRejectedBy: userId,
                auditObservations: observations,
                updatedAt: new Date(),
            });
            this.logger.log(`Contrato ${contractId} rejeitado pela auditoria`);
            const userProfile = await this.ownerRoleRelationRepository.findOne({
                where: [
                    { ownerId: userId },
                    { businessId: userId },
                ],
                relations: { role: true },
            });
            await this.createNotificationService.create({
                userOwnerRoleRelationId: userProfile.id,
                description: `Contrato ${contractId} foi rejeitado pela auditoria. Motivo: ${observations}`,
                title: `Contrato Rejeitado pela Auditoria`,
                type: notification_entity_1.NotificationTypeEnum.CONTRACT_REJECTED,
                contractId: contractId,
                contractValue: contract.investmentValue || 0,
                investorId: contract.investorId,
            });
            return {
                success: true,
                message: 'Contrato rejeitado pela auditoria.',
            };
        }
        catch (error) {
            this.logger.error('Erro ao rejeitar contrato:', error);
            throw new common_1.BadRequestException(`Erro ao rejeitar contrato: ${error.message}`);
        }
    }
    async getContractsAwaitingAudit(userId) {
        this.logger.log(`Buscando contratos aguardando auditoria para usuário ${userId}`);
        await this.validateAuditPermissions(userId);
        const contracts = await this.contractRepository.find({
            where: { status: contract_status_enum_1.ContractStatusEnum.AWAITING_AUDIT },
            relations: ['investor', 'investor.owner', 'investor.business'],
            order: { createdAt: 'DESC' },
        });
        this.logger.log(`Encontrados ${contracts.length} contratos aguardando auditoria`);
        return contracts;
    }
    async validateAuditPermissions(userId) {
        const userProfile = await this.ownerRoleRelationRepository.findOne({
            where: [
                { ownerId: userId },
                { businessId: userId },
            ],
            relations: { role: true },
        });
        if (!userProfile) {
            throw new common_1.BadRequestException('Perfil de usuário não encontrado.');
        }
        if (userProfile.role.name !== 'superadmin') {
            throw new common_1.BadRequestException('Apenas super administradores podem realizar auditoria de contratos.');
        }
    }
};
exports.AuditContractService = AuditContractService;
exports.AuditContractService = AuditContractService = AuditContractService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        create_notification_service_1.CreateNotificationService])
], AuditContractService);
//# sourceMappingURL=audit-contract.service.js.map