{"version": 3, "file": "audit-contract.service.js", "sourceRoot": "/", "sources": ["modules/contract/services/audit-contract.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,+FAAsF;AACtF,qFAA2E;AAC3E,qCAAqC;AACrC,0GAA2G;AAC3G,uGAAgG;AAChG,qHAA0G;AASnG,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAG/B,YAEE,kBAA+D,EAE/D,2BAAiF,EAChE,yBAAoD;QAHpD,uBAAkB,GAAlB,kBAAkB,CAA4B;QAE9C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAChE,8BAAyB,GAAzB,yBAAyB,CAA2B;QAPtD,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;IAQ7D,CAAC;IAKJ,KAAK,CAAC,eAAe,CACnB,UAAkB,EAClB,MAAc,EACd,YAAqB;QAErB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,UAAU,iBAAiB,MAAM,EAAE,CAAC,CAAC;QAGxF,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAG5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,SAAS,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;SAC/D,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAGD,IAAI,QAAQ,CAAC,MAAM,KAAK,yCAAkB,CAAC,cAAc,EAAE,CAAC;YAC1D,MAAM,IAAI,4BAAmB,CAC3B,2DAA2D,CAC5D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC/C,MAAM,EAAE,yCAAkB,CAAC,MAAM;gBACjC,eAAe,EAAE,IAAI,IAAI,EAAE;gBAC3B,eAAe,EAAE,MAAM;gBACvB,iBAAiB,EAAE,YAAY;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,UAAU,iCAAiC,CAAC,CAAC;YAGzE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBACjE,KAAK,EAAE;oBACL,EAAE,OAAO,EAAE,MAAM,EAAE;oBACnB,EAAE,UAAU,EAAE,MAAM,EAAE;iBACvB;gBACD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;gBAC1C,uBAAuB,EAAE,WAAW,CAAC,EAAE;gBACvC,WAAW,EAAE,YAAY,UAAU,kDAAkD;gBACrF,KAAK,EAAE,6BAA6B;gBACpC,IAAI,EAAE,0CAAoB,CAAC,iBAAiB;gBAC5C,UAAU,EAAE,UAAU;gBACtB,aAAa,EAAE,QAAQ,CAAC,eAAe,IAAI,CAAC;gBAC5C,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,0CAA0C;aACpD,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAClB,UAAkB,EAClB,MAAc,EACd,YAAoB;QAEpB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,UAAU,iBAAiB,MAAM,EAAE,CAAC,CAAC;QAGvF,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAG5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,SAAS,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;SAC/D,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAGD,IAAI,QAAQ,CAAC,MAAM,KAAK,yCAAkB,CAAC,cAAc,EAAE,CAAC;YAC1D,MAAM,IAAI,4BAAmB,CAC3B,4DAA4D,CAC7D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC/C,MAAM,EAAE,yCAAkB,CAAC,iBAAiB;gBAC5C,eAAe,EAAE,IAAI,IAAI,EAAE;gBAC3B,eAAe,EAAE,MAAM;gBACvB,iBAAiB,EAAE,YAAY;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,UAAU,2BAA2B,CAAC,CAAC;YAGnE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBACjE,KAAK,EAAE;oBACL,EAAE,OAAO,EAAE,MAAM,EAAE;oBACnB,EAAE,UAAU,EAAE,MAAM,EAAE;iBACvB;gBACD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;gBAC1C,uBAAuB,EAAE,WAAW,CAAC,EAAE;gBACvC,WAAW,EAAE,YAAY,UAAU,0CAA0C,YAAY,EAAE;gBAC3F,KAAK,EAAE,mCAAmC;gBAC1C,IAAI,EAAE,0CAAoB,CAAC,iBAAiB;gBAC5C,UAAU,EAAE,UAAU;gBACtB,aAAa,EAAE,QAAQ,CAAC,eAAe,IAAI,CAAC;gBAC5C,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,oCAAoC;aAC9C,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,4BAAmB,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAAC,MAAc;QAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wDAAwD,MAAM,EAAE,CAAC,CAAC;QAGlF,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAE5C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACnD,KAAK,EAAE,EAAE,MAAM,EAAE,yCAAkB,CAAC,cAAc,EAAE;YACpD,SAAS,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;YAC9D,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,SAAS,CAAC,MAAM,iCAAiC,CAAC,CAAC;QAElF,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,KAAK,CAAC,wBAAwB,CAAC,MAAc;QACnD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,MAAM,EAAE;gBACnB,EAAE,UAAU,EAAE,MAAM,EAAE;aACvB;YACD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;QAGD,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC3C,MAAM,IAAI,4BAAmB,CAC3B,qEAAqE,CACtE,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAtMY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADL,oBAAU;QAED,oBAAU;QACZ,uDAAyB;GAR5D,oBAAoB,CAsMhC", "sourcesContent": ["import { BadRequestException, Injectable, NotFoundException, Logger } from '@nestjs/common';\nimport { InjectRepository } from '@nestjs/typeorm';\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\nimport { Repository } from 'typeorm';\nimport { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';\nimport { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\n\nexport interface AuditDecisionDto {\n  contractId: string;\n  decision: 'approve' | 'reject';\n  observations?: string;\n}\n\n@Injectable()\nexport class AuditContractService {\n  private readonly logger = new Logger(AuditContractService.name);\n\n  constructor(\n    @InjectRepository(ContractEntity)\n    private readonly contractRepository: Repository<ContractEntity>,\n    @InjectRepository(OwnerRoleRelationEntity)\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\n    private readonly createNotificationService: CreateNotificationService,\n  ) {}\n\n  /**\n   * Aprovar contrato pela auditoria\n   */\n  async approveContract(\n    contractId: string,\n    userId: string,\n    observations?: string,\n  ): Promise<{ success: boolean; message: string }> {\n    this.logger.log(`Iniciando aprovação de contrato ${contractId} pelo auditor ${userId}`);\n\n    // 1. Validar permissões de auditoria\n    await this.validateAuditPermissions(userId);\n\n    // 2. Buscar contrato\n    const contract = await this.contractRepository.findOne({\n      where: { id: contractId },\n      relations: ['investor', 'investor.owner', 'investor.business'],\n    });\n\n    if (!contract) {\n      throw new NotFoundException('Contrato não encontrado');\n    }\n\n    // 3. Validar status do contrato\n    if (contract.status !== ContractStatusEnum.AWAITING_AUDIT) {\n      throw new BadRequestException(\n        'Apenas contratos aguardando auditoria podem ser aprovados'\n      );\n    }\n\n    try {\n      // 4. Atualizar status para ativo\n      await this.contractRepository.update(contractId, {\n        status: ContractStatusEnum.ACTIVE,\n        auditApprovedAt: new Date(),\n        auditApprovedBy: userId,\n        auditObservations: observations,\n        updatedAt: new Date(),\n      });\n\n      this.logger.log(`Contrato ${contractId} aprovado e ativado com sucesso`);\n\n      // 5. Criar notificação para o broker\n      const userProfile = await this.ownerRoleRelationRepository.findOne({\n        where: [\n          { ownerId: userId },\n          { businessId: userId },\n        ],\n        relations: { role: true },\n      });\n\n      await this.createNotificationService.create({\n        userOwnerRoleRelationId: userProfile.id,\n        description: `Contrato ${contractId} foi aprovado pela auditoria e está agora ativo.`,\n        title: `Contrato Aprovado e Ativado`,\n        type: NotificationTypeEnum.CONTRACT_APPROVED,\n        contractId: contractId,\n        contractValue: contract.investmentValue || 0,\n        investorId: contract.investorId,\n      });\n\n      return {\n        success: true,\n        message: 'Contrato aprovado e ativado com sucesso.',\n      };\n\n    } catch (error) {\n      this.logger.error('Erro ao aprovar contrato:', error);\n      throw new BadRequestException(`Erro ao aprovar contrato: ${error.message}`);\n    }\n  }\n\n  /**\n   * Rejeitar contrato pela auditoria\n   */\n  async rejectContract(\n    contractId: string,\n    userId: string,\n    observations: string,\n  ): Promise<{ success: boolean; message: string }> {\n    this.logger.log(`Iniciando rejeição de contrato ${contractId} pelo auditor ${userId}`);\n\n    // 1. Validar permissões de auditoria\n    await this.validateAuditPermissions(userId);\n\n    // 2. Buscar contrato\n    const contract = await this.contractRepository.findOne({\n      where: { id: contractId },\n      relations: ['investor', 'investor.owner', 'investor.business'],\n    });\n\n    if (!contract) {\n      throw new NotFoundException('Contrato não encontrado');\n    }\n\n    // 3. Validar status do contrato\n    if (contract.status !== ContractStatusEnum.AWAITING_AUDIT) {\n      throw new BadRequestException(\n        'Apenas contratos aguardando auditoria podem ser rejeitados'\n      );\n    }\n\n    try {\n      // 4. Atualizar status para rejeitado\n      await this.contractRepository.update(contractId, {\n        status: ContractStatusEnum.REJECTED_BY_AUDIT,\n        auditRejectedAt: new Date(),\n        auditRejectedBy: userId,\n        auditObservations: observations,\n        updatedAt: new Date(),\n      });\n\n      this.logger.log(`Contrato ${contractId} rejeitado pela auditoria`);\n\n      // 5. Criar notificação para o broker\n      const userProfile = await this.ownerRoleRelationRepository.findOne({\n        where: [\n          { ownerId: userId },\n          { businessId: userId },\n        ],\n        relations: { role: true },\n      });\n\n      await this.createNotificationService.create({\n        userOwnerRoleRelationId: userProfile.id,\n        description: `Contrato ${contractId} foi rejeitado pela auditoria. Motivo: ${observations}`,\n        title: `Contrato Rejeitado pela Auditoria`,\n        type: NotificationTypeEnum.CONTRACT_REJECTED,\n        contractId: contractId,\n        contractValue: contract.investmentValue || 0,\n        investorId: contract.investorId,\n      });\n\n      return {\n        success: true,\n        message: 'Contrato rejeitado pela auditoria.',\n      };\n\n    } catch (error) {\n      this.logger.error('Erro ao rejeitar contrato:', error);\n      throw new BadRequestException(`Erro ao rejeitar contrato: ${error.message}`);\n    }\n  }\n\n  /**\n   * Listar contratos aguardando auditoria\n   */\n  async getContractsAwaitingAudit(userId: string): Promise<ContractEntity[]> {\n    this.logger.log(`Buscando contratos aguardando auditoria para usuário ${userId}`);\n\n    // Validar permissões de auditoria\n    await this.validateAuditPermissions(userId);\n\n    const contracts = await this.contractRepository.find({\n      where: { status: ContractStatusEnum.AWAITING_AUDIT },\n      relations: ['investor', 'investor.owner', 'investor.business'],\n      order: { createdAt: 'DESC' },\n    });\n\n    this.logger.log(`Encontrados ${contracts.length} contratos aguardando auditoria`);\n\n    return contracts;\n  }\n\n  /**\n   * Validar se o usuário tem permissões de auditoria\n   */\n  private async validateAuditPermissions(userId: string): Promise<void> {\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\n      where: [\n        { ownerId: userId },\n        { businessId: userId },\n      ],\n      relations: { role: true },\n    });\n\n    if (!userProfile) {\n      throw new BadRequestException('Perfil de usuário não encontrado.');\n    }\n\n    // Apenas superadmin pode fazer auditoria\n    if (userProfile.role.name !== 'superadmin') {\n      throw new BadRequestException(\n        'Apenas super administradores podem realizar auditoria de contratos.'\n      );\n    }\n  }\n}\n"]}