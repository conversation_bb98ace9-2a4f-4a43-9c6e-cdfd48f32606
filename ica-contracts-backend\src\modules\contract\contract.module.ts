import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApisModule } from 'src/apis/apis.module';
import { ContractDeletionEntity } from 'src/shared/database/typeorm/entities/contract-deletion.entity';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { ValidateAdviserOwnerMiddleware } from 'src/shared/middlewares/validate-adviser-owner.middleware';
import { SharedModule } from 'src/shared/shared.module';

import { NotificationModule } from '../notifications/notification.module';
import { ContractController } from './controller/contract.controller';
import { AddSignatoriesService } from './services/add-signataries.service';
import { CreateContractAdditiveManualService } from './services/create-contract-additive-manual.service';
import { CreateContractAdditiveService } from './services/create-contract-additive.service';
import { CheckDuplicateContractHandler } from './services/create-contract-manual/concrete-handlers/check-duplicate-contract.handler';
import { SendContractHandler } from './services/create-contract-manual/concrete-handlers/send-contract.handler';
import { ValidateAdvisorHandler } from './services/create-contract-manual/concrete-handlers/validate-advisors.handler';
import { CreateContractManualService } from './services/create-contract-manual/create-contract-manual.service';
import { CreateContractService } from './services/create-contract.service';
import { DeleteContractService } from './services/delete-contract.service';
import { EditNewContractService } from './services/editt-new-contract.service';
import { GetContractsByInvestorService } from './services/get-contract-by-investor.service';
import { GetContractDetailService } from './services/get-contract-detail.service';
import { GetContractsService } from './services/get-contracts.service';
import { GetContratAddendumsByIdService } from './services/get-contrat-addendums-by-id.service';
import { GetOneContractService } from './services/get-one-contracts.service';
import { ListContractsSuperadminService } from './services/list-contracts-superadmin.service';
import { RenewContractService } from './services/renew-contract.service';
import { SendEmailNotificationContract } from './services/send-notification.service';
import { UploadProofPaymentAddendumService } from './services/upload-proof-payment-addendum.service';
import { UploadProofPaymentService } from './services/upload-proof-payment.service';
import { UpgradeContractService } from './services/upgrade-contract.service';

@Module({
  imports: [
    SharedModule,
    ApisModule,
    NotificationModule,
    TypeOrmModule.forFeature([
      ContractEntity,
      OwnerRoleRelationEntity,
      ContractDeletionEntity,
    ]),
  ],
  controllers: [ContractController],
  providers: [
    CreateContractService,
    GetContractsService,
    GetOneContractService,
    AddSignatoriesService,
    CreateContractManualService,
    RenewContractService,
    GetContractsByInvestorService,
    UploadProofPaymentService,
    SendEmailNotificationContract,
    CreateContractAdditiveService,
    CreateContractAdditiveManualService,
    CheckDuplicateContractHandler,
    SendContractHandler,
    ValidateAdvisorHandler,
    ListContractsSuperadminService,
    GetContratAddendumsByIdService,
    UploadProofPaymentAddendumService,
    GetContractDetailService,
    DeleteContractService,
    EditNewContractService,
    UpgradeContractService,
  ],
  exports: [GetContractsByInvestorService],
})
export class ContractModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ValidateAdviserOwnerMiddleware)
      .forRoutes(
        { path: 'contract', method: RequestMethod.GET },
        { path: 'contract/add-signatories', method: RequestMethod.POST },
        { path: 'contract/add-signatories', method: RequestMethod.POST },
      );
  }
}
