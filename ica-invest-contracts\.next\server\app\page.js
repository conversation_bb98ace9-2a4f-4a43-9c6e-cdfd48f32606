(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},68795:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>d,pages:()=>p,routeModule:()=>m,tree:()=>c});var r=s(73137),a=s(54647),n=s(4183),o=s.n(n),i=s(71775),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let u=r.AppPageRouteModule,c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76768)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51918,23)),"next/dist/client/components/not-found-error"]}],p=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\page.tsx"],d="/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new u({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29670:(e,t,s)=>{Promise.resolve().then(s.bind(s,24138))},24138:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Home});var r=s(60080),a=s(69957);s(29533);var n=s(32411),o=s(45682),i=s(96413),l=s(9885),u=s(57114);function Home(){let{handleSignInUser:e}=(0,l.useContext)(o.Z),t=(0,u.useRouter)(),[s,c]=(0,l.useState)(""),[p,d]=(0,l.useState)(""),[x,m]=(0,l.useState)("admin"),[h,g]=(0,l.useState)(!1),v=(0,l.useRef)(null),f=(0,l.useRef)(null);(0,l.useEffect)(()=>{let e="true"===sessionStorage.getItem("isAuthenticated"),s=sessionStorage.getItem("token");e&&s&&t.push("/home")},[t]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{let e=v.current?.value||"",t=f.current?.value||"";if(e){let t=e.length<=14?(0,i.VL)(e):(0,i.PK)(e);c(t)}t&&d(t)},300);return()=>clearTimeout(e)},[]);let handleSubmit=()=>{let t=(0,i.p4)(s);if(!t||!p){console.warn("Campos obrigat\xf3rios n\xe3o preenchidos");return}e({document:t,password:p,setLoading:g,type:x})};return(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"absolute w-full h-screen",children:r.jsx("div",{className:"absolute inset-0 bg-half-half w-full"})}),(0,r.jsxs)("div",{className:"flex min-h-full h-screen flex-1 flex-col items-center justify-center px-6 py-12 lg:px-8 relative z-10",children:[r.jsx("img",{className:"mx-auto h-10 w-auto",src:"/logo.svg",alt:"Your Company"}),r.jsx("div",{className:"md:w-4/12 p-14 m-auto bg-opacity-30 backdrop-blur-sm border border-[#FF9900] rounded-lg shadow-2xl shadow-current bg-zinc-900",style:{borderWidth:"1px"},children:(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-sm",children:[r.jsx("div",{children:r.jsx("div",{className:"mb-2",children:r.jsx(n.Z,{id:"document",label:"",name:"document",placeholder:"Documento",type:"text",value:s,onInput:({target:e})=>{let t=e.value;t.length<=14?c((0,i.VL)(t)):c((0,i.PK)(t))},onChange:({target:e})=>{let{value:t}=e;t.length<=14?c((0,i.VL)(t)):c((0,i.PK)(t))},ref:v})})}),r.jsx("div",{children:r.jsx("div",{className:"mb-5",children:r.jsx(n.Z,{id:"password",label:"",name:"password",placeholder:"Senha",type:"password",onKeyDown:e=>{"Enter"===e.key&&handleSubmit()},ref:f,value:p,onInput:({target:e})=>{d(e.value)},onChange:({target:e})=>{d(e.value)}})})}),r.jsx("div",{children:r.jsx(a.z,{onClick:handleSubmit,loading:h,disabled:h,size:"lg",className:"w-full",children:"Entrar"})})]})})]})]})}},76768:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>l});var r=s(17536);let a=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\page.tsx`),{__esModule:n,$$typeof:o}=a,i=a.default,l=i},29533:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[4103,6426,1808,7878,7207,278,2411],()=>__webpack_exec__(68795));module.exports=s})();