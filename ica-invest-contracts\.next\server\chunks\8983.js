"use strict";exports.id=8983,exports.ids=[8983],exports.modules={76027:(e,a,t)=>{t.d(a,{Z:()=>FilterModal});var l=t(60080),r=t(47541),s=t(69957),n=t(80223),d=t(11330),o=t(13649);let i={startData:"",endData:"",type:"all",status:"Todos"},u=[{label:"Todos",value:"all"},{label:"Contratos SCP",value:"SCP"},{label:"Contratos M\xfatuo",value:"P2P"}];function FilterModal({activeModal:e,setActiveModal:a,filterData:t,setFilterData:c,handleSearch:x,hidenButton:m,setPage:p,signatarie:f}){let updateFilter=e=>{c(e),p(1),x(f,e)};return(0,l.jsxs)(o.h_,{children:[(0,l.jsxs)(o.$F,{onClick:()=>a(!e),className:"flex w-24 text-sm justify-around p-2 rounded-lg bg-[#3A3A3A] cursor-pointer",children:[l.jsx(r.Z,{width:15,color:"#fff"}),l.jsx("p",{children:"Filtros"})]}),(0,l.jsxs)(o.AW,{className:"mr-1 max-w-[270px] px-3",children:[l.jsx(o.Ju,{className:"text-md",children:"Filtros"}),l.jsx(o.VD,{}),(0,l.jsxs)("div",{className:"flex flex-col justify-between mt-2",children:[(0,l.jsxs)("div",{className:"flex md:flex-row flex-col justify-between mb-4",children:[(0,l.jsxs)("div",{className:"mb-2 md:mb-0 md:w-[48%]",children:[l.jsx("p",{className:"text-xs",children:"In\xedcio"}),l.jsx("input",{value:t.startData,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:({target:e})=>{let a={...t,startData:e.value};updateFilter(a)},type:"date"})]}),(0,l.jsxs)("div",{className:"mb-2 md:mb-0 md:w-[48%]",children:[l.jsx("p",{className:"text-xs",children:"Fim"}),l.jsx("input",{value:t.endData,className:"rounded-md text-xs bg-black border border-orange p-2 w-full",onChange:({target:e})=>{let a={...t,endData:e.value};updateFilter(a)},type:"date"})]})]}),(0,l.jsxs)("div",{className:"w-full",children:[l.jsx("p",{className:"text-xs",children:"Tipo de contrato"}),l.jsx(n.Z,{value:t.type,onChange:e=>{let a={...t,type:e.target.value};updateFilter(a)},children:u.map((e,a)=>l.jsx("option",{value:e.value,children:e.label},a))})]}),(0,l.jsxs)("div",{className:"w-full mt-4",children:[l.jsx("p",{className:"text-xs",children:"Status do contrato"}),l.jsx(n.Z,{value:t.status,onChange:e=>{let a={...t,status:e.target.value};updateFilter(a)},children:d.Z.map((e,a)=>l.jsx("option",{value:e.value,children:e.label},a))})]})]}),!m&&l.jsx("div",{className:"m-auto mt-5 flex gap-2 mb-1",children:(""!==t.startData||""!==t.endData||"all"!==t.type||"Todos"!==t.status)&&l.jsx(s.z,{variant:"secondary",onClick:()=>{c(i),p(1),x(f,i),a(!1)},className:"w-full",children:"Resetar"})})]})]})}},69145:(e,a,t)=>{t.d(a,{Z:()=>InputTextArea});var l=t(60080),r=t(9885);function InputTextArea({setValue:e,value:a,error:t,errorMessage:s,width:n="100%",register:d,name:o,placeholder:i="",className:u=""}){return(0,r.useEffect)(()=>{},[n]),l.jsx("div",{className:"input",style:{width:n},children:l.jsx("textarea",{...d&&{...d(o)},value:a&&a,placeholder:i,id:o,onChange:({target:a})=>{e&&e(a.value)},className:`${u} h-12 w-full min-h-40 p-2 text-white rounded-xl ${t?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})})}},49714:(e,a,t)=>{t.d(a,{l:()=>l});let l="Shayra Madalena Lyra de Pinho"},16039:(e,a,t)=>{t.d(a,{f:()=>profileCanDeleteContract});var l=t(32775),r=t(90682);function profileCanDeleteContract(e){let a=(0,r.e)();if("superadmin"===a.name){if(e===l.rd.DRAFT||e===l.rd.AWAITING_AUDIT||e===l.rd.REJECTED_BY_AUDIT||e===l.rd.AWAITING_AUDIT_SIGNATURE||e===l.rd.AWAITING_INVESTOR_SIGNATURE||e===l.rd.EXPIRED_BY_AUDIT||e===l.rd.EXPIRED_BY_INVESTOR||e===l.rd.EXPIRED_FAILURE_PROOF_PAYMENT||e===l.rd.GENERATE_CONTRACT_FAILED)return!0}else if("broker"===a.name&&e===l.rd.DRAFT)return!0}},33050:(e,a,t)=>{t.d(a,{Z:()=>formatDate,l:()=>formatDateToEnglishType});var l=t(95081),r=t.n(l);function formatDate(e){return r().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?r().utc(e).format("YYYY-MM-DD"):r().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},85334:(e,a,t)=>{t.d(a,{H:()=>useNavigation});var l=t(57114);let useNavigation=()=>{let e=(0,l.useRouter)();return{navigation:a=>e.push(a)}}},50194:(e,a,t)=>{function calculateTotalContractValue(e){let a=Number(e?.valorInvestimento||0),t=e?.addendum?.reduce((e,a)=>e+Number(a.value||0),0)||0;return(a+t).toLocaleString("pt-br",{style:"currency",currency:"BRL"})}t.d(a,{F:()=>calculateTotalContractValue})},69258:(e,a,t)=>{t.d(a,{U:()=>l});let l={CONTRACTS:(e,a,t,l,r)=>["contracts",e,a,t,...Object.values(l),r]}}};