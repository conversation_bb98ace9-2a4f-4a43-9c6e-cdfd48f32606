import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm';

import { OwnerRoleRelationEntity } from './owner-role-relation.entity';
import { ContractEntity } from './contract.entity';
import { AddendumEntity } from './addendum.entity';

export enum NotificationTypeEnum {
  DUPLICATED_DOCUMENT = 'DUPLICATED_DOCUMENT',
  NEW_CONTRACT = 'NEW_CONTRACT',
  INCLUDE_ADDITIVE = 'INCLUDE_ADDITIVE',
}

@Entity('notification')
export class NotificationEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    name: 'type',
    enum: NotificationTypeEnum,
  })
  type: NotificationTypeEnum;

  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'description' })
  description: string;

  @Column({
    name: 'viewed',
    default: false,
  })
  viewed: boolean;

  @Column({
    name: 'contract_id',
  })
  contractId: string;

  @Column({
    name:'addendum_id',
    nullable: true,
  })
  addendumId: number;

  @Column({
    name:'investor_id',
    nullable: true,
  })
  investorId: string

  @Column({
    name: 'contract_value',
    nullable: true,
    type: 'decimal',
  })
  contractValue: number | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;

  @ManyToOne(() => OwnerRoleRelationEntity, (item) => item.userNotifications)
  @JoinColumn({
    name: 'broker_id',
    referencedColumnName: 'id',
  })
  brokerOwnerRoleRelation: Relation<OwnerRoleRelationEntity>;

  @ManyToOne(() => OwnerRoleRelationEntity, (item) => item.investorNotifications)
  @JoinColumn({
    name: 'investor_id',
    referencedColumnName: 'id',
  })
  investor: Relation<OwnerRoleRelationEntity>;

  @ManyToOne(() => OwnerRoleRelationEntity, (item) => item.adminNotifications)
  @JoinColumn({
    name: 'admin_owner_role_relation_id',
    referencedColumnName: 'id',
  })
  adminOwnerRoleRelation: Relation<OwnerRoleRelationEntity>;

  @ManyToOne(() => ContractEntity, (item) => item.notifications)
  @JoinColumn({
    name: 'contract_id',
    referencedColumnName: 'id',
  })
  contract: Relation<ContractEntity>;

  @ManyToOne(() => AddendumEntity, (item) => item.addendumNotifications)
  @JoinColumn({
    name: 'addendum_id',
    referencedColumnName: 'id',
  })
  addendum: Relation<AddendumEntity>;
}
