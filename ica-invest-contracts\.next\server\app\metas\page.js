(()=>{var e={};e.id=2903,e.ids=[2903],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},54012:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>m,routeModule:()=>p,tree:()=>c});var r=a(73137),s=a(54647),l=a(4183),o=a.n(l),n=a(71775),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);a.d(t,i);let d=r.AppPageRouteModule,c=["",{children:["metas",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,24103)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],m=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\metas\\page.tsx"],x="/metas/page",u={require:a,loadChunk:()=>Promise.resolve()},p=new d({definition:{kind:s.x.APP_PAGE,page:"/metas/page",pathname:"/metas",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},72405:(e,t,a)=>{Promise.resolve().then(a.bind(a,12839))},12839:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Metas});var r=a(60080),s=a(97669),l=a(47956);function ProgressTable({goals:e,filters:t,filteredType:a}){function ProgressBar({goal:e}){let t=Number(e.targetAmount),s=Number(e.perspectiveAmount),l="goals"===a?e.percentAchieved||0:s/t*100||0;return(0,r.jsxs)("div",{className:"text-white my-5 flex items-center w-10/12",children:[r.jsx("p",{className:"w-[30%]",children:e.brokerName}),(0,r.jsxs)("div",{className:"flex-1 bg-[#1C1C1C] rounded-lg ml-10 h-9 relative",children:[r.jsx("div",{style:{width:`${l<=100?l:100}%`},className:`relative text-center bg-orange-linear rounded-l-lg ${l?"rounded-r-lg":""} h-full flex items-center justify-center`,children:(0,r.jsxs)("p",{className:` font-extrabold ${l<5?"absolute right-[-5px] translate-x-[100%]":"text-[#282828]"}`,children:[Math.floor(l||0),"%"]})}),(0,r.jsxs)("p",{className:"absolute right-5 top-[50%] translate-y-[-50%] text-xs",children:[Number("goals"===a?e.totalAchieved:e.perspectiveAmount||0).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})," / ",Number(e.targetAmount).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})]})]})]})}return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-[20px] h-[20px] bg-orange-linear"}),r.jsx("p",{className:"ml-1 text-sm",children:t[0].label})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-[20px] h-[20px] bg-[#3A3A3A]"}),r.jsx("p",{className:"ml-1 text-sm",children:t[1].label})]})]}),r.jsx("div",{children:e&&e?.length>0?e?.map(e=>r.jsx(ProgressBar,{goal:e},e.goalName)):r.jsx("div",{className:"mt-5 text-center",children:r.jsx("p",{children:"Nenhuma meta cadastrada."})})})]})}var o=a(9885),n=a(85814),i=a(34751),d=a(22796),c=a(32411),m=a(96413),x=a(64731),u=a.n(x),p=a(90682),h=a(69957);function AddGoals({brokerAdvisor:e,setAddgoalModal:t,golsData:a,brokerGoalId:s}){let[l,x]=(0,o.useState)(e),[g,v]=(0,o.useState)(""),[b,f]=(0,o.useState)([]),[j,N]=(0,o.useState)(!1),y=(0,p.e)();return(0,r.jsxs)("div",{children:[r.jsx("div",{children:r.jsx("div",{className:"md:w-3/4 mb-5 m-auto",children:e&&r.jsx(d.Z,{selectable:!0,label:"",items:l,value:g,setValue:e=>{let t=String(e).split(" - ")[1]||"",r=String(e).split(" - ")[0]||"",o=a?a.split("-")[0]:String(u()().year()),n=a?a.split("-")[1]:String(u()().month()+1),i=l.filter(e=>e.id!==t);x(i);let d=`${o}-${n.padStart(2,"0")}-01`,c=`${o}-${n.padStart(2,"0")}-${u()(`${o}-${n}`,"YYYY-MM").endOf("month").date()}`;f([...b,{ownerRelationId:t,name:r,dateFrom:d,dateTo:c,observations:"",targetAmount:"",adminOwnerRelationId:"superadmin"===y.name?y.roleId:void 0,brokerGoalId:"broker"===y.name?s:void 0}])}})})}),(0,r.jsxs)("div",{className:"flex gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-[20px] h-[20px] bg-orange-linear"}),r.jsx("p",{className:"ml-1 text-sm",children:"Meta Atingida"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-[20px] h-[20px] bg-[#3A3A3A]"}),r.jsx("p",{className:"ml-1 text-sm",children:"Meta Restante"})]})]}),r.jsx("div",{children:b.map(e=>(0,r.jsxs)("div",{className:"text-white my-5 flex items-center",children:[r.jsx("p",{className:"w-3/12",children:e.name}),(0,r.jsxs)("div",{className:"flex-1 rounded-lg ml-10 h-9 flex items-center gap-4",children:[r.jsx("div",{children:r.jsx(c.Z,{id:"",label:"",placeholder:"R$",name:"",type:"text",value:e.targetAmount,onChange:t=>{let a=b.filter(t=>t.ownerRelationId!==e.ownerRelationId);f([...a,{...e,targetAmount:(0,m.Ht)(t.target.value)}])}})}),r.jsx("div",{className:"w-7/12",children:r.jsx(c.Z,{id:"",label:"",placeholder:"Observa\xe7\xf5es",name:"",type:"text",value:e.observations,onChange:t=>{let a=b.filter(t=>t.ownerRelationId!==e.ownerRelationId);f([...a,{...e,observations:t.target.value}])}})})]})]},e.ownerRelationId))}),r.jsx("div",{className:"flex w-full justify-end mt-10 mb-5",children:r.jsx("div",{children:r.jsx(h.z,{onClick:function(){N(!0);let e=b.map(e=>({...e,targetAmount:Number(e.targetAmount.replaceAll(".","").replace(",","."))}));n.Z.post(`/goals/${"superadmin"===y.name?"broker":"advisor"}`,e).then(e=>{i.Am.success("Metas cadastrada com sucesso!"),t(!1)}).catch(e=>{i.Am.error(e.response.data.message||"N\xe3o foi possivel cadastrar as metas")}).finally(()=>N(!1))},loading:j,children:"Concluir"})})})]})}var g=a(52451),v=a.n(g),b=a(40491);let f={src:"/_next/static/media/graphic.6e8f2042.svg",height:31,width:31,blurWidth:0,blurHeight:0};function AdminDashboard({data:e}){return(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 mt-5",children:[r.jsx("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[r.jsx("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:r.jsx(v(),{alt:"",src:b.Z,width:20})}),r.jsx("p",{className:"text-sm",children:"Meta de capta\xe7\xe3o"}),r.jsx("p",{className:"text-xs font-extralight capitalize",children:e.monthName}),r.jsx("p",{className:"mt-3 text-2xl font-bold",children:Number(e?.targetAmount).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})})]})}),r.jsx("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 pb-2 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[r.jsx("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:r.jsx(v(),{alt:"",src:f,width:20})}),r.jsx("p",{className:"text-sm",children:"Progresso"}),r.jsx("p",{className:"text-xs font-extralight",children:"Porcentagem"}),(0,r.jsxs)("p",{className:"mt-3 text-2xl font-bold",children:[Math.floor(e.percentage||0),"%"]}),(0,r.jsxs)("p",{className:"text-xs font-extralight",children:["*",Number(e?.amountAchieved).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})]})]})}),r.jsx("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[r.jsx("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:r.jsx(v(),{alt:"",src:f,width:20})}),r.jsx("p",{className:"text-sm",children:e.over<0?"Valor excedido":"Valor \xe0 Captar"}),r.jsx("p",{className:"text-xs font-extralight",children:e.over<0?"Valor excedido da meta":"Valor Faltante"}),r.jsx("p",{className:"mt-3 text-2xl font-bold",children:e.over<0?(-1*e.over).toLocaleString("pt-BR",{style:"currency",currency:"BRL"}):e.over.toLocaleString("pt-BR",{style:"currency",currency:"BRL"})})]})})]})}function GoalDashboard({data:e}){let t=(0,p.e)();return(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 mt-5",children:[r.jsx("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[r.jsx("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:r.jsx(v(),{alt:"",src:b.Z,width:20})}),r.jsx("p",{className:"text-sm",children:"Meta de capta\xe7\xe3o"}),r.jsx("p",{className:"text-xs font-extralight capitalize",children:e.monthName}),r.jsx("p",{className:"mt-3 text-2xl font-bold",children:Number(e?.targetAmount).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})})]})}),r.jsx("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full",children:[r.jsx("div",{className:"bg-orange-linear p-1 rounded-full mb-1",children:r.jsx(v(),{alt:"",src:f,width:20})}),r.jsx("p",{className:"text-sm",children:"Per\xedodo"}),r.jsx("p",{className:"text-xs font-extralight capitalize",children:e.monthName}),r.jsx("p",{className:"mt-3 text-2xl font-bold",children:e.month})]})}),("broker"===t.name||"advisor"===t.name)&&r.jsx("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:(0,r.jsxs)("div",{className:"flex flex-col w-full",children:[r.jsx("p",{className:"text-sm text-center",children:"Observa\xe7\xf5es"}),r.jsx("p",{className:"text-xs font-extralight mt-5 text-start",children:e.observation})]})})]})}a(73079);var j=a(47541),N=a(88401),y=a(34129);function newFormatDate(e){let t=e.split("T")[0],a=t.split("-");return{dateFormated:`${a[2]}/${a[1]}`,month:u()().locale("pt-br").month(Number(a[1])-1).format("MMMM")}}var A=a(24577);function AddPerspective({brokerGoalId:e}){let[t,a]=(0,o.useState)(""),[s,l]=(0,o.useState)(""),[d,x]=(0,o.useState)(!1);return(0,r.jsxs)("div",{className:"mt-10",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-white mb-2",children:"Adicionar o valor"}),r.jsx("div",{className:"w-72",children:r.jsx(c.Z,{bg:"fill",id:"",label:"",placeholder:"R$",name:"",type:"text",value:t,onChange:e=>{a((0,m.Ht)(e.target.value))}})})]}),(0,r.jsxs)("div",{className:"mt-5",children:[r.jsx("p",{className:"text-white mb-2",children:"Adicione Observa\xe7\xf5es (caso necess\xe1rio)"}),r.jsx("div",{className:"w-[500px]",children:r.jsx("textarea",{value:s,placeholder:"Observa\xe7\xf5es",className:"bg-[#1C1C1C] h-32 w-full p-2 text-white rounded-xl ring-1 ring-inset flex-1 ring-[#FF9900]",onChange:({target:e})=>l(e.value)})})]}),r.jsx("div",{children:r.jsx("div",{className:"flex justify-end mt-10",children:r.jsx("div",{children:r.jsx(h.z,{onClick:()=>{x(!0);let a={brokerGoalId:e,observation:s,amount:Number(t.replaceAll(".","").replace(",","."))};n.Z.patch("/goals/perspective-broker-amount",a).then(e=>{i.Am.success("Perspectiva cadastrada com sucesso!"),window.location.reload()}).catch(e=>{(0,A.Z)(e,"Erro ao criar perspectiva!")}).finally(()=>x(!1))},loading:d,children:"Concluir"})})})})]})}var w=a(93640);function Metas(){let[e,t]=(0,o.useState)(),[a,d]=(0,o.useState)(""),[c,m]=(0,o.useState)(!1),[x,g]=(0,o.useState)(!1),[v,b]=(0,o.useState)(!1),[f,$]=(0,o.useState)(""),[D,C]=(0,o.useState)(),[k,S]=(0,o.useState)("goals"),[F,M]=(0,o.useState)("goals"),[P,B]=(0,o.useState)({percentage:0,month:"",monthName:"",observation:"",over:0,amountAchieved:0,targetAmount:0});function getDate(){let e="",t="";return""===f?(t=String(u()().year()),e=String(u()().month()+1)):(t=f.split("-")[0],e=f.split("-")[1]),{month:e,year:t}}let _=(0,p.e)();function handleSelectTypeGoals(){"broker"===_.name&&(n.Z.get(`/goals/broker/${_.roleId}`,{params:{year:getDate().year,month:getDate().month,type:k}}).then(e=>{let a=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-01`).dateFormated,r=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-${u()(`${getDate().year}-${getDate().month}`,"YYYY-MM").endOf("month").date()}`).dateFormated,s=e.data.advisorMetrics.map(e=>({goalName:e.goalName,brokerName:e.advisorName,brokerDocument:e.advisorDocument,targetAmount:e.targetAmount,totalAchieved:e.totalAchieved,percentAchieved:e.percentAchieved}));t({results:s,totalBrokerAchieved:0,totalGoalAmount:0}),d(e.data.id),B({amountAchieved:e.data.totalAchieved,month:`${a} \xe0 ${r}`,monthName:newFormatDate(`${getDate().year}-${getDate().month}-01`).month,observation:e.data.observations,over:Number(e.data.targetAmount)-Number(e.data.totalAchieved),percentage:e.data.percentAchieved,targetAmount:e.data.targetAmount})}).catch(e=>{(0,A.Z)(e,"N\xe3o conseguimos encontrar as metas!")}),getAcessors()),"advisor"===_.name&&n.Z.get(`/goals/advisor/${_.roleId}`,{params:{ownerRelationId:_.roleId,year:getDate().year,month:getDate().month}}).then(e=>{let a=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-01`).dateFormated,r=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-${u()(`${getDate().year}-${getDate().month}`,"YYYY-MM").endOf("month").date()}`).dateFormated;t({totalBrokerAchieved:0,totalGoalAmount:0,results:[{goalName:e.data.goalName,brokerName:e.data.advisorName,brokerDocument:e.data.advisorDocument,percentAchieved:e.data.percentAchieved,perspectiveAmount:e.data.perspectiveAmount,targetAmount:e.data.targetAmount,totalAchieved:e.data.totalAchieved}]}),B({amountAchieved:e.data.totalAchieved,month:`${a} \xe0 ${r}`,monthName:newFormatDate(`${getDate().year}-${getDate().month}-01`).month,observation:"",over:Number(e.data.targetAmount)-Number(e.data.totalAchieved),percentage:e.data.percentAchieved,targetAmount:e.data.targetAmount})}).catch(e=>{(0,A.Z)(e,"N\xe3o conseguimos encontrar as metas!")}),"admin"===_.name&&getGoalsadmin(),"superadmin"===_.name&&(n.Z.get("/goals/superadmin/broker",{params:{year:getDate().year,month:getDate().month,type:k}}).then(e=>{let a=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-01`).dateFormated,r=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-${u()(`${getDate().year}-${getDate().month}`,"YYYY-MM").endOf("month").date()}`).dateFormated;t(e.data);let s=Number(e.data.totalGoalAmount),l=Number(e.data.totalBrokerAchieved);B({amountAchieved:e.data.totalBrokerAchieved,month:`${a} \xe0 ${r}`,monthName:newFormatDate(`${getDate().year}-${getDate().month}-01`).month,observation:"",over:Number(e.data.totalGoalAmount)-Number(e.data.totalBrokerAchieved),percentage:l/s*100,targetAmount:e.data.totalGoalAmount})}).catch(e=>{(0,A.Z)(e,"N\xe3o conseguimos encontrar as metas!")}),getBrokers())}(0,o.useEffect)(()=>{handleSelectTypeGoals()},[c]);let getGoalsadmin=()=>{n.Z.get(`/goals/admin/${_.roleId}/broker`,{params:{year:getDate().year,month:getDate().month}}).then(e=>{let a=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-01`).dateFormated,r=newFormatDate(`${getDate().year}-${getDate().month.padStart(2,"0")}-${u()(`${getDate().year}-${getDate().month}`,"YYYY-MM").endOf("month").date()}`).dateFormated,s=Number(e.data.totalGoalAmount),l=Number(e.data.totalBrokerAchieved);t(e.data),B({amountAchieved:e.data.totalBrokerAchieved,month:`${a} \xe0 ${r}`,monthName:newFormatDate(`${getDate().year}-${getDate().month}-01`).month,observation:"",over:Number(e.data.totalGoalAmount)-Number(e.data.totalBrokerAchieved),percentage:l/s*100,targetAmount:e.data.totalGoalAmount})}).catch(e=>{(0,A.Z)(e,"N\xe3o conseguimos encontrar as metas!")})},getAcessors=async()=>{try{let{data:e}=await n.Z.get("/wallets/broker/advisors",{params:{adviserId:_.roleId}});C(e)}catch(e){(0,A.Z)(e,"N\xe3o conseguimos encontrar as metas!")}},getBrokers=async()=>{try{let{data:e}=await n.Z.get("/wallets/list-brokers");C(e)}catch(e){(0,A.Z)(e,"N\xe3o conseguimos encontrar as metas!")}},returnTypePage=()=>c?{title:"Nova Meta",component:r.jsx(GoalDashboard,{data:P}),subtitle:(0,r.jsxs)("h2",{className:"text-2xl text-center",children:["Adicionar Meta para ","admin"===_.name||"superadmin"===_.name?"Brokers":"Colaboradores"]}),page:r.jsx(AddGoals,{brokerAdvisor:D,setAddgoalModal:m,brokerGoalId:a,golsData:f})}:x?{title:"Nova Perspectiva",component:r.jsx("div",{}),subtitle:r.jsx("p",{}),page:r.jsx(AddPerspective,{brokerGoalId:a})}:{title:"Metas",component:r.jsx(AdminDashboard,{data:P}),subtitle:r.jsx("h2",{className:"text-2xl text-center",children:"perspective"===F?`Capta\xe7\xe3o da Perspectiva dos Brokers`:`Meta de Capta\xe7\xe3o por ${"admin"===_.name||"superadmin"===_.name?"Brokers":"Colaborador"}`}),page:e&&r.jsx(ProgressTable,{filteredType:F,filters:[{label:"perspective"===F?"Perspectiva":"Meta atingida"},{label:"perspective"===F?"Meta Total":"Meta Restante"}],goals:e.results})};return(0,r.jsxs)("div",{children:[r.jsx(s.Z,{}),r.jsx(l.Z,{children:r.jsx("div",{className:"text-white",children:(0,r.jsxs)("div",{className:"w-full",children:[r.jsx("p",{className:"text-2xl text-center",children:returnTypePage().title}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-start gap-2 md:gap-4 max-w-full overflow-hidden",children:[(0,r.jsxs)("div",{className:"relative",children:[!c&&!x&&(0,r.jsxs)(h.z,{onClick:()=>b(!v),className:"flex items-center gap-1 text-[9px] px-2 py-1 whitespace-nowrap bg-[#3A3A3A] hover:bg-[#4a4a4a] text-white rounded-md md:text-sm md:px-4 md:py-2",children:[r.jsx(j.Z,{className:"w-3 h-3 md:w-4 md:h-4",color:"#fff"}),r.jsx("span",{className:"text-[9px] md:text-sm",children:"Filtro"}),v?r.jsx(N.Z,{className:"w-3 h-3 md:w-4 md:h-4",color:"#fff"}):r.jsx(y.Z,{className:"w-3 h-3 md:w-4 md:h-4",color:"#fff"})]}),v&&!c&&!x&&r.jsx("div",{className:"absolute w-[300px] bg-[#3A3A3A] p-5 top-10 rounded-tr-lg rounded-b-lg z-10",children:(0,r.jsxs)("div",{className:"flex flex-col justify-between",children:[(0,r.jsxs)("div",{className:"",children:[r.jsx("p",{className:"text-sm mb-1",children:"Data"}),r.jsx("input",{value:f,className:"p-1 w-full rounded-md text-xs bg-transparent border placeholder-white text-white [color-scheme:dark] ",onChange:({target:e})=>{$(e.value)},type:"month"})]}),("broker"===_.name||"superadmin"===_.name)&&r.jsx("div",{className:"my-2",children:r.jsx(w.Z,{selected:k,setSelected:S,options:[{label:"Metas Internas",value:"goals"},{label:"Perspectiva dos Brokers",value:"perspective"}]})}),r.jsx("div",{className:"mt-4",children:r.jsx(h.z,{className:"text-[9px] px-2 py-1 whitespace-nowrap md:text-sm md:px-4 md:py-2",onClick:()=>{if("broker"!==_.name&&"superadmin"!==_.name&&""===f)return i.Am.info("Selecione um m\xeas para poder aplicar o filtro");i.Am.info("Filtrando resultados!"),handleSelectTypeGoals(),b(!1),M(k)},children:"Aplicar"})})]})})]}),("broker"===_.name||"superadmin"===_.name)&&!x&&r.jsx("div",{children:r.jsx(h.z,{className:"text-[9px] px-2 py-1 whitespace-nowrap md:text-sm md:px-4 md:py-2",variant:c?"secondary":"default",onClick:()=>m(!c),children:c?"Cancelar":"Adicionar Meta"})}),"broker"===_.name&&!c&&r.jsx(h.z,{className:"text-[9px] px-2 py-1 whitespace-nowrap md:text-sm md:px-4 md:py-2",variant:x?"secondary":"default",onClick:()=>g(!x),children:x?"Cancelar":"Adicionar Perspectiva"})]}),returnTypePage().component,r.jsx("div",{className:"my-5",children:returnTypePage().subtitle}),r.jsx("div",{children:returnTypePage().page})]})})})]})}},24103:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>o,__esModule:()=>l,default:()=>i});var r=a(17536);let s=(0,r.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\metas\page.tsx`),{__esModule:l,$$typeof:o}=s,n=s.default,i=n}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,1808,7878,3079,7207,278,7669,2411,2796],()=>__webpack_exec__(54012));module.exports=a})();