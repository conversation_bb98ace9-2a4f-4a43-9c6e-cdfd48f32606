{"version": 3, "file": "create-state-city.dto.js", "sourceRoot": "/", "sources": ["modules/location/dto/create-state-city.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA+D;AAE/D,MAAa,QAAQ;CAYpB;AAZD,4BAYC;AARC;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,CAAC;;sCACF;AAKb;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,CAAC;;8CACO;AAKvB,MAAa,OAAO;CAKnB;AALD,0BAKC;AADC;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,CAAC;;qCACF", "sourcesContent": ["import { IsNotEmpty, IsString, Length } from 'class-validator';\r\n\r\nexport class StateDto {\r\n  @IsNotEmpty()\r\n  @IsString()\r\n  @Length(2, 255)\r\n  name: string;\r\n\r\n  @IsNotEmpty()\r\n  @IsString()\r\n  @Length(2, 10)\r\n  abbreviation: string;\r\n\r\n  cities: Array<CityDto>;\r\n}\r\n\r\nexport class CityDto {\r\n  @IsNotEmpty()\r\n  @IsString()\r\n  @Length(2, 255)\r\n  name: string;\r\n}\r\n"]}