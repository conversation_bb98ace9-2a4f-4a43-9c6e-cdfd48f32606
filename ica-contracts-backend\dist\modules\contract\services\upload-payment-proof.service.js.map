{"version": 3, "file": "upload-payment-proof.service.js", "sourceRoot": "/", "sources": ["modules/contract/services/upload-payment-proof.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,+FAAsF;AACtF,qFAA2E;AAC3E,qCAAqC;AAErC,0GAA2G;AAC3G,uGAAgG;AAChG,qHAA0G;AAGnG,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAGpC,YAEE,kBAA+D,EAE/D,2BAAiF,EAChE,yBAAoD;QAHpD,uBAAkB,GAAlB,kBAAkB,CAA4B;QAE9C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAChE,8BAAyB,GAAzB,yBAAyB,CAA2B;QAPtD,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;IAQlE,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,UAAkB,EAClB,IAAyB,EACzB,IAA2B,EAC3B,MAAc;QAEd,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,UAAU,iBAAiB,MAAM,EAAE,CAAC,CAAC;QAGtG,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,SAAS,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;SAC/D,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAGD,IAAI,QAAQ,CAAC,MAAM,KAAK,yCAAkB,CAAC,sBAAsB,EAAE,CAAC;YAClE,MAAM,IAAI,4BAAmB,CAC3B,2EAA2E,CAC5E,CAAC;QACJ,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,MAAM,EAAE;gBACnB,EAAE,UAAU,EAAE,MAAM,EAAE;aACvB;YACD,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC;YAGH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAGrD,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC/C,eAAe,EAAE,OAAO;gBACxB,sBAAsB,EAAE,IAAI,IAAI,EAAE;gBAClC,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,MAAM,EAAE,yCAAkB,CAAC,cAAc;gBACzC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,UAAU,EAAE,CAAC,CAAC;YAG/E,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;gBAC1C,uBAAuB,EAAE,WAAW,CAAC,EAAE;gBACvC,WAAW,EAAE,kDAAkD,UAAU,oCAAoC;gBAC7G,KAAK,EAAE,kCAAkC;gBACzC,IAAI,EAAE,0CAAoB,CAAC,sBAAsB;gBACjD,UAAU,EAAE,UAAU;gBACtB,aAAa,EAAE,QAAQ,CAAC,eAAe,IAAI,CAAC;gBAC5C,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAE/D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,mEAAmE;aAC7E,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,IAAyB;QAGzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,iBAAiB,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QAOnE,OAAO,8CAA8C,QAAQ,EAAE,CAAC;IAClE,CAAC;IAKD,KAAK,CAAC,gCAAgC,CAAC,MAAc;QACnD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0DAA0D,MAAM,EAAE,CAAC,CAAC;QAGpF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,MAAM,EAAE;gBACnB,EAAE,UAAU,EAAE,MAAM,EAAE;aACvB;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;QAGD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB;aACzC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,iBAAiB,CAAC,mBAAmB,EAAE,UAAU,CAAC;aAClD,KAAK,CAAC,2BAA2B,EAAE;YAClC,MAAM,EAAE,yCAAkB,CAAC,sBAAsB;SAClD,CAAC,CAAC;QAGL,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC3C,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE;gBACrD,QAAQ,EAAE,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,OAAO;aACxD,CAAC,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,YAAY;aACjC,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC;aAC5C,iBAAiB,CAAC,mBAAmB,EAAE,UAAU,CAAC;aAClD,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC;aACrC,OAAO,EAAE,CAAC;QAEb,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,SAAS,CAAC,MAAM,mCAAmC,CAAC,CAAC;QAEpF,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AA5JY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADL,oBAAU;QAED,oBAAU;QACZ,uDAAyB;GAR5D,yBAAyB,CA4JrC", "sourcesContent": ["import { BadRequestException, Injectable, NotFoundException, Logger } from '@nestjs/common';\nimport { InjectRepository } from '@nestjs/typeorm';\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\nimport { Repository } from 'typeorm';\nimport { UploadPaymentProofDto } from '../dto/upload-payment-proof.dto';\nimport { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';\nimport { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\n\n@Injectable()\nexport class UploadPaymentProofService {\n  private readonly logger = new Logger(UploadPaymentProofService.name);\n\n  constructor(\n    @InjectRepository(ContractEntity)\n    private readonly contractRepository: Repository<ContractEntity>,\n    @InjectRepository(OwnerRoleRelationEntity)\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\n    private readonly createNotificationService: CreateNotificationService,\n  ) {}\n\n  async perform(\n    contractId: string,\n    file: Express.Multer.File,\n    data: UploadPaymentProofDto,\n    userId: string,\n  ): Promise<{ success: boolean; message: string }> {\n    this.logger.log(`Iniciando upload de comprovante para contrato ${contractId} pelo usuário ${userId}`);\n\n    // 1. Buscar contrato\n    const contract = await this.contractRepository.findOne({\n      where: { id: contractId },\n      relations: ['investor', 'investor.owner', 'investor.business'],\n    });\n\n    if (!contract) {\n      throw new NotFoundException('Contrato não encontrado');\n    }\n\n    // 2. Validar se contrato está no status correto\n    if (contract.status !== ContractStatusEnum.AWAITING_PAYMENT_PROOF) {\n      throw new BadRequestException(\n        'Apenas contratos aguardando comprovante de pagamento podem receber anexos'\n      );\n    }\n\n    // 3. Buscar perfil do usuário\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\n      where: [\n        { ownerId: userId },\n        { businessId: userId },\n      ],\n      relations: {\n        business: true,\n        owner: true,\n        role: true,\n      },\n    });\n\n    if (!userProfile) {\n      throw new BadRequestException('Perfil de usuário não encontrado.');\n    }\n\n    try {\n      // 4. Salvar informações do arquivo no contrato\n      // Aqui você pode implementar a lógica de upload para S3 ou outro storage\n      const fileUrl = await this.uploadFileToStorage(file);\n\n      // 5. Atualizar contrato com informações do comprovante\n      await this.contractRepository.update(contractId, {\n        paymentProofUrl: fileUrl,\n        paymentProofUploadedAt: new Date(),\n        observations: data.observations,\n        status: ContractStatusEnum.AWAITING_AUDIT,\n        updatedAt: new Date(),\n      });\n\n      this.logger.log(`Comprovante anexado com sucesso para contrato ${contractId}`);\n\n      // 6. Criar notificação para auditoria\n      await this.createNotificationService.create({\n        userOwnerRoleRelationId: userProfile.id,\n        description: `Comprovante de pagamento anexado para contrato ${contractId}. Aguardando análise da auditoria.`,\n        title: `Comprovante de Pagamento Anexado`,\n        type: NotificationTypeEnum.PAYMENT_PROOF_UPLOADED,\n        contractId: contractId,\n        contractValue: contract.investmentValue || 0,\n        investorId: contract.investorId,\n      });\n\n      this.logger.log('Notificação de auditoria criada com sucesso');\n\n      return {\n        success: true,\n        message: 'Comprovante anexado com sucesso. Contrato enviado para auditoria.',\n      };\n\n    } catch (error) {\n      this.logger.error('Erro ao anexar comprovante:', error);\n      throw new BadRequestException(`Erro ao anexar comprovante: ${error.message}`);\n    }\n  }\n\n  /**\n   * Upload do arquivo para storage (implementar conforme sua infraestrutura)\n   */\n  private async uploadFileToStorage(file: Express.Multer.File): Promise<string> {\n    // Implementar upload para S3, Google Cloud Storage, etc.\n    // Por enquanto, retorna um URL fictício\n    const timestamp = Date.now();\n    const fileName = `payment-proof-${timestamp}-${file.originalname}`;\n    \n    // Aqui você implementaria o upload real\n    // const uploadResult = await this.s3Service.upload(file, fileName);\n    // return uploadResult.Location;\n    \n    // Retorno fictício para exemplo\n    return `https://storage.example.com/payment-proofs/${fileName}`;\n  }\n\n  /**\n   * Listar contratos aguardando comprovante de pagamento\n   */\n  async getContractsAwaitingPaymentProof(userId: string): Promise<ContractEntity[]> {\n    this.logger.log(`Buscando contratos aguardando comprovante para usuário ${userId}`);\n\n    // Buscar perfil do usuário para determinar quais contratos pode ver\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\n      where: [\n        { ownerId: userId },\n        { businessId: userId },\n      ],\n      relations: {\n        role: true,\n      },\n    });\n\n    if (!userProfile) {\n      throw new BadRequestException('Perfil de usuário não encontrado.');\n    }\n\n    // Buscar contratos baseado no perfil do usuário\n    const queryBuilder = this.contractRepository\n      .createQueryBuilder('contract')\n      .leftJoinAndSelect('contract.investor', 'investor')\n      .where('contract.status = :status', { \n        status: ContractStatusEnum.AWAITING_PAYMENT_PROOF \n      });\n\n    // Se não for superadmin, filtrar por broker\n    if (userProfile.role.name !== 'superadmin') {\n      queryBuilder.andWhere('contract.brokerId = :brokerId', {\n        brokerId: userProfile.businessId || userProfile.ownerId,\n      });\n    }\n\n    const contracts = await queryBuilder\n      .leftJoinAndSelect('investor.owner', 'owner')\n      .leftJoinAndSelect('investor.business', 'business')\n      .orderBy('contract.createdAt', 'DESC')\n      .getMany();\n\n    this.logger.log(`Encontrados ${contracts.length} contratos aguardando comprovante`);\n\n    return contracts;\n  }\n}\n"]}