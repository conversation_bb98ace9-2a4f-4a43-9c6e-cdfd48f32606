"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatesCitiesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const state_entity_1 = require("../../../shared/database/typeorm/entities/state.entity");
const typeorm_2 = require("typeorm");
let StatesCitiesService = class StatesCitiesService {
    constructor(stateRepository) {
        this.stateRepository = stateRepository;
    }
    async perform() {
        const states = await this.stateRepository.find({
            where: { isActive: true },
            relations: ['cities'],
        });
        const totalStates = states.length;
        let totalCities = 0;
        const statesWithCities = states.map((state) => {
            const totalCitiesInState = state.cities.filter((cities) => cities.isActive === true);
            const totalCitiesActive = totalCitiesInState.length;
            totalCities += totalCitiesActive;
            return { state, totalCities };
        });
        return { states: statesWithCities, totalStates, totalCities };
    }
};
exports.StatesCitiesService = StatesCitiesService;
exports.StatesCitiesService = StatesCitiesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(state_entity_1.StateEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], StatesCitiesService);
//# sourceMappingURL=get-states-cities.service.js.map