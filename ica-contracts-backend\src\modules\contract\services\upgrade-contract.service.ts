import { BadRequestException, Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';
import { Repository } from 'typeorm';
import { EditNewContractDto } from '../dto/edit-new-contract.dto';
import { CreateNewContractService } from 'src/apis/ica-contract-service/services/create-new-contract.service';
import { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';
import { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';

@Injectable()
export class UpgradeContractService {
  private readonly logger = new Logger(UpgradeContractService.name);

  constructor(
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,
    private readonly createNewContractService: CreateNewContractService,
    private readonly createNotificationService: CreateNotificationService,
  ) {}

  async perform(
    contractId: string,
    data: EditNewContractDto,
    userId: string,
  ): Promise<{ id: string; status: string }> {
    this.logger.log(`Iniciando upgrade do contrato ${contractId} pelo usuário ${userId}`);

    // 1. Buscar contrato existente
    const existingContract = await this.contractRepository.findOne({
      where: { id: contractId },
      relations: ['investor', 'ownerRoleRelation'],
    });

    if (!existingContract) {
      throw new NotFoundException('Contrato não encontrado');
    }

    if (existingContract.status !== ContractStatusEnum.ACTIVE) {
      throw new BadRequestException(
        'Apenas contratos ativos podem ser atualizados',
      );
    }

    // 2. Buscar perfil do usuário
    this.logger.log(`Buscando perfil do usuário ${userId} com role: ${data.role}`);

    const userProfile = await this.ownerRoleRelationRepository.findOne({
      where: [
        {
          ownerId: userId,
        },
        {
          businessId: userId,
        },
      ],
      relations: {
        business: true,
        owner: true,
        role: true,
        upper: true,
        bottom: true,
      },
    });

    if (!userProfile) {
      throw new BadRequestException('Perfil de usuário não encontrado.');
    }

    this.logger.log(`Perfil do usuário encontrado: ${userProfile.role.name}`);
    this.logger.log(`Dados recebidos:`, JSON.stringify(data, null, 2));

    // 3. Validações específicas de upgrade
    await this.validateUpgradeRules(existingContract, data);

    // 4. Criar novo contrato no contract-service (seguindo o mesmo fluxo)
    try {
      this.logger.log('Enviando dados para o contract-service...');

      // Validar campos obrigatórios
      if (!data.bankAccount) {
        throw new BadRequestException('Dados bancários são obrigatórios para o upgrade');
      }

      if (!data.investment) {
        throw new BadRequestException('Dados de investimento são obrigatórios para o upgrade');
      }

      if (data.personType === 'PF' && !data.individual) {
        throw new BadRequestException('Dados da pessoa física são obrigatórios');
      }

      if (data.personType === 'PJ' && !data.company) {
        throw new BadRequestException('Dados da empresa são obrigatórios');
      }

      // Determinar o brokerId correto baseado no contrato original
      let brokerId = existingContract.brokerId;

      // Se não há brokerId no contrato original, usar o ID do usuário atual
      if (!brokerId) {
        brokerId = userProfile.id; // Usar o ID da relação owner-role
        this.logger.log(`Usando userProfile.id como brokerId: ${brokerId}`);
      } else {
        this.logger.log(`Usando brokerId do contrato original: ${brokerId}`);
      }

      const contractPayload = {
        personType: data.personType,
        contractType: data.contractType,
        brokerId: brokerId,
        investment: data.investment,
        advisors: [], // Será preenchido com os assessores do contrato original se necessário
        bankAccount: data.bankAccount,
        individual: data.individual,
        company: data.company,
      };

      this.logger.log('Payload para contract-service:', JSON.stringify(contractPayload, null, 2));

      const newContract = await this.createNewContractService.perform(contractPayload);

      this.logger.log(`Novo contrato criado com sucesso: ${newContract.id}`);

      // 4. Criar notificação para auditoria (mesmo fluxo do contrato normal)
      await this.createNotificationService.create({
        userOwnerRoleRelationId: userProfile.id,
        description: `Um upgrade de contrato foi realizado e precisa ser auditado. Contrato original: ${contractId}`,
        title: `Upgrade de Contrato Gerado!`,
        type: NotificationTypeEnum.NEW_CONTRACT,
        contractId: newContract.id,
        contractValue: data.investment?.amount || 0,
        investorId: existingContract.investorId,
      });

      this.logger.log('Notificação de auditoria criada com sucesso');

      // 5. Retornar dados do novo contrato
      return {
        id: newContract.id,
        status: newContract.status,
      };

    } catch (error) {
      this.logger.error('Erro ao criar upgrade do contrato:', error);
      this.logger.error('Stack trace:', error.stack);

      // Se for erro do contract-service, propagar a mensagem original
      if (error.response?.data) {
        this.logger.error('Erro do contract-service:', JSON.stringify(error.response.data, null, 2));
        throw new BadRequestException(`Erro do contract-service: ${JSON.stringify(error.response.data)}`);
      }

      throw new BadRequestException(`Erro ao processar upgrade do contrato: ${error.message}`);
    }
  }
} 