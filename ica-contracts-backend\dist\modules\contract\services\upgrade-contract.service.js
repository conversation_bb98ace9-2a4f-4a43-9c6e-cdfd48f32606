"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UpgradeContractService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpgradeContractService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const typeorm_2 = require("typeorm");
const create_new_contract_service_1 = require("../../../apis/ica-contract-service/services/create-new-contract.service");
const create_notification_service_1 = require("../../notifications/services/create-notification.service");
const notification_entity_1 = require("../../../shared/database/typeorm/entities/notification.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
let UpgradeContractService = UpgradeContractService_1 = class UpgradeContractService {
    constructor(contractRepository, ownerRoleRelationRepository, createNewContractService, createNotificationService) {
        this.contractRepository = contractRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.createNewContractService = createNewContractService;
        this.createNotificationService = createNotificationService;
        this.logger = new common_1.Logger(UpgradeContractService_1.name);
    }
    async perform(contractId, data, userId) {
        this.logger.log(`Iniciando upgrade do contrato ${contractId} pelo usuário ${userId}`);
        const existingContract = await this.contractRepository.findOne({
            where: { id: contractId },
            relations: ['investor', 'ownerRoleRelation'],
        });
        if (!existingContract) {
            throw new common_1.NotFoundException('Contrato não encontrado');
        }
        if (existingContract.status !== contract_status_enum_1.ContractStatusEnum.ACTIVE) {
            throw new common_1.BadRequestException('Apenas contratos ativos podem ser atualizados');
        }
        this.logger.log(`Buscando perfil do usuário ${userId} com role: ${data.role}`);
        const userProfile = await this.ownerRoleRelationRepository.findOne({
            where: [
                {
                    ownerId: userId,
                },
                {
                    businessId: userId,
                },
            ],
            relations: {
                business: true,
                owner: true,
                role: true,
                upper: true,
                bottom: true,
            },
        });
        if (!userProfile) {
            throw new common_1.BadRequestException('Perfil de usuário não encontrado.');
        }
        this.logger.log(`Perfil do usuário encontrado: ${userProfile.role.name}`);
        this.logger.log(`Dados recebidos:`, JSON.stringify(data, null, 2));
        try {
            this.logger.log('Enviando dados para o contract-service...');
            if (!data.bankAccount) {
                throw new common_1.BadRequestException('Dados bancários são obrigatórios para o upgrade');
            }
            if (!data.investment) {
                throw new common_1.BadRequestException('Dados de investimento são obrigatórios para o upgrade');
            }
            if (data.personType === 'PF' && !data.individual) {
                throw new common_1.BadRequestException('Dados da pessoa física são obrigatórios');
            }
            if (data.personType === 'PJ' && !data.company) {
                throw new common_1.BadRequestException('Dados da empresa são obrigatórios');
            }
            const contractPayload = {
                personType: data.personType,
                contractType: data.contractType,
                brokerId: userProfile.businessId || userProfile.ownerId,
                investment: data.investment,
                advisors: [],
                bankAccount: data.bankAccount,
                individual: data.individual,
                company: data.company,
            };
            this.logger.log('Payload para contract-service:', JSON.stringify(contractPayload, null, 2));
            const newContract = await this.createNewContractService.perform(contractPayload);
            this.logger.log(`Novo contrato criado com sucesso: ${newContract.id}`);
            await this.createNotificationService.create({
                userOwnerRoleRelationId: userProfile.id,
                description: `Um upgrade de contrato foi realizado e precisa ser auditado. Contrato original: ${contractId}`,
                title: `Upgrade de Contrato Gerado!`,
                type: notification_entity_1.NotificationTypeEnum.NEW_CONTRACT,
                contractId: newContract.id,
                contractValue: data.investment?.amount || 0,
                investorId: existingContract.investorId,
            });
            this.logger.log('Notificação de auditoria criada com sucesso');
            return {
                id: newContract.id,
                status: newContract.status,
            };
        }
        catch (error) {
            this.logger.error('Erro ao criar upgrade do contrato:', error);
            this.logger.error('Stack trace:', error.stack);
            if (error.response?.data) {
                this.logger.error('Erro do contract-service:', JSON.stringify(error.response.data, null, 2));
                throw new common_1.BadRequestException(`Erro do contract-service: ${JSON.stringify(error.response.data)}`);
            }
            throw new common_1.BadRequestException(`Erro ao processar upgrade do contrato: ${error.message}`);
        }
    }
};
exports.UpgradeContractService = UpgradeContractService;
exports.UpgradeContractService = UpgradeContractService = UpgradeContractService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        create_new_contract_service_1.CreateNewContractService,
        create_notification_service_1.CreateNotificationService])
], UpgradeContractService);
//# sourceMappingURL=upgrade-contract.service.js.map