(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1951],{3905:function(e,t,a){Promise.resolve().then(a.bind(a,458))},458:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return Home}});var s=a(7437),r=a(3877),l=a(8637),i=a(2265),n=a(2875),d=a(7395),o=a(4568),c=a(919),x=a(3256),m=a(6654),u=a(3277),h=a(5968),f=a(7934),p=a(7848),v=a(6610),g=a(9701),b=a(2067),j=a.n(b),N=a(4279),w=a.n(N),y=a(1865),C=a(3014);function ModalRegister(e){let{setModal:t}=e,[a,r]=(0,i.useState)(!1),l=(0,x.e)(),[b,N]=(0,i.useState)("PHYSICAL"),[Z,_]=(0,i.useState)(""),[E,F]=(0,i.useState)(""),{register:D,handleSubmit:S,watch:q,setValue:O,reset:T,formState:{errors:A}}=(0,y.cI)({resolver:(0,g.X)(f.bs)}),R=q("startContract"),P=q("term");(0,i.useEffect)(()=>{if(R&&P){let e=(0,c.H)({investDate:P,startDate:R});O("endContract",e)}},[R,P]);let M=q("modality"),k=q("document");(0,i.useEffect)(()=>{O("modality","P2P")},[]),(0,i.useEffect)(()=>{(null==k?void 0:k.length)<=14?N("PHYSICAL"):N("BUSINESS")},[k]);let handleCopy=e=>{navigator.clipboard.writeText(e).then(()=>{C.Am.info("Link de cadastro copiado!")}).catch(e=>{console.error("Erro ao copiar o texto: ",e)})};return(0,s.jsx)("div",{className:"z-20 absolute top-0 left-0 w-screen h-screen bg-[#1c1c1c71]",children:(0,s.jsxs)("div",{className:"z-20 bg-[#1C1C1C] md:w-[65%] w-full absolute right-0 md:border-l md:border-t md:border-b border-[#FF9900] p-10 text-white overflow-auto h-full",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center",children:(0,s.jsx)(v.Z,{color:"#000",width:20})}),(0,s.jsx)("div",{className:"gap-y-1 flex flex-col",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:"Pr\xe9-cadastro para gerar link"})})]}),(0,s.jsxs)("form",{action:"",onSubmit:S(e=>{if((0,h.p4)(e.document||"").length<=11){if(!(0,p.p)((0,h.p4)(e.document||"")))return C.Am.warn("CPF do investidor inv\xe1lido!")}else{if(!(0,p.w)((0,h.p4)(e.document||"")))return C.Am.warn("CNPJ inv\xe1lido!");if(!(0,p.p)((0,h.p4)(E)))return C.Am.warn("CPF do representante inv\xe1lido!")}r(!0);let t={adviserId:l.roleId,document:(0,h.p4)(e.document||""),email:e.email,signIca:d.l,investment:{value:(0,u.Z)(e.value),term:e.term,modality:e.modality,yield:Number(e.yield),purchaseWith:e.purchaseWith,amountQuotes:Number(e.amountQuotes||"0"),gracePeriod:j()(e.startContract).add(1,"day").format("YYYY-MM-DD"),debenture:"s"===e.debenture,startContract:e.startContract,endContract:j()(e.endContract,"DD/MM/YYYY").format("YYYY-MM-DD"),profile:e.profile,details:""},accountType:b,owner:"BUSINESS"===b?{name:Z,cpf:(0,h.p4)(E)}:void 0};o.Z.post("/pre-register/send",t).then(e=>{let t=w().tz.guess(),a=w()().tz(t),s=a.hour();s>=9&&s<18?C.Am.success("Conta pr\xe9 cadastrada com sucesso!"):C.Am.success("Contrato criado com sucesso. Informamos que, por ter sido realizado fora do hor\xe1rio comercial, o registro cont\xe1bil ser\xe1 processado somente no pr\xf3ximo dia \xfatil.",{delay:6e3}),handleCopy("".concat(window.location.protocol,"//").concat(window.location.host,"/registro/").concat(e.data.token)),r(!1),T()}).catch(e=>{r(!1),(0,m.Z)(e,"Tivemos um erro ao cadastrar a conta")})}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("p",{className:"text-xl text-white mr-1",children:"Dados cadastro"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["E-mail ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.email&&"- ".concat(A.email.message)})]}),(0,s.jsx)("input",{...D("email"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.email?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["CPF/CNPJ ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.document&&"- ".concat(A.document.message)})]}),(0,s.jsx)("input",{...D("document"),onChange:e=>{let{target:t}=e,a=t.value;a.length<=14?O("document",(0,h.VL)(a)):O("document",(0,h.PK)(a))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.document?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),"BUSINESS"===b&&(0,s.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white mb-1",children:"Nome do representante"}),(0,s.jsx)("input",{value:Z,onChange:e=>{let{target:t}=e;return _(t.value)},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1"})]})}),(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white mb-1",children:"CPF do representante "}),(0,s.jsx)("input",{value:E,onChange:e=>{let{target:t}=e;return F((0,h.VL)(t.value))},className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900]ring-1 ring-inset bg-black flex-1"})]})})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("p",{className:"text-xl text-white mr-1",children:"Dados de Investimento"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Valor do investimento ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.value&&"- ".concat(A.value.message)})]}),(0,s.jsx)("input",{...D("value"),onChange:e=>{let{target:t}=e;return O("value",(0,h.Ht)(t.value))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.value?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Prazo do Investimento - em meses ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.term&&"- ".concat(A.term.message)})]}),(0,s.jsx)("input",{...D("term"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.term?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,s.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Taxa de Remunera\xe7\xe3o Mensal % ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.yield&&"- ".concat(A.yield.message)})]}),(0,s.jsx)("input",{...D("yield"),type:"text",className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.yield?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,s.jsx)("div",{className:"md:w-2/4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Tipo do Investimento ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.modality&&"- ".concat(A.modality.message)})]}),(0,s.jsxs)("select",{...D("modality"),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1",children:[(0,s.jsx)("option",{className:"cursor-pointer",value:"P2P",children:"M\xfatuo"}),(0,s.jsx)("option",{className:"cursor-pointer",value:"SCP",children:"SCP"})]})]})})]}),(0,s.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-start mt-4",children:[(0,s.jsx)("div",{className:"md:w-1/3",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Comprar com ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.purchaseWith&&"- ".concat(A.purchaseWith.message)})]}),(0,s.jsx)("input",{...D("purchaseWith"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.purchaseWith?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),"P2P"!==M&&(0,s.jsx)("div",{className:"md:w-1/3",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Quantidade de cotas ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.amountQuotes&&"- ".concat(A.amountQuotes.message)})]}),(0,s.jsx)("input",{...D("amountQuotes"),type:"number",className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.amountQuotes?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,s.jsx)("div",{className:"md:w-1/3",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Inicio do contrato ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.startContract&&"- ".concat(A.startContract.message)})]}),(0,s.jsx)("input",{...D("startContract"),type:"date",min:w().utc().format("YYYY-MM-DD"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.startContract?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})})]}),(0,s.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[(0,s.jsx)("div",{className:"md:w-1/3",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Final do contrato ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.endContract&&"- ".concat(A.endContract.message)})]}),(0,s.jsx)("input",{...D("endContract"),type:"text",disabled:!0,className:"h-12 w-full px-4 text-zinc-400 rounded-xl ".concat(A.endContract?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,s.jsx)("div",{className:"md:w-1/3",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Perfil investidor ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.profile&&"- ".concat(A.profile.message)})]}),(0,s.jsx)("input",{...D("profile"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.profile?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]})}),(0,s.jsx)("div",{className:"md:w-1/3",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white mb-1",children:["Deb\xeanture ",(0,s.jsx)("b",{className:"text-red-500 font-light text-sm",children:A.debenture&&"- ".concat(A.debenture.message)})]}),(0,s.jsxs)("select",{...D("debenture"),className:"h-12 w-full px-4 text-white rounded-xl ".concat(A.debenture?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1"),children:[(0,s.jsx)("option",{value:"s",children:"Sim"}),(0,s.jsx)("option",{value:"n",children:"N\xe3o"})]})]})})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-start mt-10",children:[(0,s.jsx)("div",{className:"w-52",children:(0,s.jsx)(n.Z,{label:"Gerar link",loading:a,className:"bg-orange-linear ",disabled:a})}),(0,s.jsx)("div",{className:"px-5 ml-10 bg-[#313131] cursor-pointer flex items-center justify-center rounded-lg",onClick:()=>t(!1),children:(0,s.jsx)("p",{className:"text-sm",children:"Fechar"})})]})]})]})})}var Z=a(5215),_=a(8689),E=a(9233),F=a(667),D=a(7031),S=a(3352),q=a(5792),O=a(6691),T=a.n(O),A={src:"/_next/static/media/doc_verify.c3f8e067.svg",height:27,width:27,blurWidth:0,blurHeight:0},R={src:"/_next/static/media/grafic_persons.877d09d7.svg",height:31,width:31,blurWidth:0,blurHeight:0},P={Sunday:"Domingo",Monday:"Segunda-feira",Tuesday:"Ter\xe7a-feira",Wednesday:"Quarta-feira",Thursday:"Quinta-feira",Friday:"Sexta-feira",Saturday:"S\xe1bado"};function BrokerData(e){var t;let{setModal:a}=e,[r,l]=(0,i.useState)(),[n,d]=(0,i.useState)(!0),c=(0,x.P)(),u=(0,x.e)(),h=c.name.split(" "),[f,p]=(0,i.useState)({type:"SCP",period:"year"}),[g,b]=(0,i.useState)({type:"SCP",period:"year"}),[N,w]=(0,i.useState)({data:[{month:"Jan",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Fev",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Mar",avgTemp:6.3,value:0,name:"Janeiro"},{month:"Abr",avgTemp:16.2,value:0,name:"Janeiro"},{month:"Mai",avgTemp:22.8,value:0,name:"Janeiro"},{month:"Jun",avgTemp:14.5,value:0,name:"Janeiro"},{month:"Jul",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Ago",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Set",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Out",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Nov",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Dez",avgTemp:8.9,value:0,name:"Janeiro"}],background:{visible:!1},series:[{type:"bar",xKey:"month",yKey:"value",tooltip:{enabled:!1},label:{enabled:!1}}],axes:[{type:"category",position:"bottom",key:"month"},{type:"number",position:"left",label:{formatter:e=>e.value.toLocaleString("pt-br",{style:"currency",currency:"BRL"})}}],padding:{top:10},theme:E.Z}),capitalizeFirstLetter=e=>e.charAt(0).toUpperCase()+e.slice(1),returnTitle=(e,t)=>"year"===t?capitalizeFirstLetter(j()(e).utc().format("MMM")):"month"===t?e:P[e],formatData=(e,t,a)=>e.map(e=>({month:"year"===a?returnTitle(e.date,a):returnTitle(e.label,a),value:Number("contract"===t?e.totalContracts:e.totalValue)})),getChartContractsData=()=>{o.Z.get("".concat(u.name,"/").concat("superadmin"!==u.name?"".concat(u.roleId,"/"):"","contracts-growth"),{params:{period:f.period,contractType:f.type}}).then(e=>{let t=formatData(e.data.data,"results",f.period);w({...N,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de contratos!")})};(0,i.useEffect)(()=>{getChartContractsData()},[f]),(0,i.useEffect)(()=>{getChartResultsData()},[g]);let getChartResultsData=()=>{o.Z.get("".concat(u.name,"/").concat("superadmin"!==u.name?"".concat(u.roleId,"/"):"","contracts-growth"),{params:{period:g.period,contractType:g.type}}).then(e=>{let t=formatData(e.data.data,"contract",g.period);C({...y,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de resultados!")})},[y,C]=(0,i.useState)({background:{visible:!1},theme:E.Z,data:[{month:"Jan",value:0},{month:"Fev",value:0},{month:"Mar",value:0},{month:"Abr",value:0},{month:"Mai",value:0},{month:"Jun",value:0},{month:"Jul",value:0},{month:"Ago",value:0},{month:"Set",value:0},{month:"Out",value:0},{month:"Nov",value:0},{month:"Dez",value:0}],series:[{type:"line",xKey:"month",xName:"Month",yKey:"value",interpolation:{type:"linear"},tooltip:{enabled:!1}}]});return(0,i.useEffect)(()=>{d(!0),o.Z.get("broker"===u.name?"/broker/dashboard":"/advisor/dashboard").then(e=>{l(e.data)}).catch(e=>{}).finally(()=>d(!1))},[]),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("div",{className:"w-full flex md:flex-row flex-col gap-2 justify-between",children:[(0,s.jsx)("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border",children:(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("div",{className:"w-6 h-6 p-1 bg-white rounded-full flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-[#FF9900] text-xs font-bold",children:"".concat(h[0][0]||"").concat(h[h.length-1][0]||"")})}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("p",{className:"ml-3 text-sm",children:null==c?void 0:c.name}),(0,s.jsx)("div",{className:"w-full flex justify-end"})]})]})}),(0,s.jsx)("div",{className:"bg-[#1C1C1C] md:w-[60%] p-5 rounded-lg border-[#FF9900] border",children:(0,s.jsxs)("div",{className:"flex-col w-full",children:[(0,s.jsx)("div",{className:"",children:(0,s.jsx)(D.Z,{width:20,color:"#FF9900"})}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-sm",children:"FAQ ICA BANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),(0,s.jsx)("div",{children:(0,s.jsx)("p",{className:"text-xs",children:"Tire suas d\xfavidas em nossa base de conhecimento"})})]})]})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mt-2",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(v.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Investidores Ativos"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,s.jsx)(_.Z,{loading:n,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==r?void 0:r.scpContractNumber})})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsx)(_.Z,{loading:n,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==r?void 0:r.p2pContractNumber})})]})]})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(S.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Total de retiradas"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsx)(_.Z,{loading:n,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:Number((null==r?void 0:r.p2pWithdraws)||0)})})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"Saldo bloqueado"}),(0,s.jsx)(_.Z,{loading:n,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:Number((null==r?void 0:r.scpWithdraws)||0)})})]})]})]}),(0,s.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(F.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Total de contratos ativos"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:n,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-3xl",children:null==r?void 0:r.activeInvestorsNumber})})})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(F.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Valor total de contratos"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:n,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-xl",children:null===(t=Number(null==r?void 0:r.p2pContractAmount)+Number(null==r?void 0:r.scpContractAmount))||void 0===t?void 0:t.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(S.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Cotas ativas"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:n,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-xl",children:null==r?void 0:r.activeQuotes})})})]})]}),(0,s.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(T(),{src:A,alt:"",width:25,style:{marginTop:"-5px"}}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Gr\xe1fico Crescente de Contratos"})]}),(0,s.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[(0,s.jsx)(Z.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:f.type,setSelected:e=>{p({...f,type:e})}}),(0,s.jsx)(Z.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:f.period,setSelected:e=>{p({...f,period:e})}})]})]}),(0,s.jsx)(q.bY,{className:"p-2",options:N})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(T(),{src:R,alt:"",width:25,style:{marginTop:"-5px"}}),(0,s.jsx)("p",{className:"text-sm",children:"Gr\xe1fico Crescente de Resultados"})]}),(0,s.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[(0,s.jsx)(Z.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:g.type,setSelected:e=>{b({...g,type:e})}}),(0,s.jsx)(Z.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:g.period,setSelected:e=>{b({...g,period:e})}})]})]}),(0,s.jsx)(q.bY,{options:y})]})]})]})]})}var M=a(1615),k={src:"/_next/static/media/saques_efetivados.58e03bdc.svg",height:27,width:27,blurWidth:0,blurHeight:0},Y=a(4033);function AdminData(e){let{userType:t}=e,[a,r]=(0,i.useState)(),[l,n]=(0,i.useState)(!1),d=(0,x.e)(),c=(0,Y.useRouter)(),[u,h]=(0,i.useState)({type:"SCP",period:"year"}),[f,p]=(0,i.useState)({type:"SCP",period:"year"}),[g,b]=(0,i.useState)({data:[{month:"Jan",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Fev",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Mar",avgTemp:6.3,value:0,name:"Janeiro"},{month:"Abr",avgTemp:16.2,value:0,name:"Janeiro"},{month:"Mai",avgTemp:22.8,value:0,name:"Janeiro"},{month:"Jun",avgTemp:14.5,value:0,name:"Janeiro"},{month:"Jul",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Ago",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Set",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Out",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Nov",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Dez",avgTemp:8.9,value:0,name:"Janeiro"}],background:{visible:!1},series:[{type:"bar",xKey:"month",yKey:"value",tooltip:{enabled:!1},label:{enabled:!1}}],axes:[{type:"category",position:"bottom",key:"month"},{type:"number",position:"left",label:{formatter:e=>e.value.toLocaleString("pt-br",{style:"currency",currency:"BRL"})}}],padding:{top:10},theme:E.Z}),capitalizeFirstLetter=e=>e.charAt(0).toUpperCase()+e.slice(1),returnTitle=(e,t)=>"year"===t?capitalizeFirstLetter(j()(e).utc().format("MMM")):"month"===t?e:P[e],formatData=(e,t,a)=>e.map(e=>({month:"year"===a?returnTitle(e.date,a):returnTitle(e.label,a),value:Number("contract"===t?e.totalContracts:e.totalValue),avgTemp:2.3,name:"year"===a?returnTitle(e.date,a):returnTitle(e.label,a)})),getChartContractsData=()=>{o.Z.get("".concat(d.name,"/").concat("superadmin"!==d.name?"".concat(d.roleId,"/"):"","contracts-growth"),{params:{period:u.period,contractType:u.type}}).then(e=>{let t=formatData(e.data.data,"results",u.period);b({...g,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de contratos!")})};(0,i.useEffect)(()=>{getChartContractsData()},[u]),(0,i.useEffect)(()=>{getChartResultsData()},[f]);let getChartResultsData=()=>{o.Z.get("".concat(d.name,"/").concat("superadmin"!==d.name?"".concat(d.roleId,"/"):"","contracts-growth"),{params:{period:f.period,contractType:f.type}}).then(e=>{let t=formatData(e.data.data,"contract",f.period);w({...N,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de resultados!")})},[N,w]=(0,i.useState)({background:{visible:!1},theme:E.Z,data:[{month:"Jan",value:0},{month:"Fev",value:0},{month:"Mar",value:0},{month:"Abr",value:0},{month:"Mai",value:0},{month:"Jun",value:0},{month:"Jul",value:0},{month:"Ago",value:0},{month:"Set",value:0},{month:"Out",value:0},{month:"Nov",value:0},{month:"Dez",value:0}],series:[{type:"line",xKey:"month",xName:"Month",yKey:"value",interpolation:{type:"linear"},tooltip:{enabled:!1}}]});return(0,i.useEffect)(()=>{d.name&&!l&&(n(!0),o.Z.get("superadmin"===d.name?"/super-admin/dashboard":"/admin/dashboard").then(e=>{r(e.data)}).catch(e=>{C.Am.error("Erro ao buscar os dados!")}).finally(()=>n(!1)))},[d.name]),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(v.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Contratos Ativos (R$)"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==a?void 0:a.scpContractAmount.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==a?void 0:a.p2pContractAmount.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(S.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Rendimentos Distribu\xeddos (R$)"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==a?void 0:a.distributedIncome.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==a?void 0:a.distributedIncome.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(T(),{src:k,alt:"",width:15})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Resgates Efetivados"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==a?void 0:a.scpWithdraws.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-lg",children:null==a?void 0:a.p2pWithdraws.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]})]}),(0,s.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(M.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Investidores Ativos"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-3xl",children:null==a?void 0:a.activeInvestorsNumber})})})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(M.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Cotistas Ativos"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-3xl",children:null==a?void 0:a.shareholder})})})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(S.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Cotas Ativas"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-3xl",children:null==a?void 0:a.activeQuotes})})})]})]}),"superadmin"===d.name&&(0,s.jsxs)("div",{className:"w-full flex md:flex-row flex-col gap-2 justify-start flex-wrap",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:w-[33%] p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(M.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Brokers Ativos"})]}),(0,s.jsx)("p",{className:"text-xs px-4 border py-2 rounded-lg select-none cursor-pointer",onClick:()=>{localStorage.setItem("searchRanking","broker"),c.push("/home/<USER>")},children:"Ver mais"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-3xl",children:null==a?void 0:a.numberBrokers})})})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:w-[33%] p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(M.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Assessores Ativos"})]}),(0,s.jsx)("p",{className:"text-xs px-4 border py-2 rounded-lg select-none cursor-pointer",onClick:()=>{localStorage.setItem("searchRanking","advisor"),c.push("/home/<USER>")},children:"Ver mais"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)(_.Z,{loading:l,height:"25px",children:(0,s.jsx)("p",{className:"font-bold text-3xl",children:null==a?void 0:a.numberAdvisors})})})]})]}),(0,s.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(T(),{src:A,alt:"",width:25,style:{marginTop:"-5px"}}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Gr\xe1fico Crescente de Contratos"})]}),(0,s.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[(0,s.jsx)(Z.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:u.type,setSelected:e=>{h({...u,type:e})}}),(0,s.jsx)(Z.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:u.period,setSelected:e=>{h({...u,period:e})}})]})]}),(0,s.jsx)(q.bY,{className:"p-2",options:g})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(T(),{src:R,alt:"",width:25,style:{marginTop:"-5px"}}),(0,s.jsx)("p",{className:"text-sm",children:"Gr\xe1fico Crescente de Resultados"})]}),(0,s.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[(0,s.jsx)(Z.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:f.type,setSelected:e=>{p({...f,type:e})}}),(0,s.jsx)(Z.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:f.period,setSelected:e=>{p({...f,period:e})}})]})]}),(0,s.jsx)(q.bY,{options:N})]})]})]})}var I=a(5587),z=a(2015);function InvestorData(){let[e,t]=(0,i.useState)(),a=(0,x.e)();(0,i.useEffect)(()=>{getInvestor()},[]);let getInvestor=()=>{o.Z.get("/investors/dashboard",{params:{roleId:a.roleId}}).then(e=>{t(e.data)}).catch(e=>{C.Am.error("Erro ao buscar dados do investidor")})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"w-full flex md:flex-row flex-col gap-2 justify-between",children:[(0,s.jsx)("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border",children:(0,s.jsxs)("div",{className:"flex w-full",children:[(0,s.jsx)("div",{className:"w-[25px] h-[25px] bg-white rounded-full flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-[#FF9900] text-xs font-bold",children:"".concat((null==e?void 0:e.advisor.owner.name.split(" ")[0][0])||"").concat((null==e?void 0:e.advisor.owner.name.split(" ")[1][0])||"")})}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"ml-3 text-base",children:(null==e?void 0:e.advisor.owner.name)||""}),(0,s.jsx)("p",{className:"ml-3 text-sm font-extralight",children:"Consultor(a)"})]}),(0,s.jsx)("div",{className:"w-full flex justify-end",children:(0,s.jsxs)("div",{className:"text-center rounded-md mt-3 cursor-pointer border border-[#1EF97C] flex items-center px-4 py-[7px]",onClick:()=>{var t,a;let s=(0,h.p4)((null==e?void 0:null===(a=e.advisor)||void 0===a?void 0:null===(t=a.owner)||void 0===t?void 0:t.phone)||""),r="https://wa.me/".concat(s);window.open(r,"_blank")},children:[(0,s.jsx)(I.Z,{width:20,color:"#1EF97C"}),(0,s.jsx)("p",{className:"text-sm text-[#1EF97C] ml-2 font-bold select-none",children:"WhatsApp"})]})})]})]})}),(0,s.jsx)("div",{className:"bg-[#1C1C1C] md:w-[60%] p-5 rounded-lg border-[#FF9900] border",children:(0,s.jsxs)("div",{className:"flex-col w-full",children:[(0,s.jsx)("div",{className:"",children:(0,s.jsx)(D.Z,{width:20,color:"#FF9900"})}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-sm",children:"FAQ ICA BANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),(0,s.jsx)("div",{children:(0,s.jsx)("p",{className:"text-xs",children:"Tire suas d\xfavidas em nossa base de conhecimento"})})]})]})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(v.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Total de Contratos Ativos"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,s.jsx)("p",{className:"font-bold text-lg",children:(null==e?void 0:e.scpQuotest)||0})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsx)("p",{className:"font-bold text-lg",children:(null==e?void 0:e.p2pQuotest)||0})]})]})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(S.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Total de Rendimentos"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,s.jsxs)("p",{className:"font-bold text-lg",children:["R$ ",(0,h.Ht)(String(null==e?void 0:e.totalIncome))||"0,00"]})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsxs)("p",{className:"font-bold text-lg",children:["R$ ",(0,h.Ht)(String(null==e?void 0:e.totalIncome))||"0,00"]})]})]})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(z.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Saldo de Rendimento"})]}),(0,s.jsxs)("div",{className:"ml-5",children:[(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,s.jsx)("p",{className:"font-bold text-lg",children:"Em andamento"})]}),(0,s.jsxs)("div",{className:"mt-1",children:[(0,s.jsx)("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,s.jsx)("p",{className:"font-bold text-lg",children:"Em andamento"})]})]})]}),(0,s.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(M.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Valor Total de Contratos Ativos "})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsxs)("p",{className:"font-bold text-xl",children:["R$ ",(0,h.Ht)(String(null==e?void 0:e.totalContractsAmounts))||"0,00"]})})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(M.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Total de Saques"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsx)("p",{className:"font-bold text-xl",children:(null==e?void 0:e.totalIncome)||0})})]}),(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:(0,s.jsx)(S.Z,{width:15,color:"#fff"})}),(0,s.jsx)("p",{className:"ml-3 text-sm",children:"Saldo Bloqueado"})]}),(0,s.jsx)("div",{className:"w-full text-center mt-3",children:(0,s.jsxs)("p",{className:"font-bold text-xl",children:["R$ ",(0,h.Ht)(String(null==e?void 0:e.blockedBalance))||"0,00"]})})]})]})]})]})}var J=a(3042),L=a(13),B=a(3217),W=a(1980),Q=a(6121),V=a(4984),U={src:"/_next/static/media/contact.a27d8b69.svg",height:54,width:54,blurWidth:0,blurHeight:0},H={src:"/_next/static/media/email.a16acbce.svg",height:37,width:43,blurWidth:0,blurHeight:0},K=a(3220);function ModalMonitoramento(e){var t,a,r,l,c,m,u;let{setModal:f,status:p,contract:v,setModalContract:g,setModalType:b,typeContract:N}=e,[w,y]=(0,i.useState)(),[Z,_]=(0,i.useState)(!1),[E,F]=(0,i.useState)(""),[D,S]=(0,i.useState)({title:"",value:""}),[q,O]=(0,i.useState)({value:"",profile:"",yield:"",date:"",bank:"",agency:"",accountNumber:"",pix:""}),A=(0,x.e)(),renewContract=()=>{let e=new FormData;if(""===D.value)return C.Am.warning("Selecione uma atualiza\xe7\xe3o para o contrato");e.append("status",D.value),e.append("comment",E),e.append("ownerRoleId",A.roleId),w&&e.append("attachments",w[0]),_(!0),o.Z.post("/contract-lifecycle-monitoring/".concat(null==v?void 0:v.id,"/retention/update"),e).then(e=>{C.Am.success("Opera\xe7\xe3o realizada com sucesso!"),_(!1),window.location.reload()}).catch(e=>{var t,a;C.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o foi possivel completar a opera\xe7\xe3o"),_(!1)})},createAditive=()=>{_(!0);let e={roleId:null==v?void 0:v.investorId,contractId:null==v?void 0:v.id,investment:{value:Number(q.value.replace(".","").replace(",",".")),profile:q.profile,yield:Number(q.yield),date:j()().format("YYYY-MM-DD")},accountBank:{bank:q.bank,accountNumber:q.accountNumber,agency:q.agency,pix:q.pix},observations:E,signIca:d.l};o.Z.post("/contract/additive",e).then(e=>{renewContract()}).catch(e=>{C.Am.error(e.message||"N\xe3o conseguimos criar o contrato de aditivo!"),_(!1)})};return(0,s.jsxs)("div",{className:"z-10 w-10/12 bg-[#000000] fixed top-0 right-0 bottom-0 text-white overflow-auto h-auto p-10 border-l",children:[(0,s.jsx)("div",{className:"w-full text-center",children:(0,s.jsx)("h1",{className:"text-2xl font-bold",children:N})}),"\xc0 vencer"===N&&(0,s.jsx)("div",{className:"flex mt-5",children:(0,s.jsx)("div",{className:"bg-[#1E1E1E94] m-auto px-5 py-1 rounded-lg",children:(0,s.jsxs)("p",{className:"",children:[(0,s.jsx)("b",{className:"text-[#FFF700]",children:"Aviso:"})," est\xe1 chegando o vencimento do contrato."]})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{children:"Dados Pessoais"}),(0,s.jsxs)("div",{className:"flex justify-between w-full mt-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"E-mail"}),(0,s.jsx)("p",{className:"text-sm",children:null==v?void 0:v.investorEmail})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"CPF"}),(0,s.jsx)("p",{className:"text-sm",children:null==v?void 0:v.investorCpf})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"RG"}),(0,s.jsx)("p",{className:"text-sm",children:"N\xe3o informado"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"Perfil"}),(0,s.jsx)("p",{className:"text-sm",children:"N\xe3o informado"})]})]})]}),(0,s.jsxs)("div",{className:"mt-10",children:[(0,s.jsx)("h2",{children:"Informa\xe7\xf5es de Endere\xe7o"}),(0,s.jsxs)("div",{className:"flex justify-between w-full mt-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"Endere\xe7o"}),(0,s.jsx)("p",{className:"text-sm",children:(null==v?void 0:null===(t=v.investorAddresses[0])||void 0===t?void 0:t.street)||"N\xe3o informado"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"N\xfamero"}),(0,s.jsx)("p",{className:"text-sm",children:(null==v?void 0:null===(a=v.investorAddresses[0])||void 0===a?void 0:a.number)||"N\xe3o informado"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"Complemento"}),(0,s.jsx)("p",{className:"text-sm",children:(null==v?void 0:null===(r=v.investorAddresses[0])||void 0===r?void 0:r.complement)||"N\xe3o informado"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"Bairro"}),(0,s.jsx)("p",{className:"text-sm",children:(null==v?void 0:null===(l=v.investorAddresses[0])||void 0===l?void 0:l.neighborhood)||"N\xe3o informado"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"Cidade"}),(0,s.jsx)("p",{className:"text-sm",children:(null==v?void 0:null===(c=v.investorAddresses[0])||void 0===c?void 0:c.city)||"N\xe3o informado"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"UF"}),(0,s.jsx)("p",{className:"text-sm",children:(null==v?void 0:null===(m=v.investorAddresses[0])||void 0===m?void 0:m.state)||"N\xe3o informado"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-bold",children:"CEP"}),(0,s.jsx)("p",{className:"text-sm",children:(null==v?void 0:null===(u=v.investorAddresses[0])||void 0===u?void 0:u.zipCode)||"N\xe3o informado"})]})]})]}),(0,s.jsxs)("div",{className:"my-10",children:[(0,s.jsx)("p",{children:"Observa\xe7\xe3o"}),(0,s.jsx)("p",{className:"mt-2 rounded-lg border w-6/12 bg-zinc-950 p-2 border-[#FFB238]",children:(null==v?void 0:v.latestEvent.comment)||"Nenhuma observa\xe7\xe3o"})]}),(0,s.jsxs)("div",{className:"w-6/12",children:[(0,s.jsx)("p",{className:"font-semibold",children:"Clique em um dos bot\xf5es abaixo para ser direcionado aos meios de contato do investidor e iniciar a comunica\xe7\xe3o."}),(0,s.jsxs)("div",{className:"flex mt-5 gap-4",children:[(null==v?void 0:v.investorPhone)&&(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)("div",{className:"bg-[#313131] rounded-lg border border-[#FFB238] flex flex-col items-center justify-center cursor-pointer w-24 h-20",onClick:()=>{let e="https://wa.me/".concat(null==v?void 0:v.investorPhone);window.open(e,"_blank")},children:(0,s.jsx)(T(),{src:U,alt:"",width:30})}),(0,s.jsx)("p",{className:"text-sm mt-2 text-center",children:"WhatsApp"})]}),(null==v?void 0:v.investorEmail)&&(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)("div",{className:"bg-[#313131] rounded-lg border border-[#FFB238] flex flex-col items-center justify-center cursor-pointer  w-24 h-20",onClick:()=>{window.open("mailto:".concat(null==v?void 0:v.investorEmail),"_blank")},children:(0,s.jsx)(T(),{src:H,alt:"",width:30})}),(0,s.jsx)("p",{className:"text-sm mt-2 text-center",children:"E-mail"})]})]})]}),(null==v?void 0:v.latestEvent.status)==="SENT_TO_RETENTION"&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"my-5",children:[(0,s.jsx)("p",{className:"font-semibold text-xl mb-3",children:"Atualiza\xe7\xe3o de Status"}),(0,s.jsx)("p",{className:"text-sm mb-3",children:"Clique na Op\xe7\xe3o Escolhida pelo Investidor"}),(0,s.jsxs)("div",{className:"flex gap-1 my-1 cursor-pointer",onClick:()=>{S({title:"Realizar Aditivo",value:"CONTRACT_ADDENDUM_REQUESTED"})},children:[(0,s.jsx)("input",{checked:"CONTRACT_ADDENDUM_REQUESTED"===D.value,type:"radio",className:""}),(0,s.jsx)("p",{className:"text-sm",children:"Realizar Aditivo"})]}),(0,s.jsxs)("div",{className:"flex gap-1 my-1 cursor-pointer",onClick:()=>{S({title:"Realizar Renova\xe7\xe3o de Contrato",value:"RENEWAL_REQUESTED"})},children:[(0,s.jsx)("input",{checked:"RENEWAL_REQUESTED"===D.value,type:"radio",className:""}),(0,s.jsx)("p",{className:"text-sm",children:"Realizar Renova\xe7\xe3o de Contrato"})]}),(0,s.jsxs)("div",{className:"flex gap-1 my-1 cursor-pointer",onClick:()=>{S({title:"Realizar Resgate",value:"REDEMPTION_REQUESTED"})},children:[(0,s.jsx)("input",{checked:"REDEMPTION_REQUESTED"===D.value,type:"radio",className:""}),(0,s.jsx)("p",{className:"text-sm",children:"Realizar Resgate"})]})]}),"CONTRACT_ADDENDUM_REQUESTED"===D.value&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mb-5",children:[(0,s.jsx)("p",{className:"mb-3 text-2xl",children:"Dados de Investimento"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(K.Z,{id:"",label:"Valor do investimento",name:"",type:"text",value:q.value,onChange:e=>{O({...q,value:(0,h.Ht)(e.target.value)})}}),(0,s.jsx)(K.Z,{id:"",label:"Taxa de Remunera\xe7\xe3o Mensal %",name:"",type:"text",value:q.yield,onChange:e=>{O({...q,yield:e.target.value})}}),(0,s.jsx)(K.Z,{id:"",label:"Perfil investidor",name:"",type:"text",value:q.profile,onChange:e=>{O({...q,profile:e.target.value})}})]})]}),(0,s.jsxs)("div",{className:"mb-10",children:[(0,s.jsx)("p",{className:"mb-3 text-2xl",children:"Dados bancarios"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(K.Z,{id:"",label:"Nome do banco",name:"",type:"text",value:q.bank,onChange:e=>{O({...q,bank:e.target.value})}}),(0,s.jsx)(K.Z,{id:"",label:"Conta",name:"",type:"text",value:q.accountNumber,onChange:e=>{O({...q,accountNumber:e.target.value})}}),(0,s.jsx)(K.Z,{id:"",label:"Ag\xeancia",name:"",type:"text",value:q.agency,onChange:e=>{O({...q,agency:e.target.value})}}),(0,s.jsx)(K.Z,{id:"",label:"Chave pix",name:"",type:"text",value:q.pix,onChange:e=>{O({...q,pix:e.target.value})}})]})]})]}),(0,s.jsxs)("div",{className:"w-6/12",children:[(0,s.jsx)("p",{children:"Observa\xe7\xf5es *"}),(0,s.jsx)("textarea",{value:E,onChange:e=>{let{target:t}=e;return F(t.value)},className:"w-full text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 p-2 mt-2"})]}),(0,s.jsxs)("div",{className:"flex flex-col text-white mt-5",children:[(0,s.jsx)("p",{className:"",children:"Anexar arquivo"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 mb-2",children:"*Anexe aqui qualquer arquivo que considere importante."}),(0,s.jsx)("div",{className:"w-28",children:(0,s.jsx)(V.Z,{onFileUploaded:y})})]})]}),(0,s.jsxs)("div",{className:"w-full flex justify-end gap-4",children:[(0,s.jsx)("div",{className:"",children:(0,s.jsx)(n.Z,{label:"Fechar",loading:!1,className:"bg-zinc-700",disabled:!1,handleSubmit:()=>f(!1)})}),(null==v?void 0:v.latestEvent.status)==="SENT_TO_RETENTION"&&(0,s.jsx)("div",{children:(0,s.jsx)(n.Z,{label:"Enviar",loading:Z,className:"bg-orange-linear",disabled:Z,handleSubmit:()=>{"CONTRACT_ADDENDUM_REQUESTED"===D.value?createAditive():renewContract()}})})]})]})}var $={src:"/_next/static/media/doc.13646452.svg",height:26,width:20,blurWidth:0,blurHeight:0};let G=[{title:"Todos",value:""},{title:"\xc0 vencer",value:"SENT_TO_RETENTION"},{title:"Resgate requisitado",value:"REDEMPTION_REQUESTED"},{title:"Renova\xe7\xe3o requisitada",value:"RENEWAL_REQUESTED"},{title:"Aditivo solicitado",value:"CONTRACT_ADDENDUM_REQUESTED"}];function Retencao(e){let{modal:t,setModal:a}=e;(0,Y.useRouter)();let[r,l]=(0,i.useState)([]),[n,d]=(0,i.useState)(),[c,x]=(0,i.useState)(!1),[m,u]=(0,i.useState)(!1),[f,p]=(0,i.useState)(G[0]),[v,g]=(0,i.useState)(1),[b,j]=(0,i.useState)(),[N,w]=(0,i.useState)(""),[y,Z]=(0,i.useState)(""),[_,E]=(0,i.useState)(""),[F,D]=(0,i.useState)({contractAddendumRequestedCount:0,expiringContractsCount:0,redemptionRequestedCount:0,renewalRequestedCount:0,contractAddendumConfirmedCount:0,redemptionConfirmedCount:0,renewalConfirmedCount:0});(0,i.useEffect)(()=>{getContracts(),getData()},[v,f]);let getData=()=>{o.Z.get("/contract-lifecycle-monitoring/dashboard").then(e=>{D(e.data)}).catch(e=>{C.Am.error(e.response.data.message||"N\xe3o conseguimos pegar os dados de monitoramento.")})},getContracts=e=>{x(!0),o.Z.get("/contract-lifecycle-monitoring/retention",{params:{statuses:""===f.value?void 0:f.value,page:v,limit:10}}).then(e=>{l(e.data.data),j({total:e.data.total,perPage:e.data.limit,page:e.data.page,lastPage:e.data.totalPages})}).catch(e=>{}).finally(()=>x(!1))},returnStatus=e=>{var t,a,r,l;let i=G.filter(t=>{var a;return t.value===(null===(a=e.latestEvent)||void 0===a?void 0:a.status)})[0];return(null===(t=e.latestEvent)||void 0===t?void 0:t.status.includes("REDEMPTION"))?(0,s.jsx)("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#FF0404]",children:(0,s.jsx)("p",{className:"text-[10px] text-center text-[#FF0404] font-bold",children:null==i?void 0:i.title})}):(null===(a=e.latestEvent)||void 0===a?void 0:a.status.includes("ADDENDUM"))?(0,s.jsx)("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#429AEC]",children:(0,s.jsx)("p",{className:"text-[10px] text-center text-[#429AEC] font-bold",children:null==i?void 0:i.title})}):(null===(r=e.latestEvent)||void 0===r?void 0:r.status.includes("RETENTION"))?(0,s.jsx)("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#FFB238]",children:(0,s.jsx)("p",{className:"text-[10px] text-center text-[#FFB238] font-bold",children:null==i?void 0:i.title})}):(null===(l=e.latestEvent)||void 0===l?void 0:l.status.includes("RENEWAL"))?(0,s.jsx)("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#1EF97C]",children:(0,s.jsx)("p",{className:"text-[10px] text-center text-[#1EF97C] font-bold",children:null==i?void 0:i.title})}):(0,s.jsx)("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border ",children:(0,s.jsx)("p",{className:"text-[10px] text-center ",children:(null==i?void 0:i.title)||"Sem status"})})};return(0,s.jsxs)("div",{className:t?"fixed w-full h-full":"relative",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white text-center mb-8 text-3xl",children:"Monitoramento"}),(0,s.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,s.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,s.jsxs)("div",{className:"mt-5 mb-5",children:[(0,s.jsx)(T(),{src:$,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),(0,s.jsx)("p",{className:"text-center text-2xl",children:"\xc0 vencer"})]}),(0,s.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:(0,s.jsx)("p",{className:"text-6xl font-semibold",children:F.expiringContractsCount})})]}),(0,s.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,s.jsxs)("div",{className:"mt-5 mb-5",children:[(0,s.jsx)(T(),{src:$,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),(0,s.jsx)("p",{className:"text-center text-2xl",children:"Renovados"})]}),(0,s.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:(0,s.jsx)("p",{className:"text-6xl font-semibold",children:F.renewalConfirmedCount})})]}),(0,s.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,s.jsxs)("div",{className:"mt-5 mb-5",children:[(0,s.jsx)(T(),{src:$,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),(0,s.jsx)("p",{className:"text-center text-2xl",children:"Aditivo"})]}),(0,s.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:(0,s.jsx)("p",{className:"text-6xl font-semibold",children:F.contractAddendumConfirmedCount})})]}),(0,s.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,s.jsxs)("div",{className:"mt-5 mb-5",children:[(0,s.jsx)(T(),{src:$,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),(0,s.jsx)("p",{className:"text-center text-2xl",children:"Resgatados"})]}),(0,s.jsx)("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:(0,s.jsx)("p",{className:"text-6xl font-semibold",children:F.redemptionConfirmedCount})})]})]})]}),(0,s.jsx)("div",{className:"flex bg-[#1C1C1C] p-2 rounded-md m-auto mt-5 text-white w-full md:w-fit flex-wrap md:flex-nowrap gap-2",children:G.map((e,t)=>(0,s.jsx)("div",{onClick:()=>{p(e),g(1)},className:"hover:bg-[#313131] px-2 py-3 rounded-md cursor-pointer text-center flex items-center ".concat((null==f?void 0:f.title)===e.title?"bg-[#313131]":""),children:(0,s.jsx)("p",{className:"".concat((null==f?void 0:f.title)===e.title?"text-[#FF9900]":""," text-xs"),children:e.title})},t))}),(0,s.jsx)("div",{className:"md:w-[1100px] min-h-[250px] m-auto",children:(0,s.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] text-white mt-10 overflow-x-auto w-full rounded-b-md border border-[#FF9900]",children:[(0,s.jsx)("div",{className:"flex w-full justify-end p-2",children:(0,s.jsx)("div",{className:"w-80",children:(0,s.jsx)(W.Z,{handleSearch:()=>{getContracts(N)},setValue:w,value:N})})}),(0,s.jsxs)("table",{className:"w-full relative min-h-20",children:[(0,s.jsx)("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,s.jsxs)("tr",{className:"w-full",children:[(0,s.jsx)("th",{className:"w-10",children:(0,s.jsx)("p",{className:"font-bold text-sm"})}),(0,s.jsx)("th",{className:"py-2",children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Nome"})}),(0,s.jsx)("th",{children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),(0,s.jsx)("th",{children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"E-mail"})}),(0,s.jsx)("th",{children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Status"})}),(0,s.jsx)("th",{children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Inicio do Contrato"})}),(0,s.jsx)("th",{children:(0,s.jsx)("p",{className:"font-bold text-sm",children:"Fim do Contrato"})})]})}),(0,s.jsx)(s.Fragment,{children:!1===c?(0,s.jsx)(s.Fragment,{children:(null==r?void 0:r.length)>0?(0,s.jsx)("tbody",{children:r.map(e=>{var t;return(0,s.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[(0,s.jsx)("td",{className:"w-10",children:(0,s.jsx)("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{a(!0),d(e);let t=G.filter(t=>{var a;return t.value===(null===(a=e.latestEvent)||void 0===a?void 0:a.status)})[0];E(t.title||"")},children:(0,s.jsx)(J.Z,{color:"#fff",width:20})})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-sm text-center py-1",children:(null==e?void 0:e.investorName)||"N\xe3o encontrado"})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-sm text-center py-1",children:(null===(t=(0,h.p4)(e.investorCpf||""))||void 0===t?void 0:t.length)<=11?(0,h.VL)((null==e?void 0:e.investorCpf)||""):(0,h.PK)((null==e?void 0:e.investorCpf)||"")})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-sm text-center py-1",children:e.investorEmail||"N\xe3o encontrado"})}),(0,s.jsx)("td",{className:"",children:returnStatus(e)}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-sm text-center py-1",children:(0,Q.Z)(e.startDate)})}),(0,s.jsx)("td",{children:(0,s.jsx)("p",{className:"text-sm text-center py-1",children:(0,Q.Z)(e.endDate)})})]},e.id)})}):(0,s.jsx)("div",{className:"text-center mt-5 absolute w-full",children:(0,s.jsx)("p",{children:"Nenhum dado encontrado"})})}):(0,s.jsx)("div",{className:"text-center mt-5 absolute w-full",children:(0,s.jsx)("p",{children:"Carregando..."})})})]}),(0,s.jsx)("div",{className:"w-full flex justify-end items-center pr-5 ",children:(0,s.jsxs)("div",{className:"py-2 flex gap-2",children:[(0,s.jsx)("p",{className:"p-1 bg-[#262626] rounded-md ".concat(Number(v)>1?"cursor-pointer":""," flex items-center"),onClick:e=>{let{}=e;Number(v)>1&&g(v-1)},children:(0,s.jsx)(L.Z,{color:Number(v)>1?"#fff":"#424242",width:20})}),(0,s.jsx)("p",{className:"font-bold bg-[#262626] rounded-md py-1 px-2",children:v}),(0,s.jsx)("p",{className:"p-1 bg-[#262626] rounded-md ".concat(v<Number(null==b?void 0:b.lastPage)?"cursor-pointer":""," flex items-center"),onClick:e=>{let{}=e;v<Number(null==b?void 0:b.lastPage)&&g(v+1)},children:(0,s.jsx)(B.Z,{color:v<Number(null==b?void 0:b.lastPage)?"#fff":"#424242",width:20})})]})})]})}),t&&(0,s.jsx)(ModalMonitoramento,{typeContract:_,loading:c,setModal:a,status:f,contract:n,setModalContract:u,setModalType:Z})]})}var X=a(5927),ee=a(727);function Financeiro(){let e=(0,x.e)(),[t,a]=(0,i.useState)(!0),r=(0,Y.useRouter)();j().locale("pt-br");let[l,n]=(0,i.useState)({background:{visible:!1},theme:E.Z,data:[{month:"Jan",value:0},{month:"Fev",value:0},{month:"Mar",value:0},{month:"Abr",value:0},{month:"Mai",value:0},{month:"Jun",value:0},{month:"Jul",value:0},{month:"Ago",value:0},{month:"Set",value:0},{month:"Out",value:0},{month:"Nov",value:0},{month:"Dez",value:0}],series:[{type:"line",xKey:"month",xName:"Month",yKey:"value",interpolation:{type:"linear"},tooltip:{enabled:!1}}],axes:[{type:"category",position:"bottom"},{type:"number",label:{fontSize:9,formatter:e=>{let{value:t}=e;return new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(t)}}}]}),capitalizeFirstLetter=e=>e.charAt(0).toUpperCase()+e.slice(1),formatData=e=>e.map(e=>({month:capitalizeFirstLetter(j()(e.date).utc().format("MMM")),value:Number(e.totalValue)}));(0,i.useEffect)(()=>{getQuotes("daily"),getQuotes("weekly"),getQuotes("monthly"),getChartData()},[]);let[d,c]=(0,i.useState)({daily:{details:[],totalContracts:0,totalValue:0},weekly:{details:[],totalContracts:0,totalValue:0},monthly:{details:[],totalContracts:0,totalValue:0}}),getQuotes=e=>{o.Z.get("/acquisition",{params:{period:e}}).then(t=>{c(a=>({...a,[e]:t.data}))}).catch(e=>{(0,m.Z)(e,"N\xe3o foi possivel pegar os dados de capta\xe7\xe3o!")}).finally(()=>a(!1))},getChartData=()=>{o.Z.get("/acquisition/chart",{params:{period:"monthly"}}).then(e=>{let t=formatData(e.data);n({...l,data:[...t]})}).catch(e=>{C.Am.error(e.response.data.message||"N\xe3o foi possivel buscar os valores de capta\xe7\xe3o")})},AcquisitinCard=a=>{let{title:r,values:l,typePdf:i}=a;return(0,s.jsxs)("div",{className:"md:w-1/3 mb-5 md:mb-0 bg-orange-linear flex flex-col justify-between rounded-t-xl rounded-b-2xl",children:[(0,s.jsxs)("div",{className:"w-full flex flex-col items-center justify-center p-3",children:[(0,s.jsx)(T(),{src:ee.Z,alt:"",width:35,color:"#fff"}),(0,s.jsx)("p",{className:"text-xl",children:r})]}),(0,s.jsx)("div",{className:"w-full bg-[#1C1C1C] p-5 rounded-xl",children:(0,s.jsxs)("div",{className:"flex w-full justify-between items-end pb-3",children:[(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)("p",{className:"text-sm",children:"Novos Contratos"}),(0,s.jsx)(_.Z,{loading:t,height:"25px",children:(0,s.jsx)("p",{className:"text-5xl font-bold",children:l.totalContracts})}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Valor total de Investimento"}),(0,s.jsx)(_.Z,{loading:t,height:"25px",children:(0,s.jsx)("p",{className:"text-xl font-bold",children:l.totalValue.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),l.totalContracts>0&&(0,s.jsx)("p",{onClick:()=>{C.Am.info("Gerando relat\xf3rio!",{autoClose:!1,toastId:"generatePdf"}),o.Z.post("/reports",{},{params:{period:i,type:"acquisition"},headers:{roleId:e.roleId}}).then(e=>{if(""===e.data.url)return C.Am.warning("N\xe3o foram encontrados dados dispon\xedveis para a gera\xe7\xe3o do relat\xf3rio.");window.open(e.data.url,"_blanck")}).catch(e=>{var t,a;C.Am.error((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"N\xe3o foi possivel exportar o relat\xf3rio!")}).finally(()=>C.Am.dismiss("generatePdf"))},className:"bg-orange-linear px-5 text-xs py-1 rounded-md cursor-pointer font-bold translate-y-4",children:"Gerar relat\xf3rio"})]})})]})};return(0,s.jsxs)("div",{className:"w-full flex flex-col gap-8 text-white",children:[(0,s.jsxs)("div",{className:"md:flex w-full gap-8",children:[(0,s.jsx)(X.Z,{type:"TODAY",push:"/home/<USER>/pagamento"}),(0,s.jsx)(X.Z,{type:"WEEK",push:"/home/<USER>/pagamento"}),(0,s.jsx)(X.Z,{type:"MONTH",push:"/home/<USER>/pagamento"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center py-3 px-7 bg-orange-linear rounded-xl cursor-pointer",onClick:()=>r.push("home/financeiro/pagamentos"),children:[(0,s.jsx)("div",{children:(0,s.jsx)("p",{children:"Visualizar lista completa de pagamentos"})}),(0,s.jsx)(B.Z,{width:30})]}),(0,s.jsxs)("div",{className:"md:flex w-full gap-8",children:[(0,s.jsx)(AcquisitinCard,{typePdf:"daily",title:"Capta\xe7\xe3o Di\xe1ria",values:d.daily}),(0,s.jsx)(AcquisitinCard,{typePdf:"weekly",title:"Capta\xe7\xe3o Semanal",values:d.weekly}),(0,s.jsx)(AcquisitinCard,{typePdf:"monthly",title:"Capta\xe7\xe3o Mensal",values:d.monthly})]}),(0,s.jsx)("div",{className:"md:w-5/12",children:(0,s.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 rounded-xl bg-orange-linear border border-[#FF9900]",children:[(0,s.jsxs)("div",{className:"flex items-start justify-center flex-col p-3",children:[(0,s.jsx)(T(),{src:ee.Z,alt:"",width:35,color:"#fff"}),(0,s.jsx)("p",{className:"text-xl",children:"Capta\xe7\xe3o"})]}),(0,s.jsx)("div",{className:"w-full bg-[#1C1C1C] p-1 rounded-xl",children:(0,s.jsx)(q.bY,{options:l})})]})})]})}function Home(){let[e,t]=(0,i.useState)(!1),[a,n]=(0,i.useState)(!1);(0,i.useEffect)(()=>{},[]);let d=(0,x.e)();return(0,s.jsxs)("div",{className:e||a?"fixed w-full":"relative",children:[(0,s.jsx)(r.Z,{}),(0,s.jsx)(l.Z,{children:(0,s.jsxs)("div",{className:"w-full text-white flex md:flex-row flex-col flex-wrap gap-2 justify-between",children:["broker"===d.name||"advisor"===d.name?(0,s.jsx)(BrokerData,{setModal:t}):(0,s.jsx)(s.Fragment,{}),"retention"===d.name&&(0,s.jsx)(Retencao,{modal:a,setModal:n}),"financial"===d.name&&(0,s.jsx)(Financeiro,{}),"admin"===d.name||"superadmin"===d.name?(0,s.jsx)(AdminData,{userType:d.name}):(0,s.jsx)(s.Fragment,{}),"investor"===d.name&&(0,s.jsx)(InvestorData,{}),e&&(0,s.jsx)(ModalRegister,{setModal:t})]})})]})}},2875:function(e,t,a){"use strict";a.d(t,{Z:function(){return Button}});var s=a(7437),r=a(8700);function Button(e){let{handleSubmit:t,loading:a,label:l,disabled:i,className:n,...d}=e;return(0,s.jsx)(r.z,{...d,onClick:t,loading:a,disabled:i,className:n,children:l})}},3220:function(e,t,a){"use strict";var s=a(7437),r=a(2265),l=a(1543),i=a(9367);let n=(0,r.forwardRef)((e,t)=>{let{label:a,bg:n,type:d,...o}=e,[c,x]=(0,r.useState)(!1);return(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white mb-1",children:a}),(0,s.jsxs)("div",{className:"custom-input-wrapper h-12 w-full text-white rounded-xl ring-[#FF9900] ring-1 ".concat("month"===d?"":"ring-inset"," ").concat("transparent"===n?"bg-black":"bg-[#1C1C1C]"," flex-1 flex relative"),children:[(0,s.jsx)("input",{ref:t,type:c&&"password"===d?"text":d,...o,className:"w-full h-12 flex-1 px-4 bg-transparent rounded-xl"}),"password"===d&&(0,s.jsx)("div",{className:"mr-2 cursor-pointer absolute right-0 top-[50%] translate-y-[-50%]",onClick:()=>x(!c),children:c?(0,s.jsx)(l.Z,{width:20}):(0,s.jsx)(i.Z,{width:20})})]})]})});n.displayName="Input",t.Z=n},5927:function(e,t,a){"use strict";a.d(t,{Z:function(){return PaymentTable}});var s=a(7437),r=a(2929),l=a(2265),i=a(4033),n=a(919),d=a(4568),o=a(3014),c=a(8689),x=a(6654),m=a(3256),u=a(8700);function PaymentTable(e){let{type:t,push:a}=e,[h,f]=(0,l.useState)("p"),[p,v]=(0,l.useState)(!1),[g,b]=(0,l.useState)(!1),j=(0,i.useRouter)(),[N,w]=(0,l.useState)([]),y=(0,m.e)(),C="broker"===y.name,returnTitle=()=>{switch(t){case"TODAY":return"hoje";case"WEEK":return"esta semana";case"MONTH":return"este m\xeas"}},returnTypeParam=()=>{switch(t){case"TODAY":return"daily";case"WEEK":return"weekly";case"MONTH":return"monthly"}};(0,l.useEffect)(()=>{getSchedulePayments()},[h]);let getSchedulePayments=()=>{v(!0);let{startDate:e,endDate:a}=(0,n.Z)(t);d.Z.get(C?"/broker/".concat(y.roleId,"/income-payment-scheduled"):"/income-payment-scheduled",{params:{startScheduledPaymentDate:e,endScheduledPaymentDate:a,status:"p"===h?"SCHEDULED":"PAID"},headers:{roleId:y.roleId}}).then(e=>{w(e.data)}).catch(e=>{var t,a;w([]),(0,x.Z)(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message,"N\xe3o foi possivel pegar os pagementos ".concat("p"===h?"pendentes":"efetuados",": ").concat(returnTitle()))}).finally(()=>v(!1))};return(0,s.jsxs)("div",{className:"".concat((()=>{switch(t){case"TODAY":return"bg-red-linear";case"WEEK":return"bg-green-linear";case"MONTH":return"bg-orange-linear"}})()," w-full md:w-1/3 mb-5 md:mb-0 rounded-2xl flex flex-col justify-between"),children:[(0,s.jsxs)("div",{className:"flex justify-between items-center bg-[#1C1C1C] rounded-t-xl",children:[(0,s.jsx)("div",{className:"".concat("p"===h?"bg-transparent":"bg-[#272727]"," p-2 flex-1 text-center hover:bg-transparent cursor-pointer rounded-tl-xl"),onClick:()=>f("p"),children:(0,s.jsx)("p",{className:"text-xs select-none",children:"Pagamentos Pendentes"})}),(0,s.jsx)("div",{className:"".concat("e"===h?"bg-transparent":"bg-[#272727]"," p-2 flex-1 text-center hover:bg-transparent cursor-pointer rounded-tr-xl"),onClick:()=>f("e"),children:(0,s.jsx)("p",{className:"text-xs select-none",children:"Pagamentos Efetuados"})})]}),(0,s.jsxs)("div",{className:"p-5",children:[(0,s.jsx)(r.Z,{width:20}),(0,s.jsxs)("p",{className:"text-sm mt-3",children:["Pagamentos ",(0,s.jsx)("b",{children:"p"===h?"Pendentes":"Efetuados"})," para"]}),(0,s.jsx)("p",{className:"font-bold uppercase text-xl",children:returnTitle()})]}),(0,s.jsx)("div",{className:"w-full bg-[#1C1C1C] min-h-64 py-5 rounded-xl flex-1",children:p?(0,s.jsxs)("div",{className:"w-full px-4",children:[(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)(c.j,{height:"25px"}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-1 w-8/12",children:[(0,s.jsx)(c.j,{height:"25px",className:"mr-1"}),(0,s.jsx)(c.j,{height:"25px"})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)(c.j,{height:"25px"}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-1 w-8/12",children:[(0,s.jsx)(c.j,{height:"25px",className:"mr-1"}),(0,s.jsx)(c.j,{height:"25px"})]})]})]}):N.length>0?(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"max-h-52 overflow-auto px-4",children:N.map(e=>{var t;return(0,s.jsxs)("div",{className:"flex w-full h-full justify-between items-center border-b pb-3 mb-2 border-[#FF9900]",children:[(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)("p",{className:"text-md",children:"Pagamento de Rendimento"}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-1 w-8/12",children:[(0,s.jsx)("p",{className:"font-extralight text-xs",children:null==e?void 0:null===(t=e.investor)||void 0===t?void 0:t.split(" ")[0]}),(0,s.jsx)("div",{className:"h-3 w-[1px] bg-white"}),(0,s.jsx)("p",{className:"font-extralight text-xs",children:Number((null==e?void 0:e.profit)||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]})]}),!C&&(0,s.jsx)(u.z,{size:"sm",className:"mx-[5px]",onClick:()=>{localStorage.setItem("payment",JSON.stringify(e)),j.push("".concat(a,"/").concat(e.id))},children:"Ver Mais"})]},e.id)})}),(0,s.jsx)("div",{className:"flex justify-end gap-4 mt-4 px-6",children:(0,s.jsx)(u.z,{className:"mx-[5px]",loading:g,onClick:()=>{b(!0),o.Am.info("Gerando relat\xf3rio!",{autoClose:!1,toastId:"generatePdf"}),d.Z.post(C?"/broker/".concat(y.roleId,"/scheduled-payment-report"):"/reports",{},{params:{period:returnTypeParam(),type:"p"===h?"scheduledPayment":"paid"},headers:{roleId:y.roleId}}).then(e=>{if(o.Am.dismiss("generatePdf"),""===e.data.url)return o.Am.warning("N\xe3o foram encontrados dados dispon\xedveis para a gera\xe7\xe3o do relat\xf3rio.");window.open(e.data.url,"_blanck")}).catch(e=>{var t,a;(0,x.Z)(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message,"N\xe3o foi possivel exportar o relat\xf3rio!")}).finally(()=>{o.Am.dismiss("generatePdf"),b(!1)})},children:"Relat\xf3rio"})})]}):(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:"text-sm text-[#b3b3b3] select-none ml-4",children:["Nenhum pagamento ","e"===h?"efetuado":"pendente para","  ",returnTitle(),"!"]})})})]})}},7395:function(e,t,a){"use strict";a.d(t,{l:function(){return s}});let s="Shayra Madalena Lyra de Pinho"},6121:function(e,t,a){"use strict";a.d(t,{Z:function(){return formatDate},l:function(){return formatDateToEnglishType}});var s=a(4279),r=a.n(s);function formatDate(e){return r().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?r().utc(e).format("YYYY-MM-DD"):r().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},919:function(e,t,a){"use strict";a.d(t,{H:function(){return getFinalDataWithMount},Z:function(){return getDataFilter}});var s=a(2067),r=a.n(s);function getDataFilter(e){switch(e){case"TODAY":{let e=r()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}case"WEEK":{let e=r()().startOf("isoWeek").format("YYYY-MM-DD"),t=r()().endOf("isoWeek").format("YYYY-MM-DD");return{startDate:e,endDate:t}}case"MONTH":{let e=r()().startOf("month").format("YYYY-MM-DD"),t=r()().endOf("month").format("YYYY-MM-DD");return{startDate:e,endDate:t}}default:{let e=r()().format("YYYY-MM-DD");return{startDate:e,endDate:e}}}}function getFinalDataWithMount(e){let{investDate:t,startDate:a}=e,s=r()(a).format("DD/MM/YYYY"),l=Number(t),i=r()(s,"DD/MM/YYYY").add(l,"months").format("DD/MM/YYYY");return i}},6654:function(e,t,a){"use strict";a.d(t,{Z:function(){return returnError}});var s=a(3014);function returnError(e,t){var a,r,l,i;let n=(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.error);if(Array.isArray(n))return n.forEach(e=>{s.Am.error(e,{toastId:e})}),n.join("\n");if("string"==typeof n)return s.Am.error(n,{toastId:n}),n;if("object"==typeof n&&null!==n){let e=Object.values(n).flat().join("\n");return s.Am.error(e,{toastId:e}),e}return s.Am.error(t,{toastId:t}),t}},9233:function(e,t){"use strict";t.Z={palette:{fills:["#FF9900"]}}},3277:function(e,t,a){"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}a.d(t,{Z:function(){return formatNumberValue}})},7157:function(e,t,a){"use strict";a.d(t,{m:function(){return isUnderage}});var s=a(2067),r=a.n(s);function isUnderage(e){let t=r()(e),a=r()().diff(t,"years");return a<18}},7934:function(e,t,a){"use strict";a.d(t,{WF:function(){return i},_n:function(){return n},bs:function(){return l}});var s=a(5691),r=a(5968);let l=s.Ry().shape({document:s.Z_().required("Obrigat\xf3rio"),email:s.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),value:s.Z_().required("Obrigat\xf3rio"),term:s.Z_().required("Obrigat\xf3rio"),modality:s.Z_().required("Obrigat\xf3rio"),yield:s.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),purchaseWith:s.Z_().required("Obrigat\xf3rio"),amountQuotes:s.Z_().default(""),startContract:s.Z_().required("Obrigat\xf3rio"),endContract:s.Z_().required("Obrigat\xf3rio"),profile:s.Z_().required("Obrigat\xf3rio"),details:s.Z_().notRequired(),debenture:s.Z_().required("Obrigat\xf3rio")}).required();s.Ry().shape({name:s.Z_().required("Obrigat\xf3rio"),rg:s.Z_().required("Obrigat\xf3rio"),document:s.Z_().required("Obrigat\xf3rio"),phoneNumber:s.Z_().min(15,"N\xfamero de telefone inv\xe1lido!").max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:s.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[t,s,r]=e.split("-");if(!t||!s||!r||4!==t.length)return!1;let l=Number(t);if(isNaN(l)||l<1900||l>new Date().getFullYear())return!1;let i=new Date(e);return!(isNaN(i.getTime())||a(7157).m(e))}),email:s.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:s.Z_().required("Obrigat\xf3rio"),neighborhood:s.Z_().required("Obrigat\xf3rio"),state:s.Z_().required("Obrigat\xf3rio"),city:s.Z_().required("Obrigat\xf3rio"),complement:s.Z_().default(""),number:s.Z_().required("Obrigat\xf3rio"),value:s.Z_().required("Obrigat\xf3rio"),term:s.Z_().required("Obrigat\xf3rio"),modality:s.Z_().required("Obrigat\xf3rio"),yield:s.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),observations:s.Z_().default(""),purchaseWith:s.Z_().required("Obrigat\xf3rio"),amountQuotes:s.Z_(),initDate:s.Z_().required("Obrigat\xf3rio"),endDate:s.Z_().required("Obrigat\xf3rio"),gracePeriod:s.Z_().required("Obrigat\xf3rio"),profile:s.Z_().required("Obrigat\xf3rio"),bank:s.Z_().required("Obrigat\xf3rio"),accountNumber:s.Z_().required("Obrigat\xf3rio"),agency:s.Z_().required("Obrigat\xf3rio"),pix:s.Z_().required("Obrigat\xf3rio"),debenture:s.Z_().required("Obrigat\xf3rio"),motherName:s.Z_(),placeOfBirth:s.Z_(),occupation:s.Z_(),issuer:s.Z_(),testifyPrimaryName:s.Z_(),testifyPrimaryCpf:s.Z_(),testifySecondaryName:s.Z_(),testifySecondaryCpf:s.Z_(),companyAddress:s.Z_(),companyCity:s.Z_(),companyUF:s.Z_(),companyType:s.Z_()}).required(),s.Ry().shape({value:s.Z_().required("Obrigat\xf3rio"),profile:s.Z_().required("Obrigat\xf3rio"),yield:s.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),date:s.Z_().required("Obrigat\xf3rio"),bank:s.Z_().required("Obrigat\xf3rio"),accountNumber:s.Z_().required("Obrigat\xf3rio"),agency:s.Z_().required("Obrigat\xf3rio"),pix:s.Z_().required("Obrigat\xf3rio")}),s.Ry().shape({name:s.Z_().required("Obrigat\xf3rio"),document:s.Z_().required("Obrigat\xf3rio"),phoneNumber:s.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:s.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[t,s,r]=e.split("-");if(!t||!s||!r||4!==t.length)return!1;let l=Number(t);if(isNaN(l)||l<1900||l>new Date().getFullYear())return!1;let i=new Date(e);return!(isNaN(i.getTime())||a(7157).m(e))}),email:s.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:s.Z_().required("Obrigat\xf3rio"),neighborhood:s.Z_().required("Obrigat\xf3rio"),state:s.Z_().required("Obrigat\xf3rio"),city:s.Z_().required("Obrigat\xf3rio"),complement:s.Z_().default(""),number:s.Z_().required("Obrigat\xf3rio"),profile:s.Z_().required("Obrigat\xf3rio"),term:s.Z_().required("Obrigat\xf3rio"),yield:s.Rx().required("Obrigat\xf3rio").min(0,"O valor m\xednimo \xe9 0").max(5,"O valor m\xe1ximo \xe9 5").typeError("O valor deve ser um n\xfamero v\xe1lido"),value:s.Z_().required("Obrigat\xf3rio"),bank:s.Z_().required("Obrigat\xf3rio"),agency:s.Z_().required("Obrigat\xf3rio"),accountNumber:s.Z_().required("Obrigat\xf3rio"),pix:s.Z_().required("Obrigat\xf3rio"),debenture:s.Z_().required("Obrigat\xf3rio"),observations:s.Z_().default(""),details:s.Z_().default(""),initDate:s.Z_().required("Obrigat\xf3rio"),endDate:s.Z_().required("Obrigat\xf3rio"),amountQuotes:s.Z_().default(""),modality:s.Z_().required("Obrigat\xf3rio"),purchaseWith:s.Z_().required("Obrigat\xf3rio"),motherName:s.Z_().when("document",(e,t)=>e[0]&&(0,r.p4)(e[0]).length<=11?t.required("Campo obrigat\xf3rio"):t.notRequired())}).required();let i=s.Ry().shape({isPf:s.O7().default(!1),birthDate:s.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return!1;let t=new Date(e),s=t.getFullYear();return!(isNaN(s)||s<1900||s>new Date().getFullYear()||t>new Date||a(7157).m(e))}),socialName:s.Z_(),isTaxable:s.Z_().required("Obrigat\xf3rio"),fullName:s.Z_().required("Obrigat\xf3rio"),cpf:s.Z_().required("Obrigat\xf3rio"),email:s.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),phoneNumber:s.Z_().required("Obrigat\xf3rio"),motherName:s.Z_().required("Obrigat\xf3rio"),pep:s.Z_().required("Obrigat\xf3rio"),ownerCep:s.Z_().required("Obrigat\xf3rio"),ownerCity:s.Z_().required("Obrigat\xf3rio"),ownerState:s.Z_().required("Obrigat\xf3rio"),ownerNeighborhood:s.Z_().required("Obrigat\xf3rio"),ownerStreet:s.Z_().required("Obrigat\xf3rio"),ownerComplement:s.Z_().notRequired(),ownerNumber:s.Z_().required("Obrigat\xf3rio"),fantasyName:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),cnpj:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),companyName:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessPhoneNumber:s.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),dtOpening:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessEmail:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Campo obrigat\xf3rio"):t.notRequired()),type:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),size:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessCep:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessCity:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessState:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessNeighborhood:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessStreet:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),businessComplement:s.Z_().when("isPf",(e,t)=>(e[0],t.notRequired())),businessNumber:s.Z_().when("isPf",(e,t)=>!1===e[0]?t.required("Campo obrigat\xf3rio"):t.notRequired()),participationPercentage:s.Rx().min(0,"O valor m\xednimo \xe9 0").max(15,"O valor m\xe1ximo \xe9 15").typeError("O valor deve ser um n\xfamero v\xe1lido").when("$hide",{is:!0,then:e=>e.notRequired(),otherwise:e=>e.required("Obrigat\xf3rio")})}).required(),n=s.Ry().shape({name:s.Z_().required("Obrigat\xf3rio"),rg:s.Z_().required("Obrigat\xf3rio"),document:s.Z_().required("Obrigat\xf3rio"),phoneNumber:s.Z_().max(15,"N\xfamero de telefone inv\xe1lido!").required("Obrigat\xf3rio"),dtBirth:s.Z_().required("Obrigat\xf3rio").test("valid-date","Data de nascimento inv\xe1lida",e=>{if(!e)return!1;let[t,s,r]=e.split("-");if(!t||!s||!r||4!==t.length)return!1;let l=Number(t);if(isNaN(l)||l<1900||l>new Date().getFullYear())return!1;let i=new Date(e);return!(isNaN(i.getTime())||a(7157).m(e))}),email:s.Z_().email("E-mail incorreto").matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,"E-mail inv\xe1lido").required("Obrigat\xf3rio"),zipCode:s.Z_().required("Obrigat\xf3rio"),neighborhood:s.Z_().required("Obrigat\xf3rio"),state:s.Z_().required("Obrigat\xf3rio"),city:s.Z_().required("Obrigat\xf3rio"),complement:s.Z_().default(""),number:s.Z_().required("Obrigat\xf3rio"),bank:s.Z_().required("Obrigat\xf3rio"),accountNumber:s.Z_().required("Obrigat\xf3rio"),agency:s.Z_().required("Obrigat\xf3rio"),pix:s.Z_().required("Obrigat\xf3rio"),motherName:s.Z_().required("Obrigat\xf3rio"),placeOfBirth:s.Z_(),occupation:s.Z_(),issuer:s.Z_(),testifyPrimaryName:s.Z_(),testifyPrimaryCpf:s.Z_(),testifySecondaryName:s.Z_(),testifySecondaryCpf:s.Z_()}).required()},7848:function(e,t,a){"use strict";function isValidateCPF(e){if(11!==(e=e.replace(/[^\d]+/g,"")).length||/^(\d)\1{10}$/.test(e))return!1;let calcularDigitoVerificador=e=>{let t=0,a=e.length+1;for(let s=0;s<e.length;s++)t+=parseInt(e.charAt(s),10)*a--;let s=11-t%11;return s>9?0:s},t=e.substring(0,9),a=calcularDigitoVerificador(t),s=calcularDigitoVerificador(t+a);return e===t+a+s}function isValidateCNPJ(e){if(14!==(e=e.replace(/[^\d]+/g,"")).length||/^(\d)\1{13}$/.test(e))return!1;let calcularDigitoVerificador=(e,t)=>{let a=0;for(let s=0;s<e.length;s++)a+=parseInt(e.charAt(s),10)*t[s];let s=11-a%11;return s>9?0:s},t=e.substring(0,12),a=calcularDigitoVerificador(t,[5,4,3,2,9,8,7,6,5,4,3,2]),s=calcularDigitoVerificador(t+a,[6,5,4,3,2,9,8,7,6,5,4,3,2]);return e===t+a+s}a.d(t,{p:function(){return isValidateCPF},w:function(){return isValidateCNPJ}})},727:function(e,t){"use strict";t.Z={src:"/_next/static/media/captation.1af10a88.svg",height:24,width:32,blurWidth:0,blurHeight:0}}},function(e){e.O(0,[6990,7326,3649,8276,5371,6946,1865,3964,526,3151,2971,7864,1744],function(){return e(e.s=3905)}),_N_E=e.O()}]);