(()=>{var e={};e.id=1951,e.ids=[1951],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},38466:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>x,routeModule:()=>u,tree:()=>c});var a=s(73137),l=s(54647),r=s(4183),n=s.n(r),i=s(71775),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let o=a.AppPageRouteModule,c=["",{children:["home",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84914)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\home\\page.tsx"],m="/home/<USER>",h={require:s,loadChunk:()=>Promise.resolve()},u=new o({definition:{kind:l.x.APP_PAGE,page:"/home/<USER>",pathname:"/home",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91758:(e,t,s)=>{Promise.resolve().then(s.bind(s,90349))},90349:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Home});var a=s(60080),l=s(97669),r=s(47956),n=s(9885),i=s(99986),d=s(49714),o=s(85814),c=s(42686),x=s(90682),m=s(24577),h=s(17871),u=s(96413),p=s(88879),f=s(22434),j=s(69195),v=s(57086),g=s(64731),b=s.n(g),N=s(95081),w=s.n(N),C=s(66558),y=s(34751);function ModalRegister({setModal:e}){let[t,s]=(0,n.useState)(!1),l=(0,x.e)(),[r,g]=(0,n.useState)("PHYSICAL"),[N,F]=(0,n.useState)(""),[E,S]=(0,n.useState)(""),{register:A,handleSubmit:T,watch:Z,setValue:D,reset:P,formState:{errors:R}}=(0,C.cI)({resolver:(0,v.X)(p.bs)}),M=Z("startContract"),k=Z("term");(0,n.useEffect)(()=>{if(M&&k){let e=(0,c.H)({investDate:k,startDate:M});D("endContract",e)}},[M,k]);let I=Z("modality"),_=Z("document");(0,n.useEffect)(()=>{D("modality","P2P")},[]),(0,n.useEffect)(()=>{_?.length<=14?g("PHYSICAL"):g("BUSINESS")},[_]);let handleCopy=e=>{navigator.clipboard.writeText(e).then(()=>{y.Am.info("Link de cadastro copiado!")}).catch(e=>{console.error("Erro ao copiar o texto: ",e)})};return a.jsx("div",{className:"z-20 absolute top-0 left-0 w-screen h-screen bg-[#1c1c1c71]",children:(0,a.jsxs)("div",{className:"z-20 bg-[#1C1C1C] md:w-[65%] w-full absolute right-0 md:border-l md:border-t md:border-b border-[#FF9900] p-10 text-white overflow-auto h-full",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center",children:a.jsx(j.Z,{color:"#000",width:20})}),a.jsx("div",{className:"gap-y-1 flex flex-col",children:a.jsx("p",{className:"font-bold text-lg",children:"Pr\xe9-cadastro para gerar link"})})]}),(0,a.jsxs)("form",{action:"",onSubmit:T(e=>{if((0,u.p4)(e.document||"").length<=11){if(!(0,f.p)((0,u.p4)(e.document||"")))return y.Am.warn("CPF do investidor inv\xe1lido!")}else{if(!(0,f.w)((0,u.p4)(e.document||"")))return y.Am.warn("CNPJ inv\xe1lido!");if(!(0,f.p)((0,u.p4)(E)))return y.Am.warn("CPF do representante inv\xe1lido!")}s(!0);let t={adviserId:l.roleId,document:(0,u.p4)(e.document||""),email:e.email,signIca:d.l,investment:{value:(0,h.Z)(e.value),term:e.term,modality:e.modality,yield:Number(e.yield),purchaseWith:e.purchaseWith,amountQuotes:Number(e.amountQuotes||"0"),gracePeriod:b()(e.startContract).add(1,"day").format("YYYY-MM-DD"),debenture:"s"===e.debenture,startContract:e.startContract,endContract:b()(e.endContract,"DD/MM/YYYY").format("YYYY-MM-DD"),profile:e.profile,details:""},accountType:r,owner:"BUSINESS"===r?{name:N,cpf:(0,u.p4)(E)}:void 0};o.Z.post("/pre-register/send",t).then(e=>{let t=w().tz.guess(),a=w()().tz(t),l=a.hour();l>=9&&l<18?y.Am.success("Conta pr\xe9 cadastrada com sucesso!"):y.Am.success("Contrato criado com sucesso. Informamos que, por ter sido realizado fora do hor\xe1rio comercial, o registro cont\xe1bil ser\xe1 processado somente no pr\xf3ximo dia \xfatil.",{delay:6e3}),handleCopy(`${window.location.protocol}//${window.location.host}/registro/${e.data.token}`),s(!1),P()}).catch(e=>{s(!1),(0,m.Z)(e,"Tivemos um erro ao cadastrar a conta")})}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("p",{className:"text-xl text-white mr-1",children:"Dados cadastro"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["E-mail ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.email&&`- ${R.email.message}`})]}),a.jsx("input",{...A("email"),className:`h-12 w-full px-4 text-white rounded-xl ${R.email?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["CPF/CNPJ ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.document&&`- ${R.document.message}`})]}),a.jsx("input",{...A("document"),onChange:({target:e})=>{let t=e.value;t.length<=14?D("document",(0,u.VL)(t)):D("document",(0,u.PK)(t))},className:`h-12 w-full px-4 text-white rounded-xl ${R.document?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),"BUSINESS"===r&&(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white mb-1",children:"Nome do representante"}),a.jsx("input",{value:N,onChange:({target:e})=>F(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1"})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white mb-1",children:"CPF do representante "}),a.jsx("input",{value:E,onChange:({target:e})=>S((0,u.VL)(e.value)),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900]ring-1 ring-inset bg-black flex-1"})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-3 mt-10",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("p",{className:"text-xl text-white mr-1",children:"Dados de Investimento"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Valor do investimento ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.value&&`- ${R.value.message}`})]}),a.jsx("input",{...A("value"),onChange:({target:e})=>D("value",(0,u.Ht)(e.value)),className:`h-12 w-full px-4 text-white rounded-xl ${R.value?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Prazo do Investimento - em meses ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.term&&`- ${R.term.message}`})]}),a.jsx("input",{...A("term"),className:`h-12 w-full px-4 text-white rounded-xl ${R.term?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Taxa de Remunera\xe7\xe3o Mensal % ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.yield&&`- ${R.yield.message}`})]}),a.jsx("input",{...A("yield"),type:"text",className:`h-12 w-full px-4 text-white rounded-xl ${R.yield?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-2/4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Tipo do Investimento ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.modality&&`- ${R.modality.message}`})]}),(0,a.jsxs)("select",{...A("modality"),className:"h-12 w-full px-4 text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1",children:[a.jsx("option",{className:"cursor-pointer",value:"P2P",children:"M\xfatuo"}),a.jsx("option",{className:"cursor-pointer",value:"SCP",children:"SCP"})]})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-start mt-4",children:[a.jsx("div",{className:"md:w-1/3",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Comprar com ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.purchaseWith&&`- ${R.purchaseWith.message}`})]}),a.jsx("input",{...A("purchaseWith"),className:`h-12 w-full px-4 text-white rounded-xl ${R.purchaseWith?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),"P2P"!==I&&a.jsx("div",{className:"md:w-1/3",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Quantidade de cotas ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.amountQuotes&&`- ${R.amountQuotes.message}`})]}),a.jsx("input",{...A("amountQuotes"),type:"number",className:`h-12 w-full px-4 text-white rounded-xl ${R.amountQuotes?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/3",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Inicio do contrato ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.startContract&&`- ${R.startContract.message}`})]}),a.jsx("input",{...A("startContract"),type:"date",min:w().utc().format("YYYY-MM-DD"),className:`h-12 w-full px-4 text-white rounded-xl ${R.startContract?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})})]}),(0,a.jsxs)("div",{className:"flex md:flex-row flex-col w-full gap-4 justify-between mt-4",children:[a.jsx("div",{className:"md:w-1/3",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Final do contrato ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.endContract&&`- ${R.endContract.message}`})]}),a.jsx("input",{...A("endContract"),type:"text",disabled:!0,className:`h-12 w-full px-4 text-zinc-400 rounded-xl ${R.endContract?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/3",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Perfil investidor ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.profile&&`- ${R.profile.message}`})]}),a.jsx("input",{...A("profile"),className:`h-12 w-full px-4 text-white rounded-xl ${R.profile?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`})]})}),a.jsx("div",{className:"md:w-1/3",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Deb\xeanture ",a.jsx("b",{className:"text-red-500 font-light text-sm",children:R.debenture&&`- ${R.debenture.message}`})]}),(0,a.jsxs)("select",{...A("debenture"),className:`h-12 w-full px-4 text-white rounded-xl ${R.debenture?"ring-[#f33636]":"ring-[#FF9900]"} ring-1 ring-inset bg-black flex-1`,children:[a.jsx("option",{value:"s",children:"Sim"}),a.jsx("option",{value:"n",children:"N\xe3o"})]})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-start mt-10",children:[a.jsx("div",{className:"w-52",children:a.jsx(i.Z,{label:"Gerar link",loading:t,className:"bg-orange-linear ",disabled:t})}),a.jsx("div",{className:"px-5 ml-10 bg-[#313131] cursor-pointer flex items-center justify-center rounded-lg",onClick:()=>e(!1),children:a.jsx("p",{className:"text-sm",children:"Fechar"})})]})]})]})})}var F=s(93640),E=s(74644),S=s(27612),A=s(43110),T=s(11862),Z=s(82671),D=s(73589),P=s(52451),R=s.n(P);let M={src:"/_next/static/media/doc_verify.c3f8e067.svg",height:27,width:27,blurWidth:0,blurHeight:0},k={src:"/_next/static/media/grafic_persons.877d09d7.svg",height:31,width:31,blurWidth:0,blurHeight:0},I={Sunday:"Domingo",Monday:"Segunda-feira",Tuesday:"Ter\xe7a-feira",Wednesday:"Quarta-feira",Thursday:"Quinta-feira",Friday:"Sexta-feira",Saturday:"S\xe1bado"};function BrokerData({setModal:e}){let[t,s]=(0,n.useState)(),[l,r]=(0,n.useState)(!0),i=(0,x.P)(),d=(0,x.e)(),c=i.name.split(" "),[h,u]=(0,n.useState)({type:"SCP",period:"year"}),[p,f]=(0,n.useState)({type:"SCP",period:"year"}),[v,g]=(0,n.useState)({data:[{month:"Jan",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Fev",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Mar",avgTemp:6.3,value:0,name:"Janeiro"},{month:"Abr",avgTemp:16.2,value:0,name:"Janeiro"},{month:"Mai",avgTemp:22.8,value:0,name:"Janeiro"},{month:"Jun",avgTemp:14.5,value:0,name:"Janeiro"},{month:"Jul",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Ago",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Set",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Out",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Nov",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Dez",avgTemp:8.9,value:0,name:"Janeiro"}],background:{visible:!1},series:[{type:"bar",xKey:"month",yKey:"value",tooltip:{enabled:!1},label:{enabled:!1}}],axes:[{type:"category",position:"bottom",key:"month"},{type:"number",position:"left",label:{formatter:e=>e.value.toLocaleString("pt-br",{style:"currency",currency:"BRL"})}}],padding:{top:10},theme:S.Z}),capitalizeFirstLetter=e=>e.charAt(0).toUpperCase()+e.slice(1),returnTitle=(e,t)=>"year"===t?capitalizeFirstLetter(b()(e).utc().format("MMM")):"month"===t?e:I[e],formatData=(e,t,s)=>e.map(e=>({month:"year"===s?returnTitle(e.date,s):returnTitle(e.label,s),value:Number("contract"===t?e.totalContracts:e.totalValue)})),getChartContractsData=()=>{o.Z.get(`${d.name}/${"superadmin"!==d.name?`${d.roleId}/`:""}contracts-growth`,{params:{period:h.period,contractType:h.type}}).then(e=>{let t=formatData(e.data.data,"results",h.period);g({...v,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de contratos!")})};(0,n.useEffect)(()=>{getChartContractsData()},[h]),(0,n.useEffect)(()=>{getChartResultsData()},[p]);let getChartResultsData=()=>{o.Z.get(`${d.name}/${"superadmin"!==d.name?`${d.roleId}/`:""}contracts-growth`,{params:{period:p.period,contractType:p.type}}).then(e=>{let t=formatData(e.data.data,"contract",p.period);w({...N,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de resultados!")})},[N,w]=(0,n.useState)({background:{visible:!1},theme:S.Z,data:[{month:"Jan",value:0},{month:"Fev",value:0},{month:"Mar",value:0},{month:"Abr",value:0},{month:"Mai",value:0},{month:"Jun",value:0},{month:"Jul",value:0},{month:"Ago",value:0},{month:"Set",value:0},{month:"Out",value:0},{month:"Nov",value:0},{month:"Dez",value:0}],series:[{type:"line",xKey:"month",xName:"Month",yKey:"value",interpolation:{type:"linear"},tooltip:{enabled:!1}}]});return(0,n.useEffect)(()=>{r(!0),o.Z.get("broker"===d.name?"/broker/dashboard":"/advisor/dashboard").then(e=>{s(e.data)}).catch(e=>{}).finally(()=>r(!1))},[]),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"w-full flex md:flex-row flex-col gap-2 justify-between",children:[a.jsx("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border",children:(0,a.jsxs)("div",{className:"flex w-full",children:[a.jsx("div",{className:"w-6 h-6 p-1 bg-white rounded-full flex items-center justify-center",children:a.jsx("p",{className:"text-[#FF9900] text-xs font-bold",children:`${c[0][0]||""}${c[c.length-1][0]||""}`})}),(0,a.jsxs)("div",{className:"w-full",children:[a.jsx("p",{className:"ml-3 text-sm",children:i?.name}),a.jsx("div",{className:"w-full flex justify-end"})]})]})}),a.jsx("div",{className:"bg-[#1C1C1C] md:w-[60%] p-5 rounded-lg border-[#FF9900] border",children:(0,a.jsxs)("div",{className:"flex-col w-full",children:[a.jsx("div",{className:"",children:a.jsx(T.Z,{width:20,color:"#FF9900"})}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-sm",children:"FAQ ICA BANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),a.jsx("div",{children:a.jsx("p",{className:"text-xs",children:"Tire suas d\xfavidas em nossa base de conhecimento"})})]})]})})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mt-2",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(j.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Investidores Ativos"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.scpContractNumber})})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.p2pContractNumber})})]})]})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(Z.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Total de retiradas"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:Number(t?.p2pWithdraws||0)})})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"Saldo bloqueado"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:Number(t?.scpWithdraws||0)})})]})]})]}),(0,a.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(A.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Total de contratos ativos"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-3xl",children:t?.activeInvestorsNumber})})})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(A.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Valor total de contratos"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-xl",children:(Number(t?.p2pContractAmount)+Number(t?.scpContractAmount))?.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(Z.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Cotas ativas"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-xl",children:t?.activeQuotes})})})]})]}),(0,a.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(R(),{src:M,alt:"",width:25,style:{marginTop:"-5px"}}),a.jsx("p",{className:"text-sm mt-2",children:"Gr\xe1fico Crescente de Contratos"})]}),(0,a.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[a.jsx(F.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:h.type,setSelected:e=>{u({...h,type:e})}}),a.jsx(F.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:h.period,setSelected:e=>{u({...h,period:e})}})]})]}),a.jsx(D.bY,{className:"p-2",options:v})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(R(),{src:k,alt:"",width:25,style:{marginTop:"-5px"}}),a.jsx("p",{className:"text-sm",children:"Gr\xe1fico Crescente de Resultados"})]}),(0,a.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[a.jsx(F.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:p.type,setSelected:e=>{f({...p,type:e})}}),a.jsx(F.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:p.period,setSelected:e=>{f({...p,period:e})}})]})]}),a.jsx(D.bY,{options:N})]})]})]})]})}var _=s(13979);let $={src:"/_next/static/media/saques_efetivados.58e03bdc.svg",height:27,width:27,blurWidth:0,blurHeight:0};var q=s(57114);function AdminData({userType:e}){let[t,s]=(0,n.useState)(),[l,r]=(0,n.useState)(!1),i=(0,x.e)(),d=(0,q.useRouter)(),[c,h]=(0,n.useState)({type:"SCP",period:"year"}),[u,p]=(0,n.useState)({type:"SCP",period:"year"}),[f,v]=(0,n.useState)({data:[{month:"Jan",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Fev",avgTemp:2.3,value:0,name:"Janeiro"},{month:"Mar",avgTemp:6.3,value:0,name:"Janeiro"},{month:"Abr",avgTemp:16.2,value:0,name:"Janeiro"},{month:"Mai",avgTemp:22.8,value:0,name:"Janeiro"},{month:"Jun",avgTemp:14.5,value:0,name:"Janeiro"},{month:"Jul",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Ago",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Set",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Out",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Nov",avgTemp:8.9,value:0,name:"Janeiro"},{month:"Dez",avgTemp:8.9,value:0,name:"Janeiro"}],background:{visible:!1},series:[{type:"bar",xKey:"month",yKey:"value",tooltip:{enabled:!1},label:{enabled:!1}}],axes:[{type:"category",position:"bottom",key:"month"},{type:"number",position:"left",label:{formatter:e=>e.value.toLocaleString("pt-br",{style:"currency",currency:"BRL"})}}],padding:{top:10},theme:S.Z}),capitalizeFirstLetter=e=>e.charAt(0).toUpperCase()+e.slice(1),returnTitle=(e,t)=>"year"===t?capitalizeFirstLetter(b()(e).utc().format("MMM")):"month"===t?e:I[e],formatData=(e,t,s)=>e.map(e=>({month:"year"===s?returnTitle(e.date,s):returnTitle(e.label,s),value:Number("contract"===t?e.totalContracts:e.totalValue),avgTemp:2.3,name:"year"===s?returnTitle(e.date,s):returnTitle(e.label,s)})),getChartContractsData=()=>{o.Z.get(`${i.name}/${"superadmin"!==i.name?`${i.roleId}/`:""}contracts-growth`,{params:{period:c.period,contractType:c.type}}).then(e=>{let t=formatData(e.data.data,"results",c.period);v({...f,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de contratos!")})};(0,n.useEffect)(()=>{getChartContractsData()},[c]),(0,n.useEffect)(()=>{getChartResultsData()},[u]);let getChartResultsData=()=>{o.Z.get(`${i.name}/${"superadmin"!==i.name?`${i.roleId}/`:""}contracts-growth`,{params:{period:u.period,contractType:u.type}}).then(e=>{let t=formatData(e.data.data,"contract",u.period);N({...g,data:[...t]})}).catch(e=>{(0,m.Z)(e,"Erro ao buscar dados do gr\xe1fico de resultados!")})},[g,N]=(0,n.useState)({background:{visible:!1},theme:S.Z,data:[{month:"Jan",value:0},{month:"Fev",value:0},{month:"Mar",value:0},{month:"Abr",value:0},{month:"Mai",value:0},{month:"Jun",value:0},{month:"Jul",value:0},{month:"Ago",value:0},{month:"Set",value:0},{month:"Out",value:0},{month:"Nov",value:0},{month:"Dez",value:0}],series:[{type:"line",xKey:"month",xName:"Month",yKey:"value",interpolation:{type:"linear"},tooltip:{enabled:!1}}]});return(0,n.useEffect)(()=>{i.name&&!l&&(r(!0),o.Z.get("superadmin"===i.name?"/super-admin/dashboard":"/admin/dashboard").then(e=>{s(e.data)}).catch(e=>{y.Am.error("Erro ao buscar os dados!")}).finally(()=>r(!1)))},[i.name]),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(j.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Contratos Ativos (R$)"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.scpContractAmount.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.p2pContractAmount.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(Z.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Rendimentos Distribu\xeddos (R$)"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.distributedIncome.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.distributedIncome.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(R(),{src:$,alt:"",width:15})}),a.jsx("p",{className:"ml-3 text-sm",children:"Resgates Efetivados"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.scpWithdraws.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-lg",children:t?.p2pWithdraws.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]})]})]}),(0,a.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(_.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Investidores Ativos"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-3xl",children:t?.activeInvestorsNumber})})})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(_.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Cotistas Ativos"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-3xl",children:t?.shareholder})})})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(Z.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Cotas Ativas"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-3xl",children:t?.activeQuotes})})})]})]}),"superadmin"===i.name&&(0,a.jsxs)("div",{className:"w-full flex md:flex-row flex-col gap-2 justify-start flex-wrap",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:w-[33%] p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(_.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Brokers Ativos"})]}),a.jsx("p",{className:"text-xs px-4 border py-2 rounded-lg select-none cursor-pointer",onClick:()=>{localStorage.setItem("searchRanking","broker"),d.push("/home/<USER>")},children:"Ver mais"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-3xl",children:t?.numberBrokers})})})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:w-[33%] p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-6 h-6 bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(_.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Assessores Ativos"})]}),a.jsx("p",{className:"text-xs px-4 border py-2 rounded-lg select-none cursor-pointer",onClick:()=>{localStorage.setItem("searchRanking","advisor"),d.push("/home/<USER>")},children:"Ver mais"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx(E.Z,{loading:l,height:"25px",children:a.jsx("p",{className:"font-bold text-3xl",children:t?.numberAdvisors})})})]})]}),(0,a.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(R(),{src:M,alt:"",width:25,style:{marginTop:"-5px"}}),a.jsx("p",{className:"text-sm mt-2",children:"Gr\xe1fico Crescente de Contratos"})]}),(0,a.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[a.jsx(F.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:c.type,setSelected:e=>{h({...c,type:e})}}),a.jsx(F.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:c.period,setSelected:e=>{h({...c,period:e})}})]})]}),a.jsx(D.bY,{className:"p-2",options:f})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row mb-5 md:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(R(),{src:k,alt:"",width:25,style:{marginTop:"-5px"}}),a.jsx("p",{className:"text-sm",children:"Gr\xe1fico Crescente de Resultados"})]}),(0,a.jsxs)("div",{className:"flex md:justify-end gap-2 md:mt-0 mt-2",children:[a.jsx(F.Z,{align:"center",options:[{label:"SCP",value:"SCP"},{label:"M\xfatuo",value:"P2P"}],size:"small",selected:u.type,setSelected:e=>{p({...u,type:e})}}),a.jsx(F.Z,{align:"center",options:[{label:"Esse ano",value:"year"},{label:"Esse m\xeas",value:"month"},{label:"Essa semana",value:"week"}],size:"small",selected:u.period,setSelected:e=>{p({...u,period:e})}})]})]}),a.jsx(D.bY,{options:g})]})]})]})}var z=s(90880),J=s(95373);function InvestorData(){let[e,t]=(0,n.useState)(),s=(0,x.e)();(0,n.useEffect)(()=>{getInvestor()},[]);let getInvestor=()=>{o.Z.get("/investors/dashboard",{params:{roleId:s.roleId}}).then(e=>{t(e.data)}).catch(e=>{y.Am.error("Erro ao buscar dados do investidor")})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"w-full flex md:flex-row flex-col gap-2 justify-between",children:[a.jsx("div",{className:"bg-[#1C1C1C] md:w-[40%] p-5 rounded-lg border-[#FF9900] border",children:(0,a.jsxs)("div",{className:"flex w-full",children:[a.jsx("div",{className:"w-[25px] h-[25px] bg-white rounded-full flex items-center justify-center",children:a.jsx("p",{className:"text-[#FF9900] text-xs font-bold",children:`${e?.advisor.owner.name.split(" ")[0][0]||""}${e?.advisor.owner.name.split(" ")[1][0]||""}`})}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"ml-3 text-base",children:e?.advisor.owner.name||""}),a.jsx("p",{className:"ml-3 text-sm font-extralight",children:"Consultor(a)"})]}),a.jsx("div",{className:"w-full flex justify-end",children:(0,a.jsxs)("div",{className:"text-center rounded-md mt-3 cursor-pointer border border-[#1EF97C] flex items-center px-4 py-[7px]",onClick:()=>{let t=(0,u.p4)(e?.advisor?.owner?.phone||""),s=`https://wa.me/${t}`;window.open(s,"_blank")},children:[a.jsx(z.Z,{width:20,color:"#1EF97C"}),a.jsx("p",{className:"text-sm text-[#1EF97C] ml-2 font-bold select-none",children:"WhatsApp"})]})})]})]})}),a.jsx("div",{className:"bg-[#1C1C1C] md:w-[60%] p-5 rounded-lg border-[#FF9900] border",children:(0,a.jsxs)("div",{className:"flex-col w-full",children:[a.jsx("div",{className:"",children:a.jsx(T.Z,{width:20,color:"#FF9900"})}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-sm",children:"FAQ ICA BANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),a.jsx("div",{children:a.jsx("p",{className:"text-xs",children:"Tire suas d\xfavidas em nossa base de conhecimento"})})]})]})})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(j.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Total de Contratos Ativos"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),a.jsx("p",{className:"font-bold text-lg",children:e?.scpQuotest||0})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),a.jsx("p",{className:"font-bold text-lg",children:e?.p2pQuotest||0})]})]})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(Z.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Total de Rendimentos"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),(0,a.jsxs)("p",{className:"font-bold text-lg",children:["R$ ",(0,u.Ht)(String(e?.totalIncome))||"0,00"]})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),(0,a.jsxs)("p",{className:"font-bold text-lg",children:["R$ ",(0,u.Ht)(String(e?.totalIncome))||"0,00"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] md:flex-1 w-full md:w-auto p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(J.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Saldo de Rendimento"})]}),(0,a.jsxs)("div",{className:"ml-5",children:[(0,a.jsxs)("div",{className:"mt-5",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"SCP"}),a.jsx("p",{className:"font-bold text-lg",children:"Em andamento"})]}),(0,a.jsxs)("div",{className:"mt-1",children:[a.jsx("p",{className:"text-gradient font-bold text-xs",children:"M\xfatuo"}),a.jsx("p",{className:"font-bold text-lg",children:"Em andamento"})]})]})]}),(0,a.jsxs)("div",{className:"w-full flex gap-2 justify-between flex-wrap",children:[(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(_.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Valor Total de Contratos Ativos "})]}),a.jsx("div",{className:"w-full text-center mt-3",children:(0,a.jsxs)("p",{className:"font-bold text-xl",children:["R$ ",(0,u.Ht)(String(e?.totalContractsAmounts))||"0,00"]})})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(_.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Total de Saques"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:a.jsx("p",{className:"font-bold text-xl",children:e?.totalIncome||0})})]}),(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 p-5 rounded-lg border-[#FF9900] border",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"w-[25px] h-[25px] bg-orange-linear rounded-full flex items-center justify-center",children:a.jsx(Z.Z,{width:15,color:"#fff"})}),a.jsx("p",{className:"ml-3 text-sm",children:"Saldo Bloqueado"})]}),a.jsx("div",{className:"w-full text-center mt-3",children:(0,a.jsxs)("p",{className:"font-bold text-xl",children:["R$ ",(0,u.Ht)(String(e?.blockedBalance))||"0,00"]})})]})]})]})]})}var L=s(28168),Y=s(9317),B=s(7537),O=s(30170),Q=s(33050),U=s(15455);let W={src:"/_next/static/media/contact.a27d8b69.svg",height:54,width:54,blurWidth:0,blurHeight:0},V={src:"/_next/static/media/email.a16acbce.svg",height:37,width:43,blurWidth:0,blurHeight:0};var H=s(32411);function ModalMonitoramento({setModal:e,status:t,contract:s,setModalContract:l,setModalType:r,typeContract:c}){let[m,h]=(0,n.useState)(),[p,f]=(0,n.useState)(!1),[j,v]=(0,n.useState)(""),[g,N]=(0,n.useState)({title:"",value:""}),[w,C]=(0,n.useState)({value:"",profile:"",yield:"",date:"",bank:"",agency:"",accountNumber:"",pix:""}),F=(0,x.e)(),renewContract=()=>{let e=new FormData;if(""===g.value)return y.Am.warning(`Selecione uma atualiza\xe7\xe3o para o contrato`);e.append("status",g.value),e.append("comment",j),e.append("ownerRoleId",F.roleId),m&&e.append("attachments",m[0]),f(!0),o.Z.post(`/contract-lifecycle-monitoring/${s?.id}/retention/update`,e).then(e=>{y.Am.success("Opera\xe7\xe3o realizada com sucesso!"),f(!1),window.location.reload()}).catch(e=>{y.Am.error(e?.response?.data?.message||"N\xe3o foi possivel completar a opera\xe7\xe3o"),f(!1)})},createAditive=()=>{f(!0);let e={roleId:s?.investorId,contractId:s?.id,investment:{value:Number(w.value.replace(".","").replace(",",".")),profile:w.profile,yield:Number(w.yield),date:b()().format("YYYY-MM-DD")},accountBank:{bank:w.bank,accountNumber:w.accountNumber,agency:w.agency,pix:w.pix},observations:j,signIca:d.l};o.Z.post("/contract/additive",e).then(e=>{renewContract()}).catch(e=>{y.Am.error(e.message||"N\xe3o conseguimos criar o contrato de aditivo!"),f(!1)})};return(0,a.jsxs)("div",{className:"z-10 w-10/12 bg-[#000000] fixed top-0 right-0 bottom-0 text-white overflow-auto h-auto p-10 border-l",children:[a.jsx("div",{className:"w-full text-center",children:a.jsx("h1",{className:"text-2xl font-bold",children:c})}),"\xc0 vencer"===c&&a.jsx("div",{className:"flex mt-5",children:a.jsx("div",{className:"bg-[#1E1E1E94] m-auto px-5 py-1 rounded-lg",children:(0,a.jsxs)("p",{className:"",children:[a.jsx("b",{className:"text-[#FFF700]",children:"Aviso:"})," est\xe1 chegando o vencimento do contrato."]})})}),(0,a.jsxs)("div",{children:[a.jsx("h2",{children:"Dados Pessoais"}),(0,a.jsxs)("div",{className:"flex justify-between w-full mt-2",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"E-mail"}),a.jsx("p",{className:"text-sm",children:s?.investorEmail})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"CPF"}),a.jsx("p",{className:"text-sm",children:s?.investorCpf})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"RG"}),a.jsx("p",{className:"text-sm",children:"N\xe3o informado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"Perfil"}),a.jsx("p",{className:"text-sm",children:"N\xe3o informado"})]})]})]}),(0,a.jsxs)("div",{className:"mt-10",children:[a.jsx("h2",{children:"Informa\xe7\xf5es de Endere\xe7o"}),(0,a.jsxs)("div",{className:"flex justify-between w-full mt-2",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"Endere\xe7o"}),a.jsx("p",{className:"text-sm",children:s?.investorAddresses[0]?.street||"N\xe3o informado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"N\xfamero"}),a.jsx("p",{className:"text-sm",children:s?.investorAddresses[0]?.number||"N\xe3o informado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"Complemento"}),a.jsx("p",{className:"text-sm",children:s?.investorAddresses[0]?.complement||"N\xe3o informado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"Bairro"}),a.jsx("p",{className:"text-sm",children:s?.investorAddresses[0]?.neighborhood||"N\xe3o informado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"Cidade"}),a.jsx("p",{className:"text-sm",children:s?.investorAddresses[0]?.city||"N\xe3o informado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"UF"}),a.jsx("p",{className:"text-sm",children:s?.investorAddresses[0]?.state||"N\xe3o informado"})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-bold",children:"CEP"}),a.jsx("p",{className:"text-sm",children:s?.investorAddresses[0]?.zipCode||"N\xe3o informado"})]})]})]}),(0,a.jsxs)("div",{className:"my-10",children:[a.jsx("p",{children:"Observa\xe7\xe3o"}),a.jsx("p",{className:"mt-2 rounded-lg border w-6/12 bg-zinc-950 p-2 border-[#FFB238]",children:s?.latestEvent.comment||"Nenhuma observa\xe7\xe3o"})]}),(0,a.jsxs)("div",{className:"w-6/12",children:[a.jsx("p",{className:"font-semibold",children:"Clique em um dos bot\xf5es abaixo para ser direcionado aos meios de contato do investidor e iniciar a comunica\xe7\xe3o."}),(0,a.jsxs)("div",{className:"flex mt-5 gap-4",children:[s?.investorPhone&&(0,a.jsxs)("div",{className:"",children:[a.jsx("div",{className:"bg-[#313131] rounded-lg border border-[#FFB238] flex flex-col items-center justify-center cursor-pointer w-24 h-20",onClick:()=>{let e=`https://wa.me/${s?.investorPhone}`;window.open(e,"_blank")},children:a.jsx(R(),{src:W,alt:"",width:30})}),a.jsx("p",{className:"text-sm mt-2 text-center",children:"WhatsApp"})]}),s?.investorEmail&&(0,a.jsxs)("div",{className:"",children:[a.jsx("div",{className:"bg-[#313131] rounded-lg border border-[#FFB238] flex flex-col items-center justify-center cursor-pointer  w-24 h-20",onClick:()=>{window.open(`mailto:${s?.investorEmail}`,"_blank")},children:a.jsx(R(),{src:V,alt:"",width:30})}),a.jsx("p",{className:"text-sm mt-2 text-center",children:"E-mail"})]})]})]}),s?.latestEvent.status==="SENT_TO_RETENTION"&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"my-5",children:[a.jsx("p",{className:"font-semibold text-xl mb-3",children:"Atualiza\xe7\xe3o de Status"}),a.jsx("p",{className:"text-sm mb-3",children:"Clique na Op\xe7\xe3o Escolhida pelo Investidor"}),(0,a.jsxs)("div",{className:"flex gap-1 my-1 cursor-pointer",onClick:()=>{N({title:"Realizar Aditivo",value:"CONTRACT_ADDENDUM_REQUESTED"})},children:[a.jsx("input",{checked:"CONTRACT_ADDENDUM_REQUESTED"===g.value,type:"radio",className:""}),a.jsx("p",{className:"text-sm",children:"Realizar Aditivo"})]}),(0,a.jsxs)("div",{className:"flex gap-1 my-1 cursor-pointer",onClick:()=>{N({title:"Realizar Renova\xe7\xe3o de Contrato",value:"RENEWAL_REQUESTED"})},children:[a.jsx("input",{checked:"RENEWAL_REQUESTED"===g.value,type:"radio",className:""}),a.jsx("p",{className:"text-sm",children:"Realizar Renova\xe7\xe3o de Contrato"})]}),(0,a.jsxs)("div",{className:"flex gap-1 my-1 cursor-pointer",onClick:()=>{N({title:"Realizar Resgate",value:"REDEMPTION_REQUESTED"})},children:[a.jsx("input",{checked:"REDEMPTION_REQUESTED"===g.value,type:"radio",className:""}),a.jsx("p",{className:"text-sm",children:"Realizar Resgate"})]})]}),"CONTRACT_ADDENDUM_REQUESTED"===g.value&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-5",children:[a.jsx("p",{className:"mb-3 text-2xl",children:"Dados de Investimento"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[a.jsx(H.Z,{id:"",label:"Valor do investimento",name:"",type:"text",value:w.value,onChange:e=>{C({...w,value:(0,u.Ht)(e.target.value)})}}),a.jsx(H.Z,{id:"",label:"Taxa de Remunera\xe7\xe3o Mensal %",name:"",type:"text",value:w.yield,onChange:e=>{C({...w,yield:e.target.value})}}),a.jsx(H.Z,{id:"",label:"Perfil investidor",name:"",type:"text",value:w.profile,onChange:e=>{C({...w,profile:e.target.value})}})]})]}),(0,a.jsxs)("div",{className:"mb-10",children:[a.jsx("p",{className:"mb-3 text-2xl",children:"Dados bancarios"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[a.jsx(H.Z,{id:"",label:"Nome do banco",name:"",type:"text",value:w.bank,onChange:e=>{C({...w,bank:e.target.value})}}),a.jsx(H.Z,{id:"",label:"Conta",name:"",type:"text",value:w.accountNumber,onChange:e=>{C({...w,accountNumber:e.target.value})}}),a.jsx(H.Z,{id:"",label:"Ag\xeancia",name:"",type:"text",value:w.agency,onChange:e=>{C({...w,agency:e.target.value})}}),a.jsx(H.Z,{id:"",label:"Chave pix",name:"",type:"text",value:w.pix,onChange:e=>{C({...w,pix:e.target.value})}})]})]})]}),(0,a.jsxs)("div",{className:"w-6/12",children:[a.jsx("p",{children:"Observa\xe7\xf5es *"}),a.jsx("textarea",{value:j,onChange:({target:e})=>v(e.value),className:"w-full text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 p-2 mt-2"})]}),(0,a.jsxs)("div",{className:"flex flex-col text-white mt-5",children:[a.jsx("p",{className:"",children:"Anexar arquivo"}),a.jsx("p",{className:"text-sm text-zinc-500 mb-2",children:"*Anexe aqui qualquer arquivo que considere importante."}),a.jsx("div",{className:"w-28",children:a.jsx(U.Z,{onFileUploaded:h})})]})]}),(0,a.jsxs)("div",{className:"w-full flex justify-end gap-4",children:[a.jsx("div",{className:"",children:a.jsx(i.Z,{label:"Fechar",loading:!1,className:"bg-zinc-700",disabled:!1,handleSubmit:()=>e(!1)})}),s?.latestEvent.status==="SENT_TO_RETENTION"&&a.jsx("div",{children:a.jsx(i.Z,{label:"Enviar",loading:p,className:"bg-orange-linear",disabled:p,handleSubmit:()=>{"CONTRACT_ADDENDUM_REQUESTED"===g.value?createAditive():renewContract()}})})]})]})}let K={src:"/_next/static/media/doc.13646452.svg",height:26,width:20,blurWidth:0,blurHeight:0},G=[{title:"Todos",value:""},{title:"\xc0 vencer",value:"SENT_TO_RETENTION"},{title:"Resgate requisitado",value:"REDEMPTION_REQUESTED"},{title:"Renova\xe7\xe3o requisitada",value:"RENEWAL_REQUESTED"},{title:"Aditivo solicitado",value:"CONTRACT_ADDENDUM_REQUESTED"}];function Retencao({modal:e,setModal:t}){(0,q.useRouter)();let[s,l]=(0,n.useState)([]),[r,i]=(0,n.useState)(),[d,c]=(0,n.useState)(!1),[x,m]=(0,n.useState)(!1),[h,p]=(0,n.useState)(G[0]),[f,j]=(0,n.useState)(1),[v,g]=(0,n.useState)(),[b,N]=(0,n.useState)(""),[w,C]=(0,n.useState)(""),[F,E]=(0,n.useState)(""),[S,A]=(0,n.useState)({contractAddendumRequestedCount:0,expiringContractsCount:0,redemptionRequestedCount:0,renewalRequestedCount:0,contractAddendumConfirmedCount:0,redemptionConfirmedCount:0,renewalConfirmedCount:0});(0,n.useEffect)(()=>{getContracts(),getData()},[f,h]);let getData=()=>{o.Z.get("/contract-lifecycle-monitoring/dashboard").then(e=>{A(e.data)}).catch(e=>{y.Am.error(e.response.data.message||"N\xe3o conseguimos pegar os dados de monitoramento.")})},getContracts=e=>{c(!0),o.Z.get("/contract-lifecycle-monitoring/retention",{params:{statuses:""===h.value?void 0:h.value,page:f,limit:10}}).then(e=>{l(e.data.data),g({total:e.data.total,perPage:e.data.limit,page:e.data.page,lastPage:e.data.totalPages})}).catch(e=>{}).finally(()=>c(!1))},returnStatus=e=>{let t=G.filter(t=>t.value===e.latestEvent?.status)[0];return e.latestEvent?.status.includes("REDEMPTION")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#FF0404]",children:a.jsx("p",{className:"text-[10px] text-center text-[#FF0404] font-bold",children:t?.title})}):e.latestEvent?.status.includes("ADDENDUM")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#429AEC]",children:a.jsx("p",{className:"text-[10px] text-center text-[#429AEC] font-bold",children:t?.title})}):e.latestEvent?.status.includes("RETENTION")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#FFB238]",children:a.jsx("p",{className:"text-[10px] text-center text-[#FFB238] font-bold",children:t?.title})}):e.latestEvent?.status.includes("RENEWAL")?a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border border-[#1EF97C]",children:a.jsx("p",{className:"text-[10px] text-center text-[#1EF97C] font-bold",children:t?.title})}):a.jsx("div",{className:"py-[5px] px-[10px] w-11/12 m-auto my-1 rounded-md border ",children:a.jsx("p",{className:"text-[10px] text-center ",children:t?.title||"Sem status"})})};return(0,a.jsxs)("div",{className:e?"fixed w-full h-full":"relative",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white text-center mb-8 text-3xl",children:"Monitoramento"}),(0,a.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,a.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,a.jsxs)("div",{className:"mt-5 mb-5",children:[a.jsx(R(),{src:K,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),a.jsx("p",{className:"text-center text-2xl",children:"\xc0 vencer"})]}),a.jsx("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:a.jsx("p",{className:"text-6xl font-semibold",children:S.expiringContractsCount})})]}),(0,a.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,a.jsxs)("div",{className:"mt-5 mb-5",children:[a.jsx(R(),{src:K,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),a.jsx("p",{className:"text-center text-2xl",children:"Renovados"})]}),a.jsx("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:a.jsx("p",{className:"text-6xl font-semibold",children:S.renewalConfirmedCount})})]}),(0,a.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,a.jsxs)("div",{className:"mt-5 mb-5",children:[a.jsx(R(),{src:K,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),a.jsx("p",{className:"text-center text-2xl",children:"Aditivo"})]}),a.jsx("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:a.jsx("p",{className:"text-6xl font-semibold",children:S.contractAddendumConfirmedCount})})]}),(0,a.jsxs)("div",{className:"bg-orange-linear w-[19%] relative rounded-lg p-[1px]",children:[(0,a.jsxs)("div",{className:"mt-5 mb-5",children:[a.jsx(R(),{src:K,alt:"teste",priority:!0,width:15,className:"absolute top-4 left-4"}),a.jsx("p",{className:"text-center text-2xl",children:"Resgatados"})]}),a.jsx("div",{className:"bg-[#1C1C1C] rounded-lg h-52 flex items-center justify-center relative",children:a.jsx("p",{className:"text-6xl font-semibold",children:S.redemptionConfirmedCount})})]})]})]}),a.jsx("div",{className:"flex bg-[#1C1C1C] p-2 rounded-md m-auto mt-5 text-white w-full md:w-fit flex-wrap md:flex-nowrap gap-2",children:G.map((e,t)=>a.jsx("div",{onClick:()=>{p(e),j(1)},className:`hover:bg-[#313131] px-2 py-3 rounded-md cursor-pointer text-center flex items-center ${h?.title===e.title?"bg-[#313131]":""}`,children:a.jsx("p",{className:`${h?.title===e.title?"text-[#FF9900]":""} text-xs`,children:e.title})},t))}),a.jsx("div",{className:"md:w-[1100px] min-h-[250px] m-auto",children:(0,a.jsxs)("div",{className:"rounded-t-md bg-[#1C1C1C] text-white mt-10 overflow-x-auto w-full rounded-b-md border border-[#FF9900]",children:[a.jsx("div",{className:"flex w-full justify-end p-2",children:a.jsx("div",{className:"w-80",children:a.jsx(O.Z,{handleSearch:()=>{getContracts(b)},setValue:N,value:b})})}),(0,a.jsxs)("table",{className:"w-full relative min-h-20",children:[a.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,a.jsxs)("tr",{className:"w-full",children:[a.jsx("th",{className:"w-10",children:a.jsx("p",{className:"font-bold text-sm"})}),a.jsx("th",{className:"py-2",children:a.jsx("p",{className:"font-bold text-sm",children:"Nome"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"CPF/CNPJ"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"E-mail"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Status"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Inicio do Contrato"})}),a.jsx("th",{children:a.jsx("p",{className:"font-bold text-sm",children:"Fim do Contrato"})})]})}),a.jsx(a.Fragment,{children:!1===d?a.jsx(a.Fragment,{children:s?.length>0?a.jsx("tbody",{children:s.map(e=>(0,a.jsxs)("tr",{className:"border-b-[1px] border-b-black",children:[a.jsx("td",{className:"w-10",children:a.jsx("div",{className:"flex items-center justify-center cursor-pointer",onClick:()=>{t(!0),i(e);let s=G.filter(t=>t.value===e.latestEvent?.status)[0];E(s.title||"")},children:a.jsx(L.Z,{color:"#fff",width:20})})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:e?.investorName||"N\xe3o encontrado"})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:u.p4(e.investorCpf||"")?.length<=11?(0,u.VL)(e?.investorCpf||""):(0,u.PK)(e?.investorCpf||"")})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:e.investorEmail||"N\xe3o encontrado"})}),a.jsx("td",{className:"",children:returnStatus(e)}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:(0,Q.Z)(e.startDate)})}),a.jsx("td",{children:a.jsx("p",{className:"text-sm text-center py-1",children:(0,Q.Z)(e.endDate)})})]},e.id))}):a.jsx("div",{className:"text-center mt-5 absolute w-full",children:a.jsx("p",{children:"Nenhum dado encontrado"})})}):a.jsx("div",{className:"text-center mt-5 absolute w-full",children:a.jsx("p",{children:"Carregando..."})})})]}),a.jsx("div",{className:"w-full flex justify-end items-center pr-5 ",children:(0,a.jsxs)("div",{className:"py-2 flex gap-2",children:[a.jsx("p",{className:`p-1 bg-[#262626] rounded-md ${Number(f)>1?"cursor-pointer":""} flex items-center`,onClick:({})=>{Number(f)>1&&j(f-1)},children:a.jsx(Y.Z,{color:Number(f)>1?"#fff":"#424242",width:20})}),a.jsx("p",{className:"font-bold bg-[#262626] rounded-md py-1 px-2",children:f}),a.jsx("p",{className:`p-1 bg-[#262626] rounded-md ${f<Number(v?.lastPage)?"cursor-pointer":""} flex items-center`,onClick:({})=>{f<Number(v?.lastPage)&&j(f+1)},children:a.jsx(B.Z,{color:f<Number(v?.lastPage)?"#fff":"#424242",width:20})})]})})]})}),e&&a.jsx(ModalMonitoramento,{typeContract:F,loading:d,setModal:t,status:h,contract:r,setModalContract:m,setModalType:C})]})}var X=s(51344),ee=s(63960);function Financeiro(){let e=(0,x.e)(),[t,s]=(0,n.useState)(!0),l=(0,q.useRouter)();b().locale("pt-br");let[r,i]=(0,n.useState)({background:{visible:!1},theme:S.Z,data:[{month:"Jan",value:0},{month:"Fev",value:0},{month:"Mar",value:0},{month:"Abr",value:0},{month:"Mai",value:0},{month:"Jun",value:0},{month:"Jul",value:0},{month:"Ago",value:0},{month:"Set",value:0},{month:"Out",value:0},{month:"Nov",value:0},{month:"Dez",value:0}],series:[{type:"line",xKey:"month",xName:"Month",yKey:"value",interpolation:{type:"linear"},tooltip:{enabled:!1}}],axes:[{type:"category",position:"bottom"},{type:"number",label:{fontSize:9,formatter:({value:e})=>new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e)}}]}),capitalizeFirstLetter=e=>e.charAt(0).toUpperCase()+e.slice(1),formatData=e=>e.map(e=>({month:capitalizeFirstLetter(b()(e.date).utc().format("MMM")),value:Number(e.totalValue)}));(0,n.useEffect)(()=>{getQuotes("daily"),getQuotes("weekly"),getQuotes("monthly"),getChartData()},[]);let[d,c]=(0,n.useState)({daily:{details:[],totalContracts:0,totalValue:0},weekly:{details:[],totalContracts:0,totalValue:0},monthly:{details:[],totalContracts:0,totalValue:0}}),getQuotes=e=>{o.Z.get("/acquisition",{params:{period:e}}).then(t=>{c(s=>({...s,[e]:t.data}))}).catch(e=>{(0,m.Z)(e,"N\xe3o foi possivel pegar os dados de capta\xe7\xe3o!")}).finally(()=>s(!1))},getChartData=()=>{o.Z.get("/acquisition/chart",{params:{period:"monthly"}}).then(e=>{let t=formatData(e.data);i({...r,data:[...t]})}).catch(e=>{y.Am.error(e.response.data.message||"N\xe3o foi possivel buscar os valores de capta\xe7\xe3o")})},AcquisitinCard=({title:s,values:l,typePdf:r})=>(0,a.jsxs)("div",{className:"md:w-1/3 mb-5 md:mb-0 bg-orange-linear flex flex-col justify-between rounded-t-xl rounded-b-2xl",children:[(0,a.jsxs)("div",{className:"w-full flex flex-col items-center justify-center p-3",children:[a.jsx(R(),{src:ee.Z,alt:"",width:35,color:"#fff"}),a.jsx("p",{className:"text-xl",children:s})]}),a.jsx("div",{className:"w-full bg-[#1C1C1C] p-5 rounded-xl",children:(0,a.jsxs)("div",{className:"flex w-full justify-between items-end pb-3",children:[(0,a.jsxs)("div",{className:"",children:[a.jsx("p",{className:"text-sm",children:"Novos Contratos"}),a.jsx(E.Z,{loading:t,height:"25px",children:a.jsx("p",{className:"text-5xl font-bold",children:l.totalContracts})}),a.jsx("p",{className:"text-sm mt-2",children:"Valor total de Investimento"}),a.jsx(E.Z,{loading:t,height:"25px",children:a.jsx("p",{className:"text-xl font-bold",children:l.totalValue.toLocaleString("pt-br",{style:"currency",currency:"BRL"})})})]}),l.totalContracts>0&&a.jsx("p",{onClick:()=>{y.Am.info("Gerando relat\xf3rio!",{autoClose:!1,toastId:"generatePdf"}),o.Z.post("/reports",{},{params:{period:r,type:"acquisition"},headers:{roleId:e.roleId}}).then(e=>{if(""===e.data.url)return y.Am.warning("N\xe3o foram encontrados dados dispon\xedveis para a gera\xe7\xe3o do relat\xf3rio.");window.open(e.data.url,"_blanck")}).catch(e=>{y.Am.error(e?.response?.data?.message||"N\xe3o foi possivel exportar o relat\xf3rio!")}).finally(()=>y.Am.dismiss("generatePdf"))},className:"bg-orange-linear px-5 text-xs py-1 rounded-md cursor-pointer font-bold translate-y-4",children:"Gerar relat\xf3rio"})]})})]});return(0,a.jsxs)("div",{className:"w-full flex flex-col gap-8 text-white",children:[(0,a.jsxs)("div",{className:"md:flex w-full gap-8",children:[a.jsx(X.Z,{type:"TODAY",push:"/home/<USER>/pagamento"}),a.jsx(X.Z,{type:"WEEK",push:"/home/<USER>/pagamento"}),a.jsx(X.Z,{type:"MONTH",push:"/home/<USER>/pagamento"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 px-7 bg-orange-linear rounded-xl cursor-pointer",onClick:()=>l.push("home/financeiro/pagamentos"),children:[a.jsx("div",{children:a.jsx("p",{children:"Visualizar lista completa de pagamentos"})}),a.jsx(B.Z,{width:30})]}),(0,a.jsxs)("div",{className:"md:flex w-full gap-8",children:[a.jsx(AcquisitinCard,{typePdf:"daily",title:"Capta\xe7\xe3o Di\xe1ria",values:d.daily}),a.jsx(AcquisitinCard,{typePdf:"weekly",title:"Capta\xe7\xe3o Semanal",values:d.weekly}),a.jsx(AcquisitinCard,{typePdf:"monthly",title:"Capta\xe7\xe3o Mensal",values:d.monthly})]}),a.jsx("div",{className:"md:w-5/12",children:(0,a.jsxs)("div",{className:"bg-[#1C1C1C] flex-1 rounded-xl bg-orange-linear border border-[#FF9900]",children:[(0,a.jsxs)("div",{className:"flex items-start justify-center flex-col p-3",children:[a.jsx(R(),{src:ee.Z,alt:"",width:35,color:"#fff"}),a.jsx("p",{className:"text-xl",children:"Capta\xe7\xe3o"})]}),a.jsx("div",{className:"w-full bg-[#1C1C1C] p-1 rounded-xl",children:a.jsx(D.bY,{options:r})})]})})]})}function Home(){let[e,t]=(0,n.useState)(!1),[s,i]=(0,n.useState)(!1);(0,n.useEffect)(()=>{},[]);let d=(0,x.e)();return(0,a.jsxs)("div",{className:e||s?"fixed w-full":"relative",children:[a.jsx(l.Z,{}),a.jsx(r.Z,{children:(0,a.jsxs)("div",{className:"w-full text-white flex md:flex-row flex-col flex-wrap gap-2 justify-between",children:["broker"===d.name||"advisor"===d.name?a.jsx(BrokerData,{setModal:t}):a.jsx(a.Fragment,{}),"retention"===d.name&&a.jsx(Retencao,{modal:s,setModal:i}),"financial"===d.name&&a.jsx(Financeiro,{}),"admin"===d.name||"superadmin"===d.name?a.jsx(AdminData,{userType:d.name}):a.jsx(a.Fragment,{}),"investor"===d.name&&a.jsx(InvestorData,{}),e&&a.jsx(ModalRegister,{setModal:t})]})})]})}},99986:(e,t,s)=>{"use strict";s.d(t,{Z:()=>Button});var a=s(60080),l=s(69957);function Button({handleSubmit:e,loading:t,label:s,disabled:r,className:n,...i}){return a.jsx(l.z,{...i,onClick:e,loading:t,disabled:r,className:n,children:s})}},49714:(e,t,s)=>{"use strict";s.d(t,{l:()=>a});let a="Shayra Madalena Lyra de Pinho"},33050:(e,t,s)=>{"use strict";s.d(t,{Z:()=>formatDate,l:()=>formatDateToEnglishType});var a=s(95081),l=s.n(a);function formatDate(e){return l().utc(e).format("DD/MM/YYYY")}function formatDateToEnglishType(e){return e.includes("-")?l().utc(e).format("YYYY-MM-DD"):l().utc(e,"DD/MM/YY").format("YYYY-MM-DD")}},17871:(e,t,s)=>{"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}s.d(t,{Z:()=>formatNumberValue})},84914:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>r,default:()=>d});var a=s(17536);let l=(0,a.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\home\page.tsx`),{__esModule:r,$$typeof:n}=l,i=l.default,d=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[4103,6426,4731,8813,5081,8394,6558,3356,1808,7878,130,4154,7207,278,7669,2411,2686,8879,2434,3130],()=>__webpack_exec__(38466));module.exports=s})();