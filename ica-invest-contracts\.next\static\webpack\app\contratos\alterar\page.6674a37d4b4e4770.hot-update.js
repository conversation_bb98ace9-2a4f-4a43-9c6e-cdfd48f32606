"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-multiple\", \"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue > 0 && numericValue % 5000 === 0;\n        }\n        return true;\n    }).test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (mutuamente exclusivos)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento) {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const cotas = Math.floor(valorNumerico / 5000);\n            setValue(\"quotaQuantity\", cotas.toString());\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto usando a estrutura do CreateContractDto\n            const requestData = {\n                name: data.nomeCompleto || \"\",\n                rg: data.identidade || \"\",\n                phoneNumber: (()=>{\n                    const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                    console.log(\"Telefone original:\", data.celular);\n                    console.log(\"Telefone limpo:\", cleanPhone);\n                    // Garantir que tenha 13 dígitos (55 + DDD + número)\n                    if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                        const phoneWithCountry = \"55\" + cleanPhone;\n                        console.log(\"Telefone com c\\xf3digo do pa\\xeds:\", phoneWithCountry);\n                        return phoneWithCountry;\n                    }\n                    return cleanPhone;\n                })(),\n                motherName: data.nomeMae || \"\",\n                dtBirth: (()=>{\n                    // Converter data para formato ISO 8601\n                    const birthDate = data.dataNascimento;\n                    console.log(\"Data nascimento original:\", birthDate);\n                    if (!birthDate) return new Date().toISOString();\n                    // Se já está no formato YYYY-MM-DD, adicionar horário\n                    if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                        const isoDate = birthDate + \"T00:00:00.000Z\";\n                        console.log(\"Data nascimento ISO:\", isoDate);\n                        return isoDate;\n                    }\n                    // Se está em outro formato, tentar converter\n                    const date = new Date(birthDate);\n                    const isoDate = date.toISOString();\n                    console.log(\"Data nascimento convertida:\", isoDate);\n                    return isoDate;\n                })(),\n                address: {\n                    zipCode: data.cep.replace(/\\D/g, \"\"),\n                    neighborhood: \"Centro\",\n                    state: data.estado || \"\",\n                    city: data.cidade,\n                    complement: data.complemento || \"\",\n                    number: data.numero\n                },\n                accountBank: {\n                    bank: data.banco,\n                    accountNumber: data.conta,\n                    agency: data.agencia,\n                    pix: data.chavePix\n                },\n                document: documento,\n                contractType: data.modalidade,\n                observations: \"\",\n                placeOfBirth: data.cidade || \"\",\n                occupation: \"Investidor\",\n                documentType: isPJ ? \"CNPJ\" : \"CPF\",\n                issuer: \"SSP\",\n                quota: data.modalidade === \"SCP\" ? parseInt(data.quotaQuantity || \"0\") : 0,\n                paymentPercentage: parseFloat(data.taxaRemuneracao) || 0,\n                parValue: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0\n            };\n            console.log(\"Enviando dados para API...\", requestData);\n            console.log(\"Data de nascimento final:\", requestData.dtBirth);\n            console.log(\"Tipo da data:\", typeof requestData.dtBirth);\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                const valorComplementarNecessario = 5000 - resto;\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Remove tudo que não for número ou vírgula\n        const limpo = valor.replace(/[^0-9,]/g, \"\").replace(\",\", \".\");\n        return parseFloat(limpo) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 763,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 766,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 787,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 907,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 765,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 764,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 932,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 934,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 933,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 980,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 981,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 994,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 992,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 762,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1029,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1010,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message,\n                                        label: \"Valor do Investimento\",\n                                        placeholder: \"ex: R$ 50.000,00\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1040,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1039,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: calcularAliquotaIR,\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1072,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1080,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1076,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1088,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1100,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1106,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1108,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1109,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1115,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1127,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1073,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1071,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1150,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDeposito\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDeposito\", true);\n                                                    setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDeposito\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1173,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center text-white text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"mr-2\",\n                                            checked: watch(\"irDesconto\"),\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setValue(\"irDesconto\", true);\n                                                    setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                } else {\n                                                    setValue(\"irDesconto\", false);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1189,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1171,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: \"Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1217,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1007,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1224,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1244,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1283,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1284,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1307,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1308,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1301,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1327,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1328,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1323,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1226,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1225,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1335,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting,\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1334,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1003,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1372,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1377,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1378,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1376,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1375,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1374,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1373,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1389,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1397,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1394,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1393,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1409,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1410,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1408,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1392,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1391,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1390,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"zi/CXSEPwkBmT5MW8EVnCE5yTH0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});