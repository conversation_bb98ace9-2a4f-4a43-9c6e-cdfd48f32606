(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3367],{4279:function(e,t,r){(e.exports=r(1223)).tz.load(r(6564))},1223:function(e,t,r){var a,o,s;s=function(e){"use strict";void 0===e.version&&e.default&&(e=e.default);var t,r,a={},o={},s={},n={},i={};e&&"string"==typeof e.version||logError("Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/");var l=e.version.split("."),d=+l[0],u=+l[1];function charCodeToInt(e){return e>96?e-87:e>64?e-29:e-48}function unpackBase60(e){var t,r=0,a=e.split("."),o=a[0],s=a[1]||"",n=1,i=0,l=1;for(45===e.charCodeAt(0)&&(r=1,l=-1);r<o.length;r++)i=60*i+(t=charCodeToInt(o.charCodeAt(r)));for(r=0;r<s.length;r++)n/=60,i+=(t=charCodeToInt(s.charCodeAt(r)))*n;return i*l}function arrayToInt(e){for(var t=0;t<e.length;t++)e[t]=unpackBase60(e[t])}function mapIndices(e,t){var r,a=[];for(r=0;r<t.length;r++)a[r]=e[t[r]];return a}function unpack(e){var t=e.split("|"),r=t[2].split(" "),a=t[3].split(""),o=t[4].split(" ");return arrayToInt(r),arrayToInt(a),arrayToInt(o),function(e,t){for(var r=0;r<t;r++)e[r]=Math.round((e[r-1]||0)+6e4*e[r]);e[t-1]=1/0}(o,a.length),{name:t[0],abbrs:mapIndices(t[1].split(" "),a),offsets:mapIndices(r,a),untils:o,population:0|t[5]}}function Zone(e){e&&this._set(unpack(e))}function Country(e,t){this.name=e,this.zones=t}function OffsetAt(e){var t=e.toTimeString(),r=t.match(/\([a-z ]+\)/i);"GMT"===(r=r&&r[0]?(r=r[0].match(/[A-Z]/g))?r.join(""):void 0:(r=t.match(/[A-Z]{3,5}/g))?r[0]:void 0)&&(r=void 0),this.at=+e,this.abbr=r,this.offset=e.getTimezoneOffset()}function ZoneScore(e){this.zone=e,this.offsetScore=0,this.abbrScore=0}function sortZoneScores(e,t){return e.offsetScore!==t.offsetScore?e.offsetScore-t.offsetScore:e.abbrScore!==t.abbrScore?e.abbrScore-t.abbrScore:e.zone.population!==t.zone.population?t.zone.population-e.zone.population:t.zone.name.localeCompare(e.zone.name)}function normalizeName(e){return(e||"").toLowerCase().replace(/\//g,"_")}function addZone(e){var t,r,o,s;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)a[s=normalizeName(r=(o=e[t].split("|"))[0])]=e[t],n[s]=r,function(e,t){var r,a;for(arrayToInt(t),r=0;r<t.length;r++)i[a=t[r]]=i[a]||{},i[a][e]=!0}(s,o[2].split(" "))}function getZone(e,t){var r,s=a[e=normalizeName(e)];return s instanceof Zone?s:"string"==typeof s?(s=new Zone(s),a[e]=s,s):o[e]&&t!==getZone&&(r=getZone(o[e],getZone))?((s=a[e]=new Zone)._set(r),s.name=n[e],s):null}function addLink(e){var t,r,a,s;for("string"==typeof e&&(e=[e]),t=0;t<e.length;t++)a=normalizeName((r=e[t].split("|"))[0]),s=normalizeName(r[1]),o[a]=s,n[a]=r[0],o[s]=a,n[s]=r[1]}function zoneExists(e){return zoneExists.didShowError||(zoneExists.didShowError=!0,logError("moment.tz.zoneExists('"+e+"') has been deprecated in favor of !moment.tz.zone('"+e+"')")),!!getZone(e)}function needsOffset(e){var t="X"===e._f||"x"===e._f;return!!(e._a&&void 0===e._tzm&&!t)}function logError(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}function tz(t){var r,a=Array.prototype.slice.call(arguments,0,-1),o=arguments[arguments.length-1],s=e.utc.apply(null,a);return!e.isMoment(t)&&needsOffset(s)&&(r=getZone(o))&&s.add(r.parse(s),"minutes"),s.tz(o),s}(d<2||2===d&&u<6)&&logError("Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js "+e.version+". See momentjs.com"),Zone.prototype={_set:function(e){this.name=e.name,this.abbrs=e.abbrs,this.untils=e.untils,this.offsets=e.offsets,this.population=e.population},_index:function(e){var t;if((t=function(e,t){var r,a=t.length;if(e<t[0])return 0;if(a>1&&t[a-1]===1/0&&e>=t[a-2])return a-1;if(e>=t[a-1])return -1;for(var o=0,s=a-1;s-o>1;)t[r=Math.floor((o+s)/2)]<=e?o=r:s=r;return s}(+e,this.untils))>=0)return t},countries:function(){var e=this.name;return Object.keys(s).filter(function(t){return -1!==s[t].zones.indexOf(e)})},parse:function(e){var t,r,a,o,s=+e,n=this.offsets,i=this.untils,l=i.length-1;for(o=0;o<l;o++)if(t=n[o],r=n[o+1],a=n[o?o-1:o],t<r&&tz.moveAmbiguousForward?t=r:t>a&&tz.moveInvalidForward&&(t=a),s<i[o]-6e4*t)return n[o];return n[l]},abbr:function(e){return this.abbrs[this._index(e)]},offset:function(e){return logError("zone.offset has been deprecated in favor of zone.utcOffset"),this.offsets[this._index(e)]},utcOffset:function(e){return this.offsets[this._index(e)]}},ZoneScore.prototype.scoreOffsetAt=function(e){this.offsetScore+=Math.abs(this.zone.utcOffset(e.at)-e.offset),this.zone.abbr(e.at).replace(/[^A-Z]/g,"")!==e.abbr&&this.abbrScore++},tz.version="0.5.48",tz.dataVersion="",tz._zones=a,tz._links=o,tz._names=n,tz._countries=s,tz.add=addZone,tz.link=addLink,tz.load=function(e){addZone(e.zones),addLink(e.links),function(e){var t,r,a,o;if(e&&e.length)for(t=0;t<e.length;t++)r=(o=e[t].split("|"))[0].toUpperCase(),a=o[1].split(" "),s[r]=new Country(r,a)}(e.countries),tz.dataVersion=e.version},tz.zone=getZone,tz.zoneExists=zoneExists,tz.guess=function(e){return(!r||e)&&(r=function(){try{var e=Intl.DateTimeFormat().resolvedOptions().timeZone;if(e&&e.length>3){var t=n[normalizeName(e)];if(t)return t;logError("Moment Timezone found "+e+" from the Intl api, but did not have that data loaded.")}}catch(e){}var r,a,o,s=function(){var e,t,r,a,o=new Date().getFullYear()-2,s=new OffsetAt(new Date(o,0,1)),n=s.offset,i=[s];for(a=1;a<48;a++)(r=new Date(o,a,1).getTimezoneOffset())!==n&&(i.push(e=function(e,t){for(var r,a;a=((t.at-e.at)/12e4|0)*6e4;)(r=new OffsetAt(new Date(e.at+a))).offset===e.offset?e=r:t=r;return e}(s,t=new OffsetAt(new Date(o,a,1)))),i.push(new OffsetAt(new Date(e.at+6e4))),s=t,n=r);for(a=0;a<4;a++)i.push(new OffsetAt(new Date(o+a,0,1))),i.push(new OffsetAt(new Date(o+a,6,1)));return i}(),l=s.length,d=function(e){var t,r,a,o,s=e.length,l={},d=[],u={};for(t=0;t<s;t++)if(a=e[t].offset,!u.hasOwnProperty(a)){for(r in o=i[a]||{})o.hasOwnProperty(r)&&(l[r]=!0);u[a]=!0}for(t in l)l.hasOwnProperty(t)&&d.push(n[t]);return d}(s),u=[];for(a=0;a<d.length;a++){for(o=0,r=new ZoneScore(getZone(d[a]),l);o<l;o++)r.scoreOffsetAt(s[o]);u.push(r)}return u.sort(sortZoneScores),u.length>0?u[0].zone.name:void 0}()),r},tz.names=function(){var e,t=[];for(e in n)n.hasOwnProperty(e)&&(a[e]||a[o[e]])&&n[e]&&t.push(n[e]);return t.sort()},tz.Zone=Zone,tz.unpack=unpack,tz.unpackBase60=unpackBase60,tz.needsOffset=needsOffset,tz.moveInvalidForward=!0,tz.moveAmbiguousForward=!1,tz.countries=function(){return Object.keys(s)},tz.zonesForCountry=function(e,t){if(!(e=s[e.toUpperCase()]||null))return null;var r=e.zones.sort();return t?r.map(function(e){var t=getZone(e);return{name:e,offset:t.utcOffset(new Date)}}):r};var c=e.fn;function abbrWrap(e){return function(){return this._z?this._z.abbr(this):e.call(this)}}function resetZoneWrap(e){return function(){return this._z=null,e.apply(this,arguments)}}e.tz=tz,e.defaultZone=null,e.updateOffset=function(t,r){var a,o=e.defaultZone;if(void 0===t._z&&(o&&needsOffset(t)&&!t._isUTC&&t.isValid()&&(t._d=e.utc(t._a)._d,t.utc().add(o.parse(t),"minutes")),t._z=o),t._z){if(16>Math.abs(a=t._z.utcOffset(t))&&(a/=60),void 0!==t.utcOffset){var s=t._z;t.utcOffset(-a,r),t._z=s}else t.zone(a,r)}},c.tz=function(t,r){if(t){if("string"!=typeof t)throw Error("Time zone name must be a string, got "+t+" ["+typeof t+"]");return this._z=getZone(t),this._z?e.updateOffset(this,r):logError("Moment Timezone has no data for "+t+". See http://momentjs.com/timezone/docs/#/data-loading/."),this}if(this._z)return this._z.name},c.zoneName=abbrWrap(c.zoneName),c.zoneAbbr=abbrWrap(c.zoneAbbr),c.utc=resetZoneWrap(c.utc),c.local=resetZoneWrap(c.local),c.utcOffset=(t=c.utcOffset,function(){return arguments.length>0&&(this._z=null),t.apply(this,arguments)}),e.tz.setDefault=function(t){return(d<2||2===d&&u<9)&&logError("Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js "+e.version+"."),e.defaultZone=t?getZone(t):null,e};var m=e.momentProperties;return"[object Array]"===Object.prototype.toString.call(m)?(m.push("_z"),m.push("_a")):m&&(m._z=null),e},e.exports?e.exports=s(r(2067)):(a=[r(2067)],void 0===(o=s.apply(t,a))||(e.exports=o))},3761:function(e,t,r){Promise.resolve().then(r.bind(r,9391))},9391:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return Register}});var a=r(7437),o=r(3877),s=r(8637),n=r(2265),i=r(4207),l=r(9701),d=r(1865),u=r(2875),c=r(5968),m=r(977),h=r(5554),p=r(7152),x=r(4568),g=r(6654),v=r(3014),f=r(3336),b=r(3220),y=r(7227),j=r(5255),w=r(5233),N=r(919),Z=r(2067),C=r.n(Z),M=r(4279),D=r.n(M),z=r(3277),S=r(3256),I=r(7412),A=r(3588),O=r(9891),Y=r(7014);let P=[{label:"MEI",value:"MEI"},{label:"EI",value:"EI"},{label:"EIRELI",value:"EIRELI"},{label:"SLU",value:"SLU"},{label:"LTDA",value:"LTDA"},{label:"SA",value:"SA"},{label:"SS",value:"SS"},{label:"CONSORCIO",value:"CONSORCIO"}],useCreateContract=()=>(0,A.D)({mutationFn:async e=>await x.Z.post("/contract/manual",e),onSuccess:()=>{let e=D().tz.guess(),t=D()().tz(e),r=t.hour();r>=9&&r<18?v.Am.success("Conta pr\xe9 cadastrada com sucesso!"):v.Am.success("Contrato criado com sucesso. Informamos que, por ter sido realizado fora do hor\xe1rio comercial, o registro cont\xe1bil ser\xe1 processado somente no pr\xf3ximo dia \xfatil.",{delay:6e3})},onError:e=>{(0,g.Z)(e,"Tivemos um erro ao cadastrar a conta")}}),useGetAcessors=()=>{let e=(0,S.e)();return(0,O.a)({queryKey:["acessors"],queryFn:async()=>await x.Z.get("/wallets/broker/advisors").then(e=>e.data),enabled:"broker"===e.name})};function BusinessRegister(e){var t,r,o,s,x,g,Z,M,D,A,T,V,E,k,_,B,F,R,L,U,q,W,Q,K,G,J,X,H,$,ee,et,er,ea,eo,es,en,ei,el,ed,eu;let{modalityContract:ec}=e,[em,eh]=(0,n.useState)(!1),[ep,ex]=(0,n.useState)(""),[eg,ev]=(0,n.useState)(""),[ef,eb]=(0,n.useState)(""),[ey,ej]=(0,n.useState)(""),[ew,eN]=(0,n.useState)([{generatedId:crypto.randomUUID(),id:"",taxValue:""}]),eZ=(0,S.e)(),eC=String(localStorage.getItem("typeCreateContract")),eM=useCreateContract(),{data:eD=[],isLoading:ez}=useGetAcessors(),{register:eS,handleSubmit:eI,watch:eA,setValue:eO,reset:eY,formState:{errors:eP,isValid:eT}}=(0,d.cI)({defaultValues:{initDate:C()().format("YYYY-MM-DD")},resolver:(0,l.X)(m._R),mode:"all"}),eV=eA("term");(0,n.useEffect)(()=>{ej(eV)},[eV]),(0,O.a)({queryKey:["address",eA("zipCode")],queryFn:async()=>{let e=await (0,I.x)((0,c.p4)(eA("zipCode")));e&&(eO("neighborhood",e.neighborhood,{shouldValidate:!0}),eO("city",e.city,{shouldValidate:!0}),eO("state",e.state,{shouldValidate:!0}),eO("street",e.street,{shouldValidate:!0}))},enabled:(null===(t=eA("zipCode"))||void 0===t?void 0:t.length)===9}),(0,O.a)({queryKey:["companyAddress",eA("companyZipCode")],queryFn:async()=>{let e=await (0,I.x)((0,c.p4)(eA("companyZipCode")));e&&(eO("companyNeighborhood",e.neighborhood,{shouldValidate:!0}),eO("companyStreet",e.street,{shouldValidate:!0}),eO("companyCity",e.city,{shouldValidate:!0}),eO("companyState",e.state,{shouldValidate:!0}))},enabled:(null===(r=eA("companyZipCode"))||void 0===r?void 0:r.length)===9}),(0,n.useEffect)(()=>{eO("isSCP","SCP"===ec)},[ec]);let eE=(0,n.useMemo)(()=>{if(eA("initDate")&&ey){let e=(0,N.H)({investDate:ey,startDate:eA("initDate")});return C()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[eA("initDate"),ey]);(0,n.useEffect)(()=>{eE&&eO("endDate",eE,{shouldValidate:!0})},[eE,eO]);let handleInputChange=(e,t)=>{eN(r=>r.map(r=>r.generatedId===e?{...r,taxValue:t}:r))};return(0,a.jsx)("div",{children:(0,a.jsxs)("form",{action:"",onSubmit:eI(e=>{if(!em)return v.Am.warn("Aceite os termos para liberar a cria\xe7\xe3o do contrato!");let t=Number(e.yield.replace(",",".")),r={amount:(0,z.Z)(e.value),monthlyRate:t,durationInMonths:Number(e.term),paymentMethod:e.purchaseWith,endDate:C()(eE,"DD/MM/YYYY").format("YYYY-MM-DD"),profile:e.profile,quotaQuantity:"SCP"===ec?Number(e.amountQuotes):void 0,isDebenture:"s"===e.isDebenture},a={bank:e.bank,agency:e.agency,account:e.accountNumber,pix:e.pix},o={street:e.street,city:e.city,state:e.state,neighborhood:e.neighborhood,postalCode:(0,c.p4)(e.zipCode),number:e.number,complement:e.complement},s={fullName:(0,Y.Z)(e.ownerName),cpf:(0,c.p4)(e.ownerDocument),rg:e.rg,issuingAgency:e.issuer,nationality:e.placeOfBirth,occupation:(0,Y.Z)(e.occupation),birthDate:e.dtBirth,email:e.email,phone:"55".concat((0,c.p4)(e.phoneNumber)),motherName:(0,Y.Z)(e.motherName),address:o},n={street:e.companyStreet,city:e.companyCity,state:e.companyState,neighborhood:e.companyNeighborhood,postalCode:(0,c.p4)(e.companyZipCode),number:e.companyNumber,complement:e.companyComplement},i={corporateName:e.name,cnpj:(0,c.p4)(e.document),type:e.companyType,address:n,representative:s},l={personType:"PJ",contractType:ec,advisors:"advisors"===eC?ew.map(e=>({advisorId:e.id,rate:Number(String(e.taxValue).replace(",","."))})):[],investment:r,bankAccount:a,company:i,role:eZ.name};eM.mutate(l,{onSuccess:()=>{eN([]),eY(),eO("initDate",C()().format("YYYY-MM-DD"))}})}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,a.jsx)(w.Z,{title:"Dados Pessoais - Representante",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(i.Z,{register:eS,name:"ownerName",width:"300px",error:!!eP.ownerName,errorMessage:null==eP?void 0:null===(o=eP.ownerName)||void 0===o?void 0:o.message,label:"Nome"}),(0,a.jsx)(i.Z,{register:eS,name:"ownerDocument",width:"200px",error:!!eP.ownerDocument,errorMessage:null==eP?void 0:null===(s=eP.ownerDocument)||void 0===s?void 0:s.message,label:"CPF",setValue:e=>eO("ownerDocument",(0,c.VL)(e||""),{shouldValidate:!0})}),(0,a.jsx)(i.Z,{register:eS,name:"rg",width:"200px",error:!!eP.rg,errorMessage:null==eP?void 0:null===(x=eP.rg)||void 0===x?void 0:x.message,label:"RG"}),(0,a.jsx)(i.Z,{register:eS,name:"issuer",width:"200px",error:!!eP.issuer,errorMessage:null==eP?void 0:null===(g=eP.issuer)||void 0===g?void 0:g.message,label:"Org\xe3o emissor"}),(0,a.jsx)(i.Z,{register:eS,name:"placeOfBirth",width:"200px",error:!!eP.placeOfBirth,errorMessage:null==eP?void 0:null===(Z=eP.placeOfBirth)||void 0===Z?void 0:Z.message,label:"Nacionalidade"}),(0,a.jsx)(i.Z,{register:eS,name:"occupation",width:"200px",error:!!eP.occupation,errorMessage:null==eP?void 0:null===(M=eP.occupation)||void 0===M?void 0:M.message,label:"Ocupa\xe7\xe3o"}),(0,a.jsx)(i.Z,{register:eS,name:"motherName",width:"250px",error:!!eP.motherName,errorMessage:null==eP?void 0:null===(D=eP.motherName)||void 0===D?void 0:D.message,label:"Nome da m\xe3e"}),(0,a.jsxs)("div",{style:{width:"200px"},children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:eP.dtBirth&&"- ".concat(eP.dtBirth.message)})]}),(0,a.jsx)("input",{...eS("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let t=e.target;t.value.length>10&&(t.value=t.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(eP.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)(i.Z,{width:"200px",register:eS,name:"phoneNumber",error:!!eP.phoneNumber,errorMessage:null==eP?void 0:null===(A=eP.phoneNumber)||void 0===A?void 0:A.message,label:"Celular",maxLength:15,setValue:e=>eO("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})}),(0,a.jsx)(i.Z,{register:eS,name:"email",width:"300px",error:!!eP.email,errorMessage:null==eP?void 0:null===(T=eP.email)||void 0===T?void 0:T.message,label:"E-mail"}),(0,a.jsx)(i.Z,{register:eS,name:"zipCode",width:"200px",error:!!eP.zipCode,errorMessage:null==eP?void 0:null===(V=eP.zipCode)||void 0===V?void 0:V.message,label:"CEP",setValue:e=>{ex(e),eO("zipCode",(0,c.Tc)(e),{shouldValidate:!0})}}),(0,a.jsx)(i.Z,{register:eS,name:"neighborhood",width:"300px",error:!!eP.neighborhood,errorMessage:null==eP?void 0:null===(E=eP.neighborhood)||void 0===E?void 0:E.message,label:"Bairro"}),(0,a.jsx)(i.Z,{register:eS,name:"street",width:"300px",error:!!eP.street,errorMessage:null==eP?void 0:null===(k=eP.street)||void 0===k?void 0:k.message,label:"Rua"}),(0,a.jsx)(i.Z,{register:eS,name:"city",width:"200px",error:!!eP.city,errorMessage:null==eP?void 0:null===(_=eP.city)||void 0===_?void 0:_.message,label:"Cidade"}),(0,a.jsx)(i.Z,{register:eS,maxLength:2,setValue:e=>eO("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!eP.state,errorMessage:null==eP?void 0:null===(B=eP.state)||void 0===B?void 0:B.message,label:"Estado"}),(0,a.jsx)(i.Z,{register:eS,name:"number",width:"200px",error:!!eP.number,errorMessage:null==eP?void 0:null===(F=eP.number)||void 0===F?void 0:F.message,label:"N\xfamero"}),(0,a.jsx)(i.Z,{register:eS,name:"complement",width:"200px",error:!!eP.complement,errorMessage:null==eP?void 0:null===(R=eP.complement)||void 0===R?void 0:R.message,label:"Complemento"})]})}),(0,a.jsx)(w.Z,{title:"Dados da empresa",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(i.Z,{register:eS,name:"name",width:"400px",error:!!eP.name,errorMessage:null==eP?void 0:null===(L=eP.name)||void 0===L?void 0:L.message,label:"Raz\xe3o Social"}),(0,a.jsx)(i.Z,{register:eS,name:"document",width:"200px",error:!!eP.document,errorMessage:null==eP?void 0:null===(U=eP.document)||void 0===U?void 0:U.message,label:"CNPJ",setValue:e=>eO("document",(0,c.PK)(e||""),{shouldValidate:!0})}),(0,a.jsx)(h.Z,{width:"200px",name:"companyType",register:eS,options:P,error:!!eP.companyType,errorMessage:null==eP?void 0:null===(q=eP.companyType)||void 0===q?void 0:q.message,label:"Tipo"}),(0,a.jsx)(i.Z,{register:eS,name:"companyZipCode",width:"200px",error:!!eP.companyZipCode,errorMessage:null==eP?void 0:null===(W=eP.companyZipCode)||void 0===W?void 0:W.message,label:"CEP",setValue:e=>{ev(e),eO("companyZipCode",(0,c.Tc)(e),{shouldValidate:!0})}}),(0,a.jsx)(i.Z,{register:eS,name:"companyNeighborhood",width:"300px",error:!!eP.companyNeighborhood,errorMessage:null==eP?void 0:null===(Q=eP.companyNeighborhood)||void 0===Q?void 0:Q.message,label:"Bairro"}),(0,a.jsx)(i.Z,{register:eS,name:"companyStreet",width:"300px",error:!!eP.companyStreet,errorMessage:null==eP?void 0:null===(K=eP.companyStreet)||void 0===K?void 0:K.message,label:"Rua"}),(0,a.jsx)(i.Z,{register:eS,name:"companyCity",width:"200px",error:!!eP.companyCity,errorMessage:null==eP?void 0:null===(G=eP.companyCity)||void 0===G?void 0:G.message,label:"Cidade"}),(0,a.jsx)(i.Z,{register:eS,maxLength:2,setValue:e=>eO("companyState",String(e).toUpperCase(),{shouldValidate:!0}),name:"companyState",width:"150px",error:!!eP.companyState,errorMessage:null==eP?void 0:null===(J=eP.companyState)||void 0===J?void 0:J.message,label:"Estado"}),(0,a.jsx)(i.Z,{register:eS,name:"companyNumber",width:"200px",error:!!eP.companyNumber,errorMessage:null==eP?void 0:null===(X=eP.companyNumber)||void 0===X?void 0:X.message,label:"N\xfamero"}),(0,a.jsx)(i.Z,{register:eS,name:"companyComplement",width:"200px",error:!!eP.companyComplement,errorMessage:null==eP?void 0:null===(H=eP.companyComplement)||void 0===H?void 0:H.message,label:"Complemento"})]})}),(0,a.jsx)(w.Z,{title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(i.Z,{register:eS,name:"value",width:"200px",error:!!eP.value,errorMessage:null==eP?void 0:null===($=eP.value)||void 0===$?void 0:$.message,label:"Valor",setValue:e=>eO("value",(0,c.Ht)(e||""),{shouldValidate:!0})}),(0,a.jsx)(i.Z,{register:eS,type:"text",name:"term",width:"250px",error:!!eP.term,errorMessage:null==eP?void 0:null===(ee=eP.term)||void 0===ee?void 0:ee.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),(0,a.jsx)(i.Z,{register:eS,type:"text",name:"yield",width:"250px",error:!!eP.yield,errorMessage:null==eP?void 0:null===(et=eP.yield)||void 0===et?void 0:et.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2,5"}),(0,a.jsx)(h.Z,{width:"200px",name:"purchaseWith",register:eS,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!(null==eP?void 0:eP.purchaseWith),errorMessage:null==eP?void 0:null===(er=eP.purchaseWith)||void 0===er?void 0:er.message,label:"Comprar com"}),"SCP"===ec&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(i.Z,{register:eS,type:"number",name:"amountQuotes",width:"150px",error:!!eP.amountQuotes,errorMessage:null==eP?void 0:null===(ea=eP.amountQuotes)||void 0===ea?void 0:ea.message,label:"Quantidade de cotas"})}),(0,a.jsx)(i.Z,{type:"date",register:eS,disabled:!0,minDate:C()().format("YYYY-MM-DD"),name:"initDate",width:"200px",error:!!eP.initDate,errorMessage:null==eP?void 0:null===(eo=eP.initDate)||void 0===eo?void 0:eo.message,label:"Inicio do contrato"}),(0,a.jsx)(i.Z,{type:"text",register:eS,name:"endDate",value:eE,width:"200px",disabled:!0,label:"Final do contrato"}),(0,a.jsx)(h.Z,{width:"200px",name:"profile",register:eS,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!eP.profile,errorMessage:null==eP?void 0:null===(es=eP.profile)||void 0===es?void 0:es.message,label:"Perfil"}),(0,a.jsx)(h.Z,{width:"100px",name:"isDebenture",register:eS,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!eP.isDebenture,errorMessage:null==eP?void 0:null===(en=eP.isDebenture)||void 0===en?void 0:en.message,label:"Deb\xeanture"})]})}),"advisors"===eC&&(0,a.jsx)(w.Z,{title:"Adicionar Assessor",children:(0,a.jsxs)("div",{children:[ew.length>0?null==ew?void 0:ew.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between items-end gap-4 mb-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(f.Z,{label:"Selecione um Assessor",items:eD,value:"",setValue:()=>{},loading:ez,handleChange:r=>{let a=ew.filter(t=>t.generatedId===e.generatedId)[0],o={generatedId:a.generatedId,id:r.id,taxValue:Number(r.rate)};ew[t]=o,eN([...ew])}})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(b.Z,{label:"Adicione a Taxa - em %",id:"advisor-".concat(t),name:"",value:String(e.taxValue),type:"text",onChange:t=>handleInputChange(e.generatedId,t.target.value)})}),(0,a.jsx)("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=ew.filter((e,r)=>r!==t);eN(e)},children:(0,a.jsx)(y.Z,{width:20})})]},t)):(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),(0,a.jsx)("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:()=>{eN([...ew,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},children:(0,a.jsx)(j.Z,{width:25,color:"#fff"})})]})}),(0,a.jsx)(w.Z,{title:"Dados bancarios",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(i.Z,{register:eS,name:"bank",width:"300px",error:!!eP.bank,errorMessage:null==eP?void 0:null===(ei=eP.bank)||void 0===ei?void 0:ei.message,label:"Banco"}),(0,a.jsx)(i.Z,{register:eS,name:"agency",width:"200px",error:!!eP.agency,errorMessage:null==eP?void 0:null===(el=eP.agency)||void 0===el?void 0:el.message,label:"Ag\xeancia"}),(0,a.jsx)(i.Z,{register:eS,name:"accountNumber",width:"200px",error:!!eP.accountNumber,errorMessage:null==eP?void 0:null===(ed=eP.accountNumber)||void 0===ed?void 0:ed.message,label:"Conta"}),(0,a.jsx)(i.Z,{register:eS,name:"pix",width:"250px",error:!!eP.pix,errorMessage:null==eP?void 0:null===(eu=eP.pix)||void 0===eu?void 0:eu.message,label:"Pix"})]})}),(0,a.jsx)(w.Z,{title:"Observa\xe7\xf5es",children:(0,a.jsx)(p.Z,{name:"observations",register:eS})}),(0,a.jsx)(w.Z,{title:"Dados para Dep\xf3sito",children:(0,a.jsxs)("div",{className:"m-auto border-none",children:[(0,a.jsx)("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),(0,a.jsx)("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,a.jsxs)("div",{className:"flex mt-5",children:[(0,a.jsx)("input",{type:"checkbox",checked:em,className:"cursor-pointer",onChange:()=>eh(!em)}),(0,a.jsx)("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(u.Z,{label:"Enviar",size:"lg",loading:eM.isPending,disabled:!em||eM.isPending||!eT||!eE})})]})})}let PhysicalRegister_useCreateContract=()=>(0,A.D)({mutationFn:async e=>await x.Z.post("/contract/manual",e),onSuccess:()=>{let e=D().tz.guess(),t=D()().tz(e),r=t.hour();r>=9&&r<18?v.Am.success("Conta pr\xe9 cadastrada com sucesso!"):v.Am.success("Contrato criado com sucesso. Informamos que, por ter sido realizado fora do hor\xe1rio comercial, o registro cont\xe1bil sera processado somente no pr\xf3ximo dia \xfatil",{delay:6e3})},onError:e=>{(0,g.Z)(e,"Tivemos um erro ao cadastrar a conta")}}),PhysicalRegister_useGetAcessors=()=>{let e=(0,S.e)();return(0,O.a)({queryKey:["acessors"],queryFn:()=>x.Z.get("/wallets/broker/advisors").then(e=>e.data),enabled:"broker"===e.name})};function PhysicalRegister(e){var t,r,o,s,x,g,Z,M,D,A,P,T,V,E,k,_,B,F,R,L,U,q,W,Q,K,G,J,X,H;let{modalityContract:$}=e,[ee,et]=(0,n.useState)(!1),[er,ea]=(0,n.useState)(""),[eo,es]=(0,n.useState)(""),[en,ei]=(0,n.useState)([{generatedId:crypto.randomUUID(),id:"",taxValue:""}]),el=(0,S.e)(),ed=String(localStorage.getItem("typeCreateContract")),eu=PhysicalRegister_useCreateContract(),{data:ec=[],isLoading:em}=PhysicalRegister_useGetAcessors(),{register:eh,handleSubmit:ep,watch:ex,setValue:eg,reset:ev,formState:{errors:ef,isValid:eb}}=(0,d.cI)({defaultValues:{initDate:C()().format("YYYY-MM-DD")},resolver:(0,l.X)(m.$r),mode:"all"}),{isLoading:ey}=(0,O.a)({queryKey:["address",ex("zipCode")],queryFn:async()=>{let e=await (0,I.x)((0,c.p4)(ex("zipCode")));e&&(eg("neighborhood",e.neighborhood,{shouldValidate:!0}),eg("city",e.city,{shouldValidate:!0}),eg("state",e.state,{shouldValidate:!0}),eg("street",e.street,{shouldValidate:!0}))},enabled:(null===(t=ex("zipCode"))||void 0===t?void 0:t.length)===9}),ej=ex("term"),ew=(0,n.useMemo)(()=>"advisors"!==ed?[]:en.map(e=>({advisorId:e.id,rate:Number(String(e.taxValue).replace(",","."))})),[en,ed]);(0,n.useEffect)(()=>{eg("isSCP","SCP"===$)},[$,eg]);let handleAdvisorChange=(e,t)=>{ei(r=>r.map(r=>r.generatedId===e?{...r,taxValue:t}:r))},eN=(0,n.useMemo)(()=>{if(ex("initDate")&&ej){let e=(0,N.H)({investDate:ej,startDate:ex("initDate")});return C()(e,"DD-MM-YYYY").format("DD/MM/YYYY")}return""},[ex("initDate"),ej]);return(0,n.useEffect)(()=>{eN&&eg("endDate",eN,{shouldValidate:!0})},[eN,eg]),(0,a.jsx)("div",{children:(0,a.jsxs)("form",{action:"",onSubmit:ep(e=>{if(!ee)return v.Am.error("Aceite os termos para liberar a cria\xe7\xe3o do contrato!");let t={personType:"PF",contractType:$,advisors:ew,investment:{amount:(0,z.Z)(e.value),monthlyRate:Number(e.yield.replace(",",".")),durationInMonths:Number(e.term),paymentMethod:e.purchaseWith,endDate:C()(eN,"DD/MM/YYYY").format("YYYY-MM-DD"),profile:e.profile,quotaQuantity:"SCP"===$?Number(e.amountQuotes):void 0,isDebenture:"s"===e.isDebenture},bankAccount:{bank:e.bank,agency:e.agency,account:e.accountNumber,pix:e.pix,type:"CORRENTE"},individual:{fullName:(0,Y.Z)(e.name),cpf:(0,c.p4)(e.document),rg:e.rg,issuingAgency:e.issuer,nationality:e.placeOfBirth,occupation:(0,Y.Z)(e.occupation),birthDate:e.dtBirth,email:e.email,phone:"55".concat((0,c.p4)(e.phoneNumber)),motherName:(0,Y.Z)(e.motherName),address:{street:e.street,neighborhood:e.neighborhood,city:e.city,state:e.state,postalCode:(0,c.p4)(e.zipCode),number:e.number}},role:el.name};eu.mutate(t,{onSuccess:()=>{ei([]),ev(),eg("initDate",C()().format("YYYY-MM-DD"))}})}),onKeyDown:e=>{"Enter"===e.key&&e.preventDefault()},children:[(0,a.jsx)(w.Z,{title:"Dados Pessoais",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(i.Z,{register:eh,name:"name",width:"300px",error:!!ef.name,errorMessage:null==ef?void 0:null===(r=ef.name)||void 0===r?void 0:r.message,label:"Nome completo"}),(0,a.jsx)(i.Z,{register:eh,name:"document",width:"200px",error:!!ef.document,errorMessage:null==ef?void 0:null===(o=ef.document)||void 0===o?void 0:o.message,label:"CPF",setValue:e=>eg("document",(0,c.VL)(e||""),{shouldValidate:!0})}),(0,a.jsx)(i.Z,{register:eh,name:"rg",width:"200px",error:!!ef.rg,errorMessage:null==ef?void 0:null===(s=ef.rg)||void 0===s?void 0:s.message,label:"Identidade"}),(0,a.jsx)(i.Z,{register:eh,name:"issuer",width:"200px",error:!!ef.issuer,errorMessage:null==ef?void 0:null===(x=ef.issuer)||void 0===x?void 0:x.message,label:"Org\xe3o emissor"}),(0,a.jsx)(i.Z,{register:eh,name:"placeOfBirth",width:"200px",error:!!ef.placeOfBirth,errorMessage:null==ef?void 0:null===(g=ef.placeOfBirth)||void 0===g?void 0:g.message,label:"Nacionalidade"}),(0,a.jsx)(i.Z,{register:eh,name:"occupation",width:"200px",error:!!ef.occupation,errorMessage:null==ef?void 0:null===(Z=ef.occupation)||void 0===Z?void 0:Z.message,label:"Ocupa\xe7\xe3o"}),(0,a.jsx)(i.Z,{width:"200px",register:eh,name:"phoneNumber",maxLength:15,error:!!ef.phoneNumber,errorMessage:null==ef?void 0:null===(M=ef.phoneNumber)||void 0===M?void 0:M.message,label:"Celular",setValue:e=>eg("phoneNumber",(0,c.gP)(e||""),{shouldValidate:!0})}),(0,a.jsxs)("div",{style:{width:"200px"},children:[(0,a.jsxs)("p",{className:"text-white mb-1",children:["Data de Nascimento"," ",(0,a.jsx)("b",{className:"text-red-500 font-light text-sm",children:ef.dtBirth&&"- ".concat(ef.dtBirth.message)})]}),(0,a.jsx)("input",{...eh("dtBirth"),type:"date",max:new Date().toISOString().split("T")[0],min:"1900-01-01",onInput:e=>{let t=e.target;t.value.length>10&&(t.value=t.value.slice(0,10))},className:"h-12 w-full px-4 text-white rounded-xl ".concat(ef.dtBirth?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})]}),(0,a.jsx)(i.Z,{register:eh,name:"email",width:"300px",error:!!ef.email,errorMessage:null==ef?void 0:null===(D=ef.email)||void 0===D?void 0:D.message,label:"E-mail"}),(0,a.jsx)(i.Z,{register:eh,name:"motherName",width:"300px",error:!!ef.motherName,errorMessage:null==ef?void 0:null===(A=ef.motherName)||void 0===A?void 0:A.message,label:"Nome da m\xe3e"}),(0,a.jsx)(i.Z,{register:eh,name:"zipCode",width:"200px",error:!!ef.zipCode,errorMessage:null==ef?void 0:null===(P=ef.zipCode)||void 0===P?void 0:P.message,label:"CEP",setValue:e=>{let t=(0,c.Tc)(e);ea(t),eg("zipCode",t,{shouldValidate:!0})}}),(0,a.jsx)(i.Z,{register:eh,name:"neighborhood",width:"200px",error:!!ef.neighborhood,errorMessage:null==ef?void 0:null===(T=ef.neighborhood)||void 0===T?void 0:T.message,label:"Bairro"}),(0,a.jsx)(i.Z,{register:eh,name:"street",width:"300px",error:!!ef.street,errorMessage:null==ef?void 0:null===(V=ef.street)||void 0===V?void 0:V.message,label:"Rua"}),(0,a.jsx)(i.Z,{register:eh,name:"city",width:"200px",error:!!ef.city,errorMessage:null==ef?void 0:null===(E=ef.city)||void 0===E?void 0:E.message,label:"Cidade"}),(0,a.jsx)(i.Z,{register:eh,maxLength:2,setValue:e=>eg("state",String(e).toUpperCase(),{shouldValidate:!0}),name:"state",width:"150px",error:!!ef.state,errorMessage:null==ef?void 0:null===(k=ef.state)||void 0===k?void 0:k.message,label:"Estado"}),(0,a.jsx)(i.Z,{register:eh,name:"number",width:"200px",error:!!ef.number,errorMessage:null==ef?void 0:null===(_=ef.number)||void 0===_?void 0:_.message,label:"N\xfamero"}),(0,a.jsx)(i.Z,{register:eh,name:"complement",width:"200px",error:!!ef.complement,errorMessage:null==ef?void 0:null===(B=ef.complement)||void 0===B?void 0:B.message,label:"Complemento"})]})}),(0,a.jsx)(w.Z,{title:"Dados de Investimento",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(i.Z,{register:eh,name:"value",width:"200px",error:!!ef.value,errorMessage:null==ef?void 0:null===(F=ef.value)||void 0===F?void 0:F.message,label:"Valor",setValue:e=>eg("value",(0,c.Ht)(e||""),{shouldValidate:!0})}),(0,a.jsx)(i.Z,{register:eh,type:"text",name:"term",width:"250px",error:!!ef.term,errorMessage:null==ef?void 0:null===(R=ef.term)||void 0===R?void 0:R.message,label:"Prazo investimento - em meses",placeholder:"ex: 12"}),(0,a.jsx)(i.Z,{register:eh,type:"text",name:"yield",width:"250px",error:!!ef.yield,errorMessage:null==ef?void 0:null===(L=ef.yield)||void 0===L?void 0:L.message,label:"Taxa Remunera\xe7\xe3o Mensal - em %",placeholder:"ex: 2"}),(0,a.jsx)(h.Z,{width:"200px",name:"purchaseWith",register:eh,options:[{label:"PIX",value:"pix"},{label:"Boleto",value:"boleto"},{label:"Transfer\xeancia",value:"bank_transfer"}],error:!!(null==ef?void 0:ef.purchaseWith),errorMessage:null==ef?void 0:null===(U=ef.purchaseWith)||void 0===U?void 0:U.message,label:"Comprar com"}),"SCP"===$&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(i.Z,{register:eh,type:"number",name:"amountQuotes",width:"150px",error:!!ef.amountQuotes,errorMessage:null==ef?void 0:null===(q=ef.amountQuotes)||void 0===q?void 0:q.message,label:"Quantidade de cotas"})}),(0,a.jsx)(i.Z,{type:"date",register:eh,disabled:!0,minDate:C()().format("YYYY-MM-DD"),name:"initDate",width:"200px",error:!!ef.initDate,errorMessage:null==ef?void 0:null===(W=ef.initDate)||void 0===W?void 0:W.message,label:"Inicio do contrato"}),(0,a.jsx)(i.Z,{type:"date",register:eh,value:eN?C()(eN,"DD/MM/YYYY").format("YYYY-MM-DD"):"",name:"endDate",width:"200px",disabled:!0,label:"Final do contrato"}),(0,a.jsx)(h.Z,{width:"200px",name:"profile",register:eh,options:[{label:"Conservador",value:"conservative"},{label:"Moderado",value:"moderate"},{label:"Agressivo",value:"aggressive"}],error:!!ef.profile,errorMessage:null==ef?void 0:null===(Q=ef.profile)||void 0===Q?void 0:Q.message,label:"Perfil"}),(0,a.jsx)(h.Z,{width:"100px",name:"isDebenture",register:eh,options:[{label:"Sim",value:"s"},{label:"N\xe3o",value:"n"}],error:!!ef.isDebenture,errorMessage:null==ef?void 0:null===(K=ef.isDebenture)||void 0===K?void 0:K.message,label:"Deb\xeanture"})]})}),"advisors"===ed&&(0,a.jsx)(w.Z,{title:"Adicionar Assessor",children:(0,a.jsxs)("div",{children:[en.length>0?null==en?void 0:en.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between items-end gap-4 mb-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(f.Z,{label:"Selecione um Assessor",items:ec,value:"",setValue:()=>{},loading:em,handleChange:r=>{let a=en.filter(t=>t.generatedId===e.generatedId)[0],o={generatedId:a.generatedId,id:r.id,taxValue:Number(r.rate)};en[t]=o,ei([...en])}})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(b.Z,{label:"Adicione a Taxa - em %",id:"",name:"",value:String(e.taxValue),type:"text",onChange:t=>handleAdvisorChange(e.generatedId,t.target.value)})}),(0,a.jsx)("div",{className:"bg-red-500 translate-y-[-40%] cursor-pointer text-white p-1 rounded-full",onClick:()=>{let e=en.filter((e,r)=>r!==t);ei(e)},children:(0,a.jsx)(y.Z,{width:20})})]},t)):(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-white",children:"Nenhum assessor adicionado!"})}),(0,a.jsx)("div",{className:"bg-orange-linear w-[40px] h-[40px] rounded-full cursor-pointer flex items-center justify-center mt-5",onClick:()=>{ei([...en,{generatedId:crypto.randomUUID(),id:"",taxValue:""}])},children:(0,a.jsx)(j.Z,{width:25,color:"#fff"})})]})}),(0,a.jsx)(w.Z,{title:"Dados bancarios",children:(0,a.jsxs)("div",{className:"flex items-end gap-4 flex-wrap",children:[(0,a.jsx)(i.Z,{register:eh,name:"bank",width:"300px",error:!!ef.bank,errorMessage:null==ef?void 0:null===(G=ef.bank)||void 0===G?void 0:G.message,label:"Banco"}),(0,a.jsx)(i.Z,{register:eh,name:"agency",width:"200px",error:!!ef.agency,errorMessage:null==ef?void 0:null===(J=ef.agency)||void 0===J?void 0:J.message,label:"Ag\xeancia"}),(0,a.jsx)(i.Z,{register:eh,name:"accountNumber",width:"200px",error:!!ef.accountNumber,errorMessage:null==ef?void 0:null===(X=ef.accountNumber)||void 0===X?void 0:X.message,label:"Conta"}),(0,a.jsx)(i.Z,{register:eh,name:"pix",width:"250px",error:!!ef.pix,errorMessage:null==ef?void 0:null===(H=ef.pix)||void 0===H?void 0:H.message,label:"Pix"})]})}),(0,a.jsx)(w.Z,{title:"Observa\xe7\xf5es",children:(0,a.jsx)(p.Z,{name:"observations",register:eh})}),(0,a.jsx)(w.Z,{title:"Dados para Dep\xf3sito",children:(0,a.jsxs)("div",{className:"m-auto border-none",children:[(0,a.jsx)("p",{className:"text-center font-bold text-white",children:"DADOS PARA DEP\xd3SITO"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"ICABANK SOLU\xc7\xd5ES FINANCEIRAS LTDA"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"CNPJ: 37.468.454/0001-00"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"INSTITUI\xc7\xc3O 332 - Acesso Solu\xe7\xf5es de Pagamento S.A"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"AG\xcaNCIA: 0001 CONTA: 2269051-4"}),(0,a.jsx)("p",{className:"text-center text-white font-extralight",children:"PIX: 37.468.454/0001-00"})]})}),(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)("p",{className:"text-white font-extralight text-sm",children:"Declaro que:"}),(0,a.jsx)("p",{className:"text-white font-extralight text-sm",children:"As informa\xe7\xf5es contidas nesta ficha cadastral, de car\xe1ter confidencial, s\xe3o exatas e de minha inteira responsabilidade, sujeitando-me, se inver\xeddicas, \xe0s penas estabelecidas no C\xf3digo Penal vigente:"}),(0,a.jsxs)("div",{className:"flex mt-5",children:[(0,a.jsx)("input",{type:"checkbox",checked:ee,className:"cursor-pointer",onChange:()=>et(!ee)}),(0,a.jsx)("p",{className:"text-white font-extralight text-sm ml-2",children:"Li e aceito os TERMOS e CONDI\xc7\xd5ES"})]})]}),(0,a.jsx)("div",{className:"md:w-52 mb-10",children:(0,a.jsx)(u.Z,{label:"Enviar",size:"lg",loading:eu.isPending,disabled:!ee||eu.isPending||!eb||!eN})})]})})}var T=r(4765);function Register(){let[e,t]=(0,n.useState)("PF"),[r,i]=(0,n.useState)("MUTUO");return(0,a.jsxs)("div",{children:[(0,a.jsx)(o.Z,{}),(0,a.jsx)(s.Z,{children:(0,a.jsxs)("div",{className:"md:px-10",children:[(0,a.jsx)(w.Z,{title:"Tipo de contrato",children:(0,a.jsxs)("div",{className:"flex gap-4 items-end",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Tipo de contrato"}),(0,a.jsx)(T.Z,{value:e,onChange:e=>t(e.target.value),children:[{label:"PF",value:"PF"},{label:"PJ",value:"PJ"}].map((e,t)=>(0,a.jsx)("option",{value:e.value,children:e.label},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white mb-1",children:"Modalidade do Investimento"}),(0,a.jsx)(T.Z,{value:r,onChange:e=>i(e.target.value),children:[{label:"M\xfatuo",value:"MUTUO"},{label:"SCP",value:"SCP"}].map((e,t)=>(0,a.jsx)("option",{value:e.value,children:e.label},t))})]})]})}),(0,a.jsxs)("div",{children:["PF"===e&&(0,a.jsx)(PhysicalRegister,{modalityContract:r}),"PJ"===e&&(0,a.jsx)(BusinessRegister,{modalityContract:r})]})]})})]})}},7152:function(e,t,r){"use strict";r.d(t,{Z:function(){return InputTextArea}});var a=r(7437),o=r(2265);function InputTextArea(e){let{setValue:t,value:r,error:s,errorMessage:n,width:i="100%",register:l,name:d,placeholder:u="",className:c=""}=e;return(0,o.useEffect)(()=>{},[i]),(0,a.jsx)("div",{className:"input",style:{width:i},children:(0,a.jsx)("textarea",{...l&&{...l(d)},value:r&&r,placeholder:u,id:d,onChange:e=>{let{target:r}=e;t&&t(r.value)},className:"".concat(c," h-12 w-full min-h-40 p-2 text-white rounded-xl ").concat(s?"ring-[#f33636]":"ring-[#FF9900]"," ring-1 ring-inset bg-black flex-1")})})}}},function(e){e.O(0,[6990,7326,8276,5371,6946,1865,3964,9891,3151,396,2971,7864,1744],function(){return e(e.s=3761)}),_N_E=e.O()}]);