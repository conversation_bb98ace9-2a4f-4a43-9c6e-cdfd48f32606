{"version": 3, "file": "1704067200000-AddContractUpgradeFields.js", "sourceRoot": "/", "sources": ["shared/database/migrations/1704067200000-AddContractUpgradeFields.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,qCAAqC;IACzC,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE;YACvC,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,gCAAgC;aAC1C,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,EAAE;gBACb,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,uBAAuB;aACjC,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,KAAK;gBACX,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,wCAAwC;aAClD,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,iCAAiC;aAC3C,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,2BAA2B;gBACjC,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,+BAA+B;aACzC,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,kCAAkC;aAC5C,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,2BAA2B;aACrC,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,iCAAiC;aAC3C,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,4BAA4B;aACtC,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,0BAA0B;aACpC,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,gCAAgC;aAC1C,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gDAAgD;aAC1D,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6DAA6D;aACvE,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,EAAE;gBACb,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,wDAAwD;aAClE,CAAC;SACH,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;KAuBvB,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,WAAW,CAAC,WAAW,CAAC,UAAU,EAAE;YACxC,eAAe;YACf,kBAAkB;YAClB,gBAAgB;YAChB,mBAAmB;YACnB,2BAA2B;YAC3B,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB;YACpB,cAAc;YACd,aAAa;YACb,aAAa;YACb,oBAAoB;SACrB,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;KAsBvB,CAAC,CAAC;IACL,CAAC;CACF;AAzKD,sFAyKC", "sourcesContent": ["import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';\n\nexport class AddContractUpgradeFields1704067200000 implements MigrationInterface {\n  public async up(queryRunner: QueryRunner): Promise<void> {\n    // Adicionar novos campos para upgrade de contratos\n    await queryRunner.addColumns('contract', [\n      new TableColumn({\n        name: 'contract_type',\n        type: 'varchar',\n        length: '50',\n        isNullable: true,\n        comment: 'Tipo do contrato: MUTUO ou SCP',\n      }),\n      new TableColumn({\n        name: 'investment_value',\n        type: 'decimal',\n        precision: 15,\n        scale: 2,\n        isNullable: true,\n        comment: 'Valor do investimento',\n      }),\n      new TableColumn({\n        name: 'quota_quantity',\n        type: 'int',\n        isNullable: true,\n        comment: 'Quantidade de cotas para contratos SCP',\n      }),\n      new TableColumn({\n        name: 'payment_proof_url',\n        type: 'varchar',\n        length: '500',\n        isNullable: true,\n        comment: 'URL do comprovante de pagamento',\n      }),\n      new TableColumn({\n        name: 'payment_proof_uploaded_at',\n        type: 'timestamp',\n        isNullable: true,\n        comment: 'Data de upload do comprovante',\n      }),\n      new TableColumn({\n        name: 'audit_approved_at',\n        type: 'timestamp',\n        isNullable: true,\n        comment: 'Data de aprovação pela auditoria',\n      }),\n      new TableColumn({\n        name: 'audit_approved_by',\n        type: 'varchar',\n        length: '255',\n        isNullable: true,\n        comment: 'ID do usuário que aprovou',\n      }),\n      new TableColumn({\n        name: 'audit_rejected_at',\n        type: 'timestamp',\n        isNullable: true,\n        comment: 'Data de rejeição pela auditoria',\n      }),\n      new TableColumn({\n        name: 'audit_rejected_by',\n        type: 'varchar',\n        length: '255',\n        isNullable: true,\n        comment: 'ID do usuário que rejeitou',\n      }),\n      new TableColumn({\n        name: 'audit_observations',\n        type: 'text',\n        isNullable: true,\n        comment: 'Observações da auditoria',\n      }),\n      new TableColumn({\n        name: 'observations',\n        type: 'text',\n        isNullable: true,\n        comment: 'Observações gerais do contrato',\n      }),\n      new TableColumn({\n        name: 'ir_deposito',\n        type: 'boolean',\n        default: false,\n        comment: 'Investidor irá depositar valor referente ao IR',\n      }),\n      new TableColumn({\n        name: 'ir_desconto',\n        type: 'boolean',\n        default: false,\n        comment: 'Investidor decidiu desconto do IR sobre o valor do contrato',\n      }),\n      new TableColumn({\n        name: 'valor_complementar',\n        type: 'decimal',\n        precision: 15,\n        scale: 2,\n        isNullable: true,\n        comment: 'Valor complementar necessário para ajuste de cotas SCP',\n      }),\n    ]);\n\n    // Atualizar enum de status para incluir novo status\n    await queryRunner.query(`\n      ALTER TABLE contract \n      MODIFY COLUMN status ENUM(\n        'aberto',\n        'DRAFT',\n        'GENERATED',\n        'SIGNATURE_SENT',\n        'AWAITING_INVESTOR_SIGNATURE',\n        'AWAITING_DEPOSIT',\n        'AWAITING_PAYMENT_PROOF',\n        'AWAITING_AUDIT',\n        'AWAITING_AUDIT_SIGNATURE',\n        'ACTIVE',\n        'SIGNATURE_FAILED',\n        'EXPIRED_BY_INVESTOR',\n        'EXPIRED_BY_AUDIT',\n        'EXPIRED_FAILURE_PROOF_PAYMENT',\n        'REJECTED',\n        'REJECTED_BY_AUDIT',\n        'GENERATE_CONTRACT_FAILED',\n        'EXPIRED',\n        'DELETED'\n      ) DEFAULT 'aberto'\n    `);\n  }\n\n  public async down(queryRunner: QueryRunner): Promise<void> {\n    // Remover colunas adicionadas\n    await queryRunner.dropColumns('contract', [\n      'contract_type',\n      'investment_value',\n      'quota_quantity',\n      'payment_proof_url',\n      'payment_proof_uploaded_at',\n      'audit_approved_at',\n      'audit_approved_by',\n      'audit_rejected_at',\n      'audit_rejected_by',\n      'audit_observations',\n      'observations',\n      'ir_deposito',\n      'ir_desconto',\n      'valor_complementar',\n    ]);\n\n    // Reverter enum de status\n    await queryRunner.query(`\n      ALTER TABLE contract \n      MODIFY COLUMN status ENUM(\n        'aberto',\n        'DRAFT',\n        'GENERATED',\n        'SIGNATURE_SENT',\n        'AWAITING_INVESTOR_SIGNATURE',\n        'AWAITING_DEPOSIT',\n        'AWAITING_AUDIT',\n        'AWAITING_AUDIT_SIGNATURE',\n        'ACTIVE',\n        'SIGNATURE_FAILED',\n        'EXPIRED_BY_INVESTOR',\n        'EXPIRED_BY_AUDIT',\n        'EXPIRED_FAILURE_PROOF_PAYMENT',\n        'REJECTED',\n        'REJECTED_BY_AUDIT',\n        'GENERATE_CONTRACT_FAILED',\n        'EXPIRED',\n        'DELETED'\n      ) DEFAULT 'aberto'\n    `);\n  }\n}\n"]}