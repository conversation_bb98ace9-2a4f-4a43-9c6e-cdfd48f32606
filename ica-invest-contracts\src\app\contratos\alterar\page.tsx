"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "react-toastify";
import { useRouter, useSearchParams } from "next/navigation";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import CoverForm from "@/components/CoverForm";
import {
  cepMask,
  clearLetters,
  cpfMask,
  phoneMask,
  valueMask,
  cnpjMask,
} from "@/utils/masks";
import moment from "moment";
import InputText from "@/components/Inputs/InputText";
import SelectCustom from "@/components/SelectCustom";
import SelectSearch from "@/components/SelectSearch";

import { isValid } from "date-fns";
import { TrashIcon, PlusIcon } from "lucide-react";

import Dropzone from "react-dropzone";
import { useMemo, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
// Schema imports removed since using custom field names
import { getFinalDataWithMount } from "@/functions/getDataFilter";
import { useMutation, useQuery } from "@tanstack/react-query";
import returnError from "@/functions/returnError";
import api from "@/core/api";
import { getUserProfile } from "@/functions/getUserData";
import formatNumberValue from "@/utils/formatNumberValue";

// Função para máscara de telefone que preserva o código do país
const phoneWithCountryMask = (value: string) => {
  const cleaned = value.replace(/\D/g, '');

  // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)
  if (cleaned.startsWith('55') && cleaned.length === 13) {
    return cleaned
      .replace(/^(\d{2})(\d{2})(\d{5})(\d{4})$/, '$1 ($2) $3-$4');
  }

  // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55
  if (!cleaned.startsWith('55') && cleaned.length === 11) {
    const withCountryCode = '55' + cleaned;
    return withCountryCode
      .replace(/^(\d{2})(\d{2})(\d{5})(\d{4})$/, '$1 ($2) $3-$4');
  }

  // Para números em construção, aplicar máscara progressiva
  if (cleaned.length <= 13) {
    if (cleaned.startsWith('55')) {
      // Já tem código do país
      if (cleaned.length <= 4) {
        return cleaned.replace(/^(\d{2})(\d{0,2})$/, '$1 ($2');
      } else if (cleaned.length <= 9) {
        return cleaned.replace(/^(\d{2})(\d{2})(\d{0,5})$/, '$1 ($2) $3');
      } else {
        return cleaned.replace(/^(\d{2})(\d{2})(\d{5})(\d{0,4})$/, '$1 ($2) $3-$4');
      }
    } else {
      // Não tem código do país, usar máscara normal
      return phoneMask(value);
    }
  }

  return value;
};

// Schema de validação
const validationSchema = yup.object().shape({
  tipoContrato: yup.string().required('Selecione o tipo de contrato'),
  nomeCompleto: yup.string().required('Nome completo obrigatório'),
  identidade: yup.string().required('Identidade obrigatória'),
  celular: yup.string().required('Celular obrigatório'),
  cpf: yup.string().required('CPF/CNPJ obrigatório'),
  dataNascimento: yup.string().optional(),
  nomeMae: yup.string().optional(),
  email: yup.string().email('E-mail inválido').required('E-mail obrigatório'),
  cep: yup.string().required('CEP obrigatório'),
  cidade: yup.string().required('Cidade obrigatória'),
  endereco: yup.string().required('Endereço obrigatório'),
  numero: yup.string().required('Número obrigatório'),
  complemento: yup.string().optional(),
  estado: yup.string().required('Estado obrigatório'),
  banco: yup.string().required('Banco obrigatório'),
  conta: yup.string().required('Conta obrigatória'),
  agencia: yup.string().required('Agência obrigatória'),
  chavePix: yup.string().required('Chave PIX obrigatória'),
  modalidade: yup.string()
    .required('Modalidade obrigatória')
    .test('scp-to-mutuo-validation', 'Não é possível alterar contratos SCP para modalidade Mútuo', function(value) {
      // Esta validação será aplicada dinamicamente no contexto do componente
      return true; // A validação real é feita no onChange e onSubmit
    }),
  valorInvestimento: yup.string()
    .required('Valor do investimento obrigatório')
    .test('scp-multiple', 'Para contratos SCP, o valor deve ser múltiplo de R$ 5.000', function(value) {
      const modalidade = this.parent.modalidade;
      if (modalidade === 'SCP' && value) {
        // Usar formatNumberValue para converter corretamente valores com máscara brasileira
        const numericValue = Number(value.replaceAll('.', '').replaceAll(',', '.')) || 0;
        return numericValue > 0 && numericValue % 5000 === 0;
      }
      return true;
    })
    .test('scp-minimum', 'Para contratos SCP, o valor mínimo é de R$ 30.000', function(value) {
      const modalidade = this.parent.modalidade;
      if (modalidade === 'SCP' && value) {
        // Usar formatNumberValue para converter corretamente valores com máscara brasileira
        const numericValue = Number(value.replaceAll('.', '').replaceAll(',', '.')) || 0;
        return numericValue >= 30000;
      }
      return true;
    }),
  prazoInvestimento: yup.string().required('Prazo do investimento obrigatório'),
  taxaRemuneracao: yup.string().required('Taxa de remuneração obrigatória'),
  comprarCom: yup.string().required('Forma de compra obrigatória'),
  inicioContrato: yup.string().required('Início do contrato obrigatório'),
  fimContrato: yup.string(),
  perfil: yup.string().required('Perfil obrigatório'),
  debenture: yup.string().required('Debênture obrigatória'),
  observacoes: yup.string().optional(),
  quotaQuantity: yup.string().optional(), // Calculado automaticamente para SCP
  // Campos de IR (obrigatórios para MUTUO)
  irDeposito: yup.boolean().optional(),
  irDesconto: yup.boolean().optional(),
});

export default function AlterarContrato() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tipo = searchParams.get("tipo"); // "rentabilidade" ou "modalidade"
  const investorId = searchParams.get("investorId");
  const userProfile = getUserProfile();

  // Buscar dados do contrato atual
  const { data: contractData, isLoading: isLoadingContract } = useQuery({
    queryKey: ["contract", investorId],
    queryFn: async () => {
      if (!investorId) return null;
      const response = await api.get(`/contract/${investorId}`);
      return response.data;
    },
    enabled: !!investorId,
  });



  // Form is managed by react-hook-form with yup resolver

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    trigger,
    formState: { errors, isValid, isSubmitting },
  } = useForm({
    resolver: yupResolver(validationSchema),
    mode: "all",
    defaultValues: {
      //PÁGINA 1
      tipoContrato: "pf",
      nomeCompleto: "",
      identidade: "",
      celular: "",
      cpf: "",
      dataNascimento: "",
      nomeMae: "",
      email: "",
      cep: "",
      cidade: "",
      endereco: "",
      numero: "",
      complemento: "",
      estado: "",
      banco: "",
      conta: "",
      agencia: "",
      chavePix: "",
      observacoes: "",
      //PÁGINA 2
      modalidade: "MUTUO",
      valorInvestimento: "",
      prazoInvestimento: "",
      taxaRemuneracao: "",
      comprarCom: "pix",
      inicioContrato: "",
      fimContrato: "",
      perfil: "conservative",
      debenture: "n",
      quotaQuantity: "",
      // Opções de IR (mutuamente exclusivas)
      irDeposito: false,
      irDesconto: false,
    },
  });



  const [step, setStep] = useState(1); // 1 = parte 1, 2 = parte 2
  const [aliquotaIR, setAliquotaIR] = useState<string>("");
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [valorComplementar, setValorComplementar] = useState<number>(0);
  const [showComplementMessage, setShowComplementMessage] = useState(false);
  const [irCalculado, setIrCalculado] = useState(false); // Controla se o IR foi calculado
  const [irOpcaoSelecionada, setIrOpcaoSelecionada] = useState(false); // Controla se uma opção de IR foi selecionada
  const [valorOriginalInvestimento, setValorOriginalInvestimento] = useState<string>(""); // Armazena valor original antes do desconto

  // Identificar modalidade atual do contrato
  const currentContractModality = useMemo(() => {
    if (!contractData || !contractData.contracts?.[0]) return null;
    const contract = contractData.contracts[0];
    return contract.tags === "P2P" ? "MUTUO" : "SCP";
  }, [contractData]);

  // Pré-preencher formulário quando os dados chegarem
  useMemo(() => {
    if (contractData && !isLoadingContract) {
      const contract = contractData.contracts?.[0]; // Pega o primeiro contrato

      if (contract) {
        // Dados pessoais
        setValue("nomeCompleto", contract.investorName || "");

        // Aplicar máscara no documento (CPF/CNPJ)
        const documento = contractData.document || "";
        if (documento) {
          const documentoComMascara = documento.length <= 11 ? cpfMask(documento) : cnpjMask(documento);
          setValue("cpf", documentoComMascara);
        } else {
          setValue("cpf", "");
        }

        setValue("identidade", contractData.rg || "");
        setValue("email", contractData.email || "");

        // Aplicar máscara no telefone (preservando o formato original para o backend)
        const telefone = contractData.phone || "";
        setValue("celular", telefone ? phoneWithCountryMask(telefone) : "");

        // Pré-preencher nome da mãe e data de nascimento com dados do investidor
        setValue("nomeMae", contractData.motherName || "");
        setValue("dataNascimento", contractData.birthDate || "");

        // Endereço
        const cep = contractData.zipCode || "";
        setValue("cep", cep ? cepMask(cep) : "");
        setValue("cidade", contractData.city || "");
        setValue("endereco", contractData.address || "");
        setValue("numero", contractData.addressNumber || "");
        setValue("complemento", contractData.complement || "");
        setValue("estado", contractData.state || "");

        // Dados bancários
        setValue("banco", contractData.bank || "");
        setValue("agencia", contractData.branch || "");
        setValue("conta", contractData.accountNumber || "");
        setValue("chavePix", contractData.phone || contractData.email || ""); // Usar telefone ou email como PIX padrão

        // Dados do investimento (do contrato atual)
        const valorInvestimento = contract.investmentValue || "";
        // Aplicar máscara no valor do investimento se houver valor
        setValue("valorInvestimento", valorInvestimento ? valueMask(valorInvestimento) : "");
        setValue("taxaRemuneracao", contract.investmentYield || "");
        setValue("prazoInvestimento", contract.investmentTerm || "");
        // Mapear forma de pagamento
        let paymentMethod = "pix"; // padrão
        if (contract.purchasedWith?.includes("TRANSFERÊNCIA") || contract.purchasedWith?.includes("BANCÁRIA")) {
          paymentMethod = "bank_transfer";
        } else if (contract.purchasedWith?.includes("PIX")) {
          paymentMethod = "pix";
        } else if (contract.purchasedWith?.includes("BOLETO")) {
          paymentMethod = "boleto";
        }
        setValue("comprarCom", paymentMethod);
        setValue("inicioContrato", contract.contractStart ? moment(contract.contractStart).format("YYYY-MM-DD") : "");

        // Modalidade baseada nas tags
        setValue("modalidade", contract.tags === "P2P" ? "MUTUO" : "SCP");

        console.log("Formulário pré-preenchido com dados do contrato:", contractData);
      }
    }
  }, [contractData, isLoadingContract, setValue]);

  // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento
  const modalidade = watch("modalidade");
  const valorInvestimento = watch("valorInvestimento");

  useEffect(() => {
    if (modalidade === "SCP" && valorInvestimento) {
      // Usar formatNumberValue para converter corretamente o valor com máscara brasileira
      const valorNumerico = formatNumberValue(valorInvestimento) || 0;
      const cotas = Math.floor(valorNumerico / 5000);
      setValue("quotaQuantity", cotas.toString());
    }
  }, [modalidade, valorInvestimento, setValue]);

  // Verificar se há contratos válidos (ativos) para habilitar o botão
  const hasActiveContracts = useMemo(() => {
    if (!contractData || !contractData.contracts?.[0]) return false;

    const contract = contractData.contracts[0];
    const contractStatus = contract.contractStatus?.toUpperCase();

    // Verificar se o contrato principal está ativo
    const mainContractActive = contractStatus === 'ACTIVE' || contractStatus === 'ATIVO';

    // Verificar se há pelo menos um aditivo ativo
    let hasActiveAddendum = false;
    if (contract.addendum && contract.addendum.length > 0) {
      hasActiveAddendum = contract.addendum.some((addendum: any) => {
        const addendumStatus = addendum.contractStatus?.toUpperCase();
        return addendumStatus === 'ACTIVE' || addendumStatus === 'ATIVO';
      });
    }

    return mainContractActive || hasActiveAddendum;
  }, [contractData]);

  // Funções para navegação das opções de upgrade
  const handleRentabilityClick = () => {
    router.push(`/contratos/alterar?tipo=rentabilidade&investorId=${investorId}`);
  };

  const handleModalityClick = () => {
    router.push(`/contratos/alterar?tipo=modalidade&investorId=${investorId}`);
  };

  // Componente para as opções de upgrade
  const upgradeContractOptions = () => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-bold text-white mb-6">Escolha o tipo de alteração</h3>

        {/* Mudança de Rentabilidade */}
        <div
          className="flex md:flex-row flex-col gap-2 justify-between cursor-pointer"
          onClick={handleRentabilityClick}
        >
          <div className="bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors">
            <div className="flex">
              <div className="">
                <p className="ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white">
                  Mudança de Rentabilidade
                </p>
                <p className="ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300">
                  Clique aqui para mudar a rentabilidade do contrato do investidor
                </p>
              </div>
              <div className="flex-1" />
              <div className="flex items-center">
                <div className="">
                  <p className="text-white text-lg font-bold">
                    →
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Mudança de Modalidade */}
        <div
          className="flex md:flex-row flex-col gap-2 justify-between cursor-pointer"
          onClick={handleModalityClick}
        >
          <div className="bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors">
            <div className="flex">
              <div className="">
                <p className="ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white">
                  Mudança de Modalidade
                </p>
                <p className="ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300">
                  Clique aqui para mudar a modalidade do contrato do investidor
                </p>
              </div>
              <div className="flex-1" />
              <div className="flex items-center">
                <div className="">
                  <p className="text-white text-lg font-bold">
                    →
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  function calcularAliquotaIR(showToast = true) {
    // Pega o prazo do investimento em meses e converte para dias
    const prazoMeses = Number(watch("prazoInvestimento"));
    if (!prazoMeses || isNaN(prazoMeses)) {
      setAliquotaIR("");
      setIrCalculado(false);
      if (showToast) {
        toast.warn("Preencha o prazo do investimento para calcular o IR.");
      }
      return;
    }
    const prazoDias = prazoMeses * 30;
    let aliquota = "";
    if (prazoDias <= 180) {
      aliquota = "22,5%";
    } else if (prazoDias <= 360) {
      aliquota = "20%";
    } else if (prazoDias <= 720) {
      aliquota = "17,5%";
    } else {
      aliquota = "15%";
    }
    setAliquotaIR(aliquota);
    setIrCalculado(true);
    if (showToast) {
      toast.success("IR calculado com sucesso! Agora selecione uma das opções de IR abaixo.");
    }
  }

  const onSubmit = async (data: any) => {
    console.log("Iniciando submissão do formulário...", data);

    // Validações específicas para MUTUO
    if (data.modalidade === "MUTUO") {
      if (!irCalculado) {
        toast.error("⚠️ Obrigatório: Clique no botão 'Calcular IR' antes de prosseguir.");
        return;
      }

      if (!data.irDeposito && !data.irDesconto) {
        toast.error("⚠️ Obrigatório: Selecione uma das opções de IR (depósito ou desconto).");
        return;
      }
    }

    // Validar se está tentando mudar de SCP para MUTUO
    if (currentContractModality === "SCP" && data.modalidade === "MUTUO") {
      toast.error("Não é possível alterar contratos SCP para modalidade Mútuo");
      return;
    }

    console.log("Investor ID:", investorId);

    try {
      const isPJ = data.tipoContrato === "pj";
      // Máscara correta para CPF/CNPJ
      const documento = isPJ
        ? data.cpf.replace(/\D/g, "") // CNPJ
        : data.cpf.replace(/\D/g, ""); // CPF

      console.log("Dados processados:", { isPJ, documento, userProfile });

      // Criar objeto usando a estrutura do CreateContractDto
      const requestData: any = {
        name: data.nomeCompleto || "",
        rg: (() => {
          const rg = data.identidade;
          console.log("RG original:", rg, "Tipo:", typeof rg);

          if (!rg) return "123456789"; // Fallback para RG válido

          // Converter para string e remover caracteres não numéricos
          const rgLimpo = rg.toString().replace(/\D/g, "");
          console.log("RG limpo:", rgLimpo);

          // Se ficou vazio após limpeza, usar fallback
          const rgFinal = rgLimpo || "123456789";
          console.log("RG final:", rgFinal, "Tipo:", typeof rgFinal);

          return rgFinal;
        })(),
        phoneNumber: (() => {
          const cleanPhone = data.celular.replace(/\D/g, "");
          console.log("Telefone original:", data.celular);
          console.log("Telefone limpo:", cleanPhone);
          // Garantir que tenha 13 dígitos (55 + DDD + número)
          if (cleanPhone.length === 11 && !cleanPhone.startsWith('55')) {
            const phoneWithCountry = '55' + cleanPhone;
            console.log("Telefone com código do país:", phoneWithCountry);
            return phoneWithCountry;
          }
          return cleanPhone;
        })(),
        motherName: data.nomeMae || "",
        dtBirth: (() => {
          // Converter data para formato ISO 8601
          const birthDate = data.dataNascimento;
          console.log("Data nascimento original:", birthDate);

          if (!birthDate) return new Date().toISOString();

          // Se já está no formato YYYY-MM-DD, adicionar horário
          if (birthDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const isoDate = birthDate + 'T00:00:00.000Z';
            console.log("Data nascimento ISO:", isoDate);
            return isoDate;
          }

          // Se está em outro formato, tentar converter
          const date = new Date(birthDate);
          const isoDate = date.toISOString();
          console.log("Data nascimento convertida:", isoDate);
          return isoDate;
        })(),
        address: {
          zipCode: (() => {
            const cep = data.cep ? data.cep.replace(/\D/g, "") : "";
            console.log("CEP processado:", cep);
            return cep || "00000000"; // Fallback para CEP válido
          })(),
          neighborhood: "Centro", // Campo obrigatório
          state: data.estado || "BA", // Fallback para estado válido
          city: data.cidade || "Cidade", // Fallback para cidade válida
          complement: data.complemento || "",
          number: (() => {
            const numero = data.numero;
            console.log("Número original:", numero, "Tipo:", typeof numero);

            if (!numero) return "1"; // Fallback para "1" em vez de "0"

            // Converter para string e remover caracteres não numéricos
            const numeroLimpo = numero.toString().replace(/\D/g, "");
            console.log("Número limpo:", numeroLimpo);

            // Se ficou vazio após limpeza, usar "1"
            const numeroFinal = numeroLimpo || "1";
            console.log("Número final:", numeroFinal, "Tipo:", typeof numeroFinal);

            return numeroFinal;
          })()
        },
        accountBank: {
          bank: data.banco || "",
          accountNumber: data.conta || "",
          agency: data.agencia || "",
          pix: data.chavePix || ""
        },
        document: documento,
        contractType: data.modalidade,
        observations: "",
        placeOfBirth: data.cidade || "",
        occupation: "Investidor",
        documentType: isPJ ? "CNPJ" : "CPF",
        issuer: "SSP",
        quota: data.modalidade === "SCP" ? (parseInt(data.quotaQuantity || "0") || 0) : 0,
        paymentPercentage: parseFloat(data.taxaRemuneracao) || 0,
        parValue: parseInt(data.valorInvestimento.replace(/\D/g, "")) || 0
      };

      console.log("=== PAYLOAD COMPLETO ===");
      console.log("Enviando dados para API...", JSON.stringify(requestData, null, 2));
      console.log("=== VALIDAÇÕES ESPECÍFICAS ===");
      console.log("Data de nascimento final:", requestData.dtBirth, "Tipo:", typeof requestData.dtBirth);
      console.log("Número do endereço:", requestData.address.number, "Tipo:", typeof requestData.address.number);
      console.log("RG:", requestData.rg, "Tipo:", typeof requestData.rg);
      console.log("Quota:", requestData.quota, "Tipo:", typeof requestData.quota);
      console.log("PaymentPercentage:", requestData.paymentPercentage, "Tipo:", typeof requestData.paymentPercentage);
      console.log("ParValue:", requestData.parValue, "Tipo:", typeof requestData.parValue);
      console.log("=== VALIDAÇÃO NUMBER STRING ===");
      console.log("RG é number string?", /^\d+$/.test(requestData.rg));
      console.log("Number é number string?", /^\d+$/.test(requestData.address.number));

      createContractMutation.mutate(requestData);
    } catch (error) {
      console.error("Erro ao processar dados:", error);
      toast.error("Erro ao processar dados do contrato");
    }
  };

  // Validação e avanço para parte 2
  // Lista dos campos do step 1
  const camposStep1: (
    | "tipoContrato"
    | "nomeCompleto"
    | "identidade"
    | "celular"
    | "cpf"
    | "dataNascimento"
    | "nomeMae"
    | "email"
    | "cep"
    | "cidade"
    | "endereco"
    | "numero"
    | "complemento"
    | "estado"
    | "banco"
    | "conta"
    | "agencia"
    | "chavePix"
    | "observacoes"
    | "modalidade"
    | "valorInvestimento"
    | "prazoInvestimento"
    | "taxaRemuneracao"
    | "comprarCom"
    | "inicioContrato"
    | "fimContrato"
    | "perfil"
    | "debenture"
    | "quotaQuantity"
  )[] = [
      "tipoContrato",
      "nomeCompleto",
      "identidade",
      "celular",
      "cpf",
      // "dataNascimento", // Opcional - não vem da API
      // "nomeMae", // Opcional - não vem da API
      "email",
      "cep",
      "cidade",
      "endereco",
      "numero",
      // "complemento", // Opcional
      "estado",
      "banco",
      "conta",
      "agencia",
      "chavePix"
      // "observacoes" // Opcional
    ];

  const handleNext = async () => {
    console.log("Validando campos da página 1:", camposStep1);
    const valid = await trigger(camposStep1);
    console.log("Resultado da validação:", valid);
    console.log("Erros atuais:", errors);

    if (valid) {
      setStep(2);
      toast.success("Dados da página 1 validados com sucesso!");
    } else {
      // Contar quantos campos obrigatórios estão com erro
      const camposComErro = camposStep1.filter(campo => errors[campo]);
      const quantidadeErros = camposComErro.length;

      if (quantidadeErros > 0) {
        toast.error(`Por favor, preencha ${quantidadeErros} campo(s) obrigatório(s) antes de continuar.`);
      } else {
        toast.error("Por favor, preencha todos os campos obrigatórios antes de continuar.");
      }
    }
  };


  const createContractMutation = useMutation({
    mutationFn: async (data: any) => {
      console.log("Mutation - Criando novo contrato");
      console.log("Dados enviados:", data);

      const response = await api.post(`/contract`, data);
      console.log("Resposta da API:", response.data);
      return response.data;
    },
    onSuccess: (response) => {
      console.log("Contrato criado com sucesso:", response);

      // Mostrar toast de sucesso com informações do novo contrato
      const newContractId = response?.id;
      if (newContractId) {
        toast.success(`Contrato criado com sucesso! ID: ${newContractId.substring(0, 8)}... Redirecionando para a home...`);
      } else {
        toast.success("Contrato criado com sucesso! Redirecionando para a home...");
      }

      // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar
      setIsRedirecting(true);
      setTimeout(() => {
        router.push("/");
      }, 3000); // 3 segundos para dar tempo de ler a mensagem
    },
    onError: (error) => {
      returnError(error, "Erro ao atualizar o contrato");
    },
  });

  const term = watch("prazoInvestimento");
  const initDate = watch("inicioContrato");

  const endDate = useMemo(() => {
    if (initDate && term) {
      const endData = getFinalDataWithMount({
        investDate: String(term),
        startDate: initDate,
      });
      const formattedEndDate = moment(endData, "DD-MM-YYYY").format("DD/MM/YYYY");
      // Atualiza o campo fimContrato automaticamente
      setValue("fimContrato", moment(endData, "DD-MM-YYYY").format("YYYY-MM-DD"), { shouldValidate: true });
      return formattedEndDate;
    }
    return "";
  }, [initDate, term, setValue]);

  // Calcular dados do contrato atual e adendos para o detalhamento
  const contractDetails = useMemo(() => {
    if (!contractData || !contractData.contracts?.[0]) return null;

    const contract = contractData.contracts[0];
    const details = [];

    // Função para calcular IR baseado no prazo
    const calculateIR = (amount: number, rate: number, days: number) => {
      const totalReturn = (amount * rate * (days / 30)) / 100;
      let irRate = 0;
      if (days <= 180) irRate = 22.5;
      else if (days <= 360) irRate = 20;
      else if (days <= 720) irRate = 17.5;
      else irRate = 15;

      const irValue = (totalReturn * irRate) / 100;
      return { totalReturn, irRate, irValue };
    };

    // Contrato principal - apenas se estiver ATIVO
    const contractStatus = contract.contractStatus?.toUpperCase();
    if (contractStatus === 'ACTIVE' || contractStatus === 'ATIVO') {
      const mainAmount = parseInt(contract.investmentValue) || 0;
      const mainRate = parseFloat(contract.investmentYield) || 0;
      const mainStartDate = contract.contractStart ? moment(contract.contractStart) : moment();
      const mainEndDate = contract.contractEnd ? moment(contract.contractEnd) : moment();
      const mainDays = mainEndDate.diff(mainStartDate, 'days');
      const mainIR = calculateIR(mainAmount, mainRate, mainDays);

      details.push({
        type: 'Contrato Inicial',
        amount: mainAmount,
        daysRentabilized: mainDays,
        monthlyRate: mainRate,
        irRate: mainIR.irRate,
        irValue: mainIR.irValue,
        totalReturn: mainIR.totalReturn,
        status: contract.contractStatus
      });
    }

    // Adendos - apenas os que estão ATIVOS
    if (contract.addendum && contract.addendum.length > 0) {
      let activeAddendumIndex = 1; // Contador para aditivos ativos
      contract.addendum.forEach((addendum: any) => {
        const addendumStatus = addendum.contractStatus?.toUpperCase();

        // Incluir apenas aditivos com status ACTIVE ou ATIVO
        if (addendumStatus === 'ACTIVE' || addendumStatus === 'ATIVO') {
          const addAmount = parseFloat(addendum.investmentValue) || 0;
          const addRate = parseFloat(addendum.investmentYield) || 0;
          const addStartDate = addendum.contractStart ? moment(addendum.contractStart) : moment();
          const addEndDate = addendum.contractEnd ? moment(addendum.contractEnd) : moment();
          const addDays = addEndDate.diff(addStartDate, 'days');
          const addIR = calculateIR(addAmount, addRate, addDays);

          details.push({
            type: `Aditivo ${activeAddendumIndex}`,
            amount: addAmount,
            daysRentabilized: addDays,
            monthlyRate: addRate,
            irRate: addIR.irRate,
            irValue: addIR.irValue,
            totalReturn: addIR.totalReturn,
            status: addendum.contractStatus
          });

          activeAddendumIndex++; // Incrementar apenas para aditivos ativos
        }
      });
    }

    // Calcular total do IR
    const totalIR = details.reduce((sum, detail) => sum + detail.irValue, 0);

    return {
      details,
      totalIR,
      contractType: contract.tags || 'MUTUO'
    };
  }, [contractData]);

  // Calcular valor após desconto de IR e validar para SCP
  const irDesconto = watch("irDesconto");

  useEffect(() => {
    if (modalidade === "SCP" && valorInvestimento && irDesconto && contractDetails?.totalIR) {
      const valorNumerico = formatNumberValue(valorInvestimento) || 0;
      const valorAposDesconto = valorNumerico - contractDetails.totalIR;

      // Verificar se o valor após desconto é divisível por 5000
      const resto = valorAposDesconto % 5000;
      if (resto !== 0) {
        const valorComplementarNecessario = 5000 - resto;
        setValorComplementar(valorComplementarNecessario);
        setShowComplementMessage(true);
      } else {
        setShowComplementMessage(false);
        setValorComplementar(0);
      }
    } else {
      setShowComplementMessage(false);
      setValorComplementar(0);
    }
  }, [modalidade, valorInvestimento, irDesconto, contractDetails?.totalIR]);

  // Reset IR states quando modalidade mudar
  useEffect(() => {
    if (modalidade === "SCP") {
      // Para SCP, não precisa de IR
      setIrCalculado(true);
      setIrOpcaoSelecionada(true);
      setValue("irDeposito", false);
      setValue("irDesconto", false);
      setValorOriginalInvestimento(""); // Reset valor original
    } else if (modalidade === "MUTUO") {
      // Para MUTUO, resetar estados
      setIrCalculado(false);
      setIrOpcaoSelecionada(false);
      setAliquotaIR("");
      setValue("irDeposito", false);
      setValue("irDesconto", false);
      setValorOriginalInvestimento(""); // Reset valor original
    }
  }, [modalidade, setValue]);

  // Função auxiliar para converter string de valor para número
  function parseValor(valor: string): number {
    if (!valor) return 0;
    // Remove tudo que não for número ou vírgula
    const limpo = valor.replace(/[^0-9,]/g, '').replace(',', '.');
    return parseFloat(limpo) || 0;
  }

  // Função para calcular valor do IR baseado no investimento e alíquota
  function calcularValorIR(valorInvestimento: number, aliquotaPercent: number): number {
    if (!valorInvestimento || !aliquotaPercent) return 0;
    return (valorInvestimento * aliquotaPercent) / 100;
  }

  // Valor investido preenchido
  const valorInvestido = parseValor(watch("valorInvestimento"));
  // Alíquota em número
  const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace('%', '').replace(',', '.')) : 0;
  // Valor total de IR
  const valorTotalIR = valorInvestido && aliquotaNumber ? (valorInvestido * (aliquotaNumber / 100)) : 0;

  // PARTE 1: Dados pessoais, bancários, observações
  const renderStep1 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-bold text-white mb-4">Dados Pessoais</h3>
      <div className="bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8">
        <div className="flex flex-col gap-4 w-full">
          <div className="w-full md:w-1/2 mb-4">
            <p className="text-white mb-1">Tipo de Contrato</p>
            <SelectCustom
              value={watch("tipoContrato")}
              onChange={(e) => setValue("tipoContrato", e.target.value, { shouldValidate: true })}
            >
              <option value={"pf"}>Pessoa Física</option>
              <option value={"pj"}>Pessoa Jurídica</option>
            </SelectCustom>
          </div>
          <div className="w-full mb-4">
            <InputText
              register={register}
              name="nomeCompleto"
              width="100%"
              error={!!errors.nomeCompleto}
              errorMessage={errors?.nomeCompleto?.message}
              label="Nome Completo"
            />
          </div>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="identidade"
                width="100%"
                error={!!errors.identidade}
                errorMessage={errors?.identidade?.message}
                label="Identidade"
              />
            </div>
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="celular"
                width="100%"
                maxLength={18}
                error={!!errors.celular}
                errorMessage={errors?.celular?.message}
                label="Celular"
                onChange={e => {
                  setValue("celular", phoneWithCountryMask(e.target.value), { shouldValidate: true });
                }}
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="cpf"
                width="100%"
                error={!!errors.cpf}
                errorMessage={errors?.cpf?.message}
                label="CPF/CNPJ"
                onChange={e => {
                  const value = watch("tipoContrato") === "pj" ? cnpjMask(e.target.value) : cpfMask(e.target.value);
                  setValue("cpf", value, { shouldValidate: true });
                }}
              />
            </div>
            <div className="w-full md:w-1/2">
              <InputText
                type="date"
                register={register}
                name="dataNascimento"
                width="100%"
                error={!!errors.dataNascimento}
                errorMessage={errors?.dataNascimento?.message}
                label="Data de Nascimento (opcional)"
              />
            </div>
          </div>
          <div className="w-full mb-4">
            <InputText
              register={register}
              name="nomeMae"
              width="100%"
              error={!!errors.nomeMae}
              errorMessage={errors?.nomeMae?.message}
              label="Nome da Mãe (opcional)"
            />
          </div>
          <div className="w-full mb-4">
            <InputText
              register={register}
              name="email"
              width="100%"
              error={!!errors.email}
              errorMessage={errors?.email?.message}
              label="E-mail"
            />
          </div>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="cep"
                width="100%"
                error={!!errors.cep}
                errorMessage={errors?.cep?.message}
                label="CEP"
                onChange={e => {
                  setValue("cep", cepMask(e.target.value), { shouldValidate: true });
                }}
              />
            </div>
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="cidade"
                width="100%"
                error={!!errors.cidade}
                errorMessage={errors?.cidade?.message}
                label="Cidade"
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="estado"
                width="100%"
                error={!!errors.estado}
                errorMessage={errors?.estado?.message}
                label="Estado"
                placeholder="ex: SC"
              />
            </div>
          </div>
          <div className="w-full mb-4">
            <InputText
              register={register}
              name="endereco"
              width="100%"
              error={!!errors.endereco}
              errorMessage={errors?.endereco?.message}
              label="Endereço"
            />
          </div>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="numero"
                width="100%"
                error={!!errors.numero}
                errorMessage={errors?.numero?.message}
                label="Número"
              />
            </div>
            <div className="w-full md:w-1/2">
              <InputText
                register={register}
                name="complemento"
                width="100%"
                error={!!errors.complemento}
                errorMessage={errors?.complemento?.message}
                label="Complemento"
              />
            </div>
          </div>
        </div>
      </div>

      <h3 className="text-lg font-bold text-white mb-4">Dados Bancários</h3>
      <div className="bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8">
        <div className="flex flex-col md:flex-row gap-4 w-full mb-4">
          <div className="w-full md:w-1/2">
            <InputText
              register={register}
              name="banco"
              width="100%"
              error={!!errors.banco}
              errorMessage={errors?.banco?.message}
              label="Nome do Banco"
            />
          </div>
          <div className="w-full md:w-1/2">
            <InputText
              register={register}
              name="conta"
              width="100%"
              error={!!errors.conta}
              errorMessage={errors?.conta?.message}
              label="Conta"
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-4 w-full">
          <div className="w-full md:w-1/2">
            <InputText
              register={register}
              name="agencia"
              width="100%"
              error={!!errors.agencia}
              errorMessage={errors?.agencia?.message}
              label="Agência"
            />
          </div>
          <div className="w-full md:w-1/2">
            <InputText
              register={register}
              name="chavePix"
              width="100%"
              error={!!errors.chavePix}
              errorMessage={errors?.chavePix?.message}
              label="Chave PIX"
            />
          </div>
        </div>
      </div>

      <h3 className="text-lg font-bold text-white mb-4">Observações</h3>
      <div className="bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8">
        <textarea
          {...register("observacoes")}
          className="w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]"
          placeholder="Observações"
        />
        {errors.observacoes && (
          <span className="text-red-500 text-xs">{errors.observacoes.message}</span>
        )}
      </div>

      <div className="flex justify-end">

        <Button size="lg" type="button" className="bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]" onClick={handleNext}>
          Próximo
        </Button>
      </div>
    </div>
  );

  // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento
  const renderStep2 = () => (
    <form onSubmit={handleSubmit(onSubmit, (errors) => {
      console.log("Erros de validação:", errors);
      toast.error("Por favor, preencha todos os campos obrigatórios");
    })}>
      <div className="space-y-6">
        <div className="my-8 flex flex-col gap-4">
          <label className="text-white mb-1 block">Modalidade de Investimento</label>
          <div className="flex flex-col md:w-1/3">
            <SelectCustom
              value={watch("modalidade")}
              onChange={e => {
                const newModality = e.target.value;
                // Validar se está tentando mudar de SCP para MUTUO
                if (currentContractModality === "SCP" && newModality === "MUTUO") {
                  toast.error("Não é possível alterar contratos SCP para modalidade Mútuo");
                  return;
                }
                setValue("modalidade", newModality, { shouldValidate: true });
              }}
            >
              <option value="MUTUO">Mútuo</option>
              <option value="SCP">SCP</option>
            </SelectCustom>
            <span className="text-[#FF9900] text-xs mt-1">*Ao alterar modalidade, pressione o botão calcular IR</span>
            {/* Aviso quando contrato atual é SCP */}
            {currentContractModality === "SCP" && (
              <div className="bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2">
                <p className="text-yellow-200 text-xs">
                  ⚠️ <strong>Contrato atual é SCP:</strong> Não é possível alterar para modalidade Mútuo.
                  Apenas upgrades dentro da mesma modalidade ou de Mútuo para SCP são permitidos.
                </p>
              </div>
            )}
          </div>
          {/* Campo de valor para SCP */}
          {watch("modalidade") === "SCP" && (
            <div className="flex flex-col md:w-1/3 mt-2">
              <InputText
                register={register}
                name="valorInvestimento"
                width="100%"
                error={!!errors.valorInvestimento}
                errorMessage={errors?.valorInvestimento?.message}
                label="Valor do Investimento"
                placeholder="ex: R$ 50.000,00"
                setValue={e => setValue("valorInvestimento", valueMask(e || ""), { shouldValidate: true })}
              />
            </div>
          )}
          {/* Calculadora de IR - apenas para MUTUO */}
          {watch("modalidade") === "MUTUO" && (
            <div className="mt-4 md:w-1/3">
              <Button type="button" className="bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full" onClick={calcularAliquotaIR}>Calcular IR</Button>
            </div>
          )}
        </div>
        {/* Resumo/calculadora - apenas para MUTUO */}
        {watch("modalidade") === "MUTUO" && (
          <div className="bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3">
            <p className="text-white text-xs mb-1">Valor investido: {valorInvestido ? valorInvestido.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : 'R$ 0,00'}</p>
            <p className="text-white font-bold">Valor total de IR: {valorTotalIR ? valorTotalIR.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) : 'R$ 0,00'}</p>
            {aliquotaIR && (
              <p className="text-[#FF9900] font-bold mt-2">Alíquota do IR calculada: {aliquotaIR}</p>
            )}
          </div>
        )}
        {/* Detalhamento - apenas para MUTUO */}
        {watch("modalidade") === "MUTUO" && (
          <div className="w-full flex flex-col gap-4">
            <h4 className="text-lg font-bold text-white">Detalhamento</h4>
          <div className="overflow-x-auto">
            <table className="w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent">
              <thead>
                <tr className="bg-[#232323]">
                  <th className="px-2 py-2 border-r border-[#FF9900] font-semibold">Tipo de Contrato</th>
                  <th className="px-2 py-2 border-r border-[#FF9900] font-semibold">Valor do Contrato</th>
                  <th className="px-2 py-2 border-r border-[#FF9900] font-semibold">Dias Rentabilizados</th>
                  <th className="px-2 py-2 border-r border-[#FF9900] font-semibold">Rentabilidade</th>
                  <th className="px-2 py-2 border-r border-[#FF9900] font-semibold">Taxa de IR</th>
                  <th className="px-2 py-2 border-r border-[#FF9900] font-semibold">Valor do IR</th>
                  <th className="px-2 py-2 font-semibold">Anexos</th>
                </tr>
              </thead>
              <tbody>
                {isLoadingContract ? (
                  <tr>
                    <td colSpan={7} className="text-center px-2 py-4">Carregando dados do contrato...</td>
                  </tr>
                ) : contractDetails ? (
                  <>
                    {/* Renderizar contrato principal e adendos - apenas contratos ativos */}
                    {contractDetails.details.length > 0 ? (
                      contractDetails.details.map((detail: any, index: number) => (
                      <tr key={index}>
                        <td className="px-2 py-2 border-r border-[#FF9900]">
                          {detail.type}
                        </td>
                        <td className="px-2 py-2 border-r border-[#FF9900]">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(detail.amount)}
                        </td>
                        <td className="px-2 py-2 border-r border-[#FF9900]">{detail.daysRentabilized} Dias</td>
                        <td className="px-2 py-2 border-r border-[#FF9900]">{detail.monthlyRate}%</td>
                        <td className="px-2 py-2 border-r border-[#FF9900]">{detail.irRate}%</td>
                        <td className="px-2 py-2 border-r border-[#FF9900]">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(detail.irValue)}
                        </td>
                        <td className="px-2 py-2">-</td>
                      </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={7} className="text-center px-2 py-4 text-gray-400">
                          Nenhum contrato ativo encontrado para cálculo de IR
                        </td>
                      </tr>
                    )}
                    <tr>
                      <td colSpan={6} className="text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]">Valor total do IR</td>
                      <td className="px-2 py-2 font-bold text-white border-t border-[#FF9900]">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(contractDetails.totalIR)}
                      </td>
                    </tr>
                  </>
                ) : (
                  <tr>
                    <td colSpan={7} className="text-center px-2 py-4 text-gray-400">
                      Dados do contrato não encontrados
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          </div>
        )}

        {/* Aviso quando não há contratos ativos */}
        {!isLoadingContract && !hasActiveContracts && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-4 mb-4">
            <h4 className="text-white font-bold mb-2">⚠️ Nenhum Contrato Ativo Encontrado</h4>
            <p className="text-white text-sm">
              Não é possível realizar upgrade pois não há contratos ativos para este investidor.
              Apenas contratos com status ATIVO podem ser alterados.
            </p>
          </div>
        )}

        {/* Aviso para SCP sobre IR */}
        {watch("modalidade") === "SCP" && (
          <div className="bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4">
            <p className="text-blue-200 text-sm">
              ℹ️ <strong>Modalidade SCP:</strong> Contratos SCP não possuem desconto de Imposto de Renda.
              A tabela de IR não será exibida para esta modalidade.
            </p>
          </div>
        )}

        {/* Aviso para calcular IR primeiro */}
        {watch("modalidade") === "MUTUO" && !irCalculado && (
          <div className="bg-red-900 border border-red-500 rounded-lg p-4 mb-4">
            <p className="text-red-200 text-sm">
              ⚠️ <strong>Ação Obrigatória:</strong> Clique no botão "Calcular IR" acima antes de prosseguir.
            </p>
          </div>
        )}

        {/* Checkboxes de IR - apenas para MUTUO e após calcular IR */}
        {watch("modalidade") === "MUTUO" && irCalculado && (
          <>
            <div className="bg-green-900 border border-green-500 rounded-lg p-3 mb-4">
              <p className="text-green-200 text-sm">
                ✅ <strong>IR Calculado:</strong> Agora selecione uma das opções abaixo (obrigatório):
              </p>
            </div>
            <div className="flex flex-col md:flex-row gap-4 mb-2">
              <label className="flex items-center text-white text-xs">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={watch("irDeposito")}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setValue("irDeposito", true);
                      setValue("irDesconto", false); // Desmarca a outra opção
                      setIrOpcaoSelecionada(true);

                      // Restaurar valor original se existir (caso estava com desconto)
                      if (valorOriginalInvestimento) {
                        setValue("valorInvestimento", valorOriginalInvestimento);
                        toast.info("Valor original do investimento restaurado. O IR será depositado separadamente.");
                      }
                    } else {
                      setValue("irDeposito", false);
                      setIrOpcaoSelecionada(!!watch("irDesconto")); // Verifica se a outra está marcada
                    }
                  }}
                />
                Investidor irá depositar valor referente ao IR
              </label>
              <label className="flex items-center text-white text-xs">
                <input
                  type="checkbox"
                  className="mr-2"
                  checked={watch("irDesconto")}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setValue("irDesconto", true);
                      setValue("irDeposito", false); // Desmarca a outra opção
                      setIrOpcaoSelecionada(true);

                      // Aplicar desconto do IR automaticamente
                      const valorAtual = watch("valorInvestimento");
                      if (valorAtual && aliquotaIR) {
                        // Salvar valor original se ainda não foi salvo
                        if (!valorOriginalInvestimento) {
                          setValorOriginalInvestimento(valorAtual);
                        }

                        const valorNumerico = parseValor(valorAtual);
                        const aliquotaNumero = parseFloat(aliquotaIR.replace('%', '').replace(',', '.'));
                        const valorIR = calcularValorIR(valorNumerico, aliquotaNumero);
                        const valorComDesconto = valorNumerico - valorIR;

                        // Aplicar o valor com desconto
                        const valorFormatado = valueMask(valorComDesconto.toString());
                        setValue("valorInvestimento", valorFormatado);

                        toast.success(`✅ Desconto aplicado! IR de ${valueMask(valorIR.toString())} foi descontado do valor do investimento.`);
                      }

                      // Mostrar aviso de adendo
                      toast.info("⚠️ Atenção: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necessário criar um adendo para ajuste de valor.");
                    } else {
                      setValue("irDesconto", false);
                      setIrOpcaoSelecionada(!!watch("irDeposito")); // Verifica se a outra está marcada

                      // Restaurar valor original se existir
                      if (valorOriginalInvestimento) {
                        setValue("valorInvestimento", valorOriginalInvestimento);
                        toast.info("Valor original do investimento restaurado.");
                      }
                    }
                  }}
                />
                Investidor decidiu desconto do IR sobre o valor do contrato
              </label>
            </div>
          </>
        )}

        {/* Resumo do desconto de IR aplicado */}
        {watch("modalidade") === "MUTUO" && watch("irDesconto") && valorOriginalInvestimento && aliquotaIR && (
          <div className="bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4">
            <h4 className="text-blue-200 font-bold mb-2">💰 Desconto de IR Aplicado</h4>
            <div className="text-blue-200 text-sm space-y-1">
              <p><strong>Valor original:</strong> {valorOriginalInvestimento}</p>
              <p><strong>Alíquota de IR:</strong> {aliquotaIR}</p>
              <p><strong>Valor do IR:</strong> {(() => {
                const valorNumerico = parseValor(valorOriginalInvestimento);
                const aliquotaNumero = parseFloat(aliquotaIR.replace('%', '').replace(',', '.'));
                const valorIR = calcularValorIR(valorNumerico, aliquotaNumero);
                return valueMask(valorIR.toString());
              })()}</p>
              <p><strong>Valor final (com desconto):</strong> {watch("valorInvestimento")}</p>
            </div>
          </div>
        )}

        {/* Mensagem de valor complementar para SCP com desconto de IR */}
        {showComplementMessage && watch("modalidade") === "SCP" && watch("irDesconto") && (
          <div className="bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4">
            <h4 className="text-yellow-200 font-bold mb-2">⚠️ Ajuste de Valor Necessário</h4>
            <p className="text-yellow-200 text-sm mb-3">
              Informamos que o valor atual, após a aplicação do desconto do Imposto de Renda sobre o valor do contrato,
              não está em conformidade com nossas regras de negócio, pois não é divisível por R$ 5.000,00.
            </p>
            <p className="text-yellow-200 text-sm font-semibold">
              Para que as cotas sejam ajustadas corretamente, será necessário o pagamento complementar no valor de{' '}
              <span className="text-yellow-100 font-bold">
                {valorComplementar.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}
              </span>.
            </p>
          </div>
        )}
      </div>
      <h3 className="text-lg font-bold text-white my-8">Dados do Investimento</h3>
      <div className="bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Coluna esquerda */}
          <div className="flex flex-col gap-4 w-full md:w-1/2">
            {/* Campo de valor do investimento só aparece para MUTUO */}
            {watch("modalidade") === "MUTUO" && (
              <InputText
                register={register}
                name="valorInvestimento"
                width="100%"
                error={!!errors.valorInvestimento}
                errorMessage={errors?.valorInvestimento?.message}
                label="Valor do Investimento"
                setValue={e => setValue("valorInvestimento", valueMask(e || ""), { shouldValidate: true })}
              />
            )}
            {/* Campo de quantidade de cotas só aparece para SCP */}
            {watch("modalidade") === "SCP" && (
              <div className="flex flex-col">
                <InputText
                  register={register}
                  name="quotaQuantity"
                  width="100%"
                  error={!!errors.quotaQuantity}
                  errorMessage={errors?.quotaQuantity?.message}
                  label="Quantidade de cotas (calculado automaticamente)"
                  placeholder="Calculado automaticamente"
                  disabled={true}
                />
              </div>
            )}
            <InputText
              register={register}
              type="text"
              name="taxaRemuneracao"
              width="100%"
              error={!!errors.taxaRemuneracao}
              errorMessage={errors?.taxaRemuneracao?.message}
              label="Taxa de Remuneração Mensal %"
              placeholder="ex: 2"
            />
            <InputText
              type="date"
              register={register}
              maxDate={moment().format("YYYY-MM-DD")}
              name="inicioContrato"
              width="100%"
              setValue={e => setValue("inicioContrato", e, { shouldValidate: true })}
              error={!!errors.inicioContrato}
              errorMessage={errors?.inicioContrato?.message}
              label="Início do Contrato"
            />
            <div>
              <label className="text-white mb-1 block">Perfil</label>
              <SelectCustom
                value={watch("perfil")}
                onChange={e => setValue("perfil", e.target.value, { shouldValidate: true })}
              >
                <option value="conservative">Conservador</option>
                <option value="moderate">Moderado</option>
                <option value="aggressive">Agressivo</option>
              </SelectCustom>
            </div>
          </div>
          {/* Coluna direita */}
          <div className="flex flex-col gap-4 w-full md:w-1/2">
            <InputText
              register={register}
              type="number"
              name="prazoInvestimento"
              width="100%"
              error={!!errors.prazoInvestimento}
              errorMessage={errors?.prazoInvestimento?.message}
              label="Prazo do Investimento - em meses"
              placeholder="ex: 12"
            />
            <div>
              <label className="text-white mb-1 block">Comprar com</label>
              <SelectCustom
                value={watch("comprarCom")}
                onChange={e => setValue("comprarCom", e.target.value, { shouldValidate: true })}
              >
                <option value="pix">PIX</option>
                <option value="boleto">Boleto</option>
                <option value="bank_transfer">Transferência Bancária</option>
              </SelectCustom>
            </div>
            <InputText
              type="date"
              register={register}
              name="fimContrato"
              value={endDate ? moment(endDate, "DD/MM/YYYY").format("YYYY-MM-DD") : ""}
              width="100%"
              disabled={true}
              label="Fim do Contrato"
            />
            <div>
              <label className="text-white mb-1 block">Debênture</label>
              <SelectCustom
                value={watch("debenture")}
                onChange={e => setValue("debenture", e.target.value, { shouldValidate: true })}
              >
                <option value="s">Sim</option>
                <option value="n">Não</option>
              </SelectCustom>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          size="lg"
          onClick={() => setStep(1)}
          className="border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white"
        >
          ← Voltar
        </Button>
        <Button
          size="lg"
          type="submit"
          className={`px-8 py-2 rounded-lg ${
            hasActiveContracts
              ? "bg-[#FF9900] text-white hover:bg-[#e68a00]"
              : "bg-gray-500 text-gray-300 cursor-not-allowed"
          }`}
          onClick={() => {
            console.log("Botão Concluir clicado");
            console.log("Estado do formulário:", { isValid, errors });
            console.log("Dados atuais:", watch());
            console.log("Contratos ativos:", hasActiveContracts);
          }}
          disabled={
            !hasActiveContracts ||
            isSubmitting ||
            createContractMutation.isPending ||
            isRedirecting ||
            (watch("modalidade") === "MUTUO" && (!irCalculado || (!watch("irDeposito") && !watch("irDesconto"))))
          }
        >
          {!hasActiveContracts ? "Nenhum contrato ativo encontrado" :
           isRedirecting ? "Redirecionando..." :
           isSubmitting || createContractMutation.isPending ? "Enviando..." :
           (watch("modalidade") === "MUTUO" && !irCalculado) ? "Calcule o IR primeiro" :
           (watch("modalidade") === "MUTUO" && (!watch("irDeposito") && !watch("irDesconto"))) ? "Selecione uma opção de IR" :
           "Alterar Contrato"}
        </Button>
      </div>
    </form>
  );

  // Mostrar loading enquanto carrega os dados
  if (isLoadingContract) {
    return (
      <>
        <Header />
        <Sidebar>
          <div className="w-full">
            <div className="p-8 flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4"></div>
                <p className="text-white">Carregando dados do contrato...</p>
              </div>
            </div>
          </div>
        </Sidebar>
      </>
    );
  }

  return (
    <>
      <Header />
      <Sidebar>
        <div className="w-full">
          <div className="p-8">
            <div className="flex items-center mb-6 w-full justify-center">
              <h1 className="text-2xl text-center w-full font-bold text-white">
                Contratos
                {contractData && (
                  <span className="text-sm text-gray-400 block mt-1">
                    {contractData.contracts?.[0]?.investorName}
                  </span>
                )}
              </h1>
            </div>
            {/* Se não há tipo definido e há contratos ativos, mostrar opções de upgrade */}
            {!tipo && hasActiveContracts ? (
              upgradeContractOptions()
            ) : !tipo && !hasActiveContracts ? (
              /* Se não há contratos ativos, mostrar mensagem */
              <div className="bg-red-900 border border-red-500 rounded-lg p-6 text-center">
                <h4 className="text-white font-bold mb-2">⚠️ Nenhum Contrato Ativo Encontrado</h4>
                <p className="text-white text-sm">
                  Não é possível realizar upgrade pois não há contratos ativos para este investidor.
                  Apenas contratos com status ATIVO podem ser alterados.
                </p>
              </div>
            ) : (
              /* Se há tipo definido, mostrar formulário normal */
              step === 1 ? renderStep1() : renderStep2()
            )}
          </div>
        </div>
      </Sidebar>
    </>
  );
}
