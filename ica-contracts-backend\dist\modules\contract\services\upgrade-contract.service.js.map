{"version": 3, "file": "upgrade-contract.service.js", "sourceRoot": "/", "sources": ["modules/contract/services/upgrade-contract.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,+FAAsF;AACtF,qFAA2E;AAC3E,qCAAqC;AAErC,yHAA8G;AAC9G,0GAA2G;AAC3G,uGAAgG;AAChG,qHAA0G;AAGnG,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAGjC,YAEE,kBAA+D,EAE/D,2BAAiF,EAChE,wBAAkD,EAClD,yBAAoD;QAJpD,uBAAkB,GAAlB,kBAAkB,CAA4B;QAE9C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAChE,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,8BAAyB,GAAzB,yBAAyB,CAA2B;QARtD,WAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAS/D,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,UAAkB,EAClB,IAAwB,EACxB,MAAc;QAEd,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,UAAU,iBAAiB,MAAM,EAAE,CAAC,CAAC;QAGtF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,SAAS,EAAE,CAAC,UAAU,EAAE,mBAAmB,CAAC;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,gBAAgB,CAAC,MAAM,KAAK,yCAAkB,CAAC,MAAM,EAAE,CAAC;YAC1D,MAAM,IAAI,4BAAmB,CAC3B,+CAA+C,CAChD,CAAC;QACJ,CAAC;QAGD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,MAAM,cAAc,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAE/E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE;gBACL;oBACE,OAAO,EAAE,MAAM;iBAChB;gBACD;oBACE,UAAU,EAAE,MAAM;iBACnB;aACF;YACD,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAGnE,MAAM,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAGxD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YAG7D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,4BAAmB,CAAC,iDAAiD,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,MAAM,IAAI,4BAAmB,CAAC,uDAAuD,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACjD,MAAM,IAAI,4BAAmB,CAAC,yCAAyC,CAAC,CAAC;YAC3E,CAAC;YAED,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC9C,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;YACrE,CAAC;YAGD,IAAI,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;YAGzC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,QAAQ,GAAG,WAAW,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,QAAQ,EAAE,CAAC,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,QAAQ,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,MAAM,eAAe,GAAG;gBACtB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,QAAQ,EAAE,EAAE;gBACZ,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAE5F,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAEjF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;YAGvE,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;gBAC1C,uBAAuB,EAAE,WAAW,CAAC,EAAE;gBACvC,WAAW,EAAE,mFAAmF,UAAU,EAAE;gBAC5G,KAAK,EAAE,6BAA6B;gBACpC,IAAI,EAAE,0CAAoB,CAAC,YAAY;gBACvC,UAAU,EAAE,WAAW,CAAC,EAAE;gBAC1B,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC;gBAC3C,UAAU,EAAE,gBAAgB,CAAC,UAAU;aACxC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAG/D,OAAO;gBACL,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,MAAM,EAAE,WAAW,CAAC,MAAM;aAC3B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAG/C,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7F,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpG,CAAC;YAED,MAAM,IAAI,4BAAmB,CAAC,0CAA0C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;CACF,CAAA;AAnJY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADL,oBAAU;QAED,oBAAU;QACb,sDAAwB;QACvB,uDAAyB;GAT5D,sBAAsB,CAmJlC", "sourcesContent": ["import { BadRequestException, Injectable, NotFoundException, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\nimport { Repository } from 'typeorm';\r\nimport { EditNewContractDto } from '../dto/edit-new-contract.dto';\r\nimport { CreateNewContractService } from 'src/apis/ica-contract-service/services/create-new-contract.service';\r\nimport { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';\r\nimport { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\n\r\n@Injectable()\r\nexport class UpgradeContractService {\r\n  private readonly logger = new Logger(UpgradeContractService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    private readonly createNewContractService: CreateNewContractService,\r\n    private readonly createNotificationService: CreateNotificationService,\r\n  ) {}\r\n\r\n  async perform(\r\n    contractId: string,\r\n    data: EditNewContractDto,\r\n    userId: string,\r\n  ): Promise<{ id: string; status: string }> {\r\n    this.logger.log(`Iniciando upgrade do contrato ${contractId} pelo usuário ${userId}`);\r\n\r\n    // 1. Buscar contrato existente\r\n    const existingContract = await this.contractRepository.findOne({\r\n      where: { id: contractId },\r\n      relations: ['investor', 'ownerRoleRelation'],\r\n    });\r\n\r\n    if (!existingContract) {\r\n      throw new NotFoundException('Contrato não encontrado');\r\n    }\r\n\r\n    if (existingContract.status !== ContractStatusEnum.ACTIVE) {\r\n      throw new BadRequestException(\r\n        'Apenas contratos ativos podem ser atualizados',\r\n      );\r\n    }\r\n\r\n    // 2. Buscar perfil do usuário\r\n    this.logger.log(`Buscando perfil do usuário ${userId} com role: ${data.role}`);\r\n\r\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\r\n      where: [\r\n        {\r\n          ownerId: userId,\r\n        },\r\n        {\r\n          businessId: userId,\r\n        },\r\n      ],\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n        role: true,\r\n        upper: true,\r\n        bottom: true,\r\n      },\r\n    });\r\n\r\n    if (!userProfile) {\r\n      throw new BadRequestException('Perfil de usuário não encontrado.');\r\n    }\r\n\r\n    this.logger.log(`Perfil do usuário encontrado: ${userProfile.role.name}`);\r\n    this.logger.log(`Dados recebidos:`, JSON.stringify(data, null, 2));\r\n\r\n    // 3. Validações específicas de upgrade\r\n    await this.validateUpgradeRules(existingContract, data);\r\n\r\n    // 4. Criar novo contrato no contract-service (seguindo o mesmo fluxo)\r\n    try {\r\n      this.logger.log('Enviando dados para o contract-service...');\r\n\r\n      // Validar campos obrigatórios\r\n      if (!data.bankAccount) {\r\n        throw new BadRequestException('Dados bancários são obrigatórios para o upgrade');\r\n      }\r\n\r\n      if (!data.investment) {\r\n        throw new BadRequestException('Dados de investimento são obrigatórios para o upgrade');\r\n      }\r\n\r\n      if (data.personType === 'PF' && !data.individual) {\r\n        throw new BadRequestException('Dados da pessoa física são obrigatórios');\r\n      }\r\n\r\n      if (data.personType === 'PJ' && !data.company) {\r\n        throw new BadRequestException('Dados da empresa são obrigatórios');\r\n      }\r\n\r\n      // Determinar o brokerId correto baseado no contrato original\r\n      let brokerId = existingContract.brokerId;\r\n\r\n      // Se não há brokerId no contrato original, usar o ID do usuário atual\r\n      if (!brokerId) {\r\n        brokerId = userProfile.id; // Usar o ID da relação owner-role\r\n        this.logger.log(`Usando userProfile.id como brokerId: ${brokerId}`);\r\n      } else {\r\n        this.logger.log(`Usando brokerId do contrato original: ${brokerId}`);\r\n      }\r\n\r\n      const contractPayload = {\r\n        personType: data.personType,\r\n        contractType: data.contractType,\r\n        brokerId: brokerId,\r\n        investment: data.investment,\r\n        advisors: [], // Será preenchido com os assessores do contrato original se necessário\r\n        bankAccount: data.bankAccount,\r\n        individual: data.individual,\r\n        company: data.company,\r\n      };\r\n\r\n      this.logger.log('Payload para contract-service:', JSON.stringify(contractPayload, null, 2));\r\n\r\n      const newContract = await this.createNewContractService.perform(contractPayload);\r\n\r\n      this.logger.log(`Novo contrato criado com sucesso: ${newContract.id}`);\r\n\r\n      // 4. Criar notificação para auditoria (mesmo fluxo do contrato normal)\r\n      await this.createNotificationService.create({\r\n        userOwnerRoleRelationId: userProfile.id,\r\n        description: `Um upgrade de contrato foi realizado e precisa ser auditado. Contrato original: ${contractId}`,\r\n        title: `Upgrade de Contrato Gerado!`,\r\n        type: NotificationTypeEnum.NEW_CONTRACT,\r\n        contractId: newContract.id,\r\n        contractValue: data.investment?.amount || 0,\r\n        investorId: existingContract.investorId,\r\n      });\r\n\r\n      this.logger.log('Notificação de auditoria criada com sucesso');\r\n\r\n      // 5. Retornar dados do novo contrato\r\n      return {\r\n        id: newContract.id,\r\n        status: newContract.status,\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Erro ao criar upgrade do contrato:', error);\r\n      this.logger.error('Stack trace:', error.stack);\r\n\r\n      // Se for erro do contract-service, propagar a mensagem original\r\n      if (error.response?.data) {\r\n        this.logger.error('Erro do contract-service:', JSON.stringify(error.response.data, null, 2));\r\n        throw new BadRequestException(`Erro do contract-service: ${JSON.stringify(error.response.data)}`);\r\n      }\r\n\r\n      throw new BadRequestException(`Erro ao processar upgrade do contrato: ${error.message}`);\r\n    }\r\n  }\r\n} "]}