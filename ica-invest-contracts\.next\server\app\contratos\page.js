(()=>{var e={};e.id=2508,e.ids=[2508],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},9474:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>x,routeModule:()=>u,tree:()=>d});var s=a(73137),n=a(54647),r=a(4183),o=a.n(r),l=a(71775),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let c=s.AppPageRouteModule,d=["",{children:["contratos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7536)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,59197)),"C:\\projetos\\3\\ica-invest-contracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,51918,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\projetos\\3\\ica-invest-contracts\\src\\app\\contratos\\page.tsx"],m="/contratos/page",p={require:a,loadChunk:()=>Promise.resolve()},u=new c({definition:{kind:n.x.APP_PAGE,page:"/contratos/page",pathname:"/contratos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},23850:(e,t,a)=>{Promise.resolve().then(a.bind(a,32163))},32163:(e,t,a)=>{"use strict";a.r(t),a.d(t,{Screen:()=>Screen});var s=a(60080),n=a(97669),r=a(47956),o=a(9885),l=a(85814),i=a(28168),c=a(12129),d=a(30170),x=a(57114),m=a(96413),p=a(33050),u=a(15455),h=a(64731),f=a.n(h),v=a(34751);function RenewContract({contract:e,setOpenModal:t}){let[a,n]=(0,o.useState)(),[r,i]=(0,o.useState)(),[c,d]=(0,o.useState)(f()().format("YYYY-MM-DD")),[x,m]=(0,o.useState)(f()().format("YYYY-MM-DD"));return s.jsx("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"w-5/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[s.jsx("p",{className:"text-lg font-bold",children:"Renova\xe7\xe3o de contrato"}),(0,s.jsxs)("div",{className:"flex gap-4 mt-5",children:[(0,s.jsxs)("div",{className:"md:w-2/4",children:[s.jsx("p",{className:"text-white mb-1",children:"Inicio do contrato"}),s.jsx("input",{value:c,onChange:({target:e})=>d(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]}),(0,s.jsxs)("div",{className:"md:w-2/4",children:[s.jsx("p",{className:"text-white mb-1",children:"Fim do contrato"}),s.jsx("input",{value:x,onChange:({target:e})=>m(e.value),className:"h-12 w-full px-4 text-white rounded-xl ring-1 ring-inset bg-transparent flex-1 ring-[#FF9900]",type:"date"})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 mb-10 text-white items-center justify-around mt-5",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Contrato antigo"}),s.jsx(u.Z,{onFileUploaded:n})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Novo Contrato"}),s.jsx(u.Z,{onFileUploaded:i})]})]}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[s.jsx("div",{className:"px-10 bg-orange-linear flex items-center cursor-pointer",onClick:()=>{if(null===c)return v.Am.warning("Precisa informar a data de inicio de contrato");let s=new FormData;s.append("newStartDate",c),s.append("newEndDate",x),a&&s.append("oldContractPdf",a[0]),r&&s.append("newContractPdf",r[0]),l.Z.put(`/contract/${e.id}/upgrade`,s).then(e=>{v.Am.success("Contrato atualizado com sucesso!"),t(!1)}).catch(e=>{})},children:s.jsx("p",{className:"text-sm",children:"Renovar contrato"})}),s.jsx("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer",onClick:()=>t(!1),children:s.jsx("p",{className:"text-sm",children:"Fechar"})})]})]})})}var j=a(69195),g=a(957),N=a(32775);function ContractData({contract:e,setAditive:t,setModal:a,setModalPayment:n,setRenew:r,loading:o,resendContract:l}){return(0,s.jsxs)("div",{className:"flex flex-col gap-y-3 mt-5 w-full",children:[e.statusContrato===N.rd.DELETED&&s.jsx("div",{className:"flex w-full border-2 border-red-500 rounded-md p-2 mb-2 pb-10",children:s.jsx("p",{className:"text-sm text-white",children:e?.cancelledReason})}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"ID"}),s.jsx("p",{className:"text-xs text-end",children:e?.idContrato})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Investidor"}),s.jsx("p",{className:"text-xs text-end",children:e?.nomeInvestidor})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"CPF/CNPJ"}),s.jsx("p",{className:"text-xs text-end",children:e?.documentoInvestidor!==void 0&&m.p4(e?.documentoInvestidor)?.length<=11?(0,m.VL)(String(e?.documentoInvestidor)):(0,m.PK)(String(e?.documentoInvestidor))})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Consultor Respons\xe1vel"}),s.jsx("p",{className:"text-xs text-end",children:e?.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Comprado com"}),s.jsx("p",{className:"text-xs text-end",children:e?.compradoCom})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Assinaturas"}),s.jsx("p",{className:"text-xs text-end",children:e?.consultorResponsavel})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Valor"}),s.jsx("p",{className:"text-xs text-end",children:Number(e?.valorInvestimento).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Contrato Solicitado em"}),s.jsx("p",{className:"text-xs text-end",children:(0,p.Z)(e?.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Final do contrato"}),s.jsx("p",{className:"text-xs text-end",children:(0,p.Z)(e?.fimContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Status de Contrato"}),s.jsx("div",{children:s.jsx(g.Z,{description:(0,N.mP)(e.statusContrato).description,text:(0,N.mP)(e.statusContrato).title,textColor:(0,N.mP)(e.statusContrato).title})})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Qtd de Cotas/Participa\xe7\xf5es"}),s.jsx("p",{className:"text-xs text-end",children:e?.cotas||"N\xe3o encontrado"})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Data de Car\xeancia"}),s.jsx("p",{className:"text-xs text-end",children:e?.periodoCarencia})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Porcentagens"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:[e?.rendimentoInvestimento,"%"]})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Ativado em"}),s.jsx("p",{className:"text-xs text-end",children:(0,p.Z)(e?.inicioContrato)})]}),(0,s.jsxs)("div",{className:"flex w-full",children:[s.jsx("p",{className:"flex-1 font-bold text-xs",children:"Tags"}),(0,s.jsxs)("p",{className:"text-xs text-end",children:["#",e?.tags==="P2P"?"M\xfatuo":e?.tags||"NE"]})]}),e.contratoPdf&&s.jsx("div",{className:"flex w-full mt-3",children:s.jsx("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(e.contratoPdf,"_blank"),children:"Ver contrato"})}),e.comprovamentePagamento&&s.jsx("div",{className:"flex w-full mt-3",children:s.jsx("p",{className:"flex-1 font-bold text-sm text-[#FF9900] cursor-pointer",onClick:()=>window.open(e.comprovamentePagamento,"_blank"),children:"Ver comprovante anexado"})})]})}var b=a(99986),w=a(32411),C=a(49714),y=a(24577),S=a(17871),P=a(95081),F=a.n(P);function AditiveContract({contract:e,setOpenModal:t}){let[a,n]=(0,o.useState)(),[r,i]=(0,o.useState)(),[c,d]=(0,o.useState)("new"),[x,p]=(0,o.useState)(!1),[h,f]=(0,o.useState)({value:"",profile:"",yield:"",date:"",bank:"",agency:"",accountNumber:"",pix:"",comment:"",ownerName:"",ownerCpf:""}),createAditive=()=>{p(!0);let a={contractId:e?.idContrato,investment:{value:(0,S.Z)(h.value),profile:h.profile,yield:Number(e.rendimentoInvestimento),date:F()(h.date).format("YYYY-MM-DD")},accountBank:{bank:h.bank,accountNumber:h.accountNumber,agency:h.agency,pix:h.pix},owner:e.documentoInvestidor.length>11?{name:h.ownerName,cpf:(0,m.p4)(h.ownerCpf)}:void 0,observations:h.comment,signIca:C.l};l.Z.post("/contract/additive",a).then(e=>{v.Am.success("Contrato de aditivo criado com sucesso!"),t(!1)}).catch(e=>{(0,y.Z)(e.message,"N\xe3o conseguimos criar o contrato de aditivo!")}).finally(()=>p(!1))},registerAditiveExists=()=>{if("exist"===c&&(!r||!a))return v.Am.error("\xc9 necess\xe1rio anexar o contrato e o comprovante de pagamento");p(!0);let s=new FormData;r&&s.append("contractPdf",r[0]),a&&s.append("proofPayment",a[0]),s.append("investment[yield]",""!==h.yield||Number(h.yield)>0?h.yield:e.rendimentoInvestimento),s.append("contractId",e?.idContrato),s.append("investment[value]",h.value.replace(".","").replace(",",".")),s.append("investment[date]",F()(h.date).format("YYYY-MM-DD")),s.append("accountBank[bank]",h.bank),s.append("accountBank[accountNumber]",h.accountNumber),s.append("accountBank[agency]",h.agency),s.append("accountBank[pix]",h.pix),s.append("observations",h.comment),l.Z.post("/contract/additive-manual",s).then(e=>{v.Am.success("Contrato de aditivo cadastrado com sucesso!"),t(!1)}).catch(e=>{v.Am.error(e?.response?.data?.message||"N\xe3o foi poss\xedvel cadastrar o aditivo")}).finally(()=>p(!1))};return s.jsx("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-20",children:(0,s.jsxs)("div",{className:"md:w-6/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900] overflow-auto h-[88%]",children:[s.jsx("p",{className:"text-2xl font-bold",children:"Criar contrato aditivo"}),(0,s.jsxs)("div",{className:"mt-5 w-full",children:[(0,s.jsxs)("div",{className:"mb-5",children:[(0,s.jsxs)("div",{className:"flex gap-4 mb-5",children:[(0,s.jsxs)("div",{children:[s.jsx("input",{type:"checkbox",name:"",checked:"new"===c,onChange:()=>d("new"),id:"novo",className:"mr-2 cursor-pointer"}),s.jsx("label",{htmlFor:"novo",className:"cursor-pointer select-none",children:"Criar novo aditivo"})]}),(0,s.jsxs)("div",{children:[s.jsx("input",{type:"checkbox",name:"",checked:"exist"===c,onChange:()=>d("exist"),id:"manual",className:"mr-2 cursor-pointer"}),s.jsx("label",{htmlFor:"manual",className:"cursor-pointer select-none",children:"Cadastrar aditivo existente"})]})]}),"new"===c&&e.documentoInvestidor.length>11&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("p",{className:"mb-3 text-xl",children:"Dados do Representante Legal"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 mb-5",children:[s.jsx(w.Z,{id:"",label:"Nome",name:"",type:"text",value:h.ownerName,onChange:e=>{f({...h,ownerName:e.target.value})}}),s.jsx(w.Z,{id:"",label:"CPF",name:"",type:"text",value:h.ownerCpf,onChange:e=>{f({...h,ownerCpf:(0,m.VL)(e.target.value)})}})]})]}),s.jsx("p",{className:"mb-3 text-xl",children:"Dados de Investimento"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[s.jsx(w.Z,{id:"",label:"Valor do investimento",name:"",type:"text",value:h.value,onChange:e=>{f({...h,value:(0,m.Ht)(e.target.value)})}}),s.jsx(w.Z,{id:"",label:"Perfil investidor",name:"",type:"text",value:h.profile,onChange:e=>{f({...h,profile:e.target.value})}}),s.jsx(w.Z,{id:"",label:"Data da aplica\xe7\xe3o",name:"",type:"date",value:h.date,min:"exist"!==c?F().utc().format("YYYY-MM-DD"):void 0,max:"exist"===c?F().utc().format("YYYY-MM-DD"):void 0,onChange:e=>{f({...h,date:e.target.value})}})]})]}),(0,s.jsxs)("div",{className:"mb-10",children:[s.jsx("p",{className:"mb-3 text-xl",children:"Dados bancarios"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[s.jsx(w.Z,{id:"",label:"Nome do banco",name:"",type:"text",value:h.bank,onChange:e=>{f({...h,bank:e.target.value})}}),s.jsx(w.Z,{id:"",label:"Conta",name:"",type:"text",value:h.accountNumber,onChange:e=>{f({...h,accountNumber:e.target.value})}}),s.jsx(w.Z,{id:"",label:"Ag\xeancia",name:"",type:"text",value:h.agency,onChange:e=>{f({...h,agency:e.target.value})}}),s.jsx(w.Z,{id:"",label:"Chave pix",name:"",type:"text",value:h.pix,onChange:e=>{f({...h,pix:e.target.value})}})]})]}),(0,s.jsxs)("div",{className:"w-10/12",children:[s.jsx("p",{children:"Observa\xe7\xf5es"}),s.jsx("textarea",{value:h.comment,onChange:({target:e})=>f({...h,comment:e.value}),className:"w-full text-white rounded-xl ring-[#FF9900] ring-1 ring-inset bg-black flex-1 p-2 mt-2"})]})]}),"exist"===c&&(0,s.jsxs)("div",{className:"md:flex-row flex flex-col gap-2 mb-10 text-white jus  mt-5",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Aditivo"}),s.jsx(u.Z,{onFileUploaded:i})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Comprovante de pagamento"}),s.jsx(u.Z,{onFileUploaded:n})]})]}),(0,s.jsxs)("div",{className:"flex mt-10 gap-4 justify-end",children:[s.jsx("div",{children:s.jsx(b.Z,{label:"Criar aditivo",loading:x,className:"bg-orange-linear",disabled:x,handleSubmit:()=>{"new"===c?createAditive():registerAditiveExists()}})}),s.jsx("div",{children:s.jsx(b.Z,{label:"Fechar",loading:!1,handleSubmit:()=>t(!1)})})]})]})})}var A=a(90682),D=a(69888),Z=a(92170);function AditiveData({contract:e,aditives:t,getAditives:a}){let[n,r]=(0,o.useState)(),[c,d]=(0,o.useState)(),[x,m]=(0,o.useState)(!1),u=(0,A.e)();(0,o.useEffect)(()=>{c&&n&&onUploadFile()},[c,n]);let h=(0,o.useCallback)(e=>{r(e)},[]),onUploadFile=()=>{if(!n)return v.Am.warning("Selecione um comprovante para anexar!");m(!0),v.Am.info("Enviando comprovante...");let e=new FormData;e.append("addendumId",String(c)),n&&e.append("proofPayment",n[0]),l.Z.post("/contract/addendum/proof-payment",e).then(e=>{v.Am.success("Comprovante anexado com sucesso!"),a(),r(void 0)}).catch(e=>{(0,y.Z)(e,"Erro ao enviar comprovante")}).finally(()=>{m(!1)})},{getRootProps:f,getInputProps:j}=(0,Z.uI)({onDrop:h,maxFiles:1,multiple:!1,onError:e=>{"Failed to execute 'createObjectURL' on 'URL': Overload resolution failed."===e.message&&v.Am.warning("Tamano de arquivo exigido tem que ser maior que 80Kb e menor que 2Mb")},accept:{"image/*":[".png",".jpeg",".pdf"]},onFileDialogCancel:()=>{r(void 0)},disabled:x}),returnAddendumFiles=({files:e,type:t})=>{let a=e.filter(e=>e.type===t)[0];return a?s.jsx("p",{className:"w-full flex items-center justify-center",children:s.jsx(i.Z,{className:"cursor-pointer",onClick:()=>{window.open(a.url,"_blank")},color:"#fff",width:20})}):a||"PAYMENT"!==t||"broker"!==u.name&&"advisor"!==u.name?s.jsx("p",{className:"w-full flex items-center justify-center",children:s.jsx(D.Z,{color:"#FF9900",width:20})}):(0,s.jsxs)("div",{...f(),children:[s.jsx("input",{...j(),disabled:x,accept:".png,.jpg,.pdf"}),s.jsx("p",{className:`w-full flex items-center justify-center bg-orange-linear py-1 rounded-lg text-sm ${x?"opacity-50":"cursor-pointer"}`,children:"Anexar"})]})};return s.jsx("div",{className:"mt-5",children:(0,s.jsxs)("table",{className:"w-full relative min-h-20",children:[s.jsx("thead",{className:"w-full bg-[#313131] border-y border-y-[#FF9900]",children:(0,s.jsxs)("tr",{className:"w-full py-2",children:[s.jsx("th",{className:"min-w-[100px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Valor"})}),s.jsx("th",{className:"min-w-[150px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Rendimento"})}),s.jsx("th",{className:"min-w-[250px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Consultor"})}),s.jsx("th",{className:"min-w-[150px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Criado em"})}),s.jsx("th",{className:"min-w-[100px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Assinatura"})}),s.jsx("th",{className:"min-w-[100px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Contrato"})}),s.jsx("th",{className:"min-w-[120px]",children:s.jsx("p",{className:"font-bold text-sm",children:"Comprovante"})})]})}),t.length>=1?s.jsx("tbody",{className:"w-full",children:t.map((t,a)=>(0,s.jsxs)("tr",{className:"border-b-[1px] border-b-black h-10",children:[s.jsx("td",{children:s.jsx("p",{className:"text-xs text-center",children:Number(t.value||0).toLocaleString("pt-br",{style:"currency",currency:"BRL"})})}),s.jsx("td",{children:(0,s.jsxs)("p",{className:"text-xs text-center",children:[t.yieldRate||"0","%"]})}),s.jsx("td",{children:s.jsx("p",{className:"text-xs text-center",children:e.consultorResponsavel})}),s.jsx("td",{children:s.jsx("p",{className:"text-xs text-center",children:(0,p.Z)(t.applicationDate)})}),s.jsx("td",{className:"select-none",children:s.jsx(g.Z,{description:(0,N.XW)(t.status).description,text:(0,N.XW)(t.status).title,textColor:(0,N.XW)(t.status).textColor})}),s.jsx("td",{children:returnAddendumFiles({files:t?.addendumFiles,type:"ADDENDUM"})}),s.jsx("td",{onClick:()=>d(t.id),children:returnAddendumFiles({files:t?.addendumFiles,type:"PAYMENT",addendumId:t.id})})]},a))}):s.jsx("div",{className:"text-center mt-5 absolute w-full",children:s.jsx("p",{children:"Nenhum dado encontrado"})})]})})}var k=a(85334),I=a(69957),q=a(69145),M=a(16039);function ModalContract({contract:e,setModal:t,setRenew:a,setModalPayment:n}){let[r,i]=(0,o.useState)(0),[c,d]=(0,o.useState)(!1),[x,m]=(0,o.useState)([]),[p,u]=(0,o.useState)(!1),{navigation:h}=(0,k.H)(),[f,g]=(0,o.useState)(!1),[b,w]=(0,o.useState)(""),[C,S]=(0,o.useState)(!1),P=(0,A.e)(),getAditives=()=>{l.Z.get(`/contract/${e.idContrato}/addendum`).then(e=>{m(e.data.addendums)}).catch(e=>{v.Am.error(e?.response?.data?.message||"N\xe3o conseguimos carregar os aditivos do contrato.")})};return(0,o.useEffect)(()=>{!1===p&&getAditives()},[p]),(0,s.jsxs)("div",{className:"",children:[(0,s.jsxs)("div",{className:"w-full text-white overflow-auto",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 bg-orange-linear rounded-full mr-5 flex items-center justify-center",children:s.jsx(j.Z,{color:"#000",width:20})}),s.jsx("div",{className:"gap-y-1 flex flex-col",children:s.jsx("p",{className:"font-bold text-xs",children:"Detalhes do Contrato"})})]}),(0,s.jsxs)("div",{className:"w-full flex flex-wrap mt-4 gap-4 justify-start",children:[s.jsx("div",{className:`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${0===r?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>i(0),children:s.jsx("p",{className:"md:text-sm text-xs",children:"Dados do Contrato"})}),s.jsx("div",{className:`cursor-pointer w-[46%] md:w-auto px-4 py-3 hover:bg-zinc-800 rounded-lg ${1===r?"bg-zinc-800 text-[#FF9900]":""}`,onClick:()=>i(1),children:s.jsx("p",{className:"md:text-sm text-xs",children:"Aditivos"})})]}),f?(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-2 mt-5",children:"Digite o motivo da exclus\xe3o do contrato"}),s.jsx(q.Z,{name:"",value:b,setValue:w,className:"h-20"})]}):0===r?s.jsx(ContractData,{contract:e,loading:c,resendContract:()=>{d(!0),l.Z.post(`/contract/send-notification/${e.idContrato}`).then(e=>{v.Am.success("Contrato encaminhado novamente para o investidor.")}).catch(e=>{v.Am.error(e?.response?.data?.message||"N\xe3o conseguimos encaminhar o contrato para o investidor.")}).finally(()=>d(!1))},setAditive:u,setModal:t,setModalPayment:n,setRenew:a}):s.jsx("div",{className:"min-h-[300px]",children:s.jsx(AditiveData,{contract:e,aditives:x,getAditives:getAditives})})]}),(0,s.jsxs)("div",{className:"w-full flex mt-10 gap-2 justify-start",children:[e.statusContrato===N.rd.AWAITING_AUDIT&&!f&&s.jsx(I.z,{onClick:()=>{h(`/contratos/contrato/${e.idContrato}`)},children:"Auditar contrato"}),f?(0,s.jsxs)(s.Fragment,{children:[s.jsx(I.z,{loading:C,disabled:C,variant:"destructive",onClick:()=>{if(""===b)return v.Am.warn("Digite o motivo para excluir o contrato");S(!0),l.Z.post(`/contract/${e.idContrato}/delete`,{role:P.roleId,reason:b}).then(e=>{v.Am.success("Contrato excluido com sucesso!"),w(""),g(!1),setTimeout(()=>{window.location.reload()},2e3)}).catch(e=>{(0,y.Z)(e,"Erro ao excluir contrato!")}).finally(()=>{S(!1)})},children:"Confirmar"}),s.jsx(I.z,{disabled:C,variant:"secondary",onClick:()=>{g(!1),w("")},children:"Fechar"})]}):(0,M.f)(e.statusContrato)?s.jsx(I.z,{variant:"destructive",onClick:()=>g(!0),children:"Excluir Contrato"}):void 0]}),p&&e&&s.jsx(AditiveContract,{contract:e,setOpenModal:u})]})}function AddPayment({contract:e,setOpenModal:t,documentSearch:a,getContracts:n}){let[r,i]=(0,o.useState)(),[c,d]=(0,o.useState)(!1),renewContract=async()=>{if(!r)return v.Am.warning("Selecione um comprovante para anexar!");d(!0);let a=new FormData;a.append("contractId",e.idContrato),r&&a.append("file",r[0]),l.Z.put("/contract/upload/proof-payment",a).then(e=>{v.Am.success("Comprovante anexado com sucesso!"),d(!1),t(!1),setTimeout(()=>{window.location.reload()},1e3)}).catch(e=>{v.Am.error(e?.response?.data?.message||"N\xe3o foi possivel anexar o comprovante a esse contrato"),d(!1)})};return s.jsx("div",{className:"fixed w-full h-full bg-[#1c1c1c71] top-0 left-0 z-10",children:(0,s.jsxs)("div",{className:"md:w-3/12 w-11/12 m-auto bg-[#1C1C1C] mt-10 text-white z-20 p-5 border border-[#FF9900]",children:[s.jsx("p",{className:"text-lg font-bold",children:"Anexar comprovante"}),s.jsx("p",{className:"text-xs",children:"*Anexe neste campo o comprovante de Pagamento do seu\xa0Investidor"}),s.jsx("div",{className:"flex gap-2 mb-10 text-white items-start justify-center mt-5",children:(0,s.jsxs)("div",{children:[s.jsx("p",{className:"mb-1",children:"Comprovante"}),s.jsx(u.Z,{onFileUploaded:i})]})}),(0,s.jsxs)("div",{className:"flex w-full mt-10 justify-between gap-10",children:[s.jsx(b.Z,{label:"Anexar",loading:c,className:"bg-orange-linear",handleSubmit:renewContract}),s.jsx("div",{className:"px-5 py-2 bg-[#313131] cursor-pointer flex items-center",onClick:()=>t(!1),children:s.jsx("p",{className:"text-sm",children:"Fechar"})})]})]})})}var R=a(26147),_=a(48764),Y=a(51778),E=a(69258),T=a(53242),z=a(76027),U=a(50194),L=a(24774);function Screen({initialSignatarie:e,initialPage:t="1",initialType:a="all",initialStartData:u="",initialEndData:h="",initialStatus:f="Todos"}){let v=(0,x.useRouter)(),j=(0,x.useSearchParams)(),[b,w]=(0,o.useState)(),[C,y]=(0,o.useState)(!1),[S,P]=(0,o.useState)(!1),[F,D]=(0,o.useState)(!1),[Z,k]=(0,o.useState)(!1),[I,q]=(0,o.useState)(f),[M,V]=(0,o.useState)(Number(t)||1),[$,B]=(0,o.useState)(e||""),O=(0,T.Nr)($,300),[G,X]=(0,o.useState)({startData:u,endData:h,type:a,status:f}),[K,W]=(0,o.useState)(!1),J=(0,A.e)(),{data:H,isLoading:Q}=(0,Y.a)({queryKey:E.U.CONTRACTS(M,O,I,G,J.roleId),queryFn:async()=>{console.log("API call dateTo:",G.endData);let e=await l.Z.get(returnRoute(),{params:{roleId:J.roleId,limit:"10",page:M,status:"Todos"===I?void 0:I,signatarie:O?(0,m.p4)(O):void 0,dateFrom:""===G.startData?void 0:G.startData,dateTo:""===G.endData?void 0:G.endData,contractType:"all"===G.type?void 0:G.type}}),t=Number(e.data?.totalPaginas)||1;return M>t?(V(1),{data:{documentos:[],totalPaginas:t,total:0}}):e},staleTime:6e4});(0,o.useEffect)(()=>{if(localStorage.setItem("typeCreateContract",""),e){let t=new URLSearchParams(j.toString());t.set("signatarie",e),t.set("page","1"),v.replace(`?${t.toString()}`)}},[e,v,j]);let returnRoute=()=>{switch(J.name){case"admin":return"/admin/list-contracts";case"superadmin":return"/contract/list-contracts/superadmin";default:return""}},translateTag=e=>e?.toUpperCase()==="P2P"?"MUTUO":e.toUpperCase(),handleSearch=(e,t={...G,status:I})=>{let a=new URLSearchParams(j.toString());a.set("signatarie",e),a.set("page","1"),a.set("type",t.type||"all"),a.set("startData",t.startData||""),a.set("endData",t.endData||""),a.set("status",t.status||"Todos"),console.log("handleSearch params:",a.toString()),v.replace(`?${a.toString()}`)};return s.jsx("div",{children:(0,s.jsxs)(L.yo,{children:[s.jsx(n.Z,{}),s.jsx(r.Z,{children:(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"w-full text-white flex flex-col flex-wrap gap-2",children:s.jsx("h1",{className:"m-auto font-bold text-2xl",children:"Contratos"})}),s.jsx(R.Z,{children:(0,s.jsxs)("div",{className:"w-full p-2 gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-1 w-full justify-between items-center gap-4 mb-2",children:[("advisor"===J.name||"broker"===J.name)&&s.jsx("div",{className:"w-32 h-10 bg-orange-linear px-10 flex items-center justify-center md:mr-5 mb-2 md:mb-0 rounded-lg cursor-pointer",onClick:()=>{"advisor"===J.name?(localStorage.setItem("typeCreateContract","broker"),v.push("/meus-contratos/registro-manual")):W(!0)},children:s.jsx("p",{children:"Criar"})}),(0,s.jsxs)("div",{className:"flex items-center gap-4 w-full justify-end",children:[s.jsx("div",{className:"md:w-3/12",children:s.jsx(d.Z,{isDocument:!0,handleSearch:()=>handleSearch($),setValue:e=>{B(e);let t=new URLSearchParams(j.toString());""===e?t.delete("signatarie"):t.set("signatarie",e),t.set("page","1"),v.replace(`?${t.toString()}`)},placeholder:"Pesquisar por CPF/CNPJ",value:$})}),s.jsx(z.Z,{activeModal:C,setActiveModal:y,filterData:{...G,status:I},setFilterData:e=>{X({startData:e.startData,endData:e.endData,type:e.type,status:e.status}),q(e.status)},handleSearch:handleSearch,setPage:V,signatarie:$})]})]}),s.jsx(_.Z,{data:H?.data?.documentos||[],headers:[{title:"",component:"id",width:"30px",render:(e,t)=>s.jsx("div",{className:"cursor-pointer",onClick:()=>{P(!0),w(t)},children:s.jsx(L.aM,{children:s.jsx(i.Z,{color:"#fff",width:20})})})},{title:"Investidor",component:"nomeInvestidor",width:"150px"},{title:"CPF/CNPJ",component:"document",position:"center",width:"150px",render:(e,t)=>s.jsx("p",{className:"text-center",children:(0,m.p4)(t.documentoInvestidor||"").length<=11?(0,m.VL)(t.documentoInvestidor||""):(0,m.PK)(t.documentoInvestidor||"")})},{title:"Valor",component:"valorInvestimento",position:"center",render:(e,t)=>s.jsx("p",{className:"text-center",children:(0,U.F)(t)})},{title:"Rendimento",component:"rendimentoInvestimento",position:"center",render:e=>(0,s.jsxs)("p",{className:"text-center",children:[String(e)||"0","%"]})},{title:"Consultor",component:"consultorResponsavel",position:"center",width:"100px"},{title:"Criado em",component:"inicioContrato",position:"center",render:e=>s.jsx("p",{className:"text-center",children:(0,p.Z)(String(e))})},{title:"Status",component:"statusContrato",position:"center",width:"100px",render:(e,t)=>s.jsx(g.Z,{description:(0,N.mP)(t.statusContrato).description,text:(0,N.mP)(t.statusContrato).title,textColor:(0,N.mP)(t.statusContrato).textColor})},{title:"Modelo",component:"inicioContrato",position:"center",render:(e,t)=>s.jsx("div",{className:"px-2",children:s.jsx("div",{className:"bg-white py-[5px] px-[10px] rounded-md text-center",children:s.jsx("p",{className:"text-xs text-[#FF9900] font-bold",children:translateTag(t.tags)||"NE"})})})}],loading:Q,pagination:{page:M,lastPage:Number(H?.data?.totalPaginas)||1,perPage:10,setPage:e=>{V(e);let t=new URLSearchParams(j.toString());t.set("page",String(e)),v.replace(`?${t.toString()}`)},totalItems:String(H?.data?.total||0)}})]})}),s.jsx(L.ue,{className:"w-full md:w-[500px]",children:s.jsx(ModalContract,{contract:b,setRenew:k,setModal:P,setModalPayment:D})}),F&&b&&s.jsx(AddPayment,{contract:b,setOpenModal:D,documentSearch:$,getContracts:handleSearch}),Z&&b&&s.jsx(RenewContract,{contract:b,setOpenModal:k}),K&&s.jsx("div",{className:"fixed z-40 top-0 left-0 w-full h-full bg-[#3A3A3AAB]",children:(0,s.jsxs)("div",{className:"absolute w-3/6 bg-[#1C1C1C] z-50 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] border border-[#FF9900] rounded-lg",children:[(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center border-b border-[#FF9900]",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Individual"}),s.jsx("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com negocia\xe7\xe3o exclusiva para voc\xea."})]}),s.jsx("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer animate-moveXRight",onClick:()=>{localStorage.setItem("typeCreateContract","broker"),v.push("/meus-contratos/registro-manual")},children:s.jsx(c.Z,{width:20})})]}),(0,s.jsxs)("div",{className:"p-4 text-white flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-bold text-base mb-1",children:"Novo Contrato Compartilhado"}),s.jsx("p",{className:"text-xs w-7/12",children:"Clique aqui para criar um novo contrato com a negocia\xe7\xe3o personalizada de m\xfaltiplos assessores."})]}),s.jsx("div",{className:"bg-orange-linear p-2 rounded-full cursor-pointer",onClick:()=>{localStorage.setItem("typeCreateContract","advisors"),v.push("/meus-contratos/registro-manual")},children:s.jsx(c.Z,{width:20})})]})]})})]})})]})})}},99986:(e,t,a)=>{"use strict";a.d(t,{Z:()=>Button});var s=a(60080),n=a(69957);function Button({handleSubmit:e,loading:t,label:a,disabled:r,className:o,...l}){return s.jsx(n.z,{...l,onClick:e,loading:t,disabled:r,className:o,children:a})}},17871:(e,t,a)=>{"use strict";function formatNumberValue(e){return Number(e.replaceAll(".","").replaceAll(",","."))}a.d(t,{Z:()=>formatNumberValue})},7536:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Page});var s=a(48144),n=a(17536);let r=(0,n.createProxy)(String.raw`C:\projetos\3\ica-invest-contracts\src\app\contratos\_screen\index.tsx`),{__esModule:o,$$typeof:l}=r;r.default;let i=r.Screen;async function Page({searchParams:e}){let{signatarie:t="",page:a="1",type:n="all",startData:r="",endData:o="",status:l="Todos"}=await e;return s.jsx(i,{initialSignatarie:t,initialPage:a,initialType:n,initialStartData:r,initialEndData:o,initialStatus:l})}}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[4103,6426,4731,8813,5081,8394,1808,7878,5459,9301,7207,278,7669,8109,9012,2411,6774,8983],()=>__webpack_exec__(9474));module.exports=a})();